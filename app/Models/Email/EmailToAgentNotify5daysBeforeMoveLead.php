<?php

namespace App\Models\Email;


class EmailToAgentNotify5daysBeforeMoveLead extends GenericEmail
{
    private $leads;
    private $agent;

    public function __construct($leads, $agent)
    {
        $this->leads = $leads;
        $this->agent = $agent;
    }

    public function getSubject()
    {
        return "Notification email for leads - 48h before move";
    }

    public function getTemplateName()
    {
        return 'components.mail.lead-notify-5days-before-move';
    }

    public function getTo()
    {
        return $this->agent->email;
    }

    public function getTemplateVars()
    {
        return ["leads" => $this->leads, 'agent' => $this->agent];
    }
}
