<?php

// app/Models/WhatsappMessage.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WhatsappMessage extends Model
{
    use HasFactory;

    protected $fillable = [
        'message_id',
        'sender_phone',
        'sender_name',
        'message_content',
        'message_type',
        'metadata',
        'ref_number',
        'agent_id',
        'agent_notified',
        'message_timestamp'
    ];

    protected $casts = [
        'metadata' => 'array',
        'message_timestamp' => 'datetime',
        'agent_notified' => 'boolean'
    ];

    public function agent()
    {
        return $this->belongsTo(Agent::class);
    }
}