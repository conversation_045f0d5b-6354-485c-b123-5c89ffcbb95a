@extends('crm.layout')

@php
$profileImageDefault = 'https://via.placeholder.com/120x120.png?text=';
@endphp

@section('content')
<link rel="stylesheet" type="text/css" href="/css/virtual-select.min.css" />
<link rel="stylesheet" href="{{ mix('css/countries-flags.css') }}" />
<style>
    .property-scrollspy {
        position: relative;
        height: calc(100vh - 120px);
        margin-top: .5rem;
        overflow-y: auto;
    }

    .vscomp-ele {
        max-width: 100%;
        width: 100%;
    }
</style>

<div class="p-0 pb-4">
    <div class="row">
        <div class="col">
            <div>
                <div class="relative">
                    <nav id="navbar-user-form" class="navbar navbar-light bg-light px-3">
                        <ul class="nav nav-pills">
                            @foreach([
                            'user-info' => 'User info',
                            'roles' => 'Roles',
                            'social-media' => 'Social Media',
                            'passwords' => 'Password',
                            'visibility' => 'Visibility',
                            'extra-features' => 'Extra features'
                            ] as $sectionId => $sectionLabel)
                            <li class="nav-item">
                                <a class="nav-link" href="#{{$sectionId}}">{{$sectionLabel}}</a>
                            </li>
                            @endforeach
                        </ul>
                    </nav>
                </div>
                <div data-bs-spy="scroll" data-bs-target="#navbar-user-form" class="property-scrollspy" data-bs-offset="0" tabindex="0">
                    @if($errors->any())
                    <div class="alert alert-danger mt-4">Please review the fields marked with red</div>
                    @endif

                    @if (session('message.success'))
                    <div class="alert alert-success mt-4">
                        {{ session('message.success') }}
                    </div>
                    @endif

                    @if (session('message.error'))
                    <div class="alert alert-danger mt-4">
                        {{ session('message.error') }}
                    </div>
                    @endif
                    @if($item->exists && !is_null($item->author))
                    <div class="d-flex flex-column flex-md-row justify-content-end pt-3 pb-3">
                        <div class="me-2">Created by: <em>
                                {{ $item->author->name }} [{{ $item->author->email }}]
                            </em>
                        </div>
                        <div> at: {{ $item->created_at }}<em></em></div>
                    </div>
                    @endif
                    <form style="width:100%" action="{{$formAction}}" method="POST" enctype="multipart/form-data">
                        @csrf

                        @component('crm.components.form.section-card', ['sectionId' => 'user-info'])
                        <h4> User data</h4>
                        <div class="row">
                            <div class="col-12 col-lg-6">
                                <div class="row">
                                    @foreach([
                                    'email' => 'Email',
                                    'name' => 'Full Name',
                                    'position' => 'Designation',
                                    'phone' => 'Phone'
                                    ] as $fieldName => $fieldLabel)
                                    <div class="col-12">
                                        @if($fieldName == 'phone')
                                        @include('crm.components.form.input-phone',
                                        [
                                        'fieldName' => 'phone',
                                        'fieldLabel' => 'Phone',
                                        'defaultValue' => defaultValue($item, 'phone'),
                                        'defaultPrefixValue' => defaultValue($item, 'prefix_phone'),
                                        ]
                                        )
                                        @else
                                        @include('crm.components.form.input',
                                        [
                                        'fieldName' => $fieldName,
                                        'fieldLabel' => $fieldLabel,
                                        'defaultValue' => defaultValue($item, $fieldName)
                                        ]
                                        )
                                        @endif
                                    </div>
                                    @endforeach
                                    <div>
                                        @include('crm.components.form.textarea',
                                        [
                                        'fieldName' => 'short_bio',
                                        'fieldLabel' => 'Bio',
                                        'defaultValue' => defaultValue($item, 'short_bio'),
                                        'rowsNo' => 3
                                        ]
                                        )
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" @if(!empty(old('should_use_whatsapp_chatbot')) || $item->should_use_whatsapp_chatbot) checked @endif type="checkbox" name="should_use_whatsapp_chatbot" id="should_use_whatsapp_chatbot" value="1">
                                            <label class="form-check-label" for="should_use_whatsapp_chatbot">Should use Whatsapp Chatbot</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6">
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <label for="nationality" class="form-label">Nationality</label>
                                        <div id="nationality" class="{{ $errors->has('nationality_id') ? 'is-invalid' : '' }}"></div>
                                        @if($errors->has('nationality_id'))
                                        <div class="invalid-feedback" role="alert">
                                            {{ $errors->first('nationality_id') }}
                                        </div>
                                        @endif
                                    </div>
                                    <div class="col-12">
                                        @include('crm.components.form.input',
                                        [
                                        'fieldName' => 'date_of_birth',
                                        'fieldLabel' => 'Date of birth',
                                        'fieldType' => 'date',
                                        'defaultValue' => defaultValue($item, 'date_of_birth')
                                        ]
                                        )
                                    </div>
                                    <div class="col-12 mb-3">
                                        @include('crm.components.form.input',
                                        [
                                        'fieldName' => 'languages',
                                        'fieldLabel' => 'Spoken Languages',
                                        'defaultValue' => defaultValue($item, 'languages')
                                        ]
                                        )
                                    </div>
                                    <div class="col-12">
                                        <div class="mb-3 {{$errors->has('image') ? 'is-invalid' : ''}}">
                                            <label for="filePreview" class="form-label">Profile image</label>
                                            <div>
                                                <img id="previewImg" style="max-width: 120px" src="{{!is_null($item) && !empty($item->profile_image) ? imageRoute('square-150', $item->profile_image) : $profileImageDefault}}" alt="Profile Image" class="img-rounded">
                                            </div>

                                            <input class="form-control form-control-sm" name="image" type="file" onchange="loadProfileImgPreview(this)">
                                            @if(!is_null($item) && !empty($item->profile_image))
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="image-remove" value="1" id="imageRemove">
                                                <label class="form-check-label" for="imageRemove">
                                                    Remove image
                                                    <small class="help">if checked, the profile image will be deleted</small>
                                                </label>
                                            </div>
                                            @endif
                                        </div>
                                        @if($errors->has('image'))
                                        <div class="invalid-feedback">
                                            {{ $errors->first('image') }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>

                        @endcomponent

                        @component('crm.components.form.section-card', ['sectionId' => 'roles'])
                        <h4> Roles</h4>
                        <div class="row">
                            <div class="col">
                                @foreach($roles as $role)
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" @if(in_array($role->id, $userRoles) || is_array(old('roles')) && in_array($role->id, old('roles'))) checked @endif type="checkbox" name="roles[]" value="{{$role->id}}" id="inlineCheckbox{{$loop->index}}">
                                    <label class="form-check-label" for="inlineCheckbox{{$loop->index}}">{{$role->name}}</label>
                                </div>
                                @endforeach
                            </div>

                            <div class="row mt-4">
                                <label for="team-leader" class="form-label">Team Leader ( if this user should belong to a team )</label>
                                <div class="col-12 col-lg-6">
                                    <select id="team_leader_id" name="team_leader_id" class="form-select">
                                        <option value="">Please select</option>
                                        @foreach (getTeamLeaderAgents() as $option)
                                        <option value="{{$option->id}}" {{ !$empty && $item->team_leader_id ? ($item->team_leader_id == $option->id ? 'selected' : '') : ''}}> {{$option->name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <label for="brokerage-license-agent" class="form-label">Brokerage license account</label>
                                <div class="col-12 col-lg-6">
                                    <select id="brokerage-license-agent" name="brokerage_license_account_id" class="form-select">
                                        <option value="">Please select</option>
                                        @foreach (getAllFGRAgents() as $option)
                                        <option value="{{$option->id}}" {{ !$empty && $item->brokerage_license_account_id ? ($item->brokerage_license_account_id == $option->id ? 'selected' : '') : ''}}> {{$option->name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-4">
                                <div class="mb-3">
                                    <label for="" class="form-label">License</label>
                                    @if(!is_null($item) && !empty($item->license_path))
                                        <div class="mb-2">
                                            <a href="{{route('admin.user.license.download', ['id' => $item->id])}}"
                                                class="link-primary">{{$item->license_title}}</a>
                                        </div>
                                    @endif
                                    <input class="form-control form-control-sm" id="fileLicense" name="license" type="file">
                                    @if(!is_null($item) && !empty($item->license_path))
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="license-remove" value="1" id="licenseRemove">
                                        <label class="form-check-label" for="licenseRemove">
                                            Remove license
                                        </label>
                                    </div>
                                    @endif
                                </div>
                                @if($errors->has('license'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('license') }}
                                </div>
                                @endif
                            </div>
                            <div class="col-12 col-md-6">
                                @include('crm.components.form.input',
                                [
                                'fieldName' => 'license_id',
                                'fieldLabel' => 'Broker Card License',
                                'fieldType' => 'input',
                                'defaultValue' => defaultValue($item, 'license_id')
                                ]
                                )
                            </div>
                        </div>
                        @endcomponent
                        @component('crm.components.form.section-card', ['sectionId' => 'social-media'])
                        <h4> Social Media</h4>
                        <div class="row">
                            <div class="col-12 col-lg-6">
                                <div class="row">
                                    @foreach([
                                    'facebook' => 'Facebook',
                                    'instagram' => 'Instagram',
                                    'linkedin' => 'Linkedin'
                                    ] as $fieldName => $fieldLabel)
                                    <div class="col-12">
                                        @include('crm.components.form.input',
                                        [
                                        'fieldName' => $fieldName,
                                        'fieldLabel' => $fieldLabel,
                                        'defaultValue' => defaultValue($item, $fieldName),
                                        'extraAttrs' => $fieldName === 'linkedin' ? ['autocomplete' => 'off'] : []
                                        ]
                                        )
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        @endcomponent
                        @component('crm.components.form.section-card', ['sectionId' => 'passwords'])
                        <h4> Password</h4>
                        <div class="row">
                            <div class="col-12 col-md-6">
                                @include('crm.components.form.input',
                                [
                                'fieldName' => 'password',
                                'fieldLabel' => 'Password',
                                'defaultValue' => '',
                                'fieldType' => 'password',
                                'extraAttrs' => ['autocomplete' => 'new-password']
                                ]
                                )
                            </div>
                            <div class="col-12 col-md-6">
                                @include('crm.components.form.input',
                                [
                                'fieldName' => 'password_confirmation',
                                'fieldLabel' => 'Password Confirmation',
                                'defaultValue' => '',
                                'fieldType' => 'password',
                                'extraAttrs' => ['autocomplete' => 'new-password']
                                ]
                                )
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex gap-2 align-items-center flex-wrap">
                                    <button type="button" id="togglePasswordVisibility" class="btn btn-outline-secondary btn-sm">
                                        <i class="bi bi-eye-fill" id="passwordVisibilityIcon"></i>
                                        <span id="passwordVisibilityText">Show Password</span>
                                    </button>
                                    <button type="button" id="generatePassword" class="btn btn-outline-primary btn-sm">
                                        <i class="bi bi-key-fill"></i>
                                        Generate Password
                                    </button>
                                    <button type="button" id="copyPassword" class="btn btn-outline-success btn-sm" style="display: none;">
                                        <i class="bi bi-clipboard" id="copyIcon"></i>
                                        <span id="copyText">Copy Password</span>
                                    </button>
                                    <div id="passwordStrength" class="ms-2" style="display: none;">
                                        <small class="text-muted">Strength: </small>
                                        <span id="strengthIndicator" class="badge"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endcomponent
                        @component('crm.components.form.section-card', ['sectionId' => 'visibility'])
                        <h4> Visibility</h4>
                        <div class="row">
                            <div class="col-12 col-lg-6">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" @if(defaultValueRequest($item, 'public_profile' )) checked @endif type="checkbox" name="public_profile" value="1" id="publicProfile">
                                    <label class="form-check-label" for="publicProfile">Visible on public website</label>
                                </div>
                            </div>
                        </div>
                        @endcomponent
                        @component('crm.components.form.section-card', ['sectionId' => 'extra-features'])
                        <h4> Extra features</h4>
                        <div class="row">
                            <div class="col-12 col-lg-6">
                                <div class="form-check form-check-inline">
                                    <input class="form-check-input" @if(defaultValueRequest($item, 'is_active_for_competition' )) checked @endif type="checkbox" name="participate_to_competition" value="1" id="participateToCompetition">
                                    <label class="form-check-label" for="participateToCompetition">Participate to competition</label>
                                </div>
                            </div>
                        </div>
                        @endcomponent
                        <div class="mt-3 mb-3">
                            <div class="col-sm-12 header-btn text-end">
                                <button class="btn btn-outline-primary" type="submit">Save</button>
                                <a href="{{ route('admin.users.index')}}" class="btn btn-outline-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        @include('admin.users.right-sidebar', ['user' => $item])
    </div>

</div>
@endsection

@section('body-js')
<script type="text/javascript" src="{{mix('/js/virtual-select.min.js')}}"></script>
<script>
    const nationalitiesMap = JSON.parse('{!! $nationalities->mapWithKeys(function($v) { return [$v->id => $v->name];  })->toJSON() !!}');
    const nationalities = Object.keys(nationalitiesMap).map((key) => ({
        label: nationalitiesMap[key],
        value: key
    }));
    const previewImg = document.querySelector('#previewImg');
    const _URL = window.URL || window.webkitURL;
    const defaultImg = '{{$profileImageDefault}}';
    const removeImageCheckbox = document.querySelector('#imageRemove');

    const loadProfileImgPreview = (fileInput) => {
        if (fileInput.files && fileInput.files[0]) {
            previewImage(fileInput.files[0]);
            removeImageCheckbox.checked = false;
        }
    }

    const previewImage = (file) => {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.setAttribute('src', e.target.result)
        }
        reader.readAsDataURL(file)
    }

    const selectSelection = document.querySelectorAll('.input-group .dropdown-menu');
    for (const selection of selectSelection) {
        selection.addEventListener('click', (evt) => {
            if (evt.target.classList.contains('countryLi')) {
                const node = evt.target;
                const countryName = node.getAttribute('data-countryname')
                const countryPrefix = node.getAttribute('data-countryprefix')
                const countryCode = node.getAttribute('data-countrycode')
                const captionContainer = node.closest('.fg-dropdown').querySelector('.dropdown__caption')
                const hiddenInput = node.closest('.fg-dropdown').querySelector('.phonePrefixInput')
                hiddenInput.value = countryPrefix;
                captionContainer.innerHTML = `<span class="flag flag-${countryCode}"></span> ${countryPrefix}`;
            }
        })
    }

    const nationalitySelectConfig = {
        ele: '#nationality',
        options: nationalities,
        search: true,
        name: 'nationality_id',
        additionalClasses: 'form-input',
        placeholder: 'Please select',
    };

    if ('{{ old("nationality_id", defaultValue($item, "nationality_id")) }}') {
        nationalitySelectConfig.selectedValue = "{{ old('nationality_id', defaultValue($item, 'nationality_id')) }}";
    }
    const configs = [
        nationalitySelectConfig,
        // towersConfig,
    ];
    window.addEventListener('load', () => {
        configs.forEach(config => VirtualSelect.init(config))
    });

    // Password functionality - Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        initPasswordFunctionality();
    });

    function initPasswordFunctionality() {
        let passwordVisible = false;

        // Try multiple ways to find the password fields
        let passwordField = document.getElementById('password');
        let passwordConfirmationField = document.getElementById('password_confirmation');

        const toggleButton = document.getElementById('togglePasswordVisibility');
        const visibilityIcon = document.getElementById('passwordVisibilityIcon');
        const visibilityText = document.getElementById('passwordVisibilityText');
        const generateButton = document.getElementById('generatePassword');
        const copyButton = document.getElementById('copyPassword');
        const copyIcon = document.getElementById('copyIcon');
        const copyText = document.getElementById('copyText');
        const strengthContainer = document.getElementById('passwordStrength');
        const strengthIndicator = document.getElementById('strengthIndicator');

        // Debug: Check if elements are found
        console.log('Password field found:', passwordField);
        console.log('Password confirmation field found:', passwordConfirmationField);
        console.log('All elements found:', {
            passwordField: !!passwordField,
            passwordConfirmationField: !!passwordConfirmationField,
            toggleButton: !!toggleButton,
            generateButton: !!generateButton
        });

        // Toggle password visibility
        if (toggleButton && passwordField && passwordConfirmationField) {
            toggleButton.addEventListener('click', function() {
                passwordVisible = !passwordVisible;
                const inputType = passwordVisible ? 'text' : 'password';

                passwordField.type = inputType;
                passwordConfirmationField.type = inputType;

                if (passwordVisible) {
                    visibilityIcon.className = 'bi bi-eye-slash';
                    visibilityText.textContent = 'Hide Password';
                } else {
                    visibilityIcon.className = 'bi bi-eye-fill';
                    visibilityText.textContent = 'Show Password';
                }
            });
        }

        // Generate password
        if (generateButton) {
            generateButton.addEventListener('click', function() {
                const password = generateSecurePassword();

                if (passwordField && passwordConfirmationField) {
                    passwordField.value = password;
                    passwordConfirmationField.value = password;

                    // Show password when generated
                    if (!passwordVisible && toggleButton) {
                        toggleButton.click();
                    }

                    // Show strength indicator and copy button
                    updatePasswordStrength(password);
                    if (copyButton) {
                        copyButton.style.display = 'inline-block';
                    }
                } else {
                    console.error('Password fields not found');
                }
            });
        }

        // Copy password to clipboard
        if (copyButton) {
            copyButton.addEventListener('click', async function() {
                if (passwordField && passwordField.value) {
                    try {
                        await navigator.clipboard.writeText(passwordField.value);

                        // Temporary feedback
                        const originalIcon = copyIcon.className;
                        const originalText = copyText.textContent;

                        copyIcon.className = 'bi bi-check-circle-fill';
                        copyText.textContent = 'Copied!';
                        copyButton.classList.remove('btn-outline-success');
                        copyButton.classList.add('btn-success');

                        setTimeout(() => {
                            copyIcon.className = originalIcon;
                            copyText.textContent = originalText;
                            copyButton.classList.remove('btn-success');
                            copyButton.classList.add('btn-outline-success');
                        }, 2000);

                    } catch (err) {
                        console.error('Failed to copy password: ', err);
                        // Fallback for older browsers
                        const textArea = document.createElement('textarea');
                        textArea.value = passwordField.value;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);

                        copyText.textContent = 'Copied!';
                        setTimeout(() => {
                            copyText.textContent = 'Copy Password';
                        }, 2000);
                    }
                }
            });
        }

        // Password strength checker
        function updatePasswordStrength(password) {
            if (strengthContainer && strengthIndicator) {
                const strength = calculatePasswordStrength(password);
                strengthContainer.style.display = 'block';

                strengthIndicator.className = `badge bg-${strength.color}`;
                strengthIndicator.textContent = strength.text;
            }
        }

        function calculatePasswordStrength(password) {
            let score = 0;

            if (password.length >= 8) score++;
            if (password.length >= 12) score++;
            if (/[a-z]/.test(password)) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/[0-9]/.test(password)) score++;
            if (/[^A-Za-z0-9]/.test(password)) score++;

            if (score < 3) return { text: 'Weak', color: 'danger' };
            if (score < 5) return { text: 'Medium', color: 'warning' };
            return { text: 'Strong', color: 'success' };
        }

        function generateSecurePassword() {
            const length = 12;
            const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
            let password = '';

            // Ensure at least one character from each category
            password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)];
            password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)];
            password += '0123456789'[Math.floor(Math.random() * 10)];
            password += '!@#$%^&*'[Math.floor(Math.random() * 8)];

            // Fill the rest randomly
            for (let i = password.length; i < length; i++) {
                password += charset[Math.floor(Math.random() * charset.length)];
            }

            // Shuffle the password
            return password.split('').sort(() => Math.random() - 0.5).join('');
        }

        // Update strength when typing
        if (passwordField) {
            passwordField.addEventListener('input', function() {
                if (this.value) {
                    updatePasswordStrength(this.value);
                    if (copyButton) {
                        copyButton.style.display = 'inline-block';
                    }
                } else {
                    if (strengthContainer) {
                        strengthContainer.style.display = 'none';
                    }
                    if (copyButton) {
                        copyButton.style.display = 'none';
                    }
                }
            });
        }
    }
</script>
@endsection