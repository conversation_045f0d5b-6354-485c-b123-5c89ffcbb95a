<?php

namespace App\Services;

use App\Models\Crm\RolesDef;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use DB;

class UserService extends GenericService
{
    function getToken()
    {
        return hash('sha256', Str::random(60));
    }

    function getPossibleListingOwners($onlyUsersInTeam = false)
    {
        $user = auth()->user();
        $userIsMatrixAgent = $user->hasRole(RolesDef::MATRIX_AGENT);
        $userIsMatrixAgentManager = $user->hasRole(RolesDef::MATRIX_AGENT_MANAGER);
        $userIsTeamLeader = $user->hasRole(RolesDef::TEAM_LEADER);

        if ($userIsMatrixAgent || $userIsMatrixAgentManager) {
            $managerId = $userIsMatrixAgent ? $user->created_by : $user->id;
            $users = User::orderBy('name', 'asc')->where(function ($qb) use ($user, $managerId) {
                $qb->where('created_by', $managerId)
                    ->orWhere('id', $managerId);
            })->get();
        } else if ($userIsTeamLeader && $onlyUsersInTeam) {
            $users = User::orderBy('name', 'asc')->where(function ($qb) use ($user) {
                $qb->where('team_leader_id', $user->id)
                    ->orWhere('id', $user->id);
            })->get();
            foreach ($users as $user) {
                $user->selectName = $user->name;
            }
        } else {
            $users = User::get()->filter(function ($innerUser) {
                return $innerUser->hasAnyRole([
                    RolesDef::MASTER_BROKER,
                    RolesDef::AGENT,
                    RolesDef::OFFICE_MANAGER,
                    RolesDef::MATRIX_AGENT,
                    RolesDef::MATRIX_AGENT_MANAGER,
                    RolesDef::TEAM_LEADER,
                ]);
            })->each(function ($item) {
                $item->selectName = $item->name;
                if ($item->hasRole(RolesDef::MATRIX_AGENT)) {
                    $item->selectName = '[M Ag] ' . $item->name;
                }
                if ($item->hasRole(RolesDef::MATRIX_AGENT_MANAGER)) {

                    $item->selectName = '[M Mg] ' . $item->name;
                }
            })
                ->sortBy('selectName');
        }
        return $users;
    }

    public function getTableItemsBaseQ(Request $request, $extraClauses = [], $groupByClause = null)
    {
        $extraClause = $this->getRequestParams($request);

        $q = DB::table('users')
            ->leftJoin('model_has_roles as mhr', function ($qb) {
                $qb->on('mhr.model_id', '=', 'users.id')
                    ->where('mhr.model_type', User::class);
            })
            ->leftJoin('roles as r', 'r.id', '=', 'mhr.role_id')
            ->leftJoin('users as blu', 'blu.id', '=', 'users.brokerage_license_account_id');

        if (!empty($extraClause['sort'])) {
            if (!empty($extraClause['dir'])) {
                $q = $q->orderBy($extraClause['sort'], $extraClause['dir']);
            } else {
                $q = $q->orderBy($extraClause['sort']);
            }
        } else {
            $q = $q->orderBy('users.name', 'asc');
        }

        if (!empty($extraClause['q'])) {
            $q->where(function ($sql) use ($extraClause) {
                $term = trim($extraClause['q']) . '%';
                $sql->where('users.name', 'like', $term)
                    ->orWhere('users.username', 'like', $term)
                    ->orWhere('users.email', 'like', $term)
                    ->orWhere('users.position', 'like', $term);
            });
        }
        // Filter by nationality
        if (!empty($extraClauses['nationality'])) {
            $q->where('users.nationality_id', $extraClauses['nationality']);
        }

        // Filter by roles
        if (!empty($extraClauses['roles'])) {
            $roles = is_array($extraClauses['roles']) ? $extraClauses['roles'] : [$extraClauses['roles']];
            $q->whereIn('r.id', $roles);
        }

        // Filter by team leader
        if (!empty($extraClauses['teamLeader'])) {
            $q->where('users.team_leader_id', $extraClauses['teamLeader']);
        }

        // Filter by competition status
        if (isset($extraClauses['competition']) && $extraClauses['competition'] !== '') {
            $q->where('users.is_active_for_competition', $extraClauses['competition'] ? 1 : 0);
        }

        $q->whereNull('users.deleted_at');
        if (isset($extraClauses['created_by'])) {
            $q->where('users.created_by', $extraClauses['created_by']);
        }

        $selectableFields = $this->getSelectableFields();
        $q->select($selectableFields);
        $q->groupBy('users.id');

        return $q;
    }

    private function getSelectableFields()
    {
        return [
            'users.id',
            'users.name',
            'users.rating',
            'users.email',
            'users.position',
            DB::raw('blu.name as brokerage_license_agent'),
            'users.profile_image',
            'users.public_profile',
            DB::raw('GROUP_CONCAT(r.name SEPARATOR ", ") as roles')
        ];
    }

    public function getUsersWithRoles($extraRoles = [])
    {
        $baseRoles = [
            RolesDef::AGENT,
            RolesDef::OFFICE_MANAGER,
            RolesDef::MASTER_BROKER,
            RolesDef::CALL_CENTER_AGENT,
        ];

        $allRoles = array_merge($baseRoles, $extraRoles);

        $users = User::orderBy('name', 'asc')
            ->whereHas('roles', function ($q) use ($allRoles) {
                $q->whereIn('name', $allRoles);
            })
            ->get();

        return $users;
    }

    public function getTodaysUserBirthday()
    {
        return User::whereRaw("DATE_FORMAT(date_of_birth,'%m-%d') = DATE_FORMAT(NOW(),'%m-%d')")->get();
    }

    public function getRequestParams(Request $request)
    {
        $columns = $request->query('columns');
        $order = $request->query('order');
        $search = $request->query('search');

        $extraConfig = ['q' => ''];

        if (!empty($columns) && is_array($columns)) {
            //build the where clauses
            $wheres = [];
            foreach ($columns as $col) {
                if (!isset($col['searchable'])) {
                    continue;
                }
                if ($col['searchable'] && isset($col['search']) && isset($col['search']['value']) && !empty($col['search']['value'])) {
                    $wheres[$col['name']] = $col['search']['value'];
                }
            }

            $extraConfig['wheres'] = $wheres;
        }

        if (!empty($order) && is_array($order) && !empty($order[0])) {
            if (!empty($columns) && is_array($columns) && !empty($columns[$order[0]['column']])) {
                $column = $columns[$order[0]['column']];

                // Use 'data' property first (DataTables standard), fallback to 'name' for backward compatibility
                $sortBy = null;
                if (isset($column['data']) && !empty($column['data']) && $column['data'] !== 'null') {
                    $sortBy = $column['data'];
                } elseif (isset($column['name']) && !empty($column['name'])) {
                    $sortBy = $column['name'];
                }

                // Only set sort if we have a valid sort field
                if ($sortBy) {
                    $extraConfig['sort'] = $sortBy;
                    $extraConfig['dir'] = $order[0]['dir'];

                    $sortCriterias = [];
                    foreach ($order as $ord) {
                        if (isset($columns[$ord['column']])) {
                            $orderColumn = $columns[$ord['column']];
                            $orderSortBy = null;

                            // Use same logic for each sort criteria
                            if (isset($orderColumn['data']) && !empty($orderColumn['data']) && $orderColumn['data'] !== 'null') {
                                $orderSortBy = $orderColumn['data'];
                            } elseif (isset($orderColumn['name']) && !empty($orderColumn['name'])) {
                                $orderSortBy = $orderColumn['name'];
                            }

                            // Only add to sort criteria if we have a valid sort field
                            if ($orderSortBy) {
                                $ec = [];
                                $ec['sort'] = $orderSortBy;
                                $ec['dir'] = $ord['dir'];
                                $sortCriterias[] = $ec;
                            }
                        }
                    }
                    $extraConfig['sortCriterias'] = $sortCriterias;
                }
            }
        }

        if (!empty($search)) {
            if (is_array($search)) {
                if (!empty($search['value'])) {
                    $extraConfig['q'] = $search['value'];
                }
            } else {
                $extraConfig['q'] = $search;
            }
        }

        // handle mobile requests
        if ($request->has('sort')) {
            $sortBy = $request->get('sort');
            $requestSortDir = $request->get('sort-dir', 'asc');

            if ($requestSortDir != 'asc' && $requestSortDir != 'desc') {
                $requestSortDir = 'asc';
            }

            $extraConfig['sort'] = $this->getFullField($sortBy);
            $extraConfig['dir'] = $requestSortDir;
        }

        return $extraConfig;
    }
}
