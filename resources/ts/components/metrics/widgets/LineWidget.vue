<template>
  <div class="metrics-container">
    <div ref="dom" class="metricsCard" :style="computedStyle"></div>
  </div>
</template>

<script setup lang="ts">
declare const request: Function;
import echarts from "../index";
import { ref, onMounted, defineProps, computed, watch, onBeforeUnmount } from "vue";
import { ECharts, EChartsCoreOption } from "echarts/core";
import { WidgetDTO } from "../models";

// Define component props
const props = defineProps<{
  widget: WidgetDTO;
  source: string;
  filters: string;
  variant: {
    type: String,
    default: null
  };
}>();

const dom = ref<HTMLElement | null>(null);
let ins: ECharts | null = null;

// Data reference
const data = ref<{ columns: string[]; series: any[] } | null>(null);
const secondPeriod = ref<{ columns: string[]; series: any[] } | null>(null);

// Backpressure handling
let resizeTimeout: number | null = null;
let pendingResize = false;
let lastResizeTime = 0;
const RESIZE_THROTTLE = 100; // Throttle to max once per 100ms

// Computed styles based on source
const computedStyle = computed(() => ({
  width: props.source === "unselectedWidgets" ? "300px" : "800px",
  height: props.source === "unselectedWidgets" ? "250px" : "450px",
}));

// Add a flag to track initialization state
const isInitialized = ref(false);
const dataLoading = ref(false);

// Computed chart options
const option = computed<EChartsCoreOption>(() => {
const series = [];
  if (data.value && data.value.series.length > 1) {
    data.value.series.forEach((item) => {
      series.push(item);
    });
  }else{
    series.push(data.value ? data.value.series : []);
  }
  if (secondPeriod.value && secondPeriod.value.series.length > 1) {
    secondPeriod.value.series.forEach((item) => {
      series.push(item);
    });
  }else{
    series.push(secondPeriod.value ? secondPeriod.value.series : []);
  }
  const legendData = series
    .filter(item => item && item.name)
    .map(item => item.name);
  const legendSelected = legendData.reduce((acc, name, index) => {
    acc[name] = index < 2; // Only set the first two items to true
    return acc;
  }, {});

return {
  title: {
    text: "",
  },
  tooltip: {
    trigger: 'axis',
    formatter: function (params) {
      var activeCount = params.length;
      var date1 = params[0].axisValueLabel;  
      var date2 = secondPeriod.value ? secondPeriod.value.columns[params[0].dataIndex] : '';
      var result = '';
      if (activeCount === 2) {
        result += date1 + ' vs ' + date2 + '<br>';
      } else if (params[0].seriesName === 'Current Period') {
        result += date1 + '<br>';
      } else {
        result += date2 + '<br>';
      }
      params.forEach(function (item) {
        result += '<span style="display:inline-block;width:10px;height:10px;border-radius:10px;background-color:' + item.color + ';margin-right:5px;"></span>';
        result += item.seriesName + ': <strong class="float-end ms-1">' + item.value + '</strong><br>';
      });
      return result;
    }
  },
  legend: {
    data: legendData,
    selected: legendSelected,
  },
  toolbox: {
    feature: {
      saveAsImage: {}
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '15%',
    containLabel: true
  },
  xAxis: {
    type: "category",
    data: data.value ? data.value.columns : [],
    axisLabel: {
      interval: 3,
      rotate: 30
    }
  },
  yAxis: {
    type: "value",
  },
  series: series,
  dataZoom: [
    {
      type: 'inside',
      start: 0,
      end: 100
    },
    {
      show: true,
      type: 'slider',
      top: '90%',
      start: 0,
      end: 100
    }
  ],
  }
});

// Fetch data with backpressure handling
async function fetchWidgetData() {
  if (dataLoading.value) {
    // Mark pending to process after current request completes
    pendingResize = true;
    return;
  }

  dataLoading.value = true;
  const endpoint = !!props.variant ? props.widget?.data?.config?.variants[props.variant]?.endpoint : props.widget?.data?.config?.endpoint;
  const filters = props.filters ? { filters: props.filters } : null;
  const queryParamsBase: Record<string, string> = {
    ...filters,
    userWidgetId: props.widget.data.id.toString(),
  };

  if (endpoint) {
    try {
      const parts = props.filters.split('/');
      const hasSecondPeriod = parts[3] && parts[4];

      const queryParamsCurrent: Record<string, string> = {
        ...queryParamsBase,
        previous: "false",
      };

      const responseCurrentData = await request(
        `${endpoint}?${new URLSearchParams(queryParamsCurrent)}`
      );
      data.value = responseCurrentData.data;

      if (ins) {
        ins.setOption(option.value);
        scheduleResize(0);
      }

      // Second request 
      if(props.filters.includes("compare") && hasSecondPeriod){
        var queryParamsTrue: Record<string, string> = {
          ...queryParamsBase,
          compare: "true",
        };
      }else{
        var queryParamsTrue: Record<string, string> = {
          ...queryParamsBase,
          previous: "true",
        };
      }

      const response2ndPeriod = await request(
        `${endpoint}?${new URLSearchParams(queryParamsTrue)}`
      );
      secondPeriod.value = response2ndPeriod.data;
      if (ins) {
        ins.setOption(option.value);
        scheduleResize(0);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      dataLoading.value = false;

      // If there was a pending request while this one was processing,
      // process it now
      if (pendingResize) {
        pendingResize = false;
        fetchWidgetData();
      }
    }
  } else {
    dataLoading.value = false;
  }
}

watch(
  () => props.filters,
  () => {
    fetchWidgetData();
  },
);

// Watch for source changes to handle resize appropriately
watch(
  () => props.source,
  () => {
    // Allow time for styles to update
    scheduleResize(100);
  }
);

// Function to schedule a resize with backpressure handling
function scheduleResize(delay = 0) {
  const now = Date.now();

  // Clear any existing timeout
  if (resizeTimeout !== null) {
    window.clearTimeout(resizeTimeout);
    resizeTimeout = null;
  }

  // If we've resized recently, extend the delay to implement throttling
  const timeSinceLastResize = now - lastResizeTime;
  const effectiveDelay = Math.max(delay, RESIZE_THROTTLE - timeSinceLastResize);

  resizeTimeout = window.setTimeout(() => {
    resizeChart();
    resizeTimeout = null;
    lastResizeTime = Date.now();
  }, effectiveDelay);
}

// Debounced resize function
const resizeChart = () => {
  if (ins && dom.value) {
    // Get the current container dimensions
    const container = dom.value;
    const containerWidth = container.clientWidth;
    const containerHeight = container.clientHeight;

    // Ensure container has dimensions
    if (containerWidth && containerHeight) {
      ins.resize({
        width: containerWidth,
        height: containerHeight
      });
    } else {
      // If container dimensions are not available, use computed styles
      const style = computedStyle.value;
      const width = parseInt(style.width) || 800;
      const height = parseInt(style.height) || 450;

      ins.resize({
        width: width,
        height: height
      });
    }
  }
};

// Use ResizeObserver if available
let resizeObserver: ResizeObserver | null = null;

// Handle window resize with backpressure
const handleWindowResize = () => {
  scheduleResize();
};

// Initialize chart
onMounted(() => {
  if (dom.value && !ins) {
    const container = dom.value;

    // Ensure the container has dimensions before initializing
    const containerWidth = getStyle(container, "width");
    const containerHeight = getStyle(container, "height");

    if (containerWidth !== "0px" && containerHeight !== "0px") {
      ins = echarts.init(container);
      ins.setOption(option.value);
      isInitialized.value = true;

      // Fetch data after initialization
      fetchWidgetData();

      // Set up resize handling with backpressure
      window.addEventListener('resize', handleWindowResize);

      // Use ResizeObserver for more accurate resize detection if available
      if (window.ResizeObserver) {
        resizeObserver = new ResizeObserver(() => {
          scheduleResize();
        });
        resizeObserver.observe(container);
      }

      // Force an initial resize after a short delay
      scheduleResize(200);
    } else {
      // If container has no dimensions, retry after a delay
      setTimeout(() => {
        if (dom.value && !ins) {
          ins = echarts.init(dom.value);
          ins.setOption(option.value);
          isInitialized.value = true;
          fetchWidgetData();

          window.addEventListener('resize', handleWindowResize);

          if (window.ResizeObserver) {
            resizeObserver = new ResizeObserver(() => {
              scheduleResize();
            });
            resizeObserver.observe(dom.value);
          }

          scheduleResize(100);
        }
      }, 100);
    }
  }
});

// Clean up event listeners and chart instance
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleWindowResize);

  if (resizeTimeout !== null) {
    window.clearTimeout(resizeTimeout);
    resizeTimeout = null;
  }

  if (resizeObserver && dom.value) {
    resizeObserver.unobserve(dom.value);
    resizeObserver.disconnect();
  }

  if (ins) {
    ins.dispose();
    ins = null;
  }
});

// Utility function to get CSS styles
function getStyle(dom: HTMLElement, attr: string) {
  return window.getComputedStyle(dom, null)[attr];
}
</script>

<style scoped>
.metrics-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.metricsCard {
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}
</style>