<?php

use App\Http\Controllers\Crm\AutomatizationController;
use App\Models\Crm\PermissionsDef;
use Illuminate\Support\Facades\Route;

Route::middleware(['permission:' . PermissionsDef::CRM_ADMIN . '|' . PermissionsDef::TEAM_LEADER])->prefix('')->group(function () {
    Route::get('', [AutomatizationController::class, 'index'])->name('crm.automatizations.index');
    Route::middleware(['permission:' . PermissionsDef::CRM_ADMIN])->group(function () {
        Route::get('create', [AutomatizationController::class, 'create'])->name('crm.automatizations.create');
        Route::post("store", [AutomatizationController::class, 'store'])->name('crm.automatizations.create.post');
    });
    Route::prefix('{automatizationId}')->group(function () {
        Route::middleware(['permission:' . PermissionsDef::CRM_ADMIN])->group(function () {
            Route::get('edit', [AutomatizationController::class, 'edit'])->name('crm.automatizations.edit');
            Route::post('update', [AutomatizationController::class, 'update'])->name('crm.automatizations.edit.post');
        });
        Route::get('details', [AutomatizationController::class, 'details'])->name('crm.automatizations.details');
    });
});