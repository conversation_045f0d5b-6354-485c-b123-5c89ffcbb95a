@php
$isUpdate = !$empty && !$readonly;
$isOwnLead = $empty || $item->created_by == auth()->user()->id;
$isAgent = !$empty && (getHighestRole(Auth::user()) == role_agent() && !$isOwnLead);
$shouldDisplayRemarksHistory = !$empty && $item->exists;

if(isset($hasInventoryTable) && $hasInventoryTable) {
$headerColumnsDefinition = [
"index" => "",
"ref_no" => "Ref No.",
"ad_type" => "Ad type",
"type" => "Property type",
"bedrooms_no" => "Bedrooms",
"bathrooms_no" => "Bathrooms",
"price" => "Price",
"address" => "Address",

"actions" => "Actions",
];

$list = ['index', 'ref_no', 'ad_type', 'type', 'bedrooms_no', 'bathrooms_no', 'price', 'address', 'actions'];
$columnsToUse = $list;

$columnsHeadersToUse = array_map(function($columnIndex) use ($headerColumnsDefinition) {
return $headerColumnsDefinition[$columnIndex];
}, $columnsToUse);
if($listViewType == 'master') {
$columnsToUse = $list;
}
}

$contactIdInitialValue = "";
$taskIdInitialValue = "";
if(!$empty && !is_null($item->contact)) {
$contactIdInitialValue = $item->contact->id;
}
if(isset($createFromTask) && !is_null($createFromTask)) {
$taskIdInitialValue = $createFromTask->id;
}
if(isset($createForContact) && !!$createForContact) {
$contactIdInitialValue = $createForContact->id;
}
if(!isset($action)) {
$action = '';
}

$newFurnishingsOptionsOffice = array_map(function($item) { return (object) $item; }, [
['value' => 'core-and-shell', 'label' => 'Core and Shell'],
['value' => 'semi-fitted', 'label' => 'Semi-Fitted'],
['value' => 'fully-fitted', 'label' => 'Fully Fitted'],
['value' => 'fully-furnished', 'label' => 'Fully Furnished'],
]);

$allOldInputs = Session::getOldInput();
$leadSourcesArr = [];
$selectedLeadSource = old('platform_from', !empty($item) ? $item->platform_from : '');
foreach($sources as $ls) {
$leadSourcesArr[] = (object)['value' => $ls->id, 'label' => $ls->name];
}
$documentOptions = app()->make(App\Services\LeadsService::class)::$documentOptions;
sort($documentOptions);
$documentsListType = app()->make(App\Services\LeadsService::class)::$documentsListType;
@endphp
<style>
    .visible-on-loading {
        display: none;
    }

    .visible-on-idle {
        display: inline-block;
    }

    .loading-state.visible-on-loading {
        display: inline-block;
    }

    .loading-state.visible-on-idle {
        display: none;
    }

    .leadLayout {
        display: flex;
        flex-direction: column;
    }

    .leadLayout__statusPanel {
        display: flex;
        gap: 8px;
        padding: 20px 0;
    }

    .leadLayout__headlinePanel {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #efefef;
        padding: 10px;
        margin-bottom: 8px;
    }

    .leadFormContainer {
        display: flex;
    }

    .leadFormContainer__left {
        flex: 1;
    }

    /* .leadFormContainer__right-sidebar {
        width: 10px;
        background-color: red;
    } */

    .leadFormContainer__right {
        position: relative;
        margin-left: 10px;
        background-color: #f8f9fa;
        flex: 0 0 10px;
        box-shadow: -1px 0 0 rgba(0, 0, 0, .1);
        padding: 8px 0;
    }

    .leadFormContainer__right lead-details-lead-activity {
        display: none;
        transition: all .3s;
    }

    .leadFormContainer__right-expandCta {
        position: absolute;
        top: 20px;
        left: -15px;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 24px;
        height: 24px;
        border: 1px solid #dddddd;
        border-radius: 50%;
        background: #f8f9fa;
        cursor: pointer;
        transition: all .3s;
        z-index: 200;
    }

    .leadLayout--withActivitiesBarExpaneded .leadFormContainer__right {
        flex-basis: 380px;
    }

    .leadLayout--withActivitiesBarExpaneded .leadFormContainer__right lead-details-lead-activity {
        display: flex;
        /* width: 100%; */
    }

    .leadLayout--withActivitiesBarExpaneded .leadFormContainer__right-expandCta {
        transform: rotate(.5turn);
    }

    /* ul.leadStatuses {
        flex: 1;
        display: flex;
        justify-content: space-between;
        list-style-type: none;
    } */

    .breadcrumbs {
        border: 1px solid #cbd2d9;
        border-radius: 0.3rem;
        display: flex;
        align-items: center;
        overflow: hidden;
        flex: 1;
    }

    .breadcrumbs__item {
        background: #fff;
        color: #333;
        outline: none;
        padding: 0.75em 0.75em 0.75em 1.25em;
        position: relative;
        text-decoration: none;
        transition: background 0.2s linear;
        flex: 1;
        cursor: pointer;
    }

    .breadcrumbs__item .full-name {
        display: none;
    }

    .breadcrumbs__item.active-item,
    .breadcrumbs__item.active-item::after,
    .breadcrumbs__item:hover:after,
    .breadcrumbs__item:hover {
        background: #edf1f5;
    }

    .breadcrumbs__item:hover .initial,
    .breadcrumbs__item.active-item .initial {
        display: none;
    }

    .breadcrumbs__item:hover .full-name,
    .breadcrumbs__item.active-item .full-name {
        display: inline-block;
    }

    .breadcrumbs__item:focus:after,
    .breadcrumbs__item:focus,
    .breadcrumbs__item.is-active:focus {
        background: #323f4a;
        color: #fff;
    }

    .breadcrumbs__item:after,
    .breadcrumbs__item:before {
        background: white;
        bottom: 0;
        clip-path: polygon(50% 50%, -50% -50%, 0 100%);
        content: "";
        left: 100%;
        position: absolute;
        top: 0;
        transition: background 0.2s linear;
        width: 1em;
        z-index: 1;
    }

    .breadcrumbs__item:before {
        background: #cbd2d9;
        margin-left: 1px;
    }

    .breadcrumbs__item:last-child {
        border-right: none;
    }

    .breadcrumbs__item.is-active {
        background: #edf1f5;
    }

    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875em;
        color: #dc3545;
    }
</style>

<div class="leadLayout leadLayout--withActivitiesBarExpaneded">
    <div class="leadLayout__statusPanel">
        <nav class="breadcrumbs">
            @foreach($leadStatuses as $status)
            @if(!in_array($status->id, [1, 5, 10]))
            <a data-lead-status-name="{{$status->name}}" class="breadcrumbs__item {{ !$empty && $item->lead_status_id ? ($item->lead_status_id == $status->id ? 'active-item' : '') : ''}}">
                <div class="initial">{{$status->name[0]}}</div>
                <div class="full-name">{{$status->name}}</div>
            </a>
            @endif
            @endforeach
        </nav>
        <!-- <div class="d-flex align-items-center">
            <button class="btn btn-primary">Mark status as complete</button>
        </div> -->
    </div>
    <div class="leadLayout__headlinePanel">
        <div>
            <h3>
                @if(!$empty)
                Contact:
                @if ($item->contact)
                {{$item->contact->name}}
                @else
                N/A
                @endif
                @else
                New Lead
                @endif
            </h3>
        </div>
        @if(!$empty)
        <div class="float-end">
            @if($item && $item->contact && isset($wasReassignedToCurrentUser) && !empty($wasReassignedToCurrentUser))
            <a class="btn btn-info btn-sm" role="button" data-bs-toggle="modal" data-bs-target="#cancelReassignationModal">
                <span data-feather="corner-up-left"></span>
                Cancel reassignment
            </a>
            @endif
            <a class="btn btn-sm btn-outline-secondary" onclick="triggerAddReminderModal('{{ $item->id }}', 'lead')">
                <i class="bi bi-calendar-check"></i>
                Reminders
            </a>
        </div>
        @endif
    </div>
    <div class="leadLayout__bottom">
        <div class="leadFormContainer">
            <div class="leadFormContainer__left">
                <form method="POST" id="leadForm" action={{$action}} onSubmit="document.getElementById('formButton').disabled=true;" enctype="multipart/form-data">
                    {!! csrf_field() !!}
                    <div class="relative">
                        <div class="property-scrollspy" class="scrollspy-example bg-light p-3 rounded-2" tabindex="0">
                            @if(request()->has('shouldAddFormsToClose'))
                            <div class="alert alert-warning">
                                @if(is_array(request('unmatchedOptions')) && count(request('unmatchedOptions')))
                                <span>In order to close this lead, the following documents should be added:</span>
                                @foreach(request('unmatchedOptions') as $option)
                                <li>{{$option['text']}}</li>
                                @endforeach
                                @elseif(!empty(request('unmatchedMessage')))
                                <span>{{ request('unmatchedMessage') }}</span>
                                @endif
                            </div>
                            @endif
                            @component('crm.components.form.section-card', ['sectionId' => 'contact', 'noMargin' => true])
                            <h4>Contact Details</h4>
                            <div class="row">
                                <input id="contact_id" type="hidden" name="contact_id" value="{{ $contactIdInitialValue }}" required data-error="The Client field is required.">
                                <input id="task_id" type="hidden" name="task_id" value="{{ $taskIdInitialValue }}" required data-error="The Client field is required.">
                                <div id="contact-details-wrapper" class="{{$empty && !$createForContact ? 'd-none' : ''}} mt-3">
                                    <div class="col-xs-12 col-sm-6">
                                        <dl class="dl-horizontal text-align-left">
                                            <dt>Full Name</dt>
                                            <dd id="contact-full-name">
                                                @if (!$empty)
                                                @if ($item->contact)
                                                {{$item->contact->name}}
                                                @else
                                                N/A
                                                @endif
                                                @elseif(!empty($createForContact))
                                                {{$createForContact->name}}
                                                @endif
                                            </dd>

                                            <dt class="margin-top-10">Mobile</dt>
                                            <dd id="contact-mobile" class="margin-top-10">
                                                @if(!$empty && !is_null($item->contact) && !is_null($item->contact->mobile_1))
                                                <a href="tel:{{getCompletePhoneNo($item->contact->prefix_mobile_1, $item->contact->mobile_1)}}">{{getCompletePhoneNo($item->contact->prefix_mobile_1, $item->contact->mobile_1)}}</a>
                                                @endif
                                                @if(!$empty && !is_null($item->contact) && !is_null($item->contact->mobile_2))
                                                <br /><a href="tel:{{getCompletePhoneNo($item->contact->prefix_mobile_2, $item->contact->mobile_2)}}">{{getCompletePhoneNo($item->contact->prefix_mobile_2, $item->contact->mobile_2)}}</a>
                                                @endif
                                                {{--@if (!$empty && isset($item->contact))
                                        @include('brix.two-links',
                                        ['value1' => $item->contact->mobile_1,
                                        'value2' => $item->contact->mobile_2,
                                        'prefix1' => $item->contact->prefix_mobile_1,
                                        'prefix2' => $item->contact->prefix_mobile_2,
                                        'type' => 'tel'])
                                    @elseif(!empty($createForContact))
                                        @include('brix.two-links',
                                        ['value1' => $createForContact->mobile_1,
                                        'value2' => $createForContact->mobile_2,
                                        'type' => 'tel'])
                                    @endif
                                    --}}
                                            </dd>

                                            @if(!$empty && !is_null($item->contact) && (!is_null($item->contact->phone_1) || !is_null($item->contact->phone_2)))
                                            <dt class="margin-top-10">Fixed-line</dt>
                                            <dd id="contact-fixed-line" class="margin-top-10">
                                                @if (!$empty && isset($item->contact))
                                                @include('brix.two-links',
                                                ['value1' => $item->contact->phone_1,
                                                'value2' => $item->contact->phone_2,
                                                'type' => 'tel'])
                                                @elseif(!empty($createForContact))
                                                @include('brix.two-links',
                                                ['value1' => $createForContact->phone_1,
                                                'value2' => $createForContact->phone_2,
                                                'type' => 'tel'])
                                                @endif
                                            </dd>
                                            @endif


                                            <dt class="margin-top-10">Email</dt>
                                            <dd id="contact-email" class="margin-top-10">
                                                @if (!$empty && isset($item->contact))
                                                @include('brix.two-links',
                                                ['value1' => $item->contact->email_1,
                                                'value2' => $item->contact->email_2,
                                                'type' => 'mailto'])
                                                @elseif(!empty($createForContact))
                                                @include('brix.two-links',
                                                ['value1' => $createForContact->email_1,
                                                'value2' => '',
                                                'type' => 'mailto'])
                                                @endif
                                            </dd>
                                        </dl>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col d-flex justify-content-start gap-2 @error('contact_id') is-invalid @enderror">
                                    @if(!$readonly && !$isAgent && !$wasReassignedByCurrentUser)
                                    <a class="btn btn-outline-secondary btn-sm" role="button" data-bs-toggle="modal" data-bs-target="#contact-lookup" data-backdrop="static" data-keyboard="false">
                                        <span data-feather="link"></span>
                                        Link Existing Contact
                                    </a>
                                    <a class="btn btn-outline-secondary btn-sm" role="button" onClick="openContactModalForCreate()">
                                        <span data-feather="plus"></span>
                                        Create New Contact and Link
                                    </a>
                                    @endif
                                    @if(!$empty && $item && $item->contact && !$wasReassignedByCurrentUser)
                                    <a class="btn btn-outline-secondary btn-sm" role="button" onClick="openContactModalForEdit()">
                                        <span data-feather="edit"></span>
                                        Edit contact
                                    </a>
                                    @endif
                                </div>
                                @error('contact_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            @endcomponent
                            @component('crm.components.form.section-card', ['sectionId' => 'assign'])
                            <h3> Assignment </h3>
                            <hr />
                            <div class="row">
                                <div class="col-12 col-md-3">
                                    <div class="mb-3">
                                        <label for="assigned-to" class="form-label">Assigned to</label>
                                        @if(auth()->user()->hasAnyRole([\App\Models\Crm\RolesDef::OFFICE_MANAGER, \App\Models\Crm\RolesDef::CALL_CENTER_AGENT]))
                                        @php
                                            $teamLeaders = $usersWithRoles->filter(function ($user) {
                                                return $user->roles->contains('name', 'Team Leader');
                                            });
                                        @endphp

                                        <select id="assigned-to" name="assigned-to" class="form-select" {{$readonly || $wasReassignedByCurrentUser ? "disabled" : ""}}>
                                            <option value="">Please Select</option>
                                            @foreach ($usersWithRoles as $option)
                                            <option value="{{$option->id}}" {{ !$empty && $item->latestAssignment ? ($item->latestAssignment->user_id == $option->id ? 'selected' : '') : ''}}> {{$option->name}}</option>
                                            @endforeach
                                               @if($teamLeaders->count())
                                                <optgroup label="Team Leaders">
                                                    @foreach ($teamLeaders as $option)
                                                        <option value="{{$option->id}}" {{ !$empty && $item->latestAssignment && $item->latestAssignment->user_id == $option->id ? 'selected' : '' }}>
                                                            {{$option->name}}
                                                        </option>
                                                    @endforeach
                                                </optgroup>
                                            @endif
                                        </select>
                                        @elseif(auth()->user()->hasAnyRole(\App\Models\Crm\RolesDef::MATRIX_AGENT_MANAGER))
                                        <select id="assigned-to" name="assigned-to" class="form-select" {{$readonly || $wasReassignedByCurrentUser ? "disabled" : ""}}>
                                            <option value=""> N/A </option>
                                            @foreach (getAllAgents() as $option)
                                            @if($option->created_by == auth()->user()->id || $option->id == auth()->user()->id)
                                            <option value="{{$option->id}}" {{ !$empty && $item->latestAssignment ? ($item->latestAssignment->user_id == $option->id ? 'selected' : '') : ''}}> {{$option->name}}</option>
                                            @endif
                                            @endforeach
                                        </select>
                                        @elseif(auth()->user()->hasAnyRole(\App\Models\Crm\RolesDef::TEAM_LEADER))
                                        <select id="assigned-to" name="assigned-to" class="form-select" {{$readonly || $wasReassignedByCurrentUser ? "disabled" : ""}}>
                                            <option value=""> N/A </option>
                                            <option value="{{auth()->user()->id}}" {{ !$empty && $item->latestAssignment ? ($item->latestAssignment->user_id == auth()->user()->id ? 'selected' : '') : ''}}> {{auth()->user()->name}}</option>
                                            @foreach (getTeamLeaderTeamAgents() as $option)
                                            <option value="{{$option->id}}" {{ !$empty && $item->latestAssignment ? ($item->latestAssignment->user_id == $option->id ? 'selected' : '') : ''}}> {{$option->name}}</option>
                                            @endforeach
                                        </select>
                                        @else
                                        <select id="assigned-to" name="assigned-to" class="form-select" {{$readonly || $wasReassignedByCurrentUser ? "disabled" : ""}}>
                                            <option value="{{auth()->user()->id}}"> {{auth()->user()->name}}</option>
                                        </select>
                                        @endif
                                        <p id="assignedToAlert" class="d-none text-danger">It is mandatory to choose another agent.</p>
                                    </div>
                                </div>
                                <div class="col-12 col-md-3">
                                    <label for="status" class="form-label">Status</label>
                                    <div class="row">
                                        <select name="lead_status_id" id="leadStatusSelect" class="form-select" aria-label="Lead status" {{$readonly || $wasReassignedByCurrentUser ? "disabled" : ""}}>
                                            <option value="">Please select</option>
                                            @foreach($leadStatuses as $status)
                                            <option value="{{$status->id}}" @if(in_array($status->id, [1, 5, 10])) disabled="disabled" @endif {{ !$empty && $item->lead_status_id ? ($item->lead_status_id == $status->id ? 'selected' : '') : ''}}>{{$status->name}}</option>
                                            @endforeach
                                        </select>
                                        <input type="hidden" id="leadsViewScheduledInput" name="leads_viewing_scheduled">
                                    </div>
                                </div>
                                <div class="col-12 col-md-3">
                                    <label for="status" class="form-label">Rating</label>
                                    <select name="rating" id="leadRatingSelect" class="form-select" aria-label="Lead rating" {{$readonly || $wasReassignedByCurrentUser ? "disabled" : ""}}>
                                        @foreach($leadRatings as $rating)
                                        <option value="{{$rating}}" @if($rating === 'X') disabled="disabled" @endif {{ !$empty && $item->rating ? ($item->rating == $rating ? 'selected' : '') : ''}}>{{empty($rating) ? 'Please select' : $rating}}{{!empty($rating) && array_key_exists($rating, $daysMap) ? ' - '. $daysMap[$rating]/86400 .' days' : ''}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            @endcomponent

                            @component('crm.components.form.section-card', ['sectionId' => 'documents'])
                            <h4>Documents</h4>
                            <div class="col-12 col-md-3">
                                <label for="documents_type" class="form-label">Documents list type</label>
                                <select name="documents_type" id="docType" class="form-select">
                                    @foreach($documentsListType as $type)
                                    <option value="{{$type['value']}}" {{ !$empty && $item->documents_type ? ($item->documents_type == $type['value'] ? 'selected' : '') : ''}}>{{$type['label']}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-12 mt-4">
                                <!-- <label class="form-label">Documents</label> -->
                                <table class="table table-bordered table-condensed">
                                    <tr>
                                        <th>Preview</th>
                                        <th>Options</th>
                                        <th>Last update date</th>
                                        <th>Download</th>
                                        <th>&nbsp;</th>
                                    </tr>
                                    @if(count($item->forms ?? []) > 0)
                                    @foreach($item->forms as $form)
                                    @php
                                    $documentsEncode = json_decode(json_encode($item->documents_options),true);
                                    $fileOption = "-";
                                    if(!empty($form->option)) {
                                    $documentOptionArr = array_filter($documentOptions, function($opt) use($form) {
                                    return $opt['value'] == $form->option;
                                    });
                                    if(count($documentOptionArr) > 0) {
                                    $vals = array_values($documentOptionArr);
                                    $fileOption = $vals[0]['text'];
                                    }
                                    }
                                    $fileExtension = pathinfo($form->path, PATHINFO_EXTENSION);
                                    $isImage = in_array(strtolower($fileExtension), ['jpg', 'jpeg', 'png', 'gif']);
                                    @endphp
                                    <tr>
                                        <td style="max-width:200px; word-wrap:break-word;">
                                            @if ($isImage)
                                            <a href="{{ route('lead.forms.preview', ['id' => $item->id, 'filePath' => $form->path]) }}"
                                                class="glightbox"
                                                data-type="image">
                                                {{$form->title}}
                                            </a>
                                            @else
                                            <a href="javascript:void(0);"
                                                class="preview-link"
                                                data-url="{{ route('lead.forms.preview', ['id' => $item->id, 'filePath' => $form->path]) }}">
                                                {{$form->title}}
                                            </a>
                                            @endif

                                        </td>
                                        <td>{{$fileOption}}</td>
                                        <td>{{$form->aTime ?? '-'}}</td>
                                        <td style="max-width:200px; word-wrap:break-word;"><a href="{{ route('lead.forms.download', ['id' => $item->id, 'filePath' => $form->path]) }}">Download</a></td>
                                        <td><label><input name="lead-forms-remove[]" type="checkbox" value="{{$loop->index}}"> Mark for removal</label></td>
                                    </tr>
                                    @endforeach
                                    @else
                                    <tr>
                                        <td colspan="5">
                                            <div>No documents uploaded</div>
                                        </td>
                                    </tr>
                                    @endif
                                </table>
                                <div id="formsFilesContainer" class="d-flex flex-column gap-1"></div>
                                <div class="p-1 d-flex">
                                    <button @if($readonly) disabled @endif type="button" id="formsAddFilesBtn" class="btn btn-secondary btn-sm">Add file</button>
                                </div>
                                <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <iframe id="previewIframe" src="" frameborder="0" style="width: 100%; height: 650px;"></iframe>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endcomponent

                            @if(isset($hasInventoryTable) && !!$hasInventoryTable)
                            @component('crm.components.form.section-card', ['sectionId' => 'request'])
                            <h4>Request</h4>
                            <div class="row">
                                <div class="card card-body p-2 m-2 bg-light">
                                    <lead-request-filters></lead-request-filters>
                                    <input type="hidden" id="leadsRequestInput" name="leads_request" value="{{old('leads_request', $leadRequest)}}">
                                </div>
                            </div>
                            <div class="row mb-3 border-top pt-2">
                                <div class="row">
                                    <div class="col-12 col-md-6">
                                        <label class="form-label">Decision Date</label>
                                        <div class="move_in_date">
                                            <input type="date" name="move_in_date" class="form-control" {{$readonly || $wasReassignedByCurrentUser ? "readonly" : ""}} {!! !$empty ? "value='$item->move_in_date'" : "" !!}>
                                            <div class="input-group-addon">
                                                <span class="glyphicon glyphicon-th"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-6">
                                        <label class="form-label">Inquired Ref No.</label>
                                        <div class="inquired_ref_no">
                                            <textarea {{$readonly || $wasReassignedByCurrentUser ? "disabled" : ""}} id="inquired_ref_no" name="inquired_ref_no" class="form-control" cols="30" rows="3">@if (!$empty){{$item->inquired_ref_no}}@endif</textarea>
                                            <div class="input-group-addon">
                                                <span class="glyphicon glyphicon-th"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div>
                                    <div class="row">
                                        <label for="requirements" class="form-label">Specific Requirements</label>
                                        <textarea {{$readonly || $wasReassignedByCurrentUser ? "disabled" : ""}} id="requirements" name="requirements" class="form-control" cols="30" rows="10">@if ($isEdit){{ old('requirements', $item->requirements) }}@else{{ old('requirements') }}@endif</textarea>
                                    </div>
                                    <div class="row mb-3 mt-3">
                                        <div class="col-xs-12" style="position: relative">
                                            @if($readonly || $wasReassignedByCurrentUser)
                                            <div style="position: absolute; top: 0; bottom: 0; left: 0; right: 0; background-color: rgba(0,0,0,0.3); z-index: 100; border-radius: 5px"></div>
                                            @endif
                                            <table class="table table-hover margin-top dt-responsive nowrap margin-top" cellspacing="0" width="100%" id="suggestionsTable">
                                                <thead>
                                                    <tr>
                                                        @foreach($columnsHeadersToUse as $columnHeader)
                                                        <th>{{$columnHeader}}</th>
                                                        @endforeach
                                                    </tr>
                                                </thead>
                                            </table>
                                        </div>
                                    </div>
                                    @endcomponent
                                    @endif
                                    @component('crm.components.form.section-card', ['sectionId' => 'source'])
                                    <h4>Source of Lead</h4>
                                    <div style="display: flex;">
                                        <div class="col-xs-6 col-sm-3">
                                            <div class="mb-3">
                                                <div id="source_of_lead_select"></div>
                                            </div>
                                            @if(auth()->user()->email === "<EMAIL>" || auth()->user()->email === "<EMAIL>" || auth()->user()->hasAnyRole(\App\Models\Crm\RolesDef::OFFICE_MANAGER))
                                                <div class="row mb-3">
                                                    <div class="col d-flex justify-content-start gap-2">
                                                        <a class="btn btn-outline-secondary btn-sm" role="button" href="javascript:toggleSourceModal()">
                                                            <span data-feather="plus"></span>
                                                            Add New
                                                        </a>
                                                    </div>
                                                    @error('contact_id')
                                                        <div class="invalid-feedback">{{ $message }}</div>
                                                    @enderror
                                                </div>
                                            @endif
                                        </div>
                                        <div id="source_extra_input" class="col-12 col-md-6 col-xl-4" style="display:none; margin-left:10px;">
                                            @if (!$empty && $item->platform_from === '191' && $item->source_contact_id)
                                                <a href="/crm/contacts-list#/{{$item->source_contact_id}}/edit" id="source_contact_link" class="form-control-plaintext">{{$item->source_contact_id}}</a>
                                            @else
                                                <input type="text" name="source_contact_id" class="form-control" placeholder="Enter Contact ID">
                                                @error('source_contact_id')
                                                    <div class="invalid-feedback">
                                                        {{ $message }}
                                                    </div>
                                                @enderror
                                            @endif
                                        </div>
                                    </div>
                                    {{-- Marketing Metadata Section --}}
                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <h5>Marketing Metadata</h5>
                                            <dl class="row">
                                                <dt class="col-sm-3">UTM Source</dt>
                                                <dd class="col-sm-9">{{ !$empty && $item->utm_source ? $item->utm_source : '-' }}</dd>

                                                <dt class="col-sm-3">UTM Medium</dt>
                                                <dd class="col-sm-9">{{ !$empty && $item->utm_medium ? $item->utm_medium : '-' }}</dd>

                                                <dt class="col-sm-3">UTM Campaign</dt>
                                                <dd class="col-sm-9">{{ !$empty && $item->utm_campaign ? $item->utm_campaign : '-' }}</dd>

                                                <dt class="col-sm-3">UTM Term</dt>
                                                <dd class="col-sm-9">{{ !$empty && $item->utm_term ? $item->utm_term : '-' }}</dd>

                                                <dt class="col-sm-3">UTM Content</dt>
                                                <dd class="col-sm-9">{{ !$empty && $item->utm_content ? $item->utm_content : '-' }}</dd>
                                            </dl>
                                        </div>
                                    </div>
                                    @endcomponent
                                    @component('crm.components.form.section-card', ['sectionId' => 'operationHistory'])
                                    <h4>History</h4>
                                    <div class="row">
                                        <div class="col-12">
                                            <div style="max-height:300px; overflow-y: auto;">
                                                @if($shouldDisplayRemarksHistory)
                                                @foreach($operationHistory as $remark)
                                                <article class="pb-2">
                                                    <aside>{{ $remark->content }}</aside>
                                                    <footer><small>{{ $remark->created_at->format('d M Y H:i') }} - <cite title="Source Title">{{ $remark->author ? $remark->author->name : '-' }}</cite></small>
                                                    </footer>
                                                </article>
                                                @endforeach
                                                @endif
                                            </div>
                                            <div class="mt-3">
                                                <label class="form-label" for="operation-history">Remarks</label>
                                                <textarea name="operation-history" id="operation-history" {{$readonly || $wasReassignedByCurrentUser ? 'disabled' : ''}} class="form-control" cols="30" rows="5"></textarea>
                                                <p id="operationHistoryAlert" class="d-none text-danger">This field is required.</p>
                                            </div>
                                        </div>
                                        <div class="col-12 col-md-6 col-xl-4">
                                            <div class="mt-3">
                                                <label class="form-label" for="last-contact-date">Last Contact Date</label>
                                                @php
                                                $defaultLastContact = '';
                                                if(!$empty && !empty($item->last_contact_date)) {
                                                $defaultLastContact = (new \DateTime($item->last_contact_date))->format('Y-m-d');
                                                }
                                                @endphp
                                                <input type="date" class="form-control" name="last_contact_date" {{$readonly || $wasReassignedByCurrentUser ? "readonly" : ""}} value="{{$defaultLastContact}}">
                                            </div>
                                        </div>
                                    </div>
                                    @endcomponent
                                </div>
                                @if(!$wasReassignedByCurrentUser)
                                <div class="mt-3 mb-3">
                                    <div class="col-sm-12 header-btn text-end">
                                        <button class="btn btn-outline-primary" type="submit" id="formButton" form="leadForm">{{ !$empty ? 'Update' : 'Create'  }}</button>
                                        <a href="{{ route('crm.leads.index')}}" class="btn btn-outline-secondary">Cancel</a>
                                    </div>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="leadFormContainer__right">
                <div class="leadFormContainer__right-expandCta shadow-sm">
                    <i class="bi bi-chevron-left"></i>
                </div>
                <section class="leadFormContainer__right-sidebar">
                    <lead-details-lead-activity></lead-details-lead-activity>
                </section>
            </div>
        </div>
    </div>
</div>


@include('brix.lookup-modal-bs5', ['itemName' => 'Contact',
'modalId' => 'contact-lookup',
'modalScript' => '/js/contact-lookup.js',
'lookupTableId' => 'contact-lookup-table',
'headers' => ['Full Name', 'Mobile Phone(s)', 'Fixed-line Phone(s)', 'Email', 'Choose'],
'filters' => ['Full Name', 'Mobile Phone(s)', 'Fixed-line Phone(s)', 'Email', ''],
'onChoose' => 'chooseContact()'])
<div class="modal fade" id="contact-create" @if(isset($item) && isset($item->contact)) data-contact-id="{{ $item->contact->id }}" @endif>
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title d-none" id="createTitle">Create New Contact and Link <i class="bi bi-person"></i></h5>
                <h5 class="modal-title d-none" id="editTitle">Edit Contact <i class="bi bi-person"></i></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="contact-create-body">
                <contacts-list-form-lite></contacts-list-form-lite>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-primary d-none" id="createContactBtn" disabled>Create</button>
                <button type="button" class="btn btn-outline-primary d-none" id="editContactBtn" disabled>Save</button>
                <button type="button" id="btn-create-contact-modal-close" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel
                </button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
<leads-view-scheduled-container-wrapper></leads-view-scheduled-container-wrapper>
@if(!$empty && isset($wasReassignedToCurrentUser) && !empty($wasReassignedToCurrentUser))
<div class="modal fade" id="cancelReassignationModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Reassignment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>This lead was reassigned to you by <strong>{{$item->reassignedByAgentRelation->reassignedByAgent->name}}</strong> on <i>{{$item->reassignedByAgentRelation->created_at->format("d.m.Y H:i")}}</i></p>
                <p>Are you sure you want to cancel the reassignment?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="cancelReassignationCta" onclick="doCancelReassignation()">
                    <span class="spinner-border spinner-border-sm visible-on-loading" role="status" aria-hidden="true"></span>
                    <span class="visible-on-loading">Loading...</span>
                    <span class="visible-on-idle">Cancel reassignment</span>
                </button>
            </div>
        </div>
    </div>
</div>
@endif
@include('crm.components.notes-modal')
@include('brix.ajax-modal')
@include('brix.success-modal', ['text' => 'The contact has been created.'])
@include('brix.failure-modal', ['text' => 'Could not create new contact.'])

@section('head-css')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css" />
<link rel="stylesheet" href="{{asset('/css/crm/listing.css')}}">
<link rel="stylesheet" href="{{asset('/css/crm-virtual-select.min.css')}}">
<link rel="stylesheet" href="{{ mix('css/countries-flags.css') }}" />
<style>
    .vscomp-ele {
        max-width: 100%;
    }

    .property-scrollspy {
        position: relative;
        height: calc(100vh - 180px);
        overflow-y: auto;
    }

    .share-container ul {
        display: flex;
        list-style: none;
    }

    .share-container ul li {
        padding: 5px 10px;
    }

    .image-card-wrapper {
        cursor: grab;
    }

    .image-card-wrapper.over .card {
        border: 2px dotted gray;
    }

    .dataTables_filter,
    .dataTables_paginate {
        display: flex;
        justify-content: flex-end;
    }

    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875em;
        color: #dc3545;
    }
</style>
@endsection

<div class="modal fade" id="lead-platform-add">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add new source</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="contact-create-body">
                <div class="mb-3">
                    <label for="leadSource" class="form-label">New Source</label>
                    <input type="text" class="form-control" id="leadSource" placeholder="">
                    <div id="leadSourceNotification"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-primary" onclick="addNewSource()">Add</button>
                <button type="button" id="btn-create-source-modal-close" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel
                </button>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
@if(isset($hasInventoryTable) && $hasInventoryTable)
<link href="https://cdn.jsdelivr.net/npm/glightbox/dist/css/glightbox.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/glightbox/dist/js/glightbox.min.js"></script>

<script>
    window.inventorySearchParams = {};
    window.contactFormLiteSaveFn = null;
    window.contactFormLiteEditFn = null;
    window.leadContactEditMode = false;
    const createContactBtnRef = document.querySelector('#createContactBtn');
    const editContactBtnRef = document.querySelector('#editContactBtn');
    const wasReassignedByCurrentUser = JSON.parse('{{json_encode($wasReassignedByCurrentUser)}}');
    createContactBtnRef.addEventListener('click', () => window.contactFormLiteSaveFn());
    editContactBtnRef.addEventListener('click', () => window.contactFormLiteEditFn());

    window.onContactFormLiteStateChange = function onContactFormLiteStateChange(newState) {
        const hasErrors = Object.keys(newState.form.errors).length > 0;
        if (hasErrors) {
            createContactBtnRef.setAttribute('disabled', true);
            editContactBtnRef.setAttribute('disabled', true);
        } else {
            createContactBtnRef.removeAttribute('disabled');
            editContactBtnRef.removeAttribute('disabled');
        }
    }
    window.onContactFormLiteAfterSave = function(contact) {
        const fullMobile1 = `${contact.prefix_mobile_1}${contact.mobile_1}`
        $("#contact_id").val(contact.id);
        $("#contact_id").change();
        $("#contact-full-name").html(contact.name);
        $("#contact-mobile").html(`<a href="tel:${fullMobile1}">${fullMobile1}</a>`);
        $("#contact-fixed-line").html("");
        $("#contact-email").html(`<a href="mailto:${contact.email_1}">${contact.email_1}</a>`);
        $("#contact-details-wrapper").removeClass("d-none");
        $("#btn-create-contact-modal-close").click();
    }

    var hasInventoryTable = true;
    const variant = 'leads';
    const agents = JSON.parse('{!! addslashes(json_encode($usersWithRoles->map(function($agent){ return $agent->only("id", "selectName"); })->values()->toArray())) !!}')
    const fu = JSON.parse('{!! addslashes(json_encode($furnishingOptions)) !!}');
    const fuOffices = JSON.parse('{!! addslashes(json_encode($newFurnishingsOptionsOffice)) !!}')
    const locations = [];
    const propertyViewsMap = JSON.parse('{!! $propertyViews->mapWithKeys(function($v) { return [$v->id => $v->name];  })->toJSON() !!}');
    const propertyViews = Object.keys(propertyViewsMap).map((key) => ({
        label: propertyViewsMap[key],
        value: key
    }))
    const propertyBills = JSON.parse('{!! json_encode($billsOptions) !!}');
    const minRentPrices = JSON.parse('{!! json_encode($prmin) !!}');
    const maxRentPrices = JSON.parse('{!! json_encode($prmax) !!}');
    const minSalePrices = JSON.parse('{!! json_encode($psmin) !!}');
    const maxSalePrices = JSON.parse('{!! json_encode($psmax) !!}');
    const statuses = JSON.parse('{!! json_encode($listingStatusOptions) !!}');
    const hasPublishingRoles = false;
    const llc = JSON.parse('{!! json_encode($isCorporateOptions) !!}');
    const ot = JSON.parse('{!! json_encode($operationTypeOptions) !!}');
    const marketingPlatforms = JSON.parse('{!! json_encode($marketingPlatforms->map(function($mp) {return $mp->only("id", "name"); })->values()->toArray()) !!}')
    const statusOptions = JSON.parse('{!! addslashes(json_encode($statusOptions)) !!}');
    const alertDescription = document.querySelector('#operationHistoryAlert');
    const alertAssignedToA = document.querySelector('#assignedToAlert');

    const leadStatusSelect = document.getElementById("leadStatusSelect");
    const initialStatus = leadStatusSelect.value;
    let selectedValue;
    leadStatusSelect.addEventListener("change", function() {
        selectedValue = leadStatusSelect.value;
        alertDescription.classList.remove('d-none');
        document.getElementById("operation-history").required = true;
        if (selectedValue === '14') {
            alertDescription.classList.add('d-none');
            document.getElementById("operation-history").required = false;
            localStorage.removeItem('leadsInventoryTableListingSelection');
            showViewingScheduledContainer('viewingScheduled');
        } else if (selectedValue === '16') {
            alertDescription.classList.add('d-none');
            localStorage.removeItem('leadsInventoryTableListingSelection');
            showViewingScheduledContainer('offerNegotiation');
        } else if (selectedValue === '15') {
            alertDescription.classList.add('d-none');
            document.getElementById("operation-history").required = false;
            showViewingScheduledContainer('followUp');
        } else if (selectedValue === '23') {
            alertDescription.classList.add('d-none');
            document.getElementById("operation-history").required = false;
            showViewingScheduledContainer('meetingScheduled');
        } else if (selectedValue === '21') {
            alertAssignedToA.classList.remove('d-none');
            document.getElementById("assigned-to").required = true;
            document.getElementById("formButton").disabled = true;
        } else if (selectedValue === '26') {
            alertDescription.classList.add('d-none');
            document.getElementById("operation-history").required = false;
            showViewingScheduledContainer('farMovingDecision');
        }
        if (!leadStatusSelect.value) {
            alertDescription.classList.add('d-none');
            document.getElementById("operation-history").required = false;
        }
    });

    function onViewingScheduledModalCancel() {
        document.getElementById("changeLeadStatusSelect").value = initialLeadStatus;
    }

    let afterChanged;
    document.querySelector('#assigned-to').addEventListener('change', () => {
        afterChanged = document.getElementById("assigned-to").value;
        reassignLead(afterChanged)
    });

    const assignedTo = document.getElementById("assigned-to").value;

    function reassignLead(afterChanged) {
        if (afterChanged != assignedTo) {
            document.getElementById("formButton").disabled = false;
            alertAssignedToA.classList.add('d-none');
        } else {
            document.getElementById("formButton").disabled = true;
            alertAssignedToA.classList.remove('d-none');
        }
    }

    const allDataFromStatus = document.getElementById('leadsViewScheduledInput');

    function onViewingScheduledModalConfirm(vsData) {
        const dataStringify = JSON.stringify(vsData);
        allDataFromStatus.value = dataStringify;
    }

    function onInventoryFiltersParamsChange(filterParams) {
        const textarea = document.getElementById('requirements');
        const questionsForRent = `{!! $questionsForRent->value !!}`;
        const questionsForSale = `{!! $questionsForSale->value !!}`;
        const isEdit = JSON.parse('{!! json_encode($isEdit) !!}');
        const oldInputs = '{!! addslashes(json_encode($allOldInputs)) !!}';
        const jsonStr = JSON.stringify(filterParams);
        document.getElementById('leadsRequestInput').value = jsonStr;
        const type = filterParams.ot;

        let questionsToDisplay = '';
        if (!isEdit && oldInputs.length < 10 && type != window.inventorySearchParams.ot) {
            if (type === 'rent') {
                textarea.value = questionsForRent;
            } else if (type === 'sale') {
                textarea.value = questionsForSale;
            }
        }
        window.inventorySearchParams = filterParams;
        if (!!window.leadsFormTable) {
            window.leadsFormTable.draw();
        }
        if (!filterParams.ot || !filterParams.t) {
            document.getElementById("formButton").disabled = true;
        } else {
            const formButton = document.getElementById("formButton");
            formButton && (formButton.disabled = false);
        }


    }

    function openContactModal() {
        const leadContactModal = new bootstrap.Modal(document.getElementById('contact-create'), {
            keyboard: false
        });
        leadContactModal.show();
    }

    function openContactModalForCreate() {
        window.leadContactEditMode = false;
        openContactModal();
        document.getElementById('editTitle').classList.add('d-none');
        document.getElementById('createTitle').classList.remove('d-none');
        document.getElementById('editContactBtn').classList.add('d-none');
        document.getElementById('createContactBtn').classList.remove('d-none');
    }

    function openContactModalForEdit() {
        window.leadContactEditMode = true;
        openContactModal();
        document.getElementById('editTitle').classList.remove('d-none');
        document.getElementById('createTitle').classList.add('d-none');
        document.getElementById('editContactBtn').classList.remove('d-none');
        document.getElementById('createContactBtn').classList.add('d-none');
    }

    const columnsToUse = JSON.parse('{!! json_encode($columnsToUse) !!}');
    const controllerListViewType = '{{$listViewType}}';
    const filterInitialState = '{!! $leadRequest !!}';
    window.inventorySearchParams = !!filterInitialState ? JSON.parse(filterInitialState) : {};

    const geographyMap = {};
    const cities = JSON.parse('{!! addslashes(json_encode($cities)) !!}');
    const regions = JSON.parse('{!! addslashes(json_encode($regions)) !!}');
    const areas = JSON.parse('{!! addslashes(json_encode($areas)) !!}');

    Object.keys(cities).forEach(cityId => {
        const cityEntry = {
            value: cities[cityId].id,
            label: cities[cityId].name,
            regions: []
        };
        geographyMap[cities[cityId].id] = cityEntry;
    });

    Object.keys(regions).forEach(key => {
        const regionEntry = {
            value: regions[key].id,
            label: regions[key].name,
            areas: []
        };

        geographyMap[regions[key].parent_id].regions.push(regionEntry);
    })

    Object.keys(areas).forEach(key => {
        const areaEntry = {
            value: areas[key].id,
            label: areas[key].name
        };

        const regionId = areas[key].parent_id;
        if (!!regions[regionId]) {
            const cityId = regions[regionId].parent_id;
            if (!!cityId) {
                const theRegion = geographyMap[cityId].regions.find(item => item.value == regionId);
                if (!!theRegion) {
                    theRegion.areas.push(areaEntry);
                }
            }
        }
    })

    Object.keys(geographyMap).forEach(cityId => {
        const cityName = geographyMap[cityId].label;
        locations.push({
            value: cityId,
            label: cityName
        });

        for (const region of geographyMap[cityId].regions) {
            const regionName = region.label;
            locations.push({
                value: region.value,
                label: `${cityName} - ${regionName}`
            });

            for (const area of region.areas) {
                const areaName = area.label;
                locations.push({
                    value: area.value,
                    label: `${cityName} - ${regionName} - ${areaName}`
                });
            }
        }
    });
</script>
@else
<script>
    const hasInventoryTable = false;
</script>
@endif
<script>
    let leadSourceModal;

    window.addEventListener('load', () => {
        leadSourceModal = new bootstrap.Modal('#lead-platform-add', {
            keyboard: false
        });
        const leadSourceArr = JSON.parse('{!! addslashes(json_encode($leadSourcesArr)) !!}');
        VirtualSelect.init({
            ele: '#source_of_lead_select',
            name: 'platform_from',
            selectedValue: '{{$selectedLeadSource}}',
            search: true,
            options: leadSourceArr,
        });
    });

    document.querySelector('#source_of_lead_select').addEventListener('change', () => {
        const selectedValue = document.querySelector('#source_of_lead_select').virtualSelect.getValue();
        const extraInputDiv = document.querySelector('#source_extra_input');
        const sourceContactInput = extraInputDiv.querySelector('input[name="source_contact_id"]');
        const sourceContactLink = extraInputDiv.querySelector('#source_contact_link');

        if (selectedValue === '191') {
            //191 is the ID for "Referral" source
            extraInputDiv.style.display = 'block';
            if (sourceContactInput && sourceContactLink) {
                const hasValue = sourceContactLink.textContent.trim() !== '';
                sourceContactInput.style.display = hasValue ? 'none' : 'block';
                sourceContactLink.style.display = hasValue ? 'block' : 'none';
            }
        } else {
            extraInputDiv.style.display = 'none';
            if (sourceContactInput) {
                sourceContactInput.value = '';
            }
        }
    });

    document.querySelector('#leadForm').addEventListener('submit', function(e) {
        const form = e.target;
        const extraInputDiv = document.querySelector('#source_extra_input');
        const sourceContactInput = extraInputDiv.querySelector('input[name="source_contact_id"]');
        
        if (sourceContactInput && sourceContactInput.value) {
            const contactId = sourceContactInput.value;
            const sourceContactLink = document.createElement('a');
            sourceContactLink.id = 'source_contact_link';
            sourceContactLink.href = '';
            sourceContactLink.className = 'form-control-plaintext';
            sourceContactLink.textContent = contactId;
            
            sourceContactInput.style.display = 'none';
            extraInputDiv.appendChild(sourceContactLink);
        }
    });



    function toggleSourceModal() {
        leadSourceModal.toggle();
        document.querySelector('#leadSourceNotification').innerHTML = '';
    }

    function addNewSource() {
        const sourceVal = document.querySelector('#leadSource').value.trim();
        request('/crm/leads/source', {
                method: 'POST',
                body: {
                    name: sourceVal
                }
            })
            .then(data => {
                const ctrl = document.querySelector('#source_of_lead_select');
                const selectedVal = ctrl.getSelectedOptions();
                // const newOption = document.createElement("option");
                // newOption.value = data.id;
                // newOption.text = data.name;
                // document.querySelector('#platform_from').add(newOption);
                document.querySelector('#leadSourceNotification').innerHTML = `<p class="text-success">The source has been successfully added</p>`;
                document.querySelector('#leadSource').value = '';
                ctrl.addOption({
                    value: '' + data.id,
                    label: data.name,
                });
                ctrl.setValue(selectedVal ? selectedVal.value : '');
                setTimeout(() => toggleSourceModal(), 3000);
            })
            .catch(response => {
                response.json().then(errData => {
                    if (errData.errors && errData.errors.name) {
                        document.querySelector('#leadSourceNotification').innerHTML = `<p class="text-danger">${errData.errors.name[0]}</p>`;
                    }
                });
            })
    }

    function setLoadingStateOnConfirmReassignationButton() {
        $('#cancelReassignationCta').prop("disabled", true);
        $('#cancelReassignationCta span').addClass('loading-state');
    }

    function removeLoadingStateFromConfirmReassignationButton() {
        $('#cancelReassignationCta').prop("disabled", false);
        $('#cancelReassignationCta span').removeClass('loading-state');
    }

    function doCancelReassignation() {
        setLoadingStateOnConfirmReassignationButton();
        request('/api/leads/{{!$empty ? $item->id : ''}}/reassignation', {
                    method: 'DELETE'
                })
            .then(data => {
                $('#cancelReassignationModal .modal-body').html('<div class="alert alert-success">The reassignation has been canceled. The window will refresh</div>');
                $('#cancelReassignationCta span').removeClass('loading-state');
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            })
            .catch(response => {
                removeLoadingStateFromConfirmReassignationButton();
            })
    }
</script>
<script>
    class FileUploader {
        filesContainer = document.querySelector('#formsFilesContainer');

        addFile() {
            const fileInput = document.createElement('input');
            fileInput.setAttribute('type', 'file');
            fileInput.setAttribute('name', 'lead-forms[]');
            fileInput.classList.add('form-control', 'form-control-sm');

            const wrapperDiv = document.createElement('div');
            wrapperDiv.classList.add('p-1', 'd-flex', 'gap-2', 'align-items-center');

            const div2 = document.createElement('div');
            const optionsSelect = document.createElement('select');
            optionsSelect.setAttribute('name', 'documents_options[]');
            optionsSelect.classList.add('form-select', 'form-select-sm');
            optionsSelect.style.width = "250px";
            optionsSelect.style.maxWidth = "250px";
            const opt = document.createElement('option');
            opt.text = "Select a option";
            opt.value = "";
            optionsSelect.add(opt);
            const optQID = document.createElement('option');
            optQID.text = "QID";
            optQID.value = "qid";
            optionsSelect.add(optQID);

            const options = JSON.parse('{!!json_encode($documentOptions)!!}');
            console.log('options', options)
            // [{
            //         text: "Contract",
            //         value: "contract",
            //         disabled: true
            //     },
            //     {
            //         text: "Lease Agreement",
            //         value: "leaseAgreement"
            //     },
            //     {
            //         text: "Sales and Purchase Agreement (SPA)",
            //         value: "SalesAndPurchaseAgreement"
            //     },
            //     {
            //         text: "Passport",
            //         value: "passport"
            //     },
            //     {
            //         text: "PVF",
            //         value: "pvf"
            //     },
            //     {
            //         text: "Breakdown Payment",
            //         value: "breakdown"
            //     },
            //     {
            //         text: "Copy of Payments",
            //         value: "copy"
            //     },
            //     {
            //         text: "Salary Certificate",
            //         value: "salary"
            //     },
            //     {
            //         text: "Credit Bureau",
            //         value: "credit"
            //     },
            //     {
            //         text: "Booking Form",
            //         value: "bookingForm"
            //     },
            //     {
            //         text: "Receipt",
            //         value: "receipt"
            //     },
            //     {
            //         text: "Handover Form",
            //         value: "handoverForm"
            //     },
            //     {
            //         text: "Trading License",
            //         value: "tradingLicense"
            //     },
            //     {
            //         text: "Company Registration",
            //         value: "companyRegistration"
            //     }
            // ];

            options.forEach(optionData => {
                const option = document.createElement('option');
                option.text = optionData.text;
                option.value = optionData.value;

                if (optionData.disabled) {
                    option.disabled = true;
                }

                optionsSelect.add(option);
            });

            div2.classList.add("p-2")
            div2.appendChild(optionsSelect)

            const fileWrapperDiv = document.createElement('div');
            fileWrapperDiv.append(fileInput);

            wrapperDiv.appendChild(fileWrapperDiv)
            wrapperDiv.appendChild(div2)
            this.filesContainer.appendChild(wrapperDiv);
        }
    }

    window.addEventListener('load', () => {
        const fu = new FileUploader();
        const addFileBtn = document.querySelector('#formsAddFilesBtn');
        addFileBtn.addEventListener('click', () => fu.addFile());

        const previewLinks = document.querySelectorAll('.preview-link');
        const previewModal = new bootstrap.Modal(document.getElementById('previewModal'));
        const previewIframe = document.getElementById('previewIframe');

        previewLinks.forEach(link => {
            link.addEventListener('click', () => {
                const url = link.getAttribute('data-url');
                previewIframe.src = url;
                previewModal.show();
            });
        });

        document.getElementById('previewModal').addEventListener('hidden.bs.modal', () => {
            previewIframe.src = '';
        });

        GLightbox({
            selector: '.glightbox',
            openEffect: 'zoom',
            closeEffect: 'fade',
        });
    });

    if (document.getElementById('operation-history')) {
        // Initialize the speech to text functionality
        const speechToText = new SpeechToText({
            language: 'en-US',
            onStart: () => {
                console.log('Started listening...');
            },
            onResult: (transcript) => {
                console.log('Got result:', transcript);
            },
            onEnd: () => {
                console.log('Finished listening');
            },
            onError: (error) => {
                console.error('Error:', error);
            }
        });

        // Initialize it on your textarea
        speechToText.init('#operation-history');
    }
</script>
<script>
    const currentLeadId = Number("{{!$empty ? $item->id : ''}}");
    const toggleSidebarBtn = document.querySelector('.leadFormContainer__right-expandCta')
    const leadLayoutElement = document.querySelector('.leadLayout');
    const toggleSidebarExpandedState = () => {
        if (leadLayoutElement.classList.contains('leadLayout--withActivitiesBarExpaneded')) {
            leadLayoutElement.classList.remove('leadLayout--withActivitiesBarExpaneded');
        } else {
            leadLayoutElement.classList.add('leadLayout--withActivitiesBarExpaneded');
        }
    }
    toggleSidebarBtn.addEventListener('click', () => toggleSidebarExpandedState());
</script>

@if(isset($hasInventoryTable) && $hasInventoryTable)
<script type="text/javascript" src="/js/virtual-select.min.js"></script>
<script type="text/javascript" src="{{mix('/js/contacts-list-form-lite.js')}}"></script>
<script type="text/javascript" src="{{mix('/js/lead-request-filters.js')}}"></script>
@endif

@if(isset($listViewType))
@section('body-js')
<script type="text/javascript" src="/js/jquery.ba-throttle-debounce.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/fixedheader/3.1.9/js/dataTables.fixedHeader.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.js"></script>
<script type="text/javascript" src="{{mix('/js/jquery.form.min.js')}}"></script>
<script type="text/javascript" src="{{mix('/js/crm/libs/datatables-util.js')}}"></script>
<script type="text/javascript" src="{{mix('js/crm/datatables-action-menu.js')}}"></script>
<script type="text/javascript" src="{{mix('/js/lead-create-form.js')}}"></script>
<script type="text/javascript" src="{{mix('/js/crm/libs/datatables-util.js')}}"></script>
<script type="text/javascript" src="{{mix('/js/leads-view-scheduled-container-wrapper.js')}}"></script>
<script type="text/javascript" src="{{mix('/js/lead-details-lead-activity.js')}}"></script>
@endsection
@endif
