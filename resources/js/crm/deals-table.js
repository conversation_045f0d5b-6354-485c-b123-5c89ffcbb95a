window.dealsSearchParams = {};
let filtersInitialized = false;
let dealsTable;

$(document).ready(function () {
    const addCustomFiltersAndSearch = (sSource, aoData, fnCallback) => {
        if (filtersInitialized) {
            const urlStr = parseDTParams(aoData);
            urlStr.push(`vt=${controllerListViewType}`);

            Object.keys(window.dealsSearchParams).forEach((k) => {
                urlStr.push(`${k}=${window.dealsSearchParams[k]}`);
            });

            const data = urlStr.join("&");
            $.ajax({
                dataType: "json",
                url: `/crm/deals?vt=${controllerListViewType}`,
                data,
                success: (input) => {
                    fnCallback(input);
                },
            });
        }
    };

    $("#deals-list-filters th").each(function () {
        var title = $(this).text();
        var html =
            '<div class="input-group input-group-sm" >' +
            '<input type="text" class="form-control" id="deals-list-col-filter-' +
            $(this).data("col-index") +
            '" placeholder="' +
            title +
            '" />' +
            '<span class="input-group-addon"><span class="glyphicon glyphicon-filter"></span></span>' +
            "</div>";
        $(this).html(html);
    });



    dealsTable = $("#deals-list").DataTable({
        serverSide: true,
        lengthMenu: [
            [10, 25, 50, -1],
            [10, 25, 50, "All"]
        ],
        pageLength: 50,
        responsive: false,
        scrollY: "calc(100vh - 350px)",
        scrollX: true,
        oLanguage: {
            sProcessing: "<img width='20' height='20' src='/images/ajax_spinner.gif'>"
        },
        processing: true,
        // ajax: {
        //     url: `/crm/deals?vt=${controllerListViewType}`,
        //     type: "GET"
        // },
        searching: true,
        order: false,
        columnDefs: [
            {
                class: "details-control",
                targets: 0,
                orderable: false,
                data: null,
                render: () => {
                    return '';
                }
            },
            {
                data: "reminder",
                name: "reminder",
                targets: 1,
                orderable: false,
                render(data, type, full, row) {
                    return data ? `<span>
                    <a onclick="triggerAddReminderModal('${full.DT_RowId}', 'deal')" class="dropdown-item" style="padding:0" href="#addNoteModal">
                        <svg fill="#000000" width="24" height="24" viewBox="0 0 32 32" id="icon" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1{fill:none;}</style></defs><title>reminder</title><path d="M30,23.3818l-2-1V20a6.0046,6.0046,0,0,0-5-5.91V12H21v2.09A6.0046,6.0046,0,0,0,16,20v2.3818l-2,1V28h6v2h4V28h6ZM28,26H16V24.6182l2-1V20a4,4,0,0,1,8,0v3.6182l2,1Z"/><path d="M28,6a2,2,0,0,0-2-2H22V2H20V4H12V2H10V4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2h4V26H6V6h4V8h2V6h8V8h2V6h4v6h2Z"/><rect id="_Transparent_Rectangle_" data-name="&lt;Transparent Rectangle&gt;" class="cls-1" width="32" height="32"/></svg>
                    </a>
                    </span>` : "";
                },

            },
            {
                name: "d.id",
                data: "deal_ref_no",
                targets: 2,
                responsivePriority: 1,
                render: function (data, type, full, row) {
                    return data;
                },
            },
            {
                targets: 3,
                data: "ref_no",
                name: "p.ref_no",
                render: function (data, type, full, row) {
                    var text = data.display;
                    var url = data.url;
                    var html = '<a href="' + url + '" target="_blank">' + text + "</a>";

                    return html;
                },
                responsivePriority: 1
            },
            {
                name: "d.unit_number",
                data: "unit_number",
                targets: 4,
                responsivePriority: 1
            },
            {
                targets: 5,
                data: "client",
                name: "c.name",
                render: function (data, type, full, row) {
                    var html = "";

                    if (!!data && !!data.display) {
                        var items = data.display.split(";");
                        html =
                            '<a href="' +
                            data.url +
                            '"' +
                            (data.url != "#" ? 'target="_blank"' : "") +
                            ">" +
                            items[0] +
                            (!!items[1] ? " <strong>" + items[1] + "</strong>" : "") +
                            "</a>";
                    }

                    return html;
                }
            },
            {
                targets: 6,
                name: "o.name",
                data: "landlord",
                render: function (data, type, full, row) {
                    var html = "";
                    if (!!data && !!data.display) {
                        var items = data.display.split(";");
                        html =
                            '<a href="' +
                            data.url +
                            '"' +
                            (data.url != "#" ? 'target="_blank"' : "") +
                            ">" +
                            items[0] +
                            (!!items[1] ? " <strong>" + items[1] + "</strong>" : "") + "</a>";
                    }

                    return html;
                }
            },
            {
                name: "d.price",
                data: "price",
                targets: 7,
                responsivePriority: 4
            },
            {
                name: "d.start_date",
                data: "start_date",
                targets: 8,
                responsivePriority: 5
            },
            {
                name: "d.end_date",
                data: "end_date",
                targets: 9,
                responsivePriority: 6
            },
            {
                name: "d.creator_name",
                targets: 10,
                responsivePriority: 7,
                render: function (data, _, row) {
                    return row.creator_name || '-';
                }
            },
            {
                name: "d.deal_status",
                targets: 11,
                responsivePriority: 8,
                render: function (data, _, row) {
                    return row.deal_status || '-';
                }
            },
            {
                data: "deal_payment_status",
                name: "d.deal_payment_status",
                targets: 12,
                // responsivePriority: 8,
                render: function (data, _, row) {
                    if (row.deal_payment_status) {
                        if (row.deal_payment_status[0] === 'STATUS_PENDING' && row.deal_payment_status[1] === 'STATUS_PENDING') {
                            return "<div class=\"statusBox text-nowrap font-small badge p-2 ps-2 pe-2\">In Pending</div>";
                        } else if (row.deal_payment_status[0] === 'STATUS_CASHED' && row.deal_payment_status[1] === 'STATUS_CASHED') {
                            return "<div class=\"statusBox text-nowrap font-small badge p-2 ps-2 pe-2\">Cashed</div>";
                        } else if (row.deal_payment_status[0] === 'STATUS_PENDING' && row.deal_payment_status[1] === 'STATUS_CASHED') {
                            return "<div class=\"statusBox text-nowrap font-small badge p-2 ps-2 pe-2\">Pending Cashed</div>";
                        } else if (row.deal_payment_status[0] === 'STATUS_CASHED' && row.deal_payment_status[1] === 'STATUS_PENDING') {
                            return "<div class=\"statusBox text-nowrap font-small badge p-2 ps-2 pe-2\">Cashed Pending</div>";
                        } else if (row.deal_payment_status[0] === 'STATUS_CASHED') {
                            return "<div class=\"statusBox text-nowrap font-small badge p-2 ps-2 pe-2\">Cashed(landlord)</div>";
                        } else if (row.deal_payment_status[0] === 'STATUS_PENDING') {
                            return "<div class=\"statusBox text-nowrap font-small badge p-2 ps-2 pe-2\">In pending(landlord)</div>";
                        } else if (row.deal_payment_status[1] === 'STATUS_CASHED') {
                            return "<div class=\"statusBox text-nowrap font-small badge p-2 ps-2 pe-2\">Cashed(client)</div>";
                        } else if (row.deal_payment_status[1] === 'STATUS_PENDING') {
                            return "<div class=\"statusBox text-nowrap font-small badge p-2 ps-2 pe-2\">In pending(client)</div>";
                        }
                    }
                    return "N/A";
                }
            },
            {
                targets: -1,
                data: null,
                className: "export-transient",
                render: function (data, type, full, row) {
                    var actions = data[8];
                    var canEdit = data['can_edit'];
                    var canApprove = data['can_approve'];
                    const canUpdateDealStatus = data['can_update_deal_status'];

                    const readLink = actions.read
                        ? `<li><a class="dropdown-item" href="${actions.read}"><i class="bi bi-eye"></i> View</a></li>`
                        : ``;

                    const editLink = actions.update && canEdit ? `<li><a class="dropdown-item" href="${actions.update}"><i class="bi bi-pen"></i> Edit</a></li>` : ``;
                    const deleteLink = actions.delete ? `<li><a class="dropdown-item" href="${actions.delete}"><i class="bi bi-trash"></i> Delete</a></li>` : ``;
                    const remindersLink = `<li><a onclick="triggerAddReminderModal('${data.DT_RowId}', 'deal')" class="dropdown-item" href="#addNoteModal"><i class="bi bi-calendar-check"></i> Reminders </a></li>`;

                    let approveLink = '';
                    let changeStatusLink = '';
                    if (canApprove) {
                        approveLink = `<li><a class="dropdown-item" onclick="triggerStatusChangeModalPayment('${data.deal_payment_status[0]}', '${data.deal_payment_status[1]}', '${data.DT_RowId}')" href="#dealPaymentStatusModal"><i class="bi bi-send-check"></i> Update Payment Status </a></li>`;
                        changeStatusLink = `<li><a class="dropdown-item" onclick="triggerStatusChangeModal('${data.deal_status}', '${data.DT_RowId}')" href="#leadStatusChangeModal"><i class="bi bi-send-check"></i> Update Deal Status </a></li>`;
                    }
                    if (canUpdateDealStatus) {
                        changeStatusLink = `<li><a class="dropdown-item" onclick="triggerStatusChangeModal('${data.deal_status}', '${data.DT_RowId}')" href="#leadStatusChangeModal"><i class="bi bi-send-check"></i> Update Deal Status </a></li>`;
                    }

                    return `<div class="dropdown" data-item-id="${data.DT_RowId}">
                    <div onclick="javascript: toggleActionMenu(${data.DT_RowId})" id="dropdownMenuButton${data.DT_RowId}">
                        <button class="btn btn-sm btn-light dropdown-toggle" type="button" >
                            Actions
                        </button>
                    </div>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton${data.DT_RowId}">
                        ${readLink}
                        ${editLink}
                        ${deleteLink}
                        ${approveLink}
                        ${remindersLink}
                        ${changeStatusLink}
                    </ul>
                </div>`;
                },
                orderable: false,
                searchable: false,
                responsivePriority: 1
            },
        ],
        orderCellsTop: true,
        // initComplete: function () {
        //     this.api()
        //         .columns()
        //         .every(function () {
        //             var that = this;
        //             $("#deals-list-col-filter-" + this.index()).keyup(function () {
        //                 if (that.search() !== this.value) {
        //                     that.search(this.value).draw();
        //                 }
        //             });
        //         });
        // },
        fnServerData: addCustomFiltersAndSearch,
        fnRowCallback: function (nRow, aData) {
            if (nRow && aData.deal_payment_status) {
                if (aData.deal_payment_status[0] === 'STATUS_PENDING' && aData.deal_payment_status[1] === 'STATUS_PENDING') {
                    $(nRow).addClass("dealsRow--statusPending");
                } else if (aData.deal_payment_status[0] === 'STATUS_CASHED' && aData.deal_payment_status[1] === 'STATUS_CASHED') {
                    $(nRow).addClass("dealsRow--statusCashed");
                } else if (aData.deal_payment_status[0] === 'STATUS_PENDING' && aData.deal_payment_status[1] === 'STATUS_CASHED') {
                    $(nRow).addClass("dealsRow--statusPendingCashed");
                } else if (aData.deal_payment_status[0] === 'STATUS_CASHED' && aData.deal_payment_status[1] === 'STATUS_PENDING') {
                    $(nRow).addClass("dealsRow--statusCashedPending");
                } else if (aData.deal_payment_status[0] === 'STATUS_CASHED') {
                    $(nRow).addClass("dealsRow--statusCashed");
                } else if (aData.deal_payment_status[0] === 'STATUS_PENDING') {
                    $(nRow).addClass("dealsRow--statusPending");
                } else if (aData.deal_payment_status[1] === 'STATUS_CASHED') {
                    $(nRow).addClass("dealsRow--statusCashed");
                } else if (aData.deal_payment_status[1] === 'STATUS_PENDING') {
                    $(nRow).addClass("dealsRow--statusPending");
                }
            }
        },
    });

    const detailRows = [];

    function format(dealData) {
        const clientPhone = `${dealData.client ? (dealData.client.prefix_mobile_1 || '') : ''}${dealData.client ? (dealData.client.mobile_1 || '') : ''}`;
        const ownerPhone = `${dealData.owner ? (dealData.owner.prefix_mobile_1 || '+974') : ''}${dealData.owner ? (dealData.owner.mobile_1 || '') : ''}`;
        const dealDocumentURL = `${!!dealData.dealDocumentURL ? dealData.dealDocumentURL : ''}`;
        const clientEmail = `${dealData.client ? (dealData.client.email_1 || '') : ''}`;
        const agentWhoListed = dealData.property ? dealData.property.owner : null;
        const authorName = dealData.author ? dealData.author.name : null;
        const agentWhoListedName = agentWhoListed ? agentWhoListed.name : null;
        const displayableObject = {
            dealType: {
                label: "Deal type",
                value: dealData.type
            },
            clientPhone: {
                label: "Client Phone",
                value: clientPhone,
                url: `<a href="tel:${clientPhone}">${clientPhone}</a>`
            },
            clientEmail: {
                label: "Client Email",
                value: clientEmail,
                url: `<a href="mailto:${clientEmail}">${clientEmail}</a>`
            },
            ownerPhone: {
                label: "Owner phone",
                value: ownerPhone,
                url: `<a href="tel:${ownerPhone}">${ownerPhone}</a>`
            },
            commissionLandlord: {
                label: "Commission Landlord",
                value: `${dealData.commission_landlord}`,
                url: ''
            },
            landlordInvoiceNo: {
                label: "Landlord invoice no",
                value: `${dealData.owner_invoice_no}`,
                url: ''
            },
            commissionClient: {
                label: "Commission Client",
                value: dealData.commission_client || '-',
                url: ''
            },
            clientInvoiceNo: {
                label: "Client invoice no",
                value: dealData.client_invoice_no || '-',
                url: ''
            },
            agentWhoListedTheProperty: {
                label: "Agent who listed",
                value: `${agentWhoListedName}`
            },
            closingAgent: {
                label: "Closing Agent",
                value: authorName
            },
            comments: {
                label: "Referred Closing Agent",
                value: dealData.comments || ''
            },
        };

        if (!!dealDocumentURL) {
            displayableObject.dealDocument = {
                label: "Deal document",
                value: dealDocumentURL,
                url: `<a href="${dealDocumentURL}">Download</a>`
            }
        }

        const fieldsMarkup = Object.values(displayableObject).map(eachItem => {
            const itemValue = !!eachItem.url ? eachItem.url : eachItem.value;
            return `<article style="width:220px; max-width: 220px; overflow: hidden" class="pb-2 pe-4 mb-2 border-bottom"><h6 class="mb-0">${eachItem.label}</h6><main>${itemValue}</main></article>`
        }).join('');

        return `
            <div class="mt-2 mb-2">
                <div class="d-flex flex-wrap">
                    ${fieldsMarkup}
                </div>
            </div>
        `;
    }

    $("#deals-list tbody").on(
        "click",
        "tr td.details-control",
        function () {
            const tr = $(this).closest("tr");
            const row = dealsTable.row(tr);
            const idx = $.inArray(tr.attr("id"), detailRows);

            if (row.child.isShown()) {
                tr.removeClass("details");
                row.child.hide();
                // Remove from the 'open' array
                detailRows.splice(idx, 1);
            } else {
                const rowData = row.data();
                Promise.all([
                    $.get(`/crm/deals/${rowData.DT_RowId}/details`),
                ])
                    .then(([dealDetails]) => {
                        tr.addClass("details");
                        row.child(
                            format(dealDetails)
                        ).show();
                        if (idx === -1) {
                            detailRows.push(tr.attr("id"));
                        }
                    })
                    .catch((err) => console.log(err));
            }
        }
    );

    if (typeof addExcelDownloadButton !== "undefined") {
        addExcelDownloadButton(table, "deals");
    }
});

function onDealsFiltersParamsChange(filterParams) {
    window.dealsSearchParams = filterParams;
    if (!filtersInitialized) {
        filtersInitialized = true;
    }
    if (!!dealsTable) {
        dealsTable.draw();
    }
}