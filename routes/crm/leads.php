<?php

use App\Http\Controllers\Crm\LeadsControllerCrm;
use App\Http\Controllers\Crm\LeadNotesController;
use App\Http\Controllers\Crm\LeadOfferController;

Route::get("", [LeadsControllerCrm::class, 'index'])->name("crm.leads.index");
Route::get("v2", [LeadsControllerCrm::class, 'viewV2'])->name("crm.leads.v2");
Route::get("properties-suggestions", [LeadsControllerCrm::class, 'propertySuggestions'])->name("crm.leads.listing.suggestions");
Route::get("create", [LeadsControllerCrm::class, 'create'])->name("crm.leads.create");
Route::get("export", [LeadsControllerCrm::class, 'export'])->name("crm.leads.export");
Route::post("export-filtered", [LeadsControllerCrm::class, 'exportFiltered'])->name("crm.leads.export-filtered");
Route::post("create/post", [LeadsControllerCrm::class, 'store'])->name("crm.leads.store");
Route::post("source", [LeadsControllerCrm::class, 'saveSource'])->name("crm.leads.store.source");
Route::get("old-leads-statuses", [LeadsControllerCrm::class, 'updateOldLeadStatuses'])->name("crm.leads.old-statuses");
Route::prefix('{id}')->group(function () {
    Route::get('', [LeadsControllerCrm::class, 'show'])->name("crm.leads.read");
    Route::patch('', [LeadsControllerCrm::class, 'patchUpdateLead'])->name("crm.leads.update");
    Route::get("edit", [LeadsControllerCrm::class, 'edit'])->middleware('check.lead.access')->name("crm.leads.edit");
    Route::get('/download-form', [LeadsControllerCrm::class, 'downloadForm'])->name('lead.forms.download');
    Route::get('/preview-form', [LeadsControllerCrm::class, 'previewForm'])->name('lead.forms.preview');
    Route::post("edit/post", [LeadsControllerCrm::class, 'save'])->name("crm.leads.save");
    Route::get("delete", [LeadsControllerCrm::class, 'delete'])->name("crm.leads.delete");
    Route::post("delete/post", [LeadsControllerCrm::class, 'postDelete'])->name("crm.leads.delete.update");
    // Route::get('operationHistory', [LeadsControllerCrm::class, 'listingOperationHistory']);
    Route::get('operation-history', [LeadsControllerCrm::class, 'listingOperationHistory'])->name('operationHistory');
    Route::post('add-operation-history', [LeadsControllerCrm::class, 'createOperationHistory'])->name('leads.operationHistory.add');
    Route::post('update-lead-status', [LeadsControllerCrm::class, 'updateLeadStatus'])->name('leads.post.updateStatus');
    Route::prefix('manage')->group(function () {
        Route::get('leads-suggestions', [LeadsControllerCrm::class, 'leadsPropertiesSuggestions']);
        /*** Leads Dashboard Offer section routes ***/
        Route::group(['prefix' => 'offer'], function () {
            Route::get("/", function ($id) {
                $controller = App::make('App\Http\Controllers\Crm\LeadDashboardController');
                return $controller->index($id, "offer");
            })->name("leads-dashboard-offer");

            Route::post("/add/{assetId}", [LeadOfferController::class, 'addOffer'])->name("leads-dashboard-offer-add");
            Route::post("/remove/{assetId}", [LeadOfferController::class, 'removeOffer'])->name("leads-dashboard-offer-remove");

            // Route::post("/add/{assetId}/note", "Crm\LeadOfferController@addNote")->name("leads-dashboard-offer-add-note");
        });
        /** END OF Leads Dashboard Offers section routes **/

        /*** Leads Dashboard Notes & Reminders section routes ***/
        Route::group(['prefix' => 'notes'], function () {
            Route::get("/", function ($id) {
                $controller = App::make('App\Http\Controllers\Crm\LeadDashboardController');

                return $controller->index($id, "notes");
            })->name("leads-dashboard-notes");
            Route::get("/calendar-view", [LeadNotesController::class, 'calendarView'])->name("leads-dashboard-notes-calendar-view");
            Route::post("/add/", [LeadNotesController::class, 'addToLead'])->name("leads-dashboard-add-note");
            Route::post("/remove/{noteId}", [LeadNotesController::class, 'remove']);
            Route::post("/update/{noteId}", [LeadNotesController::class, 'update']);
            Route::post("/update/{noteId}/add-reminder", [LeadNotesController::class, 'addReminder']);
        });

        Route::group(['prefix' => 'reminders'], function () {
            Route::post("/remove/{reminderId}", "App\Http\Controllers\Crm\LeadNotesController@removeReminder");
            Route::post("/update/{reminderId}", "App\Http\Controllers\Crm\LeadNotesController@updateReminder");
        });
        /** END OF Leads Dashboard Notes & Reminders section routes **/

        /*** Leads Dashboard Close section routes ***/
        Route::group(['prefix' => 'close'], function () {
            Route::get("/", function ($id) {
                $controller = App::make('App\Http\Controllers\Crm\LeadDashboardController');
                return $controller->index($id, "close");
            })->name("leads-dashboard-close");

            Route::post("/promote", "App\Http\Controllers\Crm\LeadCloseController@promote")->name("leads-dashboard-close-promote");
            Route::post("/hold", "App\Http\Controllers\Crm\LeadCloseController@hold")->name("leads-dashboard-close-hold");
            Route::post("/discard", "App\Http\Controllers\Crm\LeadCloseController@discard")->name("leads-dashboard-close-discard");
        });
        /** END OF Leads Dashboard Close section routes **/
    });

    Route::get("get-proposals", 'App\Http\Controllers\Crm\LeadOfferController@index')->name("lead-dashboard-offers-list");
    Route::get("get-notes", 'App\Http\Controllers\Crm\LeadNotesController@index')->name("lead-dashbord-notes-list");

    Route::group(['prefix' => 'js'], function () {
        Route::get("offer.js", 'App\Http\Controllers\Crm\LeadDashboardController@offersJavascript')->name('lead-dashboard-offers-js');
        Route::get("close.js", 'App\Http\Controllers\Crm\LeadDashboardController@closeJavascript')->name('lead-dashboard-close-js');
        Route::get("notes-list.js", 'App\Http\Controllers\Crm\LeadDashboardController@notesListJavascript')->name('lead-dashboard-notes-list-js');
        Route::get("notes-common.js", 'App\Http\Controllers\Crm\LeadDashboardController@notesCommonJavascript')->name('lead-dashboard-notes-common-js');
        Route::get("notes-calendar.js", 'App\Http\Controllers\Crm\LeadDashboardController@notesCalendarJavascript')->name('lead-dashboard-notes-calendar-js');
        Route::get("notes-reminders.js", 'App\Http\Controllers\Crm\LeadDashboardController@notesRemindersJavascript')->name('lead-dashboard-notes-reminders-js');
    });
});
