<style>
    .vscomp-ele {
        max-width: 100%;
    }
</style>
<script>
    const agents = JSON.parse('{!! addslashes(json_encode($agents->map(function($agent){ return $agent->only("id", "selectName"); })->values()->toArray())) !!}')
    const leadSourcesMap = JSON.parse('{!! $sources->mapWithKeys(function($s) { return [$s->id => $s->name];  })->toJSON() !!}');
    const teamLeaderAgents = JSON.parse('{!! addslashes(json_encode($teamLeaderAgents->map(function($agent){ return $agent->only("id", "name"); })->values()->toArray())) !!}')
    const leadSources = Object.keys(leadSourcesMap).map((key) => ({
        label: leadSourcesMap[key],
        value: key
    }))
</script>
<div class="col-xs-12 filters-container">
    <div class="row">
        <deals-filters></deals-filters>
    </div>
</div>

<script type="text/javascript" src="{{mix('/js/deals-filters.js')}}"></script>
