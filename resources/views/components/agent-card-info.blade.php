@php
$whatsappText = __("Hi, I want to know more about one of your listings");
$agentMessage = "";
if(isset($snapshot)) {
    $whatsappText = getDefaultWhatsappMessage($snapshotUrl);
    $agentMessage = __("Hi, I found your property with ref: refNo on https://www.fgrealty.qa. Please get in touch with me. Thank you.", ['refNo' => isset($snapshot) ? $snapshot->ref_no : '']);
}


$profileImageDefault = 'https://via.placeholder.com/120x120.png?text=';
$whatsappPhone = $agent->getCompletePhoneNo();
$agentCompletePhoneNo = $agent->getCompletePhoneNo();
$brokerageLicenseUser = $agent->brokerageLicenseUser;
$agentEmail = $agent->email;
$usedAgent = $agent;

/*if($agent->should_use_whatsapp_chatbot) {
    $whatsappPhone = env('WHATSAPP_CHATBOT_PHONE_NO');
}*/
if(!is_null($brokerageLicenseUser)) {
    $whatsappPhone = $brokerageLicenseUser->getCompletePhoneNo();
    $agentCompletePhoneNo = $brokerageLicenseUser->getCompletePhoneNo();
    $agentEmail = $brokerageLicenseUser->email;
    $usedAgent = $brokerageLicenseUser;
}

if(request()->has('isTestingWhatsapp')) {
    $whatsappPhone = "15551799888";
    $whatsappText = "Hi, Please provide me with more details for listing [".$snapshot->ref_no."]";
}

$agentSectionVariantCSSClassName = "";
$agentProfileVariantCSSClassName = "";
if(isset($variant)) {
if($variant == 'formVariant') {
$agentSectionVariantCSSClassName = "agent-section--formVariant";
$agentProfileVariantCSSClassName = "agent-profile--formVariant";
} else if($variant == 'mobileVariant') {
$agentSectionVariantCSSClassName = "agent-section--mobileVariant";
$agentProfileVariantCSSClassName = "agent-profile--mobileVariant";
}
}
@endphp
<div class="agent-section {{$agentSectionVariantCSSClassName}} {{isset($additionalCssClasses) ? $additionalCssClasses : ''}} ">
    <div class="agent-section__close absolute top-2.5 right-2.5">
        <svg class="cursor-pointer" xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
            <g id="close" transform="translate(-1183 -656)">
                <path id="Path_535" data-name="Path 535" d="M1.184,0,16.192.212a1.054,1.054,0,0,1,1.1,1.1A1.244,1.244,0,0,1,16.114,2.5L1.106,2.288A1.054,1.054,0,0,1,0,1.184,1.244,1.244,0,0,1,1.184,0Z" transform="translate(1195.231 670) rotate(-135)" fill="#1a1a1a" />
                <path id="Path_536" data-name="Path 536" d="M1.184,2.5l15.009-.212a1.054,1.054,0,0,0,1.1-1.1A1.244,1.244,0,0,0,16.114,0L1.106.212A1.054,1.054,0,0,0,0,1.317,1.244,1.244,0,0,0,1.184,2.5Z" transform="translate(1183 668.232) rotate(-45)" fill="#1a1a1a" />
            </g>
        </svg>
    </div>
    <a href="{{persistQueryParamsToURL(route('agents.single', ['slug' => $usedAgent->slug]))}}">
        @if(!empty($usedAgent->profile_image))
        <img class="agent-section__image" src="{{!is_null($usedAgent) && !empty($usedAgent->profile_image) ? imageRoute('agent-big', $usedAgent->profile_image) : '/images/no-agent-images.png'}}" alt="{{$usedAgent->name}}" class="img-rounded">
        @else
        <img class="agent-section__image" src="{{asset('images/no-agent-images.png')}}" alt="Profile Image" class="img-rounded">
        @endif
    </a>
    <div class="agent-profile {{$agentProfileVariantCSSClassName}}">
        <div class="agent-profile__mainInfo">
            <div class="agent-profile__mainInfo-topLine">
                <div class="agent-profile__mainInfo-topLine-left">
                    <span class="agent-profile__mainInfo-name"> {{$usedAgent->name}} </span>
                    <div class="agent-profile__mainInfo-position">{{__("$usedAgent->position")}} </div>
                    <div class="agent-profile__mainInfo-location">
                        <img src="{{ asset('images/svg/agent-location.svg') }}" alt="{{__("Location")}}">
                        <span class="location"> {{__("Doha, Qatar")}} </span>
                    </div>
                </div>
                <div class="agent-profile__mainInfo-topLine-right">
                    <div class="agents-rating">
                        <span class="agents-rating__star rating-star">
                            <i class="star"></i>
                            <i class="star"></i>
                            <i class="star"></i>
                        </span>
                        <span class="agents-rating__average">{{$usedAgent->rating > 0 ? number_format($usedAgent->rating, 1) : '-'}}</span>
                        <span class="agents-rating__maximum">/5</span>
                    </div>
                </div>
            </div>
            <div class="agent-profile__mainInfo-agentInfo agent-info">
                <div class="agent-info__phone">
                    <div class="agent-info__phone-text">Phone: </div>
                    <a class="agent-info__phone-a" href="tel:{{$agentCompletePhoneNo}}">{{$agentCompletePhoneNo}}</a>
                </div>
                <div class="agent-info__email">
                    <div class="agent-info__email-text">Email: </div>
                    <a class="agent-info__email-a" href="mailto:{{$agentEmail}}">{{$agentEmail}}</a>
                </div>
            </div>
            <div class="agent-profile__mainInfo-cta cursor-pointer">
                <div>
                    <svg width="18px" height="14px" viewBox="0 0 24 24" fill="#ffffff" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.4" d="M8.5 10.5H15.5" stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                        <path d="M7 18.4302H11L15.45 21.3902C16.11 21.8302 17 21.3602 17 20.5602V18.4302C20 18.4302 22 16.4302 22 13.4302V7.43018C22 4.43018 20 2.43018 17 2.43018H7C4 2.43018 2 4.43018 2 7.43018V13.4302C2 16.4302 4 18.4302 7 18.4302Z" stroke="#292D32" stroke-width="1.5" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                </div>
                <div>{{ __("Send agentName a quick message", ['agentName' => $usedAgent->getFirstNamePart()]) }}</div>

            </div>
            <div class="buttons">
                <a class="btn-call" href="tel:{{$agentCompletePhoneNo}}">
                    <svg class="svg-phone" xmlns="http://www.w3.org/2000/svg" width="14.912" height="16" viewBox="0 0 16 16">
                        <defs>
                            <style>
                                .a {
                                    fill: #ffffff;
                                }
                            </style>
                        </defs>
                        <g transform="translate(-0.872 0)">
                            <path class="a" d="M14.113,11.136c-.967-.827-1.947-1.327-2.9-.5l-.57.5c-.417.362-1.193,2.054-4.191-1.395s-1.214-3.981-.8-4.34l.573-.5c.95-.827.591-1.869-.094-2.941L5.72,1.308C5.032.238,4.283-.464,3.331.362l-.515.45a4.83,4.83,0,0,0-1.883,3.2c-.343,2.272.74,4.873,3.222,7.727s4.906,4.29,7.2,4.265a4.843,4.843,0,0,0,3.425-1.419l.516-.45c.95-.825.36-1.666-.608-2.494Z" transform="translate(0 0)" />
                        </g>
                    </svg>
                </a>
                <a class="btn-whatsapp" href="https://wa.me/{{str_replace(' ', '', $whatsappPhone)}}?text={{$whatsappText}}">
                    <svg class="svg-whatsapp" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                        <defs>
                            <style>
                                .a {
                                    fill: #ffffff;
                                }
                            </style>
                        </defs>
                        <path class="a" d="M9.907,8.935c-.3.124-.5.6-.693.84a.29.29,0,0,1-.375.081A5.279,5.279,0,0,1,6.2,7.6a.323.323,0,0,1,.041-.441,1.785,1.785,0,0,0,.479-.777c.114-.665-.756-2.728-1.9-1.793-3.3,2.692,5.511,9.833,7.1,5.971C12.373,9.464,10.409,8.73,9.907,8.935ZM8,14.6a6.6,6.6,0,0,1-3.325-.9.7.7,0,0,0-.537-.071l-2.187.6.762-1.678a.7.7,0,0,0-.069-.7A6.6,6.6,0,1,1,8,14.6ZM8,0A8,8,0,0,0,1.275,12.336L.06,15.012A.7.7,0,0,0,.7,16c.451,0,2.908-.772,3.533-.944A8,8,0,1,0,8,0Z" transform="translate(0.003)" />
                    </svg>
                </a>
                <a class="btn-email" href="mailto:{{$agentEmail}}">
                    <svg class="svg-email" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 18 16">
                        <g transform="translate(-921.287 -779)">
                            <g transform="translate(921.287 779)">
                                <path class="a" d="M1.833,14A1.866,1.866,0,0,1,0,12.109V1.893A1.867,1.867,0,0,1,1.833,0H16.162A1.869,1.869,0,0,1,18,1.893V12.109A1.867,1.867,0,0,1,16.162,14Zm-.366-1.891a.372.372,0,0,0,.366.378H16.162a.371.371,0,0,0,.369-.378V3.872L10.019,8.346a1.786,1.786,0,0,1-2.038,0L1.467,3.872Zm0-10.25.07.2.086.1c.022.014.039.027.058.037L8.795,7.086A.36.36,0,0,0,9,7.151a.364.364,0,0,0,.2-.065l7.2-4.941a.3.3,0,0,0,.132-.252.373.373,0,0,0-.369-.38H1.833A.375.375,0,0,0,1.467,1.859Z" --}} transform="translate(0)" />
                            </g>
                        </g>
                    </svg>
                </a>
            </div>
        </div>

        <div class="agent-profile__ratingContainer">
            <div class="agents-rating">
                <span class="agents-rating__star rating-star">
                    <i class="star"></i>
                    <i class="star"></i>
                    <i class="star"></i>
                </span>
                <span class="agents-rating__average">{{$usedAgent->rating > 0 ? number_format($usedAgent->rating, 1) : '-'}}</span>
                <span class="agents-rating__maximum">/5</span>
            </div>
            @if($usedAgent->approvedRatingReviews->count() > 0)
            <div class="agent-profile__ratingContainer-total">
                <span>{{$usedAgent->approvedRatingReviews->count()}} Verified Reviews</span>
                <div class="tooltip">
                    <img src="{{asset('images/svg/info.svg')}}" width="16px" height="16px" alt="">
                    <span class="tooltiptext">{{__("Each review’s source is individually verified by the FGRealty back-office.")}}</span>
                </div>
            </div>
            @endif
        </div>
    </div>
    <div class="agent-section__description">
        @if(!empty($usedAgent->short_bio))
        <p>
            {{$usedAgent->short_bio}}
        </p>
        @else
        <p>
            No info
        </p>
        @endif
    </div>
    <div class="agent-section__contact">
        <h3>Connect with {{$usedAgent->name}}</h3>
        @if(!empty($usedAgent->linkedin))
        <a href="{{$usedAgent->linkedin}}" class="btn btn-inverted btn-secondary--border">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                <path class="a" style="fill: #1A1A1A" d="M18.826,18.525h0V12.656c0-2.871-.618-5.082-3.974-5.082A3.484,3.484,0,0,0,11.718,9.3h-.047V7.842H8.489V18.524H11.8V13.235c0-1.393.264-2.739,1.989-2.739,1.7,0,1.725,1.589,1.725,2.829v5.2Z" transform="translate(-2.83 -2.525)" fill="#1A1A1A" />
                <path class="a" style="fill: #1A1A1A" d="M.4,7.977H3.713V18.659H.4Z" transform="translate(-0.132 -2.659)" />
                <path class="a" style="fill: #1A1A1A" d="M1.921,0A1.93,1.93,0,1,0,3.843,1.921,1.922,1.922,0,0,0,1.921,0Z" />
            </svg>
            <span>Linkedln Profile</span>
        </a>
        @endif
        <a href="mailto:{{$agentEmail}}" class="btn btn-inverted btn-secondary--border">
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="14" viewBox="0 0 18 14">
                <g id="email" transform="translate(-921.287 -779)">
                    <g id="Group_1366" data-name="Group 1366" transform="translate(921.287 779)">
                        <path id="Union_76" data-name="Union 76" d="M1.833,14A1.866,1.866,0,0,1,0,12.109V1.893A1.867,1.867,0,0,1,1.833,0H16.162A1.869,1.869,0,0,1,18,1.893V12.109A1.867,1.867,0,0,1,16.162,14Zm-.366-1.891a.372.372,0,0,0,.366.378H16.162a.371.371,0,0,0,.369-.378V3.872L10.019,8.346a1.786,1.786,0,0,1-2.038,0L1.467,3.872Zm0-10.25.07.2.086.1c.022.014.039.027.058.037L8.795,7.086A.36.36,0,0,0,9,7.151a.364.364,0,0,0,.2-.065l7.2-4.941a.3.3,0,0,0,.132-.252.373.373,0,0,0-.369-.38H1.833A.375.375,0,0,0,1.467,1.859Z" transform="translate(0)" fill="#1A1A1A" />
                    </g>
                </g>
            </svg>
            <span>Write an email</span>
        </a>
        <a href="https://wa.me/{{str_replace(' ', '', $whatsappPhone)}}?text={{$whatsappText}}" class="btn btn-inverted btn-secondary--border">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                <path id="whatsapp" d="M9.907,8.935c-.3.124-.5.6-.693.84a.29.29,0,0,1-.375.081A5.279,5.279,0,0,1,6.2,7.6a.323.323,0,0,1,.041-.441,1.785,1.785,0,0,0,.479-.777c.114-.665-.756-2.728-1.9-1.793-3.3,2.692,5.511,9.833,7.1,5.971C12.373,9.464,10.409,8.73,9.907,8.935ZM8,14.6a6.6,6.6,0,0,1-3.325-.9.7.7,0,0,0-.537-.071l-2.187.6.762-1.678a.7.7,0,0,0-.069-.7A6.6,6.6,0,1,1,8,14.6ZM8,0A8,8,0,0,0,1.275,12.336L.06,15.012A.7.7,0,0,0,.7,16c.451,0,2.908-.772,3.533-.944A8,8,0,1,0,8,0Z" transform="translate(0.003)" fill="#1A1A1A" fill-rule="evenodd" />
            </svg>
            <span>Send a message</span>
        </a>
    </div>
    <div class="agent-section__agentInfo agent-info">
        <h3>Professional information</h3>
        <div class="agent-info__phone">
            <div class="agent-info__phone-text">Phone: </div>
            <a class="agent-info__phone-a" href="tel:{{$agentCompletePhoneNo}}">{{$agentCompletePhoneNo}}</a>
        </div>
        <div class="agent-info__email">
            <div class="agent-info__email-text">Email: </div>
            <a class="agent-info__email-a" href="mailto:{{$agentEmail}}">{{$agentEmail}}</a>
        </div>
        @if(!empty($usedAgent->license_path))
            <div class="agent-info__license-text flex flex-row items-center">
                <div class="agent-info__nationality-text">License: </div>
                <span><a target="_blank" href="{{route('web.user.license.download', ['id' => $usedAgent->id])}}"
                    class="link-primary ml-1">@if($usedAgent->license_id) {{$usedAgent->license_id}} @else Broker Card License @endif </a></span>
            </div>
        @endif
        @if(!is_null($usedAgent->nationality))
        <div class="agent-info__nationality">
            <div class="agent-info__nationality-text">{{__('Nationality:') }}</div>
            <span class="agent-info__nationality-span">{{$usedAgent->nationality->name}}</span>
        </div>
        @endif
        @if(!is_null($usedAgent->languages))
        <div class="agent-info__languages">
            <div class="agent-info__languages-text">{{ __('Spoken Languages:') }}</div>
            <span class="agent-info__languages-span">{{$usedAgent->languages}}</span>
        </div>
        @endif
        <div class="agent-info__button">
            <button onclick="javascript: showWorkWithAgentModal('{{$usedAgent->uniq_id}}')" class="w-full text-center">
                {{ __("Start working with agentName", ['agentName' => $usedAgent->getFirstNamePart()]) }}
            </button>
            <button onclick="javascript: showRatingAgentModal('{{$usedAgent->uniq_id}}')" class="w-full text-center ag-rating">
                {{ __("Rate this Agent") }}
            </button>
        </div>
    </div>
    <div class="agent-section__form" style="width:100%">
        <div class="agent-card-form-section" style="width:100%">
            <form class="agent-card-form" onsubmit="javascript: return submitContactForm(this);">
                <div>
                    <input required type="text" class=" w-full" name="name" placeholder="{{__("Your Name*")}}">
                </div>
                <div class="flex flex-col md:flex-row gap-1 justify-between">
                    <div class="flex-1"><input required type="email" class="w-full" name="email" placeholder="{{__("Your Email*")}}"></div>
                </div>
                <div class="flex flex-col md:flex-row gap-1 justify-between">
                    @if(isset($countries))
                    @php
                        $qatarCountry = $countries->where('id', 187)->first();
                    @endphp
                    <article class="inputGroup inputGroup--borderColorVariant w-full">
                        <section class="inputGroup__left">
                            <div class="dropdown fg-dropdown">
                                <input type="hidden" class="hidden phonePrefixInput" name="prefix" value="{{$qatarCountry->phone_prefix}}">
                                <span class="dropdown__caption">
                                    <span class="flag flag-{{strtolower($qatarCountry->code)}}"></span> {{$qatarCountry->phone_prefix}}
                                </span>
                                <div class="dropdown__options" style="width: 250px">
                                    <ul>
                                        @foreach ($countries as $country)
                                        <li class="countryLi" data-countryName="{{$country->name}}" data-countryPrefix="{{$country->phone_prefix}}" data-countryCode="{{strtolower($country->code)}}">
                                            <span class="flag flag-{{strtolower($country->code)}}"></span>
                                            {{ $country->name}} ({{ $country->phone_prefix }})
                                        </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </section>
                        <section class="inputGroup__right">
                            <input required pattern="\+?[0-9]{0,40}$" type="text" class="w-full" name="phone" placeholder="{{__("Your Phone*")}}">
                        </section>
                    </article>
                    @endif
                    <!-- <div class="flex-1"><input required type="text" class="w-full" name="phone" placeholder="{{__("Your Phone*")}}"></div> -->
                </div>
                <div>
                    <textarea rows="4" name="message" required class=" w-full">{{ $agentMessage }}</textarea>
                </div>
                <div class="w-full">
                    <span class="agent-form-error-message hidden text-red-500 py-1 text-sm text-gray-80 flex-1"></span>
                    <div class="flex justify-end gap-1 items-center">
                        @if(isset($isMobile) && $isMobile)
                        <input type="button" class="btn-card-close btn btn--small" value="Cancel" />
                        @endif
                        <button class="btn w-full">
                            Send {{$usedAgent->getFirstNamePart()}} a quick message
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="{{mix("js/connect-with-agent.js")}}" async defer></script>
