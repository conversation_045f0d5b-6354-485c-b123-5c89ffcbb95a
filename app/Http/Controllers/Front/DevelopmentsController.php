<?php

namespace App\Http\Controllers\Front;

use App\Models\Country;
use App\Models\Development;
use App\Models\DevelopmentFloor;
use App\Services\DevelopmentsService;
use App\Services\GeographyService;
use App\Services\SEOContentMetaBuilderService;
use App\Services\SEOContentService;
use App\Models\DevelopmentUx;
use Illuminate\Http\Request;
use App\Services\EmailService;
use App\Services\PropertiesService;
use App\Models\User;

class DevelopmentsController extends FrontController
{

    private $developmentsService;
    private $emailService;
    protected $propertiesService;

    public function __construct(
        DevelopmentsService $developmentsService,
        SEOContentService $SEOContentService,
        GeographyService $geographyService,
        SEOContentMetaBuilderService $SEOContentMetaBuilderService,
        EmailService $emailService,
        PropertiesService $propertiesService
    ) {
        parent::__construct($SEOContentService, $geographyService, $SEOContentMetaBuilderService);
        $this->developmentsService = $developmentsService;
        $this->emailService = $emailService;
        $this->propertiesService = $propertiesService;
    }

    public function index($locale)
    {
        $developments = $this->developmentsService->getDevelopments();
        $this->handleMetas([], $locale);
        return view('developments', compact(
            'developments'
        ));
    }

    public function developments(Request $request)
    {

        $validData = request()->validate([
            'name' => 'required',
            'email_phone' => 'required',
            'message' => 'required'
        ]);

        $developments = DevelopmentUx::create($validData);
        $developments->save();

        $destinationEmail = env('EMAIL_CSR');
        $this->emailService->sendEmailDevelopments($validData, $destinationEmail);

        return response(null, 200);
    }

    public function dev()
    {

        return view('dev');
    }

    public function floorView($locale, $projectsWord, $projectSlug, $floorId)
    {
        $floor = DevelopmentFloor::findOrFail($floorId);
        $countries = Country::all();
        return view('projects.cview.floor', ['floor' => $floor, 'projectSlug' => $projectSlug, 'countries' => $countries]);
    }

    public function projectIndex($locale, $projectSlug)
    {
        $countries = Country::all();
        return view('project.home', ['projectSlug' => $projectSlug, 'countries' => $countries]);
    }

    public function walkthrough($locale, $projectsWord, $projectSlug)
    {
        $development = Development::with(['floors'])->where(['slug' => $projectSlug])->firstOrFail();
        $floorsMap = $development->floors->mapWithKeys(function ($item) {
            return [$item->id => $item];
        })->toArray();
        $floorsArr = [7, 6, 5, 4, 3, 2, 1, 0];
        $countries = Country::all();
        return view('projects.' . $projectSlug . '.3dwalkthrough', compact('projectSlug', 'floorsMap', 'floorsArr', 'countries'));
    }

    public function project360View($locale, $projectsWord, $projectSlug)
    {
        $countries = Country::all();
        return view('projects.' . $projectSlug . '.360', compact('countries'));
    }

    public function thankyou()
    {
        $countries = Country::all();
        return view('project.thank-you', ['locale' => 'en', 'projectSlug' => 'cview', 'projectsWord' => __('projects'), 'countries' => $countries]);
    }

    public function cviewLanding() {
        $countries = Country::with(['translations' => function($query) {
            $query->where('language', 'ar');
        }])->get();

        $paginator = $this->propertiesService->searchLite(['ids' => [9379, 9380, 9706, 10135]], null, [], 100, app()->getLocale());
        $agents = User::whereIn('id', [3, 146, 124, 300, 299, 398])->get();
        $items = $paginator->items();
        $listings = [];
        
        foreach ($items as $item) {
            $listings[] = $this->propertiesService->mapSnapshotModelToListItem($item, 'list-item', app()->getLocale());
        }
        return view('projects.cview.landing', compact('listings', 'agents', 'countries'));
    }

    public function cviewLandingThankyou() {
        return view('projects.cview.landing-thankyou', ['projectsWord' => __('projects')]);
    }

    public function cviewLandingDownloadBrochure() {
        $pdfUrl = "https://www.fgrealty.qa/docs/Cview_Brochure.pdf";
        $pdfContent = @file_get_contents($pdfUrl);

        return response($pdfContent)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'attachment; filename="Cview_Brochure.pdf"');
    }

    public function marinaHeightsLandingPage() {
        $countries = Country::with(['translations' => function($query) {
            $query->where('language', 'ar');
        }])->get();

        $paginator = $this->propertiesService->searchLite(['projectId' => 24], null, [], 8, app()->getLocale());
        $agents = User::whereIn('id', [3, 146, 124, 300, 299, 398])->get();
        $items = $paginator->items();
        $listings = [];
        
        foreach ($items as $item) {
            $listings[] = $this->propertiesService->mapSnapshotModelToListItem($item, 'list-item', app()->getLocale());
        }
        return view('projects.marina-heights.landing', compact('listings', 'agents', 'countries'));
    }

    public function marinaHeightsDownloadBrochure() {
        $pdfUrl = "https://www.fgrealty.qa/docs/LMHBrochure.pdf";
        $pdfContent = @file_get_contents($pdfUrl);

        return response($pdfContent)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'attachment; filename="LMH_Brochure.pdf"');
    }
    
}