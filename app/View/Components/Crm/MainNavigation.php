<?php

namespace App\View\Components\Crm;

use App\Models\Crm\PermissionsDef;
use Illuminate\View\Component;
use Illuminate\Support\Str;

class MainNavigation extends Component
{
    public $menuGroups = [
        [
            'type' => 'link',
            'route' => 'dashboard.index',
            'label' => 'Overview',
            'icon' => 'house',
            'activeFor' => ['dashboard.index'],
        ],
        [
            'title' => 'Inventory',
            'links' => [
                [
                    'route' => 'inventory.index',
                    'label' => 'Inventory',
                    'icon' => 'box2',
                    'activeFor' => ['inventory.index', 'inventory.create', 'inventory.edit'],
                    'permissions' => [
                        PermissionsDef::INVENTORY_MASTER_READ,
                        PermissionsDef::INVENTORY_UPDATE,
                        PermissionsDef::INVENTORY_DELETE,
                        PermissionsDef::DASHBOARD,
                        PermissionsDef::LEADS_READ,
                        PermissionsDef::CRM_ADMIN,
                        PermissionsDef::SEO_MODULE,
                        PermissionsDef::INVENTORY_WAITING_FOR_PICTURES,
                    ]
                ],
                [
                    'route' => 'broker-landlords.index',
                    'label' => 'Landlords',
                    'icon' => 'person-bounding-box',
                    'activeFor' => ['broker-landlords.index', 'broker-landlords.create', 'broker-landlords.edit'],
                    'permissions' => [
                        PermissionsDef::INVENTORY_MASTER_READ,
                        PermissionsDef::INVENTORY_UPDATE,
                        PermissionsDef::INVENTORY_DELETE,
                    ]
                ],
            ],
        ],
        [
            'title' => 'Site Management',
            'links' => [
                [
                    'route' => 'magazine.index',
                    'label' => 'Site Content',
                    'icon' => 'book-half',
                    'activeFor' => [
                        'site-content',
                        'magazine.index',
                        'magazine.create',
                        'magazine.edit',
                        'developments.index',
                        'developments.create',
                        'developments.edit',
                        'geographies.index',
                        'geographies.create',
                        'geographies.edit',
                    ],
                    'permissions' => [
                        PermissionsDef::SITE_CONTENT_READ,
                        PermissionsDef::SITE_CONTENT_ADD,
                        PermissionsDef::CRM_ADMIN,
                    ]
                ],
                [
                    'route' => 'seo.index',
                    'label' => 'SEO',
                    'icon' => 'globe',
                    'activeFor' => ['seo.index'],
                    'linkHref' => "#/pages",
                    'permissions' => [
                        PermissionsDef::SEO_MODULE,
                        PermissionsDef::SEO_CONTENT_EDIT,
                        PermissionsDef::SEO_DYNAMIC_MENU_EDIT
                    ]
                ],
            ],
        ],
        [
            'title' => 'Marketing',
            'links' => [
                [
                    'route' => 'crm.projects.index',
                    'label' => 'Projects',
                    'icon' => 'folder-fill',
                    'activeFor' => ['crm.projects.index', 'crm.projects.create', 'crm.projects.edit'],
                    'permissions' => [
                        PermissionsDef::CRM_ADMIN,
                       // has role Marketing Manager
                    ]
                ],
            ]
        ],
        [
            'title' => 'CRM',
            'links' => [
                [
                    'route' => 'crm.leads.index',
                    'label' => 'Leads',
                    'icon' => 'flag',
                    'activeFor' => ['crm.leads.index', 'crm.leads.create', 'crm.leads.read', 'crm.leads.edit', 'crm.leads.delete'],
                    'permissions' => [
                        PermissionsDef::CRM_ADMIN,
                        PermissionsDef::LEADS_READ,
                        PermissionsDef::LEADS_CREATE,
                        PermissionsDef::LEADS_UPDATE,
                        PermissionsDef::LEADS_DELETE
                    ]
                ],
                [
                    'route' => 'crm.tasks.index',
                    'label' => 'Tasks',
                    'icon' => 'list-task',
                    'activeFor' => ['crm.tasks.index', 'crm.tasks.create', 'crm.tasks.read', 'crm.tasks.edit', 'crm.tasks.delete'],
                    'permissions' => [
                        PermissionsDef::TASKS,
                    ]
                ],
                [
                    'route' => 'crm.deals.index',
                    'label' => 'Deals',
                    'icon' => 'award',
                    'activeFor' => ['crm.deals.index', 'crm.deals.create', 'crm.deals.read', 'crm.deals.edit', 'crm.deals.delete'],
                    'permissions' => [
                        PermissionsDef::DEALS_READ,
                        PermissionsDef::DEALS_UPDATE,
                        PermissionsDef::DEALS_DELETE
                    ]
                ],
                [
                    'route' => 'crm.accountant.index',
                    'label' => 'Accountant',
                    'icon' => 'calculator',
                    'activeFor' => ['crm.accountant.index'],
                    'permissions' => [
                        PermissionsDef::CAN_ACCESS_ACCOUNTANT_SECTION,
                    ]
                ],
            ],
        ],
        [
            'title' => 'Admin',
            'links' => [
                [
                    'route' => 'admin.users.index',
                    'label' => 'Users',
                    'icon' => 'people',
                    'activeFor' => ['admin.users.index', 'admin.users.new', 'admin.users.edit'],
                    'permissions' => [PermissionsDef::ADMIN_USERS]
                ],
                [
                    'route' => 'crm.automatizations.index',
                    'label' => 'Automations',
                    'icon' => 'calendar',
                    'activeFor' => ['crm.automatizations.index'],
                    'permissions' => [
                        PermissionsDef::CRM_ADMIN,
                        PermissionsDef::CAN_PREVIEW_FEATURES,
                        PermissionsDef::TEAM_LEADER,
                    ]
                ],
                [
                    'route' => 'admin.marketing-platforms.index',
                    'label' => 'Marketing Platforms',
                    'icon' => 'megaphone',
                    'activeFor' => ['admin.marketing-platforms.index'],
                    'permissions' => [PermissionsDef::CRM_ADMIN]
                ],
                [
                    'route' => 'admin.lead-sources.index',
                    'label' => 'Lead Sources',
                    'icon' => 'geo-alt',
                    'activeFor' => ['admin.lead-sources.index', 'admin.lead-sources.create', 'admin.lead-sources.edit'],
                    'permissions' => [
                        PermissionsDef::CRM_ADMIN,
                    ]
                ],
                [
                    'route' => 'admin.companies.index',
                    'label' => 'Companies',
                    'icon' => 'building',
                    'activeFor' => ['admin.companies.index'],
                    'permissions' => [
                        PermissionsDef::CRM_ADMIN,
                    ]
                ],
            ],
        ],
        [
            'title' => 'Data',
            'links' => [
                [
                    'route' => 'crm.contacts-list.index',
                    'label' => 'Contacts',
                    'icon' => 'telephone',
                    'activeFor' => ['crm.contacts-list.index', 'crm.contacts-list.new', 'crm.contacts-list.edit'],
                    'permissions' => [PermissionsDef::SECTION_CONTACTS]
                ],
                [
                    'route' => 'crm.trainings.index',
                    'label' => 'Trainings',
                    'icon' => 'mortarboard',
                    'activeFor' => ['crm.trainings.index'],
                    'permissions' => [
                        PermissionsDef::TRAININGS,
                    ]
                ],
            ],
        ],
        [
            'title' => 'Short Term Rentals',
            'links' => [
                [
                    'route' => 'short-term-rentals.hotels.index',
                    'label' => 'Hotels',
                    'icon' => 'star',
                    'activeFor' => ['short-term-rentals.hotels.index', 'short-term-rentals.hotels.edit'],
                    'permissions' => [
                        PermissionsDef::CRM_ADMIN,
                        PermissionsDef::SHORT_STAY_ADMIN
                    ]
                ],
                [
                    'route' => 'short-stay.user-hotels',
                    'label' => 'Hotel configuration',
                    'icon' => 'building',
                    'activeFor' => ['short-stay.index', 'short-stay.user-hotels', 'inventory.create', 'inventory.edit', 'inventory.rooms.index'],
                    'permissions' => [
                        PermissionsDef::SHORT_STAY_USER,
                    ]
                ],
                [
                    'route' => 'bookings.index',
                    'label' => 'Bookings',
                    'icon' => 'calendar-event',
                    'activeFor' => ['bookings.index', 'bookings.details'],
                    'permissions' => [
                        PermissionsDef::SHORT_STAY_USER,
                    ]
                ],
            ]
        ]
    ];

    public $activeRoute;

    /**
     * Create a new component instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->activeRoute = request()->route()->getName();
        if (auth()->user()->email === '<EMAIL>' || auth()->user()->email === '<EMAIL>') {
            $this->menuGroups[] =   [
                'title' => 'Reports',
                'links' => [
                    [
                        'route' => 'admin.performance.index',
                        'label' => 'Performance',
                        'icon' => 'graph-up-arrow',
                        'activeFor' => ['admin.performance.index'],
                        'permissions' => [PermissionsDef::CRM_ADMIN]
                    ],
                ]
            ];
        }
        $this->menuGroups[] =   [
            'title' => 'Legal',
            'links' => [
                [
                    'route' => 'admin.policies.index',
                    'label' => 'Policies and Procedures',
                    'icon' => 'book',
                    'activeFor' => ['admin.policies.index'],
                    'permissions' => [PermissionsDef::SECTION_POLICIES_AND_PROCEDURES]
                ],
                [
                    'route' => 'admin.forms.index',
                    'label' => 'Forms',
                    'icon' => 'file',
                    'activeFor' => ['admin.forms.index'],
                    'permissions' => [PermissionsDef::SECTION_FORMS]
                ],
            ]
        ];
    }

    /**
     * Get the view / contents that represent the component.
     *
     * @return \Illuminate\Contracts\View\View|string
     */
    public function render()
    {
        return view('components.crm.main-navigation');
    }
}
