const centerTextPlugin = {
    id: 'centerText',
    beforeDraw(chart) {
        const { width, height, ctx } = chart;
        ctx.restore();

        const text = chart.config.options.plugins.centerText.text || '';
        const fontSize = chart.config.options.plugins.centerText.fontSize || '16px';
        const fontFamily = chart.config.options.plugins.centerText.fontFamily || 'Arial';
        const color = chart.config.options.plugins.centerText.color || '#000';

        ctx.font = `${fontSize} ${fontFamily}`;
        ctx.textBaseline = 'middle';
        ctx.textAlign = 'center';
        ctx.fillStyle = color;

        ctx.fillText(text, width / 2, height / 2);
        ctx.save();
    },
};

export default centerTextPlugin;