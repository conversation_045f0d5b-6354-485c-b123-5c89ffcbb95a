<?php

namespace App\Services;

use App\Models\Crm\Deal;
use App\Models\User;
use App\Services\EmailService;
use App\Services\SmsService;
use App\Services\NotesService;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class DealNotificationService
{
    protected $emailService;
    protected $smsService;
    protected $notesService;
    protected $operationHistoryService;

    public function __construct(
        EmailService $emailService,
        SmsService $smsService,
        NotesService $notesService,
        OperationHistoryService $operationHistoryService
    ) {
        $this->emailService = $emailService;
        $this->smsService = $smsService;
        $this->notesService = $notesService;
        $this->operationHistoryService = $operationHistoryService;
    }

    /**
     * Handle all notifications when a deal is approved
     *
     * @param Deal $deal
     * @return void
     */
    public function handleDealApprovalNotifications(Deal $deal)
    {
        try {
            // Check if the feature flag is enabled
            if (!env('FEATURE_REMINDER_FOR_GOOGLE_REVIEW', false)) {
                Log::info("Feature flag FEATURE_REMINDER_FOR_GOOGLE_REVIEW is disabled, skipping deal approval notifications");
                return;
            }

            // Get user IDs from environment variable
            $userIds = $this->getNotificationUserIds();

            if (empty($userIds)) {
                Log::warning("No notification user IDs configured for deal approval notifications");
                $this->operationHistoryService->addOperationHistory($deal, "[Deal] Approval notification skipped. No notification user IDs configured for deal approval notifications", auth()->user());
                return;
            }

            // Get users to notify
            $users = User::whereIn('id', $userIds)->get();

            if ($users->isEmpty()) {
                Log::warning("No valid users found for deal approval notifications", ['user_ids' => $userIds]);
                $this->operationHistoryService->addOperationHistory($deal, "[Deal] Approval notification skipped. No valid users found for deal approval notifications", auth()->user());
                return;
            }

            // Load deal relationships for email template
            $deal->load(['client', 'owner', 'property', 'author']);

            foreach ($users as $user) {
                // Send email notification
                $this->sendEmailNotification($deal, $user);
                
                // Send SMS notification
                $this->sendSmsNotification($deal, $user);
                
                // Create reminder for follow-up
                $this->createFollowUpReminder($deal, $user);
                
                $this->operationHistoryService->addOperationHistory($deal, "[Deal] Approval notification sent to " . $user->name, auth()->user());
            }

            Log::info("Deal approval notifications sent successfully", [
                'deal_id' => $deal->id,
                'notified_users' => $users->pluck('id')->toArray()
            ]);

        } catch (\Exception $e) {
            Log::error("Error sending deal approval notifications", [
                'deal_id' => $deal->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Send email notification to user
     *
     * @param Deal $deal
     * @param User $user
     * @return void
     */
    protected function sendEmailNotification(Deal $deal, User $user)
    {
        try {
            $this->emailService->sendDealApprovalNotification($deal, $user->email);
            Log::info("Deal approval email sent", [
                'deal_id' => $deal->id,
                'user_id' => $user->id,
                'email' => $user->email
            ]);
            $this->operationHistoryService->addOperationHistory($deal, "[Deal] Approval email sent to " . $user->name, auth()->user());
        } catch (\Exception $e) {
            Log::error("Failed to send deal approval email", [
                'deal_id' => $deal->id,
                'user_id' => $user->id,
                'email' => $user->email,
                'error' => $e->getMessage()
            ]);
            $this->operationHistoryService->addOperationHistory($deal, "[Deal] Approval email could not be sent to " . $user->name, auth()->user());
        }
    }

    /**
     * Send SMS notification to user
     *
     * @param Deal $deal
     * @param User $user
     * @return void
     */
    protected function sendSmsNotification(Deal $deal, User $user)
    {
        try {
            $phoneNumber = $user->getCompletePhoneNo();
            
            if (empty($phoneNumber)) {
                Log::warning("User has no phone number for SMS notification", [
                    'deal_id' => $deal->id,
                    'user_id' => $user->id
                ]);
                return;
            }

            $message = $this->buildSmsMessage($deal);
            Log::info("Semd SMS notification", [
                'deal_id' => $deal->id,
                'user_id' => $user->id,
                'phone' => $phoneNumber,
                'message' => $message
            ]);
            $result = $this->smsService->sendSms($phoneNumber, $message);
            
            if ($result['success'] ?? false) {
                Log::info("Deal approval SMS sent", [
                    'deal_id' => $deal->id,
                    'user_id' => $user->id,
                    'phone' => $phoneNumber
                ]);
                $this->operationHistoryService->addOperationHistory($deal, "[Deal] Approval SMS sent to " . $user->name, auth()->user());
            } else {
                Log::error("Failed to send deal approval SMS", [
                    'deal_id' => $deal->id,
                    'user_id' => $user->id,
                    'phone' => $phoneNumber,
                    'error' => $result['message'] ?? 'Unknown error'
                ]);
                $this->operationHistoryService->addOperationHistory($deal, "[Deal] Approval SMS could not be sent to " . $user->name, auth()->user());
            }
        } catch (\Exception $e) {
            Log::error("Exception while sending deal approval SMS", [
                'deal_id' => $deal->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            $this->operationHistoryService->addOperationHistory($deal, "[Deal] Approval SMS could not be sent to " . $user->name." due to exception. ", auth()->user());
        }
    }

    /**
     * Create follow-up reminder for user
     *
     * @param Deal $deal
     * @param User $user
     * @return void
     */
    protected function createFollowUpReminder(Deal $deal, User $user)
    {
        try {
            // Set reminder for 1 day from now
            $reminderDate = Carbon::now()->addDays(3);

            // Create reminder directly since we need to set specific user
            $newReminder = new \App\Models\Crm\Reminder();
            $newReminder->title = 'Follow-up: Contact client for review - Deal ' . $deal->getRefNo();
            $newReminder->text = 'Please contact the client to ask for a review for the approved deal ' . $deal->getRefNo() . '. Client: ' . ($deal->client->name ?? 'N/A');
            $newReminder->due_date = $reminderDate;
            $newReminder->priority = 'HIGH';
            $newReminder->send_email = true;
            $newReminder->send_email_date = $reminderDate;
            $newReminder->email = $user->email;
            $newReminder->note_id = '0';
            $newReminder->object_type = $deal->getTable();
            $newReminder->object_id = $deal->id;
            $newReminder->created_by = $user->id;
            $newReminder->save();

            Log::info("Deal approval follow-up reminder created", [
                'deal_id' => $deal->id,
                'user_id' => $user->id,
                'reminder_date' => $reminderDate->toDateTimeString()
            ]);
            $this->operationHistoryService->addOperationHistory($deal, "[Deal] Approval reminder created for " . $user->name, auth()->user());

        } catch (\Exception $e) {
            Log::error("Failed to create deal approval follow-up reminder", [
                'deal_id' => $deal->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
            $this->operationHistoryService->addOperationHistory($deal, "[Deal] Approval reminder could not be created for " . $user->name, auth()->user());
        }
    }

    /**
     * Send SMS notification about the reminder
     *
     * @param Deal $deal
     * @param User $user
     * @return void
     */
    protected function sendReminderSmsNotification(Deal $deal, User $user)
    {
        try {
            $phoneNumber = $user->getCompletePhoneNo();
            
            if (empty($phoneNumber)) {
                return;
            }

            $message = "Reminder set: Please contact client for review regarding approved deal " . $deal->getRefNo() . ". Follow-up scheduled for tomorrow.";
            $result = $this->smsService->sendSms($phoneNumber, $message);
            
            if ($result['success'] ?? false) {
                Log::info("Deal approval reminder SMS sent", [
                    'deal_id' => $deal->id,
                    'user_id' => $user->id,
                    'phone' => $phoneNumber
                ]);
            }
        } catch (\Exception $e) {
            Log::error("Exception while sending deal approval reminder SMS", [
                'deal_id' => $deal->id,
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Build SMS message for deal approval notification
     *
     * @param Deal $deal
     * @return string
     */
    protected function buildSmsMessage(Deal $deal)
    {
        $clientName = $deal->client->name ?? 'N/A';
        
        return "Deal {$deal->getRefNo()} approved! Please contact client {$clientName} to request a review.";
    }

    /**
     * Get notification user IDs from environment variable
     *
     * @return array
     */
    protected function getNotificationUserIds()
    {
        $userIdsString = env('DEAL_APPROVAL_NOTIFICATION_USER_IDS', '');
        
        if (empty($userIdsString)) {
            return [];
        }

        return array_filter(array_map('intval', explode(',', $userIdsString)));
    }
}
