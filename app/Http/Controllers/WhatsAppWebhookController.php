<?php

// app/Http/Controllers/WhatsAppWebhookController.php
namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\WhatsappWebhookLog;
use App\Services\WhatsAppMessageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class WhatsAppWebhookController extends Controller
{
    protected $messageService;

    public function __construct(WhatsAppMessageService $messageService)
    {
        $this->messageService = $messageService;
    }

    /**
     * Handle webhook verification (GET request)
     */
    public function verify(Request $request)
    {
        $verifyToken = config('services.whatsapp.verify_token');
        $mode = $request->query('hub_mode');
        $token = $request->query('hub_verify_token');
        $challenge = $request->query('hub_challenge');

        if ($mode === 'subscribe' && $token === $verifyToken) {
            Log::info('WhatsApp webhook verified successfully');
            return response($challenge, 200);
        }

        Log::warning('[WhatsAppWebhookController] WhatsApp webhook verification failed', [
            'mode' => $mode,
            'token' => $token
        ]);

        return response('Forbidden', 403);
    }

    /**
     * Handle incoming webhook messages (POST request)
     */
    public function handle(Request $request)
    {
        try {
            $payload = $request->all();

            Log::info('[WhatsAppWebhookController] WhatsApp webhook received', ['payload' => $payload]);
            
            // Log the webhook request
            $log = WhatsappWebhookLog::create([
                'source' => 'whatsapp',
                'payload' => $payload,
                'status' => 'processing'
            ]);

            // Verify webhook signature
            // if (!$this->verifySignature($request)) {
            //     Log::warning('[WhatsAppWebhookController] Invalid WhatsApp webhook signature');
            //     return response('Unauthorized', 401);
            // }

            // Process the webhook
            $this->processWebhook($payload);

            // Update webhook log status to success
            $log->status = "success";
            $log->save();
            // WhatsappWebhookLog::where('payload', $payload)
            //     ->where('status', 'processing')
            //     ->update(['status' => 'success']);

            return response('OK', 200);

        } catch (\Exception $e) {
            Log::error('[WhatsAppWebhookController] WhatsApp webhook processing failed', [
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            WhatsappWebhookLog::create([
                'source' => 'whatsapp',
                'payload' => $request->all(),
                'status' => 'error',
                'error_message' => $e->getMessage()
            ]);

            return response('Internal Server Error', 500);
        }
    }

    /**
     * Process the webhook payload
     */
    protected function processWebhook(array $payload)
    {
        // Validate payload structure
        if (!isset($payload['object']) || $payload['object'] !== 'whatsapp_business_account') {
            Log::warning('[WhatsAppWebhookController] Invalid webhook object type', [
                'object' => $payload['object'] ?? 'missing'
            ]);
            return;
        }

        if (!isset($payload['entry']) || !is_array($payload['entry'])) {
            Log::warning('[WhatsAppWebhookController] Missing or invalid entry array');
            return;
        }

        foreach ($payload['entry'] as $entry) {
            if (!isset($entry['changes']) || !is_array($entry['changes'])) {
                continue;
            }

            foreach ($entry['changes'] as $change) {
                if (!isset($change['field']) || $change['field'] !== 'messages') {
                    continue;
                }

                if (!isset($change['value'])) {
                    Log::warning('[WhatsAppWebhookController] Missing value in change', [
                        'change' => $change
                    ]);
                    continue;
                }

                $this->processMessages($change['value'], $entry['id'] ?? null);
            }
        }
    }

    /**
     * Process individual messages
     */
    protected function processMessages(array $messageData, ?string $entryId = null)
    {
        // Validate messaging product
        if (!isset($messageData['messaging_product']) || $messageData['messaging_product'] !== 'whatsapp') {
            Log::warning('[WhatsAppWebhookController] Invalid messaging product', [
                'messaging_product' => $messageData['messaging_product'] ?? 'missing'
            ]);
            return;
        }

        // Process incoming messages
        if (isset($messageData['messages']) && is_array($messageData['messages'])) {
            foreach ($messageData['messages'] as $message) {
                $this->messageService->processMessage($message, $messageData, $entryId);
            }
        }

        // Process message status updates (delivery receipts, read receipts, etc.)
        if (isset($messageData['statuses']) && is_array($messageData['statuses'])) {
            foreach ($messageData['statuses'] as $status) {
                $this->messageService->processMessageStatus($status, $messageData, $entryId);
            }
        }

        // Log if no messages or statuses found
        if (!isset($messageData['messages']) && !isset($messageData['statuses'])) {
            Log::info('[WhatsAppWebhookController] No messages or statuses in webhook', [
                'messageData' => $messageData
            ]);
        }
    }

    /**
     * Verify webhook signature
     */
    protected function verifySignature(Request $request): bool
    {
        $signature = $request->header('X-Hub-Signature-256');
        $payload = $request->getContent();
        $appSecret = config('services.whatsapp.app_secret');

        if (!$signature || !$appSecret) {
            return false;
        }

        $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $appSecret);
        
        return hash_equals($expectedSignature, $signature);
    }
}
