<template>
    <div class="chart-container">
        <canvas ref="chartRef" width="200" height="100"></canvas>
    </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue';
import { tasksChartConstants } from './chart-data-config';
import {
    Chart,
    BarController,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend
} from 'chart.js';

// Register the required Chart.js components
Chart.register(
    BarController,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend
);

// Props definition
const props = defineProps({
    chartData: {
        type: Object,
        default: () => ({
            labels: tasksChartConstants.labels,
            datasets: [
                {
                    label: 'Tasks',
                    backgroundColor: tasksChartConstants.colors,
                    borderColor: tasksChartConstants.colors,
                    borderWidth: 1,
                    data: [0, 0, 0, 0, 0, 0]
                }
            ]
        })
    }
});

// Template ref for the canvas element
const chartRef = ref(null);
// Store chart instance to be able to destroy it later
let chartInstance = null;

// Make the chart instance accessible via template ref
defineExpose({
    getChart: () => chartInstance
});

// Function to create or update chart
const createChart = () => {
    // Destroy existing chart if it exists
    if (chartInstance) {
        chartInstance.destroy();
    }

    // Create new chart
    if (chartRef.value) {
        const ctx = chartRef.value.getContext('2d');
        chartInstance = new Chart(ctx, {
            type: 'bar',
            data: props.chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: false,
                        text: 'Lead Metrics Comparison'
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    },
                    legend: {
                        display: false,
                        position: 'top',
                    },
                },
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: 'Tasks.'
                        },
                    },
                    x: {
                        ticks: {
                            display: false
                        }
                    }
                }
            }
        });
    }
};

// Lifecycle hooks
onMounted(() => {
    createChart();
});

onBeforeUnmount(() => {
    if (chartInstance) {
        chartInstance.destroy();
    }
});

// Watch for data changes to update chart
watch(
    () => props.chartData,
    () => {
        createChart();
    },
    { deep: true }
);
</script>

<style scoped>
.chart-container {
    position: relative;
    margin: auto;
    height: 140px;
    width: 230px;
}
</style>