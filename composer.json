{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "alymosul/exponent-server-sdk-php": "^1.3", "artesaos/seotools": "^0.20.1", "barryvdh/laravel-dompdf": "^2.2", "cocur/slugify": "^4.0", "cogpowered/finediff": "^0.3.1", "ctwillie/expo-server-sdk-php": "^2.0", "darkaonline/l5-swagger": "^8.3", "fideloper/proxy": "^4.4", "fruitcake/laravel-cors": "^2.0", "grimzy/laravel-mysql-spatial": "^5.0", "guzzlehttp/guzzle": "^7.0.1", "intervention/image": "^2.5.1", "intervention/imagecache": "^2.5", "jorenvanhocht/laravel-share": "^4.1", "laravel/framework": "^8.12", "laravel/tinker": "^2.5", "laravel/ui": "^3.4", "maatwebsite/excel": "^3.1", "mailjet/laravel-mailjet": "^3.0", "mews/purifier": "^3.3", "mobiledetect/mobiledetectlib": "^2.8", "openai-php/client": "^0.4.1", "php-curl-class/php-curl-class": "^9.13", "phpoffice/phpspreadsheet": "^1.23", "spatie/laravel-cookie-consent": "^2.12", "spatie/laravel-medialibrary": "^8.0", "spatie/laravel-permission": "^5.5", "spatie/schema-org": "^3.3", "twilio/sdk": "^8.5", "watson/sitemap": "^4.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.5", "facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "kitloong/laravel-migrations-generator": "^5.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.3.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/form.php", "app/Helpers/template.php", "app/Helpers/util.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}