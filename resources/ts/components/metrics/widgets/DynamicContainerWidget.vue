<template>
  <article class="dynamicContainerWidget">
    <section class="dynamicContainerWidget__first">
      <div>{{ props.userWidgetConfig?.widget?.data?.name }}</div>
      <component :is="ComponentsMap[widget.data?.config?.chartType]" :widget="widget" :userWidgetId="widget.data.id"
        :filters="filtersWithDateRange" source="userWidgets" variant=""/>
    </section>
    <section class="dynamicContainerWidget__second">
      <div class="barContainerWidget__controls">
                <div :class="[
                    'barContainerWidget__dateFilter',
                    selectedFiltersRef === 'custom' ? '' : 'd-none']">
                    <label class="text-gray-50 small mb-0">Date Range</label>
                    <div class="input-group input-group-sm">
                        <input type="text" class="form-control form-control-sm" id="dateRangeInput">
                        <button @click="clearDaterange('dateRangeInput')" class="btn btn-xs btn-outline-secondary">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                </div>
                <div :class="[
                    'barContainerWidget__dateFilter',
                    selectedFiltersRef === 'compare' ? '' : 'd-none']">
                    <label class="text-gray-50 small mb-0">First Period</label>
                    <div class="input-group input-group-sm">
                        <input type="text" class="form-control form-control-sm" id="dateRangeFirstPeriod">
                        <button @click="clearDaterange('dateRangeFirstPeriod')" class="btn btn-xs btn-outline-secondary">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                </div>
                <div :class="[
                    'barContainerWidget__dateFilter',
                    selectedFiltersRef === 'compare' ? '' : 'd-none']">
                    <label class="text-gray-50 small mb-0">Second Period</label>
                    <div class="input-group input-group-sm">
                        <input type="text" class="form-control form-control-sm" id="dateRangeSecondPeriod">
                        <button @click="clearDaterange('dateRangeSecondPeriod')" class="btn btn-xs btn-outline-secondary">
                            <i class="bi bi-x-lg"></i>
                        </button>
                    </div>
                </div>
                <div>
                    <label class="text-gray-50 small mb-0">Filter</label>
                    <select class="form-select form-select-sm w-auto text-muted" v-model="selectedFiltersRef">
                      <option v-for="filter in userWidgetConfig?.widget.data?.config?.filters ?? []" :key="filter.value"
                        :value="filter.value">{{
                          filter.tag }}
                      </option>
                    </select>
                </div>
            </div>
    </section>
  </article>
</template>

<script setup lang="ts">
import { defineProps, ref, markRaw, reactive, computed, onMounted } from "vue"
import NumberWidget from "./NumberWidget.vue";
import LineWidget from "./LineWidget.vue";
import BarWidget from "./BarWidget.vue";
import PieWidget from "./PieWidget.vue";
import TreemapWidget from "./TreemapWidget.vue";

const props = defineProps({
  userWidgetConfig: Object,
  source: String
});

/**
 * Aici trebuie sa avem tipuri generice, gen pie, line, bar, treemap, etc.
 * cheile vor fi valorile din BEWidgetConfig.chartType - specificate in DB
 */
const ComponentsMap = markRaw({
  pie: PieWidget,
  line: LineWidget,
  treemap: TreemapWidget,
  bar: BarWidget,
  number: NumberWidget,
  // total_sales_volume: LineWidget,
  // number_of_closed_deals: LineWidget,
  // lead_to_client_conversion: NumberWidget,
  // leads_per_sources_pie: PieWidget,
  // deals_per_agent: PieWidget,
  // pipeline_value: NumberWidget,
  // calls_meetings_property_viewings: BarWidget,
  // average_commission_per_agent: BarWidget,
  // lead_response_time: NumberWidget,
  // exclusive_nonexclusive_listings: NumberWidget,
  // leads_per_sources_bar: BarWidget,
  // leads_per_sources_per_utm_source: PieWidget,
  // l_p_s_p_u_s_treemap: TreemapWidget,
});

declare global {
  interface Window {
    $: any;
  }
}

const widget = props.userWidgetConfig?.widget;
let selectedFilters = '';

if (props.userWidgetConfig?.filters?.onRenderFilters) {
  selectedFilters = props.userWidgetConfig?.filters.onRenderFilters.value;
} else if (props.userWidgetConfig?.widget?.data?.config?.defaultFilterValue) {
  selectedFilters = props.userWidgetConfig?.widget?.data?.config?.defaultFilterValue;
} else {
  selectedFilters = props.userWidgetConfig?.widget?.data?.config?.filters?.[0]?.value;
}
const dateRange = reactive({
    dateFrom: "",
    dateTo: ""
});

const dateRange2 = reactive({
    dateFrom: "",
    dateTo: ""
});

const dateRange3 = reactive({
    dateFrom: "",
    dateTo: ""
});

const inputs = [
      { id: 'dateRangeInput', binding: dateRange },
      { id: 'dateRangeFirstPeriod', binding: dateRange2 },
      { id: 'dateRangeSecondPeriod', binding: dateRange3 }
    ];
  
// Date range map for input IDs
const dateRangeMap = {
  'dateRangeInput': {
    binding: dateRange,
    from: 'dateFrom',
    to: 'dateTo'
  },
  'dateRangeFirstPeriod': {
    binding: dateRange2,
    from: 'dateFrom',
    to: 'dateTo'
  },
  'dateRangeSecondPeriod': {
    binding: dateRange3,
    from: 'dateFrom',
    to: 'dateTo'
  }
};

const clearDaterange = (daterangeId: string) => {
  const input = document.getElementById(daterangeId) as HTMLInputElement | null;
  input.value = '';

  const map = dateRangeMap[daterangeId];
  if (input && map) {
    map.binding[map.from] = '';
    map.binding[map.to] = '';
  } else {
    console.warn(`No mapping found for daterangeId: ${daterangeId}`);
  }
};

const filtersWithDateRange = computed(() => {
    if (dateRange.dateFrom && dateRange.dateTo) {
        return `${selectedFiltersRef.value}/${dateRange.dateFrom}/${dateRange.dateTo}`;
    }
    const from1 = dateRange2.dateFrom;
    const to1 = dateRange2.dateTo;
    const from2 = dateRange3.dateFrom;
    const to2 = dateRange3.dateTo;
    if (from1 && to1 || from2 && to2) {
      return `${selectedFiltersRef.value}/${from1}/${to1}/${from2}/${to2}`;
    }
    return selectedFiltersRef.value;
});

const selectedFiltersRef = ref<string>(selectedFilters)

onMounted(() => {
  setTimeout(() => {

    inputs.forEach(({ id, binding }) => {
      try {
        const el = document.getElementById(id) as HTMLInputElement | null
        if (
          el &&
          typeof window.$ !== 'undefined' &&
          typeof window.$.fn.daterangepicker !== 'undefined'
        ) {
          window.$(el).daterangepicker(
            {
              opens: 'left',
              autoApply: true
            },
            function (start, end) {
              binding.dateFrom = start.format('YYYY-MM-DD')
              binding.dateTo = end.format('YYYY-MM-DD')
            }
          )

          window.$(el).on('apply.daterangepicker', function (ev, picker) {
            binding.dateFrom = picker.startDate.format('YYYY-MM-DD')
            binding.dateTo = picker.endDate.format('YYYY-MM-DD')
            el.value = `${binding.dateFrom} - ${binding.dateTo}`
          })

          el.value = '' // Clear the input initially
        } else {
          console.error(`jQuery or daterangepicker not available for ${id}`)
        }
      } catch (err) {
        console.error(`Error initializing daterangepicker for ${id}:`, err)
      }
    })
  }, 1000)
})

</script>
<style lang="scss">
.dynamicContainerWidget {
  display: flex;
  justify-content: space-between;
  height: 100%;

  &__first {
    flex: 1;
  }
}
</style>
