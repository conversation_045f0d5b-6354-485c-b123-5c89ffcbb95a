<!DOCTYPE html>
<html lang="en">
@php
$isProductionEnvironment = env('APP_ENV') == 'production';
$metaTitle = __("CView Residence: FGRealty. Luxurious Waterfront Living: Lusail Marina");
$metaDescription = __('Discover modern luxury: CView Residence in Lusail Marina. Enjoy panoramic Arabian Gulf views, elegant interiors, world-class amenities. Exceptional ROI potential.');
@endphp

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $metaTitle}}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <meta name="description" content="{{$metaDescription}}">

    <meta property="og:url" content="https://www.fgrealty.qa/en/projects/cview">
    <meta property="og:type" content="website">
    <meta property="og:title" content="{{$metaTitle}}">
    <meta property="og:description" content="{{$metaDescription}}">
    <meta property="og:image" content="{{asset('/images/projects/cview/CView_OG.jpg')}}">

    <meta name="twitter:card" content="summary_large_image">
    <meta property="twitter:domain" content="fgrealty.qa">
    <meta property="twitter:url" content="https://www.fgrealty.qa/en/projects/cview">
    <meta name="twitter:title" content="{{$metaTitle}}">
    <meta name="twitter:description" content="{{$metaDescription}}">
    <meta name="twitter:image" content="{{asset('/images/projects/cview/CView_OG.jpg')}}">

    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .modal {
            font-family: 'Arial', sans-serif;
            display: none;
            position: fixed;
            z-index: 1;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            justify-content: center;
            align-items: center;
            display: none;
            overflow: hidden;
            background-color: rgba(0, 0, 0, 0.4);
            z-index: 10000;
        }

        .modal-content {
            margin: 15% auto;
            padding: 20px;
            border: 1px solid #888;
            border-radius: 8px;
            width: 80%;
            max-width: 500px;
            background-color: #f9f9f9;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1)
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
        }

        .close:hover,
        .close:focus {
            color: black;
            text-decoration: none;
            cursor: pointer;
        }

        form {
            display: flex;
            flex-direction: column;
            gap: 20px;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px 0;
            /* background-color: #f9f9f9; */
            border-radius: 8px;
            /* box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); */
        }

        input,
        select,
        textarea,
        button {
            width: 100%;
            padding: 15px;
            font-size: 16px;
            border: 1px solid #ccc;
            border-radius: 8px;
            margin: 0;
            box-sizing: border-box;
            background-color: #fff;
            transition: border-color 0.3s ease, box-shadow 0.3s ease;
        }

        input:focus,
        select:focus,
        textarea:focus,
        button:focus {
            border-color: #007bff;
            box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
            outline: none;
        }

        input[type="text"],
        input[type="tel"],
        input[type="email"] {
            font-family: 'Arial', sans-serif;
        }

        textarea {
            min-height: 120px;
            resize: vertical;
            font-family: 'Arial', sans-serif;
        }

        .submitbutton {
            background-color: black;
            color: white;
            cursor: pointer;
            border: none;
            font-weight: bold;
            transition: background-color 0.3s ease;
        }

        .submitbutton:hover {
            background-color: gray;
        }

        @media screen and (min-width: 760px) {
            form {
                padding: 20px;
            }
        }

        @media screen and (min-width: 890px) {
            .mobile-only {
                display: none;
            }
        }
    </style>
    <link rel="icon" type="image/png" href="/favicon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link href="{{ mix('css/cview-layout.css') }}" rel="stylesheet">
    <link rel="preconnect" href="https://www.googletagmanager.com">
    @if($isProductionEnvironment)
    <script>
        window.console.log = function() {};
        window.console.error = function() {};
        (function(w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-WPD3N3NC');
    </script>
    @endif
</head>

<body>
    <nav class="navbar" style="top: 0">
        <div class="logo">
        <a href="{{persistQueryParamsToRoute('project.cview.landing', ['projectsWord' => __('projects')])}}"><img src="{{ asset('images/svg/CView_Logo.svg') }}" alt="CView"></a>
            <div class="logo__separator"></div>
            <a href="{{persistQueryParamsToRoute('home.localized', ['locale' => 'en'])}}"><img src="{{ asset('images/svg/FGREALTY_CView_Logo.svg') }}" alt="CView"></a>
        </div>

        <div class="hamburger" id="hamburger">
            <img src="{{ asset('images/svg/hamburger.svg') }}" alt="CView">
        </div>

        <ul class="nav-links" id="nav-menu">
            <li class="nav-links__closeCta"><a id="navLinksCloseCta" href="#">&times;</a></li>
            <li class="mobile-only"><a href="{{ persistQueryParamsToRoute('project.cview.landing', ['projectsWord' => __('projects')]) }}">Home</a></li>
            <li @if(request()->route()->getName() == 'project.index') class="active" @endif><a href="{{ persistQueryParamsToRoute('project.index', ['locale' => $locale, 'projectsWord' => __('projects'), 'projectSlug' => 'cview']) }}">Image renders</a></li>
            <li @if(request()->route()->getName() == 'project.3dwalkthrough') class="active" @endif><a href="{{ persistQueryParamsToRoute('project.3dwalkthrough', ['locale' => $locale, 'projectsWord' => __('projects'), 'projectSlug' => 'cview']) }}">3D walkthrough</a></li>
            <li @if(request()->route()->getName() == 'project.360') class="active" @endif><a href="{{ persistQueryParamsToRoute('project.360', ['locale' => $locale, 'projectsWord' => __('projects'), 'projectSlug' => 'cview']) }}">360 rotate</a></li>
            <li><a href="javascript:bookGeneralViewing()" class="book-now">Book now</a></li>
        </ul>
    </nav>

    <main class="py-4" style="height: 100vh">
        @yield('content')
        <div class="overlay"></div>
    </main>

    <div id="bookingModal" class="modal">
        <div class="modal-content">
            <span class="close" id="closeModalBtn">&times;</span>
            <h2 id="modalTitle">Book now</h2>
            <p>Fill the following form to make booking.</p>
            <form action="{{persistQueryParamsToRoute('webhooks.enquire')}}" method="POST">
                @csrf
                <input type="text" id="name" name="Name" required placeholder="Name*">
                <input type="tel" name="Phone" id="phone" required placeholder="Phone*" pattern="[0-9()#&amp;+*-=.]+" title="Only numbers and phone characters (#, -, *, etc) are accepted.">
                <input type="email" id="email" name="E_mail" required placeholder="Email*">
                <select id="country" name="Country" required>
                    <option selected disabled value="">Select Your Country*</option>
                    @foreach ($countries as $country)
                    <option value="{{$country->id}}">{{$country->name}}</option>
                    @endforeach
                </select>
                <input type="hidden" name="UnitType" value="1">
                <input type="hidden" name="form_name" value="CView_Booking_Form">
                <input type="hidden" name="network" value="website">
                <input type="hidden" name="gad_source" value="">
                <input type="hidden" name="fbclid" value="">
                <input type="hidden" name="form_id" value="CView_Booking_Form">
                <input type="hidden" name="redirect_to" value="{{persistQueryParamsToRoute('project.thankyou', ['projectSlug' => 'cview', 'locale' => $locale, 'projectsWord' => __('projects')])}}">
                <textarea name="Message" id="message" rows="4" required>Hi, I'm interested one of the CView project apartment</textarea>
                <input class="submitbutton" type="submit" value="Book" style="cursor: pointer;">
            </form>
        </div>
    </div>

    <script>
        // Get the modal and buttons
        var modal = document.getElementById("bookingModal");
        var closeModalBtn = document.getElementById("closeModalBtn");
        var unitIdInput = document.getElementById("unitId");
        var modalTitle = document.getElementById("modalTitle");
        const hamburger = document.getElementById("hamburger");
        const navLinksCloseCta = document.getElementById("navLinksCloseCta");
        const navMenu = document.getElementById("nav-menu");

        hamburger.addEventListener("click", () => {
            navMenu.classList.toggle("show");
        });
        navLinksCloseCta.addEventListener("click", () => {
            navMenu.classList.toggle("show");
        });

        document.addEventListener("click", (e) => {
            if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
                navMenu.classList.remove("show");
            }
        });

        // When the user clicks the button, open the modal
        function openModal(unitId, unitNo) {
            // unitIdInput.value = unitId;
            modal.style.display = "flex";
            modalTitle.innerText = 'Book now Apartment #' + unitNo;
        }

        function bookGeneralViewing() {
            modal.style.display = "flex";
            modalTitle.innerText = 'Book a viewing';
            navMenu.classList.toggle("hide");
        }

        // When the user clicks on <span> (x), close the modal
        closeModalBtn.onclick = function() {
            modal.style.display = "none";
        }

        // When the user clicks anywhere outside of the modal, close it
        window.onclick = function(event) {
            if (event.target === modal) {
                modal.style.display = "none";
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            var bookingForm = document.querySelector('#bookingModal form');
            if (bookingForm) {
                bookingForm.addEventListener('submit', function(e) {
                    var submitBtn = bookingForm.querySelector('.submitbutton');
                    if (submitBtn) {
                        submitBtn.disabled = true;
                        submitBtn.value = 'Sending...';
                    }
                });
            }
        });
    </script>

</body>

</html>