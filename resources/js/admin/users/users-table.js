let usersTable;
let selectedRows = new Set(); // Store selected row IDs
window.usersSearchParams = {};
let filtersInitialized = false;

$(document).ready(function () {
    let currentRequest = null;
    const addCustomFiltersAndSearch = (sSource, aoData, fnCallback) => {
        if (filtersInitialized) {
            const urlStr = parseDTParams(aoData);

            Object.keys(window.usersSearchParams).forEach((k) => {
                urlStr.push(`${k}=${window.usersSearchParams[k]}`);
            });

            const data = urlStr.join("&");
            if (currentRequest && currentRequest.readyState !== 4) {
                currentRequest.abort();
            }
            currentRequest = $.ajax({
                dataType: "json",
                url: "/admin/users",
                data,
                success: (input) => {
                    fnCallback(input);
                    feather.replace();
                    setTimeout(() => {
                        $('.dropdown-toggle').dropdown();
                        $('[data-bs-toggle="tooltip"]').tooltip();
                    }, 300);
                },
            });
        } else {
            // Initial load without filters
            const urlStr = parseDTParams(aoData);
            const data = urlStr.join("&");
            $.ajax({
                dataType: "json",
                url: "/admin/users",
                data,
                success: (input) => {
                    fnCallback(input);
                    feather.replace();
                    setTimeout(() => {
                        $('.dropdown-toggle').dropdown();
                        $('[data-bs-toggle="tooltip"]').tooltip();
                    }, 300);
                },
            });
        }
    };

    const colDefs = [
        {
            name: "selection",
            orderable: false,
            data: null,
            render(data, type, row) {
                // Only show checkbox if user has CRM_ADMIN permission
                if (typeof window.userCanManageUsers !== 'undefined' && window.userCanManageUsers) {
                    return `<input type="checkbox" class="row-checkbox" data-id="${row.id}">`;
                }
                return '';
            },
        },
        {
            data: "profile_image_url",
            render(profile_image_url, _, row) {
                return profile_image_url ? `<img alt="" class="img-thumbnail" style="max-width: 60px;" src="${profile_image_url}">` : ''
            }
        },
        {data: "name"},
        {data: "rating", render(data) { return data === undefined ? 'N/a' : data;}},
        {data: "email"},
        {data: "position"},
        {data: "brokerage_license_account"},
        {data: "roles"},
        {data: "public_profile", render(is_public_profile) { return is_public_profile ? 'Yes' : 'No' }},
        {
            data: "id",
            render(id, _, row) {
                const deleteButton = row.canDelete
                    ? `<a class="dropdown-item" href="#deleteModal" onclick="triggerDeleteModal('${id}')"> <span data-feather="x"></span> Delete</a>`
                    : ``;

                return `
                    <div class="dropdown">
                        <button class="btn btn-sm btn-light dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="dropdownMenuButton${id}">
                            Actions
                        </button>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton${id}">
                            <a class="dropdown-item" href="/admin/users/${id}/edit"> <span data-feather="edit-2"></span> Edit</a>
                            ${deleteButton}
                        </div>
                    </div> `
            },
            orderable: false,
            responsivePriority: 1
        },
    ];
    usersTable = $("#usersTable").DataTable({
        serverSide: true,
        lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]],
        responsive: true,
        scrollY: 'calc(100vh - 350px)',
        oLanguage: {
            sProcessing: `
          <div class="spinner-border m-5" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
      `
        },
        processing: true,
        searching: true,
        order: [[(typeof window.userCanManageUsers !== 'undefined' && window.userCanManageUsers) ? 2 : 1, "asc"]],
        columns: colDefs,
        // orderCellsTop: true,
        fnServerData: addCustomFiltersAndSearch,
        drawCallback: function () {
            selectedRows.clear(); // Clear selection on page change
            updateSelectionCount();
            updateSelectAllCheckbox();
        }
    });

    // Function to handle filter changes
    window.onUsersFiltersParamsChange = function(filterParams) {
        window.usersSearchParams = filterParams;
        if (!filtersInitialized) {
            filtersInitialized = true;
        }
        usersTable.draw();
    };

    // Initialize filters after table is ready
    setTimeout(() => {
        filtersInitialized = true;
    }, 1000);

    // Selection functionality
    function updateSelectionCount() {
        let selectedCount = selectedRows.size;
        $('#selectedCount').text(selectedCount);
        let hasSelection = selectedCount > 0;
        $('#bulkAction').prop('disabled', !hasSelection);
        $('#applyAction').prop('disabled', !hasSelection);
    }

    // Handle 'Select All' checkbox
    $('#selectAll').on('click', function () {
        let isChecked = this.checked;
        $('.row-checkbox').each(function () {
            let rowId = $(this).data('id');
            if (isChecked) {
                selectedRows.add(rowId);
            } else {
                selectedRows.delete(rowId);
            }
            $(this).prop('checked', isChecked);
        });
        updateSelectionCount();
    });

    // Handle individual row checkbox
    $(document).on('change', '.row-checkbox', function () {
        let rowId = $(this).data('id');
        if ($(this).is(':checked')) {
            selectedRows.add(rowId);
        } else {
            selectedRows.delete(rowId);
        }
        updateSelectionCount();
        updateSelectAllCheckbox();
    });

    // Function to update the "Select All" checkbox
    function updateSelectAllCheckbox() {
        let totalCheckboxes = $('.row-checkbox').length;
        let checkedCheckboxes = $('.row-checkbox:checked').length;
        $('#selectAll').prop('checked', totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes);
    }

    // Apply Bulk Action
    $('#applyAction').on('click', function () {
        let selectedIds = Array.from(selectedRows);
        let action = $('#bulkAction').val();
        if (!action) return alert("Please select an action!");

        if (action === "reset_password") {
            triggerResetPasswordModal(selectedIds);
        }
    });

    // Reset Password Modal
    function triggerResetPasswordModal(selectedIds) {
        $('#selectedUsersCount').text(selectedIds.length);
        $('#resetPasswordModal').modal('show');

        // Store selected IDs for confirmation
        window.selectedUserIds = selectedIds;
    }

    // Confirm Reset Password
    $('#confirmResetPassword').on('click', function () {
        let selectedIds = window.selectedUserIds;
        if (!selectedIds || selectedIds.length === 0) return;

        // Show loading state
        $(this).prop('disabled', true).text('Processing...');

        // Make API call to reset passwords
        $.ajax({
            url: '/admin/users/bulk-reset-password',
            method: 'POST',
            data: {
                userIds: selectedIds,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                $('#resetPasswordModal').modal('hide');
                if (response.success) {
                    alert(`Passwords have been reset for ${response.count} user(s). Emails have been sent with new passwords.`);
                    // Clear selection and reload table
                    selectedRows.clear();
                    updateSelectionCount();
                    usersTable.draw();
                } else {
                    alert('Error: ' + (response.message || 'Failed to reset passwords'));
                }
            },
            error: function(xhr) {
                $('#resetPasswordModal').modal('hide');
                let errorMessage = 'Failed to reset passwords';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                alert('Error: ' + errorMessage);
            },
            complete: function() {
                $('#confirmResetPassword').prop('disabled', false).text('Reset Passwords');
            }
        });
    });
});
