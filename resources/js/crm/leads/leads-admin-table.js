const getUsedColumns = cols => {
    const usedColumns = columnsToUse.map(colIndex => cols.find(colDef => colDef.localIndex === colIndex));
    return usedColumns;
}

const getUsedOrder = () => {
    return columnsToUse.includes('date') ? [[2, "desc"]] : undefined;
}

const leadsDuplicatesMap = {};

function addDuplicatesForLead(leadId, duplicates) {
    leadsDuplicatesMap[leadId] = duplicates;
}

const observableFields = [
    "llc",
    "ot",
    "loc",
    "t",
    "status",
    "agent",
    "pantry",
    "pf",
    "pt",
];

const nonObservableFields = [];

let leadsTable = undefined;
window.leadsSearchParams = {};
let filtersInitialized = false;
$(document).ready(function () {
    let currentRequest = null;
    const addCustomFiltersAndSearch = (sSource, aoData, fnCallback) => {
        // Always execute search, even if filters haven't been initialized yet
        // This allows DataTable's built-in search to work immediately
        const urlStr = parseDTParams(aoData);
        urlStr.push(`vt=${vt}`);

        Object.keys(window.leadsSearchParams).forEach((k) => {
            urlStr.push(`${k}=${window.leadsSearchParams[k]}`);
        });

        const data = urlStr.join("&");
        if (currentRequest && currentRequest.readyState !== 4) {
            currentRequest.abort();
        }
        currentRequest = $.ajax({
            dataType: "json",
            url: `/crm/leads`,
            data,
            success: (input) => {
                fnCallback(input);
                feather.replace();
                setTimeout(() => {
                    $('[data-bs-toggle="tooltip"]').tooltip();
                }, 200);
            },
        });
    };

    $("#leads-list-filters th").each(function () {
        var title = $(this).text();
        var html =
            '<div class="input-group input-group-sm" >' +
            '<input type="text" class="form-control" id="leads-list-col-filter-' +
            $(this).data("col-index") +
            '" placeholder="' +
            title +
            '" />' +
            '<span class="input-group-addon"><span class="glyphicon glyphicon-filter"></span></span>' +
            "</div>";
        $(this).html(html);
    });

    const columnDefs = [
        {
            "class": "details-control",
            "orderable": false,
            "data": null,
            "defaultContent": "",
            localIndex: 'index'
        },
        {
            name: "reminder",
            data: "reminder",
            localIndex: 'reminder',
            render(data, type, full, row) {
                return data ? `<span>
                    <a onclick="triggerAddReminderModal('${full.DT_RowId}', 'lead')" class="dropdown-item" style="padding:0" href="#addNoteModal">
                        <svg fill="#000000" width="24" height="24" viewBox="0 0 32 32" id="icon" xmlns="http://www.w3.org/2000/svg"><defs><style>.cls-1{fill:none;}</style></defs><title>reminder</title><path d="M30,23.3818l-2-1V20a6.0046,6.0046,0,0,0-5-5.91V12H21v2.09A6.0046,6.0046,0,0,0,16,20v2.3818l-2,1V28h6v2h4V28h6ZM28,26H16V24.6182l2-1V20a4,4,0,0,1,8,0v3.6182l2,1Z"/><path d="M28,6a2,2,0,0,0-2-2H22V2H20V4H12V2H10V4H6A2,2,0,0,0,4,6V26a2,2,0,0,0,2,2h4V26H6V6h4V8h2V6h8V8h2V6h4v6h2Z"/><rect id="_Transparent_Rectangle_" data-name="&lt;Transparent Rectangle&gt;" class="cls-1" width="32" height="32"/></svg>
                    </a>
                    </span>` : "";
            },
        },
        {
            name: "duplicate_contact",
            data: "duplicate_contact",
            localIndex: 'duplicate_contact',
            render(data, type, full, row) {
                const reassignedBadge = full.is_reassigned ? `
                    <div data-bs-toggle="tooltip" title="Reassigned by: ${full.reassign_agent_name}" style="white-space: nowrap; cursor: pointer">
                        <span style="display: inline-block; background-color: #3DED97; color: #ffffff; width: 30px; height: 30px; border-radius: 50%; text-align: center; line-height: 30px;">R</span>
                    </div>` : ``;

                const duplicateMarkup = data ? `<a onclick="triggerSeeDuplicates('${full.DT_RowId}')" href="#duplicatesModal">
                    <span style="display: inline-block; background-color: #007FFF; color: #ffffff; width: 30px; height: 30px; border-radius: 50%; text-align: center; line-height: 30px;">D</span>
                    </a>` : "";

                const interactionsCount = full.interactions_count || 0;
                const interactionsMarkup = `<div data-bs-toggle="tooltip" title="Total interactions: ${interactionsCount}" style="white-space: nowrap; cursor: pointer">
                    <span class="bg-light inline-block p-2 round">${interactionsCount}</span>
                </div>`;

                return `<div style="display: flex; justify-content: flex-end; align-items: center; gap: 4px">${reassignedBadge}${duplicateMarkup}${interactionsMarkup}</div>`;
            },
        },
        {
            name: "last_call",
            data: "last_call",
            localIndex: 'last_call',
            render: function (data, type, full, row) {
                if (data && data == 'answered') {
                    return ` <div data-bs-toggle="tooltip" title="Answered at: ${full.last_call_timing}" style="white-space: nowrap">
                        <span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#198754" class="bi bi-telephone-fill" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M1.885.511a1.745 1.745 0 0 1 2.61.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.68.68 0 0 0 .178.643l2.457 2.457a.68.68 0 0 0 .644.178l2.189-.547a1.75 1.75 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.6 18.6 0 0 1-7.01-4.42 18.6 18.6 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877z"/>
                            </svg>
                        </span>
                    </div>`;
                } else if (data && data == 'not_answered') {
                    return ` <div data-bs-toggle="tooltip" title="Not Answered at: ${full.last_call_timing}" style="white-space: nowrap">
                        <span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="#DC143C" class="bi bi-telephone-fill" viewBox="0 0 16 16">
                                <path fill-rule="evenodd" d="M1.885.511a1.745 1.745 0 0 1 2.61.163L6.29 2.98c.329.423.445.974.315 1.494l-.547 2.19a.68.68 0 0 0 .178.643l2.457 2.457a.68.68 0 0 0 .644.178l2.189-.547a1.75 1.75 0 0 1 1.494.315l2.306 1.794c.829.645.905 1.87.163 2.611l-1.034 1.034c-.74.74-1.846 1.065-2.877.702a18.6 18.6 0 0 1-7.01-4.42 18.6 18.6 0 0 1-4.42-7.009c-.362-1.03-.037-2.137.703-2.877z"/>
                            </svg>
                        </span>
                    </div>`;
                } else if (data && data == 'inactive') {
                    return ` <div data-bs-toggle="tooltip" title="Inactive at: ${full.last_call_timing}" style="white-space: nowrap">
                        <span>
                            <svg class="svg-icon" style="width: 1.5em; height: 1.5em;vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M910.28599912 912.05A179.02500029 179.02500029 0 0 1 785.14849971 962h-0.30000059a181.95000029 181.95000029 0 0 1-125.47499941-49.98750029 167.92499971 167.92499971 0 0 1-51.93750059-120.67499971 168.03749971 168.03749971 0 0 1 51.93750059-120.67499971 181.8 181.8 0 0 1 125.2125-49.95c46.95000029 0 92.77499971 18.225 125.7374997 49.98749942 16.23750029 15.59999971 29.025 33.82499971 38.025 54.225 9.225 21.07500029 13.91249971 43.425 13.91249971 66.45000058a167.92499971 167.92499971 0 0 1-51.975 120.67499971z m-29.84999941-212.625a136.6875 136.6875 0 0 0-95.43750029-38.09999971c-35.73749971 0-70.61249971 13.91249971-95.69999971 38.09999971a128.025 128.025 0 0 0-39.63750029 91.95000029 127.98749971 127.98749971 0 0 0 39.6 91.9125 138.44999971 138.44999971 0 0 0 95.39999999 38.09999971h0.3750003c35.51249971 0 70.27499971-13.87500029 95.3624997-38.09999971 12.41250029-11.88749971 22.20000029-25.80000029 29.025-41.32500029 7.04999971-16.01250029 10.61250029-33.03749971 10.6125003-50.58749971a127.98749971 127.98749971 0 0 0-39.6-91.95000029z m-12.41250029 114.00000029h-166.34999971c-13.31250029 0-22.95-9.29999971-22.95-22.05 0-12.78749971 9.63749971-22.08750029 22.95-22.08750029h166.34999971c13.31250029 0 22.95 9.29999971 22.95 22.08750029 0 12.75000029-9.63749971 22.05-22.95 22.05z m-112.12499971-214.9875a29.25 29.25 0 0 1-19.83750029-7.61250058l-1.4625-1.16249942a389.43749971 389.43749971 0 0 0-230.85-74.96250029c-211.57499971 0-383.69999971 165.56249971-383.69999971 369.07499971-0.07499971 15.3-13.05 27.74999971-28.95000029 27.75000058-15.8625 0-28.87499971-12.4875-28.9125-27.82500029a409.95 409.95 0 0 1 79.7249997-243 439.35000029 439.35000029 0 0 1 203.92500059-153.18749971l26.47499941-9.7875-21.52499941-17.73750058c-52.3125-43.16249971-82.27500029-105.75-82.27500029-171.71249942C268.51099941 163.51250029 374.03599941 62 503.74849941 62c129.7125 0 235.27500029 101.51250029 235.2750003 226.27500029 0 65.96250029-30.0375 128.54999971-82.35 171.71249942l-21.52500029 17.73750058 26.5124997 9.7875c36.9 13.57499971 72 32.10000029 104.36250059 55.08749971l7.875 6.22500029a27.26250029 27.26250029 0 0 1 10.94999941 21.74999942c0 15.33750029-12.97500029 27.82500029-28.94999941
                                27.86250058z m-74.77500059-310.1625c0-94.08750029-79.57500029-170.62499971-177.3749997-170.62500058-97.80000029 0-177.37499971 76.53750029-177.37499971 170.62500058 0 94.05 79.57500029 170.58750029 177.37499971 170.58749942 97.80000029 0 177.37499971-76.53750029 177.3749997-170.58749942z"  />
                            </svg>
                        </span>
                    </div>`;
                } else if (data && data == 'not_interested') {
                    return ` <div data-bs-toggle="tooltip" title="Not Interested at: ${full.last_call_timing}" style="white-space: nowrap">
                        <span>
                            <svg class="svg-icon" style="width: 1.5em; height: 1.5em;vertical-align: middle;fill: currentColor;overflow: hidden;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M512 85.333333C276.266667 85.333333 85.333333 276.266667 85.333333 512s190.933333 426.666667 426.666667 426.666667 426.666667-190.933333 426.666667-426.666667S747.733333 85.333333 512 85.333333z m0 768c-188.586667 0-341.333333-152.746667-341.333333-341.333333 0-78.933333 27.093333-151.253333 71.893333-209.066667L721.066667 781.44C663.253333 826.24 590.933333 853.333333 512 853.333333z m269.44-132.266666L302.933333 242.56C360.746667 197.76 433.066667 170.666667 512 170.666667c188.586667 0 341.333333 152.746667 341.333333 341.333333 0 78.933333-27.093333 151.253333-71.893333 209.066667z"  />
                            </svg>
                        </span>
                    </div>`;
                } else {
                    return `-`
                }
            }
        },
        {
            /*Added By*/
            name: 'l.created_at',
            data: 'created_at',
            localIndex: 'created_at',
            render(data) {
                return data ? data : '-';
            },
        },
        {
            name: 'l.last_contact_date',
            data: "last_contact_date",
            localIndex: 'last_contact_date',
            render: function (data, type, full, row) {
                return !!data ? data : 'N/A';
            }
        },
        /*{
            name: 'l.move_in_date',
            data: "move_in_date",
            localIndex: 'move_in_date',
            render: function(data, type, full, row) {
                return !!data ? data : 'N/A';
            }
        },*/
        {
            /*Status*/
            name: 'l.status',
            data: "status",
            localIndex: 'status',
            render: function (data, type, full, row) {
                let html = "";
                if (data.id) {
                    html = `<div data-bs-toggle="tooltip" title="${full.last_remark}"  class="text-nowrap font-small badge p-2 ps-2 pe-2" style="background-color: ${data.background_color}">${data.name}</div>`;
                } else {
                    html = `<div class="text-nowrap font-small badge p-2 ps-2 pe-2 text-dark">${data.name ? data.name : ''}</div>`;
                }
                html = full.can_edit ? `<a href="javascript:triggerLeadUpdateStatusModal('${full.DT_RowId}', '${full.status.id}', '${full.assigned_to ? full.assigned_to.id : null}')" href="#leadUpdateStatusModal">${html}</a>` : html;
                return html;
            }
        },
        {
            /*Agent Name*/
            name: 'agent_name',
            data: "agent_name",
            localIndex: 'agent_name',
            render: function (data) {
                return data ? data : '-';
            }
        },
        {
            /*Rating*/
            name: 'l.rating',
            data: "rating",
            localIndex: 'rating',
            render: function (data, type, full, row) {
                if (data) {
                    let value;
                    if (data == 'A') {
                        value = '3 days';
                    } else if (data == 'B') {
                        value = '5 days';
                    } else if (data == 'C') {
                        value = '10 days';
                    } else if (data == 'D') {
                        value = '15 days';
                    } else if (data == 'E') {
                        value = '30 days';
                    } else if (data == 'F') {
                        value = '60 days';
                    } else if (data == 'X') {
                        value = '-';
                    }
                    html = `<div data-bs-toggle="tooltip" title="${value}" class="text-nowrap p-2 ps-2 pe-2 ">${data}</div>`;
                } else {
                    html = 'N/A'
                }

                return html;
            }
        },
        {
            /*Full Name*/
            name: 'c.name',
            data: 'full_name',
            localIndex: 'full_name',
            render: function (data, type, full, row) {
                var items = (!!data ? data : full.email).split(";");
                var html = "";
                
                const currentDate = new Date();
                const updatedAt = new Date(full.updated_at_date);
                const diffTime = Math.abs(currentDate - updatedAt);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                
                const containerStyle = diffDays > 5 ? "float:left; background-color: red; color: white; padding: 2px 8px; border-radius: 4px;" : "float:left;";
                
                html += `<div style='${containerStyle}'>`;

                for (var i = 0; i < items.length; i++) {
                    if (i != 0) {
                        html += "<strong>" + items[i] + "</strong> ";
                    } else {
                        html += items[i] + " ";
                    }
                }
                html += "</div>";

                if (full.client_status) {
                    html +=
                        '<button style="background-color:inherit; border: none;" data-toggle="tooltip" data-placement="bottom" title="' + full.client_status.replace("-", " ") + '"> <i class="fa fa-info-circle"></i></button>';
                }

                return html;
            },
            createdCell: function (td, cellData, rowData, row, col) {
                var $td = $(td);
                switch (rowData.client_status) {
                    case "RENTED":
                        $td.addClass("client-status-RENTED");
                        break;
                    case "FOLLOW-UP":
                        $td.addClass("client-status-FOLLOW-UP");
                        break;
                    case "FOCUS-ON":
                        $td.addClass("client-status-FOCUS-ON");
                        break;
                    case "FAR-MOVING-DATE":
                        $td.addClass("client-status-FAR-MOVING-DATE");
                        break;
                    case "STUCK":
                        $td.addClass("client-status-STUCK");
                        break;
                }
                $td.find('[data-toggle="tooltip"]').tooltip();
            },
            responsivePriority: 0
        },
        {
            name: 'c.mobile_1',
            data: 'phone',
            localIndex: 'phone',
            render: function (data, type, full, row) {
                const items = data.split(";");
                let html = "";

                for (var i = 0; i < items.length; i++) {
                    if (i != 0) {
                        html += "<br/>";
                    }
                    if (!!items[i] && items[i] != "N/A") {
                        html += '<a style="white-space: nowrap;" href="tel:' + items[i] + '">' + items[i] + "</a>";
                    } else {
                        html += 'N/A';
                    }
                }
                return html;
            },
            responsivePriority: 1
        },
        /*{
            name: 'c.email_1',
            data: 'email',
            localIndex: 'email',
            width: '80px',
            render: function(data, type, full, row) {
                const items = data.split(";");
                let html = "";

                for (var i = 0; i < items.length; i++) {
                    if (i != 0) {
                        html += "<br/>";
                    }
                    html += '<a href="mailto:' + items[i] + '">' + items[i] + "</a>";
                }

                return html;
            },
            responsivePriority: 3
        },*/
        {
            name: 'l.filter_operation_type',
            data: 'filter_operation_type',
            localIndex: 'filter_operation_type',
        },
        {
            name: 'l.filter_property_type',
            data: 'filter_property_type',
            localIndex: 'filter_property_type',
            render(data) {
                return data ? `<div style="white-space: nowrap; width: 200px; overflow: hidden; text-overflow: ellipsis;">${data}</div>` : '-'
            },
        },
        {
            /*Assigned to*/
            name: 'u.name',
            data: 'assigned_to',
            localIndex: 'assigned_to',
            responsivePriority: 0,
            render: function (data, type, full, row) {
                let html = null;
                if (data.hasOwnProperty("name")) {
                    html = `<div style="white-space: nowrap;"> ${data.name} </div>`;
                } else {
                    html = data;
                }
                return html;
            },
        },
        {
            /*Desired Area*/
            name: 'location_id',
            data: 'location_id',
            localIndex: 'location_id',
            width: '100px',
            createdCell: function (td, cellData, rowData, row, col) {
                var $td = $(td);
                $td.addClass("abbr-column-25");
                $td.prop("title", $td.text());
            },
            render(data) {
                return data ? `<div style="white-space: nowrap; width: 200px; overflow: hidden; text-overflow: ellipsis;">${data}</div>` : '-'
            },
            responsivePriority: 2
        },
        {
            /*Platform from*/
            name: 'platform_from',
            data: 'platform_from',
            localIndex: 'platform_from',
            render(data) {
                return data ? `<div style="white-space: nowrap; width: 200px; overflow: hidden; text-overflow: ellipsis;">${data}</div>` : '-'
            },
        },
        {
            /*Budget*/
            name: 'budget',
            data: 'budget',
            localIndex: 'budget',
            width: '100px',
            render(data) {
                return data ? `<div style="white-space: nowrap">${data}</div>` : '-'
            },
            responsivePriority: 2
        },
        {
            data: 'actions',
            data: null,
            className: "export-transient",
            render: function (data, type, full, row) {
                const canEdit = data.can_edit;
                const actions = data["actions"];
                // const readLink = actions.read ? `<li><a class="dropdown-item" href="${actions.read}"><i class="bi bi-eye"></i> View</a></li>` : '';
                const editLink = actions.update ? `<li><a class="dropdown-item" href="${actions.update}"><i class="bi bi-pencil"></i> Edit</a></li>` : '';
                const deleteLink = actions.delete ? `<li><a onclick="confirmDelete('${data.DT_RowId}')" class="dropdown-item"><i class="bi bi-eraser"></i> Delete </a></li>` : '';

                const updateStatusLink = data.can_edit ? `<li><a class="dropdown-item" onclick="triggerLeadUpdateStatusModal('${data.DT_RowId}', '${data.status.id}', '${!!data.assigned_to ? data.assigned_to.id : null}')" href="#leadUpdateStatusModal"><i class="bi bi-send-check"></i> Update Lead Status </a></li>` : '';
                const offerLink = `<li><a class="dropdown-item" href="/crm/leads/${data.DT_RowId}/manage/offer"><i class="bi bi-folder2-open"></i> Offer</a></li>`;
                const notesLink = `<li><a onclick="triggerAddReminderModal('${data.DT_RowId}', 'lead')" class="dropdown-item" href="#addNoteModal"><i class="bi bi-calendar-check"></i> Reminders </a></li>`;
                const closeLink = '';
                const reassignLink = full.is_reassigned ? '' : `<li><a onclick="triggerLeadReassignModal('${data.DT_RowId}')" class="dropdown-item" href="#leadReassignModal"><i class="bi bi-arrow-clockwise"></i> Reassign </a></li>`;

                return `
                        <div class="dropdown" data-item-id="${data.DT_RowId}">
                             <button class="btn btn-sm btn-light dropdown-toggle" type="button" id="dropdownMenuButton${data.DT_RowId}" data-bs-toggle="dropdown" aria-expanded="false">
                                Actions
                              </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton${data.DT_RowId} ">
                                <li><h6 class="dropdown-header">ACTIONS</h6></li>
                                ${editLink}
                                ${deleteLink}
                                ${updateStatusLink}
                                ${reassignLink}
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header">MANAGE</h6></li>
                                ${canEdit ? offerLink : ''}
                                ${notesLink}
                                ${canEdit ? closeLink : ''}
                            </ul>
                        </div>
                    `
            },

            orderable: false,
            searchable: false,
            responsivePriority: 1,
            localIndex: 'actions'

        }
    ];

    leadsTable = $("#leads-list").DataTable({
        serverSide: true,
        lengthMenu: [
            [10, 25, 50, -1],
            [10, 25, 50, "All"]
        ],
        pageLength: 50,
        responsive: false,
        scrollY: 'calc(100vh - 350px)',
        scrollX: true,
        aaSorting: [],
        oLanguage: {
            sProcessing: "Processing..."
        },
        processing: true,
        order: getUsedOrder(),
        columns: getUsedColumns(columnDefs),
        searching: true,
        orderCellsTop: true,
        fnServerData: addCustomFiltersAndSearch,
        fnRowCallback: function (nRow, aData) {
            if (aData.duplicate_contact !== 'undefined') {
                addDuplicatesForLead(aData.DT_RowId, aData.duplicate_contact)
            }
            if (aData.highlighted_row) {
                $(nRow).addClass("bg-warning");
            }

          
        },

        initComplete: function () {
            this.api()
                .columns()
                .every(function () {
                    var that = this;
                    $("#leads-list-col-filter-" + this.index()).keyup(function () {
                        if (that.search() !== this.value) {
                            that.search(this.value).draw();
                        }
                    });
                });
        }
    });
    const detailRows = [];

    function format(d, operationHistory) {
        // const carouselId = Math.random().toString(36).replace(/[^a-z]+/g, '').substr(0, 10);
        let metadataContent = '<div class="d-flex flex-column">';
        for (const [key, value] of ['utm_source', 'utm_campaign'].map(key => [key, d[key]])) {
            metadataContent += `
                <div class="mb-2">
                    <strong class="text-muted text-uppercase">${key}:</strong>
                    <span class="ms-1">${value || '-'}</span>
                </div>
            `;
        }
        metadataContent += '</div>';


        return `
            <div class="mt-2 mb-2">
                <div class="pb-2 mb-4 border-bottom d-flex flex-col justify-content-start flex-wrap w-50 gap-4">
                    ${!!d.assigned_to ? '<div> <h6> Date added </h6> ' + (d.created_at_date) + ' </div>' : ''}
                    <div>
                        <h6> When needed  </h6>
                        ${d.move_in_date == "0000-00-00" ? "N/A" : d.move_in_date}
                    </div>
                    ${!!d.assigned_to ? '<div> <h6> Assigned to </h6> ' + (d.assigned_to.hasOwnProperty("name") ? d.assigned_to.name : d.assigned_to) + ' </div>' : ''}
                    <div>
                        <h6> Email </h6>
                        ${d.email ? '<a href="mailto:d.email">' + d.email + '</a>' : "-"}
                    </div>
                    <div>
                        <h6> Added by </h6>
                        ${d.created_by ? d.created_by : "-"}
                    </div>
                    <div>
                        <h6> Ref No </h6>
                        ${d.inquired_ref_no ? d.inquired_ref_no : "-"}
                    </div>
                    <div>
                        <h6> Metadata </h6>
                        ${metadataContent}
                    </div>
                </div>
                <div class="row">
                    <article>
                        <h6 class="mb-0">Remarks <a href="javascript:triggerAddOperationHistoryModal(${d.DT_RowId}, true)" class="btn btn-sm btn-outline-secondary"><i class="bi bi-pencil-square"></i> Add remark</a></h6>
                    </article>
                    <span id="rowExpandedOperationHistoryList" style="overflow-y: scroll; height: 200px">
                        ${operationHistory.length > 0 ? operationHistory.map(r => '<article class="mt-4">' + r.content + '<footer>by <em>' + r.created_by + '</em> on ' + r.created_at + '</footer></article>').join('') : '<span>No remarks</span>'}
                    </span>
                </div>
            </div>
        `;
    }

    $("#leads-list tbody").on('click', 'tr td.details-control', function () {
        const tr = $(this).closest('tr');
        const row = leadsTable.row(tr);
        const idx = $.inArray(tr.attr('id'), detailRows);

        if (row.child.isShown()) {
            tr.removeClass('details');
            row.child.hide();
            // Remove from the 'open' array
            detailRows.splice(idx, 1);
        } else {
            const rowData = row.data();
            Promise.all([
                $.get(`/crm/leads/${rowData.DT_RowId}/operation-history`)
            ])
                .then(([operationHistory]) => {
                    tr.addClass('details');
                    row.child(format(rowData, operationHistory)).show();
                    if (idx === -1) {
                        detailRows.push(tr.attr('id'));
                    }
                    // $('#carouselExampleControls').carousel();
                })
                .catch(err => console.log(err));
        }
    })
    if (typeof addExcelDownloadButton !== "undefined") {
        addExcelDownloadButton(leadsTable, "leads");
    }
});

function onLeadsFiltersParamsChange(filterParams) {
    window.leadsSearchParams = filterParams;
    if (!filtersInitialized) {
        filtersInitialized = true;
    }
    if (!!leadsTable) {
        leadsTable.draw();
    }
}
