<?php

namespace App\Models;

class CacheKeys
{
    const ROLES = 'roles';
    const FILTER_ATTRIBUTE_VALUE_LISTS = 'filterAttributeValueLists';
    const AMENITIES_GROUP = 'amenitiesGroup';
    const HOMEPAGE_RECENT_PROPERTIES_MAP = 'homepageRecentPropertiesMap';
    const HOMEPAGE_LISTING_COUNTERS = 'homepageListingCounters';
    const INVESTMENT_OPPORTUNITIES_MAP = 'investmentOpportunitiesMap';
    const REGION_LAST_PROPERTIES_MAP = 'regionLastProperties';
    const SNAPSHOT_PART = 'SNAPSHOT_';

    const AMENITIES_DEFINITIONS_IDS = 'amenitiesDefinitionIds';
    const ATTRIBUTE_DEFINITION_IDS = 'attributeDefinitionIds';
    const AMENITIES_ATTRIBUTES = 'amenitiesAttributes';
    const LOCALE_GEOGRAPHIES_MAP = 'localeGeographiesMap';
    const USED_PROPERTY_TYPES = 'usedPropertyTypes';
    const ALL_PROPERTY_TYPES = 'allPropertyTypes';
    const USED_PROPERTY_TYPES_WITH_TRANSLATIONS = 'usedPropertyTypesWithTranslations';
    const MAIN_GEOGRAPHIES = 'mainGeographies';
    const SEO_MENUS = 'seoMenus';

    const AVAILABLE_PROPERTY_TYPES = 'availablePropertyTypes';
    const ALL_GEOGRAPHIES_WITH_PARENT = 'allGeographiesWithParent';
    const VISIBLE_GEOGRAPHIES_WITH_DATA = 'visibleGeographiesWithData';
    const LOCALE_GEOGRAPHY_MAP_PREFIX = 'locale_geography_map_';

    const SNAPSHOT_STATS = 'snapshotStats';
    const SNAPSHOT_FULL_STATS = 'snapshotFullStats';
    const COUNTRIES = 'countriesData';

    const LEAD_SOURCES = 'leadSources';
    const LEAD_STATUSES = 'leadStatuses';
    const LEAD_STATUSES_MAP = 'leadStatusesMap';
    const LEAD_FOLLOWUP_TASK_WINDOW = 'leadFollowupTaskWindow';

    const LEAD_MASTER_STATUSES_IDS = 'leadMasterStatusesIds';

    const UTM_SOURCES = 'utmSources';
}
