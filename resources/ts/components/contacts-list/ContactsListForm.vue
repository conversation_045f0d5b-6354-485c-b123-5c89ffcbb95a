<template>
    <article class="container-fluid g-3 pb-4 needs-validation" novalidate>
        <div class="row">
            <div class="col">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col pt-3">
                            <h1 class="h2" v-if="!state.loading">
                                <span v-if="state.editedEntity.id">Contact Details</span>
                                <span v-else>Add new Contact</span>
                            </h1>
                        </div>
                    </div>
                    <div class="row" v-if="leadParamId">
                        <div class="col-12 mt-3">
                            <div class="alert alert-warning">In order to complete the deal, please make sure the contact
                                info is complete. Also don't forget to save. Then go back to deal screen.<div
                                    v-if="leadInvalidFields.length">Mandatory fields for deal: [{{
                                        leadInvalidFields.join(", ") }}]</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mt-3">
                            <div class="form-check">
                                <input :disabled="state.loading" :checked="!!state.editedEntity.verified" @input="
                                    debouncedFieldChange('verified', $event)
                                    " class="form-check-input" type="checkbox" id="gridCheck" />
                                <label class="form-check-label" for="gridCheck">
                                    Verified
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-8 mt-3 has-validation">
                            <label for="name" class="form-label">Fullname</label>
                            <input @input="debouncedFieldChange('name', $event)" :disabled="state.loading"
                                :value="state.editedEntity.name" type="text" class="form-control" :class="{
                                    'is-invalid': !!state.form.errors['name'],
                                }" id="name" />
                            <div v-if="!!state.form.errors['name']" class="invalid-feedback">
                                {{ state.form.errors["name"] }}
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6 col-lg-4 mt-3">
                            <label for="gender" class="form-label">Gender</label>
                            <select v-model="state.editedEntity.gender" @change="debouncedFieldChange('gender', $event)"
                                id="gender" class="form-select" :disabled="state.loading" :class="{
                                    'is-invalid': !!state.form.errors['gender'],
                                }">
                                <option selected value="">Please select</option>
                                <option value="M">M</option>
                                <option value="F">F</option>
                            </select>
                            <div v-if="!!state.form.errors['gender']" class="invalid-feedback">
                                {{ state.form.errors["gender"] }}
                            </div>
                        </div>
                        <div class="col-sm-6 col-lg-4 d-flex flex-column mt-3">
                            <label class="text-gray-50 mb-2">Nationality</label>
                            <div class="nationality-select">
                                <FGVirtualSelect :selectorClassName="FGVirtualSelectNationalityClass
                                    " :class="{
                                        'vscomp-ele': true,
                                        [FGVirtualSelectNationalityClass]: true,
                                    }" :options="state.nationalities" :config="state.nationalitiesSelectorConfig"
                                    :selected-value="state.editedEntity.nationality_id
                                        " @change="
                                            debouncedFieldChange(
                                                'nationality_id',
                                                $event
                                            )
                                            " />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-6 col-lg-4 mt-3 has-validation" :class="{
                            'errors-displayed':
                                !!state.form.errors['mobile_1'],
                        }">
                            <label for="mobile" class="form-label">Mobile</label>
                            <div class="input-group fg-dropdown">
                                <CountriesSelectorCrm :selected-prefix="prefix1DTO.data.selectedOption
                                    " @selectionChange="
                                        debouncedFieldChange(
                                            'prefix_mobile_1',
                                            $event
                                        )
                                        " />
                                <input :value="state.editedEntity.mobile_1" :disabled="state.loading" @input="
                                    debouncedFieldChange('mobile_1', $event)
                                    " type="text" class="form-control" :class="{
                                        'is-invalid':
                                            !!state.form.errors['mobile_1'],
                                    }" id="mobile" placeholder="Mobile Phone" />
                            </div>

                            <div v-if="!!state.form.errors['mobile_1']" class="invalid-feedback">
                                {{ state.form.errors["mobile_1"] }}
                            </div>
                            <div v-if="!!state.form.errors['mobile_1']" class="invalid-feedback">
                                <button v-if="!!state.existingAgentIdByPhone" :disabled="state.loading"
                                    @click="linkAndRedirectToContact('phone')" class="btn btn-outline-secondary btn-sm"
                                    type="button">
                                    Use this contact
                                </button>
                            </div>
                        </div>
                        <div class="col-6 col-lg-4 mt-3 has-validation" :class="{
                            'errors-displayed':
                                !!state.form.errors['mobile_2'],
                        }">
                            <label for="mobile" class="form-label">Mobile(2)</label>
                            <div class="input-group mb-3 fg-dropdown">
                                <CountriesSelectorCrm :selected-prefix="prefix2DTO.data.selectedOption
                                    " @selectionChange="
                                        debouncedFieldChange(
                                            'prefix_mobile_2',
                                            $event
                                        )
                                        " />
                                <input :value="state.editedEntity.mobile_2" :disabled="state.loading" @input="
                                    debouncedFieldChange('mobile_2', $event)
                                    " type="text" class="form-control" :class="{
                                        'is-invalid':
                                            !!state.form.errors['mobile_2'],
                                    }" id="mobile2" placeholder="Mobile Phone(2)" />
                            </div>
                            <div v-if="!!state.form.errors['mobile_2']" class="invalid-feedback">
                                {{ state.form.errors["mobile_2"] }}
                            </div>
                            <div v-if="!!state.form.errors['mobile_2']" class="invalid-feedback">
                                <button v-if="!!state.existingAgentIdByPhone" :disabled="state.loading"
                                    @click="linkAndRedirectToContact('phone')" class="btn btn-outline-secondary btn-sm"
                                    type="button">
                                    Use this contact
                                </button>
                            </div>
                        </div>
                        <div class="col-6 col-lg-4 mt-3 has-validation">
                            <label for="email_1" class="form-label">Email</label>
                            <input :value="state.editedEntity.email_1" :disabled="state.loading"
                                @input="debouncedFieldChange('email_1', $event)" type="email" class="form-control"
                                :class="{
                                    'is-invalid':
                                        !!state.form.errors['email_1'],
                                }" id="email_1" />
                            <div v-if="!!state.form.errors['email_1']" class="invalid-feedback">
                                {{ state.form.errors["email_1"] }}
                                <button v-if="!!state.existingAgentIdByEmail" :disabled="state.loading"
                                    @click="linkAndRedirectToContact('email')" class="btn btn-outline-secondary btn-sm"
                                    type="button">
                                    Use this contact
                                </button>
                            </div>
                        </div>
                        <!-- </div>
                    <div class="row"> -->
                        <div class="col-6 col-lg-4 mt-3">
                            <label for="dateOfBirth" class="form-label">Date of Birth</label>
                            <input :value="state.editedEntity.date_of_birth" :disabled="state.loading" @input="
                                debouncedFieldChange(
                                    'date_of_birth',
                                    $event
                                )
                                " type="date" class="form-control" id="dateOfBirth" />
                        </div>
                        <div class="col-6 col-lg-4 mt-3">
                            <label for="qatar_id_no" class="form-label">QID No</label>
                            <input :value="state.editedEntity.qatar_id_no" :disabled="state.loading" @input="
                                debouncedFieldChange('qatar_id_no', $event)
                                " type="text" class="form-control" id="qatar_id_no" />
                        </div>
                        <div class="col-6 col-lg-4 mt-3">
                            <label for="occupation" class="form-label">Occupation</label>
                            <input :value="state.editedEntity.occupation" :disabled="state.loading" @input="
                                debouncedFieldChange('occupation', $event)
                                " type="text" class="form-control" id="occupation" />
                        </div>
                        <div class="col-6 col-lg-4 mt-3">
                            <label for="position" class="form-label">Position</label>
                            <input :value="state.editedEntity.position" :disabled="state.loading" @input="
                                debouncedFieldChange('position', $event)
                                " type="text" class="form-control" id="position" />
                        </div>
                        <!-- </div>
                    <div class="row"> -->
                        <div class="col-6 col-lg-4 mt-3">
                            <label for="company_name" class="form-label">Company</label>
                            <input :value="state.editedEntity.company_name" :disabled="state.loading" @input="
                                debouncedFieldChange('company_name', $event)
                                " type="text" class="form-control" id="company_name" />
                        </div>
                        <div class="col-6 col-lg-4 mt-3">
                            <label for="residency" class="form-label">Residency</label>
                            <input :value="state.editedEntity.residency" :disabled="state.loading" @input="
                                debouncedFieldChange('residency', $event)
                                " type="text" class="form-control" id="residency" />
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <div class="col pt-3">
                            <h4>Tags</h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mt-3">
                            <label class="text-gray-50 mb-2">Tags</label>
                            <div>
                                <FGVirtualSelect :selectorClassName="FGVirtualSelectTagsClass
                                    " :class="{
                                        'vscomp-ele': true,
                                        [FGVirtualSelectTagsClass]: true,
                                    }" :options="state.tags" :config="state.tagsSelectorConfig"
                                    :selected-value="state.editedEntity.tags" @change="
                                        debouncedFieldChange('tags', $event)
                                        " />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col pt-3">
                            <h4>Metadata</h4>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mt-3">
                            <table v-if="!!state.editedEntity?.metadata" class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Property</th>
                                        <th>Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(value, key) in state.editedEntity?.metadata">
                                        <th>{{ key }}</th>
                                        <td>{{ value }}</td>
                                    </tr>
                                </tbody>
                            </table>
                            <table v-else class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Property</th>
                                        <th>Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="2">No metadata</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <hr v-if="!!state.editedEntity?.leads_ids?.length" />
                    <div v-if="!!state.editedEntity?.leads_ids?.length" class="row">
                        <div class="col-12 mt-3">
                            <h4>Leads</h4>
                        </div>
                        <div class="col-12">
                            <ul>
                                <li v-for="leadId in state.editedEntity
                                    .leads_ids" :key="`lead${leadId}`">
                                    <a :href="`/crm/leads/${leadId}/edit`">Lead #{{ leadId }}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <hr v-if="!!state.editedEntity?.deals_ids?.length" />
                    <div v-if="!!state.editedEntity?.deals_ids?.length" class="row">
                        <div class="col-12 mt-3">
                            <h4>Deals</h4>
                        </div>
                        <div class="col-12">
                            <ul>
                                <li v-for="dealId in state.editedEntity
                                    .deals_ids" :key="`deal${dealId}`">
                                    <a :href="`/crm/deals/${dealId}/edit`">Deal #{{ dealId }}</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <div class="col-12 mt-3">
                            <h4>Remarks</h4>
                        </div>
                        <div class="col-12 remarks-space">
                            <div>
                                <textarea @input="
                                    debouncedFieldChange('remarks', $event)
                                    " :value="state.editedEntity.remarks" :disabled="state.loading"
                                    class="form-control"></textarea>
                            </div>
                            <section v-if="!!state.operationHistory.length" style="max-height: 400px; overflow-y: auto">
                                <article v-for="item in state.operationHistory" class="mt-4">
                                    {{ item.content }}
                                    <footer>
                                        by <em>{{ item.created_by }}</em> on
                                        {{ item.created_at }}
                                    </footer>
                                </article>
                            </section>
                        </div>
                    </div>
                    <div class="row" v-if="state.editedEntity.id">
                        <div class="col-12 mt-3">
                            <h4>Duplicates</h4>
                        </div>
                        <div class="col-12 mt-3">
                            <ContactDuplicates :contactId="state.editedEntity?.id" />
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-4 col-lg-3 bg-light mt-2"
                style="width: 100%; max-width: 330px; min-width: 330px;">
                <div class="mt-xl-4" :class="{ 'position-sticky': !state.isActivityPanelModalOpen }" style="top: 80px">
                    <h5 class="lh-1">Actions [{{ state.editedEntity.id }}]</h5>
                    <hr class="text-300" />
                    <ul class="nav nav-vertical flex-column doc-nav" data-doc-nav="data-doc-nav">
                        <li class="nav-item d-flex gap-1">
                            <span v-if="!!state.editedEntity.is_master_contact"
                                class="badge text-primary border border-primary">Master Contact</span>
                            <span v-if="!!state.editedEntity.landlord_id"
                                class="badge text-primary border border-primary">Landlord</span>
                            <span v-if="!!state.editedEntity?.leads_ids?.length"
                                class="badge text-info border border-info">Leads</span>
                        </li>
                        <li v-if="
                            !!state.editedEntity && !!state.editedEntity.id
                        " class="nav-item">
                            <a role="button" class="nav-link"
                                :href="`/crm/leads/create?source=contact&contactId=${state.editedEntity.id}`">
                                <i class="bi bi-plus"></i>
                                <span class="ms-1">Create lead</span>
                            </a>
                        </li>
                        <li v-if="
                            !!state.editedEntity && !!state.editedEntity.id
                        " class="nav-item">
                            <a v-if="!state.editedEntity.landlord_id" role="button" class="nav-link"
                                :href="`/crm/broker-landlords/${state.editedEntity.id}/edit`">
                                <i class="bi bi-plus"></i>
                                <span class="ms-1">Create Landlord</span>
                            </a>
                            <a v-else role="button" class="nav-link"
                                :href="`/crm/broker-landlords/${state.editedEntity.id}/edit`">
                                <i class="bi bi-arrow-right"></i>
                                <span class="ms-1">View Landlord Profile</span>
                            </a>
                            <a v-if="!!state.editedEntity.master_contact_id" role="button" class="nav-link"
                                :href="`/crm/broker-landlords/${state.editedEntity.id}/edit`">
                                <i class="bi bi-arrow-right"></i>
                                <span class="ms-1">View Landlord Profile</span>
                            </a>
                            <router-link v-if="!!state.editedEntity.master_contact_id"
                                :to="`/${state.editedEntity.master_contact_id}/edit`" class="nav-link">
                                <i class="bi bi-arrow-right"></i>
                                <span class="ms-1">Master Contact</span>
                            </router-link>
                        </li>
                        <li v-if="
                            leadParamId
                        " class="nav-item">
                            <a role="button" class="nav-link"
                                :href="`/crm/leads/${leadParamId}/manage/close?promote=1`">
                                <i class="bi bi-arrow-left"></i>
                                <span class="ms-1">Back to Deal</span>
                            </a>
                        </li>
                        <li v-if="
                            !!state.editedEntity && !!state.editedEntity.id
                        " class="nav-item">
                            <hr class="text-300" />
                        </li>
                        <li class="nav-item">
                            <Alert v-if="state.saving.isAlertVisible" :variant="state.saving.isFailure &&
                                !state.saving.isSuccess
                                ? 'danger'
                                : 'success'
                                ">Saved.</Alert>
                            <div class="d-flex gap-2 justify-content-center">
                                <button class="btn btn-sm btn-outline-primary" type="submit" form="landlordForm"
                                    :disabled="!!state.form.isPristine ||
                                        Object.keys(state.form.errors).length >
                                        0 ||
                                        state.loading
                                        " @click="
                                            !state.form.isPristine &&
                                            Object.keys(state.form.errors)
                                                .length === 0 &&
                                            doSaveEntry()
                                            ">
                                    Save
                                </button>
                                <router-link to="/" class="btn btn-sm btn-outline-secondary">Cancel</router-link>
                            </div>
                        </li>
                    </ul>
                    <h5 class="lh-1 mt-5">Activity</h5>
                    <hr class="text-300" />
                    <div v-if="state.editedEntity?.id" class="activityBar">
                        <ActivityPanel variant="contactsList"
                            @modal-visibility-change="onActivityPanelModalVisibilityChange($event)"
                            :current-contact-id="state.editedEntity?.id" />
                    </div>
                </div>
            </div>
        </div>
    </article>
</template>

<script lang="ts">
import { defineComponent, onMounted, watch, ref } from "vue";
import { useRoute, useRouter } from "vue-router";

import isEqual from "lodash/isEqual";
import debounce from "lodash/debounce";

import { ContactsListFormState } from "../../model/fe-models.interface";
import { Contact } from "../../model/fe-models.interface";
import UtilService from "../../services/utils.service";
import ContactsListService from "./contacts-list.service";
import FGVirtualSelect from "../FGVirtualSelect.vue";
import CountriesSelectorCrm from "../generic/CountriesSelectorCrm.vue";
import ContactDuplicates from "./ContactDuplicates.vue";
import Alert from "../generic/alert.vue";
import ActivityPanel from "../leadDetailsV2/ActivityPanel.vue";

const QATAR_PHONE_PREFIX = "+974";
const initialEditedEntity: Contact = {
    company_name: "",
    date_of_birth: "",
    contact_id: null,
    landlord_id: null,
    email_1: "",
    gender: "",
    id: 0,
    prefix_mobile_1: QATAR_PHONE_PREFIX,
    mobile_1: "",
    prefix_mobile_2: QATAR_PHONE_PREFIX,
    mobile_2: "",
    name: "",
    nationality_id: 0,
    occupation: "",
    position: "",
    qatar_id_no: "",
    verified: false,
    type: "private",
    tags: [],
    residency: "",
    tag_labels: [],
    reminders: "",
    leads_ids: [],
    deals_ids: [],
    remarks: "",
    agent_name: "",
    created_at: "",
    is_master_contact: null,
    master_contact_id: null,
    master_contact: null,
};

export default defineComponent({
    name: "ContactListForm",

    setup() {
        const route = useRoute();
        const router = useRouter();
        const leadParamId = route.query.leadId;
        const leadInvalidFields = ref([]);
        const state = ref<ContactsListFormState>(initComponentState());
        const FGVirtualSelectTagsClass = virtualSelectClassName();
        const FGVirtualSelectNationalityClass = virtualSelectClassName();
        let abortController: AbortController = new AbortController();

        const prefix1DTO = ref({
            data: {
                selectedOption: initialEditedEntity.prefix_mobile_1 ?? QATAR_PHONE_PREFIX,
                key: "p1",
            },
            state: {
                shouldAddComponentClass: false,
            },
        });
        const prefix2DTO = ref({
            data: {
                selectedOption: initialEditedEntity.prefix_mobile_2 ?? QATAR_PHONE_PREFIX,
                key: "p2",
            },
            state: {
                shouldAddComponentClass: true,
            },
        });

        const onActivityPanelModalVisibilityChange = (evt: { isModalOpen: boolean }) => {
            state.value.isActivityPanelModalOpen = evt.isModalOpen;
        }

        const updateStateValidity = async (fieldName: string = "") => {
            const stateValue = state.value;
            const nextFormErrors: { [key: string]: string } = {
                ...state.value.form.errors,
            };

            for (const mandatoryField of [
                "name",
                "gender",
                "mobile_1",
            ] as Array<keyof Contact>) {
                delete nextFormErrors[mandatoryField];
                if (!stateValue.editedEntity[mandatoryField]) {
                    nextFormErrors[mandatoryField] = "This field is required";
                }
            }
            if (fieldName === "email_1") {
                if (!!stateValue.editedEntity.email_1) {
                    delete nextFormErrors["email_1"];
                    if (
                        !UtilService.isValidEmail(
                            stateValue.editedEntity.email_1
                        )
                    ) {
                        nextFormErrors["email_1"] = "This field is invalid";
                    } else {
                        if (state.value.isContactExistsCheckInProgress) {
                            abortController.abort();
                            abortController = new AbortController();
                        }
                        state.value.isContactExistsCheckInProgress = true;
                        await ContactsListService.checkContactExists(
                            {
                                email: stateValue.editedEntity.email_1,
                                id: stateValue.editedEntity.id,
                            },
                            abortController
                        ).then((data: { existingContactId?: number }) => {
                            state.value.isContactExistsCheckInProgress = false;
                            if (!!data && !!data.existingContactId) {
                                nextFormErrors[
                                    "email_1"
                                ] = `This contact already exists`;
                                state.value.form.errors = nextFormErrors;
                                state.value.existingAgentIdByEmail =
                                    data.existingContactId;
                            }
                        });
                    }
                }
            }
            if (fieldName === "mobile_1") {
                delete nextFormErrors["mobile_1"];
                var pattern = /^[0-9]{4,40}$/;
                if (!pattern.test(stateValue.editedEntity.mobile_1)) {
                    nextFormErrors[
                        "mobile_1"
                    ] = `The format of the field is invalid.`;
                } else {
                    if (!!stateValue.editedEntity.mobile_1) {
                        if (state.value.isContactExistsCheckInProgress) {
                            abortController.abort();
                            abortController = new AbortController();
                        }
                        state.value.isContactExistsCheckInProgress = true;
                        await ContactsListService.checkContactExistsByPhone(
                            {
                                phone: stateValue.editedEntity.mobile_1,
                                id: stateValue.editedEntity.id,
                            },
                            abortController
                        ).then((data: { existingContactId?: number }) => {
                            state.value.isContactExistsCheckInProgress = false;
                            if (!!data && !!data.existingContactId) {
                                nextFormErrors[
                                    "mobile_1"
                                ] = `This contact already exists`;
                                state.value.form.errors = nextFormErrors;
                                state.value.existingAgentIdByPhone =
                                    data.existingContactId;
                            }
                        });
                    }
                }
            }
            if (fieldName === "mobile_2") {
                delete nextFormErrors["mobile_2"];
                if (!!stateValue.editedEntity.mobile_2) {
                    var pattern = /^[0-9]{4,40}$/;
                    if (!pattern.test(stateValue.editedEntity.mobile_2)) {
                        nextFormErrors[
                            "mobile_2"
                        ] = `The format of the field is invalid.`;
                    } else {
                        if (state.value.isContactExistsCheckInProgress) {
                            abortController.abort();
                            abortController = new AbortController();
                        }
                        state.value.isContactExistsCheckInProgress = true;
                        await ContactsListService.checkContactExistsByPhone(
                            {
                                phone: stateValue.editedEntity.mobile_2,
                                id: stateValue.editedEntity.id,
                            },
                            abortController
                        ).then((data: { existingContactId?: number }) => {
                            state.value.isContactExistsCheckInProgress = false;
                            if (!!data && !!data.existingContactId) {
                                nextFormErrors[
                                    "mobile_2"
                                ] = `This contact already exists`;
                                state.value.form.errors = nextFormErrors;
                                state.value.existingAgentIdByPhone =
                                    data.existingContactId;
                            }
                        });
                    }
                }
            }

            if (!isEqual(state.value.form.errors, nextFormErrors)) {
                state.value.form.errors = nextFormErrors;
            }
        };
        const debouncedFieldChange = debounce((fieldName: string, evt) => {
            // validate object
            const nextEditedEntityValues: any = {
                ...state.value.editedEntity,
            };
            let valuesChanged = false;
            // update the state
            if (
                [
                    "id",
                    "company_name",
                    "date_of_birth",
                    "email_1",
                    "gender",
                    "mobile_1",
                    "mobile_2",
                    "name",
                    "occupation",
                    "position",
                    "qatar_id_no",
                    "type",
                    "residency",
                    "remarks",
                ].includes(fieldName)
            ) {
                if (nextEditedEntityValues[fieldName] !== evt.target.value) {
                    nextEditedEntityValues[fieldName] = evt.target.value;
                    valuesChanged = true;
                } else if (fieldName === "gender") {
                    valuesChanged = true;
                }
            } else if (["nationality_id"].includes(fieldName)) {
                if (nextEditedEntityValues[fieldName] !== evt) {
                    nextEditedEntityValues[fieldName] = evt;
                    valuesChanged = true;
                }
            } else if (fieldName === "tags") {
                if (!isEqual(nextEditedEntityValues[fieldName], evt)) {
                    nextEditedEntityValues[fieldName] = evt;
                    valuesChanged = true;
                }
            } else if (fieldName === "verified") {
                if (nextEditedEntityValues["verified"] !== evt.target.checked) {
                    nextEditedEntityValues["verified"] = evt.target.checked;
                    valuesChanged = true;
                }
            } else if (
                fieldName === "prefix_mobile_1" ||
                fieldName === "prefix_mobile_2"
            ) {
                if (nextEditedEntityValues[fieldName] !== evt.phone_prefix) {
                    nextEditedEntityValues[fieldName] = evt.phone_prefix;
                    valuesChanged = true;
                }
            }

            if (valuesChanged) {
                state.value.editedEntity = nextEditedEntityValues;
                updateStateValidity(fieldName);
                state.value.form.isPristine = false;
            }

            // compute lead valid message
            const leadInvalidFieldsArr = [];
            if (!nextEditedEntityValues.name) {
                leadInvalidFieldsArr.push("Name");
            }
            if (!nextEditedEntityValues.gender) {
                leadInvalidFieldsArr.push("Gender");
            }
            if (!nextEditedEntityValues.mobile_1) {
                leadInvalidFieldsArr.push("Mobile");
            }
            if (!nextEditedEntityValues.date_of_birth) {
                leadInvalidFieldsArr.push("Date of Birth");
            }
            if (!nextEditedEntityValues.qatar_id_no) {
                leadInvalidFieldsArr.push("QID No");
            }
            leadInvalidFields.value = leadInvalidFieldsArr;
        }, 300);

        function virtualSelectClassName() {
            return "r" + (Math.random() + 1).toString(36).substring(7);
        }

        const updateEditStateFromEditEntry = (editEntry: Contact) => {
            const nextState = initComponentState();
            if (!!editEntry && !!editEntry.id) {
                nextState.editedEntity = {
                    ...nextState.editedEntity,
                    id: editEntry.id,
                    name: editEntry.name,
                    gender: editEntry.gender,
                    position: editEntry.position,
                    prefix_mobile_1:
                        editEntry.prefix_mobile_1 ? editEntry.prefix_mobile_1 : QATAR_PHONE_PREFIX,
                    mobile_1: editEntry.mobile_1,
                    landlord_id: editEntry.landlord_id,
                    prefix_mobile_2:
                        editEntry.prefix_mobile_2 ? editEntry.prefix_mobile_2 : QATAR_PHONE_PREFIX,
                    mobile_2: editEntry.mobile_2,
                    email_1: editEntry.email_1,
                    company_name: editEntry.company_name,
                    nationality_id: editEntry.nationality_id,
                    verified: editEntry.verified, //verified or not
                    date_of_birth: editEntry.date_of_birth, //verified or not
                    qatar_id_no: editEntry.qatar_id_no,
                    occupation: editEntry.occupation,
                    residency: editEntry.residency,
                    tags: !!editEntry.tags ? editEntry.tags : [],
                    tag_labels: [],
                    reminders: editEntry.reminders,
                    leads_ids: editEntry.leads_ids,
                    deals_ids: editEntry.deals_ids,
                    type: "",
                    master_contact_id: editEntry.master_contact_id,
                    is_master_contact: editEntry.is_master_contact,
                    master_contact: editEntry.master_contact,
                    metadata: editEntry.metadata
                };
                if (
                    !!editEntry &&
                    !!editEntry.operation_history &&
                    editEntry.operation_history.length > 0
                ) {
                    nextState.operationHistory = editEntry.operation_history;
                }

                prefix1DTO.value.data.selectedOption =
                    editEntry.prefix_mobile_1 ? editEntry.prefix_mobile_1 : QATAR_PHONE_PREFIX;
                prefix2DTO.value.data.selectedOption =
                    editEntry.prefix_mobile_2 ? editEntry.prefix_mobile_2 : QATAR_PHONE_PREFIX;
            }

            if (!!editEntry && !!editEntry.tags) {
                nextState.tagsSelectorConfig = {
                    ...nextState.tagsSelectorConfig,
                    selectedValue: editEntry.tags,
                };
            }
            state.value = nextState;
        };

        const fetchDataAndUpdateState = async (): Promise<void> => {
            if (!route.path.endsWith("new")) {
                const leadId = route.query.leadId;
                const contactId = Number(route.params["id"]);
                const contact: Contact =
                    await ContactsListService.fetchSingleContact(contactId, leadId as string);
                updateEditStateFromEditEntry(contact);
            }
            Promise.all([
                fetchAndLoadTags(),
                ContactsListService.fetchNationalities().then(
                    (nationalities) =>
                    (state.value.nationalities = nationalities.map(
                        (item) => ({
                            label: item.value,
                            value: item.key,
                        })
                    ))
                ),
            ])
                .then(() => updateStateValidity())
                .finally(() => {
                    state.value.loading = false;
                    state.value.nationalitiesSelectorConfig.disabled = false;
                    state.value.tagsSelectorConfig.disabled = false;
                });
        };

        const fetchAndLoadTags = (): Promise<
            void | { label: string; value: number }[]
        > => {
            return ContactsListService.fetchTags()
                .then(
                    (data) =>
                    (state.value.tags = data.map((item) => ({
                        label: item.label,
                        value: item.id,
                    })))
                )
                .catch((err: any) => console.log(err));
        };

        const doSaveEntry = async () => {
            state.value.saving.isSuccess = false;
            state.value.saving.isFailure = false;
            state.value.saving.isAlertVisible = false;
            state.value.loading = true;
            state.value.tagsSelectorConfig.disabled = true;
            state.value.nationalitiesSelectorConfig.disabled = true;
            const data = state.value.editedEntity;

            try {
                const updatedContact: Contact =
                    await ContactsListService.saveEntry(data, route.query.leadId as string);
                state.value.saving.isSuccess = true;
                state.value.saving.isFailure = false;
                state.value.saving.isAlertVisible = true;
                if (route.name !== "contacts-list.edit") {
                    setTimeout(() => {
                        router.push({
                            name: "contacts-list.edit",
                            params: { id: updatedContact.id },
                        });
                    }, 3000);
                } else {
                    updateEditStateFromEditEntry(updatedContact);
                    state.value.editedEntity.remarks = "";
                    state.value.loading = false;
                    state.value.tagsSelectorConfig.disabled = false;
                    state.value.nationalitiesSelectorConfig.disabled = false;
                }
            } catch (Ex) {
                state.value.saving.isSuccess = false;
                state.value.saving.isFailure = true;
                state.value.saving.isAlertVisible = true;
            } finally {
                setTimeout(() => {
                    state.value.saving.isSuccess = false;
                    state.value.saving.isFailure = false;
                    state.value.saving.isAlertVisible = false;
                }, 3000);
            }
        };

        const linkAndRedirectToContact = async (section: string) => {
            const usedId =
                section == "email"
                    ? state.value.existingAgentIdByEmail
                    : state.value.existingAgentIdByPhone;
            try {
                await ContactsListService.connectContactWithUser(
                    usedId as number
                );
                await router.push({
                    name: "contacts-list.edit",
                    params: { id: usedId },
                });
            } catch (Ex) {
                console.error("ERR: ", Ex);
            }
        };

        onMounted(() => {
            fetchDataAndUpdateState();
        });

        watch(
            () => route.params,
            async (newParams) => {
                if (!!newParams.id) {
                    try {
                        const contact =
                            await ContactsListService.fetchSingleContact(
                                Number(newParams.id),
                                route.query.leadId as string
                            );
                        updateEditStateFromEditEntry(contact);
                    } catch (Ex) {
                    } finally {
                        state.value.loading = false;
                    }
                }
            }
        );

        return {
            state,
            leadParamId,
            debouncedFieldChange,
            FGVirtualSelectTagsClass,
            FGVirtualSelectNationalityClass,
            prefix1DTO,
            prefix2DTO,
            leadInvalidFields,

            doSaveEntry,
            linkAndRedirectToContact,
            onActivityPanelModalVisibilityChange
        };
    },

    components: {
        FGVirtualSelect,
        Alert,
        CountriesSelectorCrm,
        ContactDuplicates,
        ActivityPanel
    },
});

function initComponentState(): ContactsListFormState {
    const state: ContactsListFormState = {
        editedEntity: initialEditedEntity,
        nationalities: [],
        nationalitiesSelectorConfig: {
            disabled: true,
            multiple: false,
            search: true,
        },
        tagsSelectorConfig: {
            disabled: true,
            multiple: true,
            search: true,
            showValueAsTags: true,
            allowNewOption: true,
            selectedValue: [],
        },
        form: {
            isPristine: true,
            errors: {},
        },
        loading: true,
        isContactExistsCheckInProgress: false,
        operationHistory: [],
        tags: [],
        saving: {
            isSuccess: false,
            isFailure: false,
            isAlertVisible: false,
        },
        existingAgentIdByEmail: 0,
        existingAgentIdByPhone: 0,
        isActivityPanelModalOpen: false
    };
    return state;
}
</script>

<style scoped lang="scss">
.vscomp-ele {
    max-width: 100%;

    @media screen and (min-width: 1200px) {
        max-width: 50%;
    }
}

.nationality-select .vscomp-ele {
    max-width: 100%;
}

.errors-displayed .invalid-feedback {
    display: block;
}
</style>
