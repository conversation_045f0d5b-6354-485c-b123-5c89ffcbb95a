window.accountantSearchParams = {};
let filtersInitialized = false;
let automatizationsTable;

$(document).ready(function () {
    const addCustomFiltersAndSearch = (sSource, aoData, fnCallback) => {
        // if (filtersInitialized) {
            const urlStr = parseDTParams(aoData);

            Object.keys(window.accountantSearchParams).forEach((k) => {
                urlStr.push(`${k}=${window.accountantSearchParams[k]}`);
            });

            const data = urlStr.join("&");
            $.ajax({
                dataType: "json",
                url: `/crm/automatizations`,
                data,
                success: (input) => {
                    fnCallback(input);
                },
            });
        // }
    };

    $("#deals-invoices-list-filters th").each(function () {
        var title = $(this).text();
        var html =
            '<div class="input-group input-group-sm" >' +
            '<input type="text" class="form-control" id="deals-invoices-list-col-filter-' +
            $(this).data("col-index") +
            '" placeholder="' +
            title +
            '" />' +
            '<span class="input-group-addon"><span class="glyphicon glyphicon-filter"></span></span>' +
            "</div>";
        $(this).html(html);
    });

    automatizationsTable = $("#automatizations-list").DataTable({
        serverSide: true,
        lengthMenu: [
            [10, 25, 50, -1],
            [10, 25, 50, "All"]
        ],
        pageLength: 50,
        responsive: false,
        scrollY: "calc(100vh - 350px)",
        oLanguage: {
            sProcessing: "<img width='20' height='20' src='/images/ajax_spinner.gif'>"
        },
        processing: true,
        searching: true,
        order: false,
        columnDefs: [
            {
                name: "a.id",
                data: "id",
                targets: 0,
                responsivePriority: 1,
            },
            {
                name: "agent_names",
                data: "agent_names",
                targets: 1,
                responsivePriority: 2,
                render(data) {
                    return data ? `<div style="white-space: nowrap; width: 200px; overflow: hidden; text-overflow: ellipsis;">${data}</div>` : '-'
                }
            },
            {
                name: "tag_names",
                data: "tag_names",
                targets: 2,
                responsivePriority: 3,
                render(data) {
                    return data ? `<div style="white-space: nowrap; width: 200px; overflow: hidden; text-overflow: ellipsis;">${data}</div>` : '-'
                }
            },
            {
                name: "a.no_of_tasks_per_day",
                data: "no_of_tasks_per_day",
                targets: 3,
                responsivePriority: 4,
            },
            {
                name: "a.status",
                data: "status",
                targets: 4,
                responsivePriority: 5,
                render(data) {
                    return data ? `<div data-bs-toggle="tooltip" title="status"  class="text-nowrap font-small badge p-2 ps-2 pe-2" style="background-color: ${data.background_color}">${data.name}</div>` : '-';
                }
            },
            {
                name: "a.start_date",
                data: "start_date",
                targets: 5,
                responsivePriority: 6,
            },
            {
                name: "a.end_date",
                data: "end_date",
                targets: 6,
                responsivePriority: 7,
            },
            {
                targets: -1,
                data: null,
                className: "export-transient",
                render: function (data, type, full, row) {
                    const editLink = data.actions.canUpdate == true ? `<li><a class="dropdown-item" href="${data.actions.update}"><i class="bi bi-pen"></i> Edit </a></li>` : '';
                    const detailsLink = `<li><a class="dropdown-item" href="${data.actions.details}"><i class="bi bi-bar-chart"></i> Statistics </a></li>`;
                    return `<div class="dropdown" data-item-id="${data.DT_RowId}">
                    <div onclick="javascript: toggleActionMenu(${data.DT_RowId})" id="dropdownMenuButton${data.DT_RowId}">
                        <button class="btn btn-sm btn-light dropdown-toggle" type="button" >
                            Actions
                        </button>
                    </div>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton${data.DT_RowId}">
                        ${editLink}
                        ${detailsLink}
                    </ul>
                </div>`;
                },
                orderable: false,
                searchable: false,
                responsivePriority: 1
            },
        ],
        orderCellsTop: true,
        fnServerData: addCustomFiltersAndSearch,
    });
});

function onDealsAccountantFiltersParamsChange(filterParams) {
    window.accountantSearchParams = filterParams;
    if (!filtersInitialized) {
        filtersInitialized = true;
    }
    if (!!automatizationsTable) {
        automatizationsTable.draw();
    }
}