<?php

namespace App\Services;

use App\Models\Crm\PermissionsDef;
use Log;
use DB;

use App\Models\Crm\RolesDef;
use App\Models\Lead;
use App\Models\LeadAssignment;
use App\Models\LeadInteractionTracking;
use App\Models\LeadStatus;
use App\Models\QueryParamsDef;
use App\Models\User;

class AuthorizationService
{
    const CATEG_WEBSITE_SEO = "website-seo";
    const CATEG_INVENTORY = "inventory";
    const CATEG_CONTACTS = "contacts";
    const CATEG_CONTACTS_LANDLORDS = "contacts-landlords";
    const CATEG_CONTACTS_TENANTS = "contacts-tenants";
    const CATEG_LEADS = "leads";
    const CATEG_DEALS = "deals";

    private $operationHistoryService;

    public function __construct(OperationHistoryService $operationHistoryService)
    {
        $this->operationHistoryService = $operationHistoryService;
    }

    public function getAllowedActions($itemClass)
    {
        $user = auth()->user();
        $create = false;
        $read = false;
        $update = false;
        $delete = false;

        switch ($itemClass) {
            case self::CATEG_INVENTORY:
                $create = $update = $user->hasPermissionTo(PermissionsDef::INVENTORY_UPDATE);
                $delete = $user->hasPermissionTo(PermissionsDef::INVENTORY_DELETE);
                $read = true;
                break;
            case self::CATEG_CONTACTS:
            case self::CATEG_CONTACTS_LANDLORDS:
                $read = false;
                $create = $update = $user->hasAnyPermission([PermissionsDef::CONTACTS_READ, PermissionsDef::CONTACTS_UPDATE]);
                $delete = $user->hasPermissionTo(PermissionsDef::CONTACTS_DELETE);
                break;
            case self::CATEG_CONTACTS_TENANTS:
                $read = false;
                $create = $update = true; //$user->hasAnyPermission(['tenants_read', 'tenants_edit']);
                $delete = true; //$user->hasPermissionTo('tenants_delete');
                break;
            case self::CATEG_WEBSITE_SEO:
            case self::CATEG_LEADS:
                $create = $user->hasPermissionTo(PermissionsDef::LEADS_CREATE);
                $delete = $user->hasPermissionTo(PermissionsDef::LEADS_DELETE);
                $update = $user->hasPermissionTo(PermissionsDef::LEADS_UPDATE);
                $read = $user->hasPermissionTo(PermissionsDef::LEADS_READ);
                break;
            case self::CATEG_DEALS:
                $update = $user->hasAnyRole(RolesDef::OFFICE_MANAGER, RolesDef::AGENT) || auth()->user()->hasPermissionTo(PermissionsDef::HAS_ACCESS_TO_DEAL_FILES);
                if ($user->hasAnyRole(RolesDef::ACCOUNTANT)) {
                    $update = true;
                }
                $delete = $user->hasPermissionTo(PermissionsDef::DEALS_DELETE);
                $read = $user->hasAnyRole(RolesDef::OFFICE_MANAGER, RolesDef::AGENT, RolesDef::TEAM_LEADER, RolesDef::ACCOUNTANT);
                break;
        }

        $retArr = compact('create', 'read', 'update', 'delete');
        return $retArr;
    }

    public function getExtraClause($extraConfig = [], $count)
    {
        $retQ = "";

        if (!count($extraConfig)) {
            return $retQ;
        }

        if (!empty($extraConfig['wheres']) && is_array($extraConfig['wheres']) && count($extraConfig['wheres']) > 0) {
            $retQ .= " AND (";
            foreach ($extraConfig['wheres'] as $colName => $colQ) {
                if ($colName == 'budget') {
                    $retQ .= "budget_min <= " . $colQ . " AND ";
                    $retQ .= "budget_max >= " . $colQ . " AND ";
                } elseif ($colName == 'c.mobile_1') {
                    $retQ .= "(c.mobile_1 LIKE '" . $colQ . "%' OR ";
                    $retQ .= "c.mobile_2 LIKE '" . $colQ . "%') AND ";
                } elseif ($colName == 'c.name') {
                    $retQ .= "c.name LIKE '" . $colQ . "%' AND ";
                } elseif ($colName == 'move_in_date' || $colName == 'la.created_at') {
                    $retQ .= $colName . " = '" . $colQ . "' AND ";
                } else {
                    $retQ .= $colName . " LIKE '%" . $colQ . "%' AND ";
                }
            }

            $retQ = substr($retQ, 0, strlen($retQ) - 4);
            $retQ .= ") ";
        }
        $userId = auth()->user()->id;
        if (isset($extraConfig[QueryParamsDef::PROPERTY_TYPE])) {
            $propertyTypes = explode(',', $extraConfig[QueryParamsDef::PROPERTY_TYPE]);
            $propertyTypeConditions = array_map(function ($type) {
                return "JSON_CONTAINS(l.lead_property_types_filter, '\"$type\"')";
            }, $propertyTypes);

            $retQ .= "AND (" . implode(" OR ", $propertyTypeConditions) . ") ";
        }
        if (isset($extraConfig[QueryParamsDef::BEDROOMS])) {
            $bedroomFilter = $extraConfig[QueryParamsDef::BEDROOMS];
            $retQ .= "AND JSON_CONTAINS(l.lead_bedrooms_filter, '\"$bedroomFilter\"')";
        }
        if (isset($extraConfig[QueryParamsDef::OPERATION_TYPE])) {
            if ($extraConfig[QueryParamsDef::OPERATION_TYPE] === 'none') {
                $retQ .= "AND (filter_operation_type IS NULL OR filter_operation_type NOT IN ('rent', 'sale', 'buy'))";
            } else if ($extraConfig[QueryParamsDef::OPERATION_TYPE] === 'buy' || $extraConfig[QueryParamsDef::OPERATION_TYPE] === 'sale') {
                $retQ .= "AND filter_operation_type IN ('buy', 'sale')";
            } else {
                $retQ .= "AND filter_operation_type = '" . $extraConfig[QueryParamsDef::OPERATION_TYPE] . "'";
            }
        }
        if (isset($extraConfig[QueryParamsDef::AGENT])) {
            $retQ .= "AND la.user_id = '" . $extraConfig[QueryParamsDef::AGENT] . "'";
        }
        if (isset($extraConfig[QueryParamsDef::STATUS])) {
            if ($extraConfig[QueryParamsDef::STATUS] == 'COLD_WARM_HOT') {
                $retQ .= "AND ls.id IN (1, 5, 10)";
            } else {
                $retQ .= "AND ls.name LIKE '" . $extraConfig[QueryParamsDef::STATUS] . "'";
            }
        }
        if (isset($extraConfig[QueryParamsDef::LEAD_STATUSES]) && is_array($extraConfig[QueryParamsDef::LEAD_STATUSES])) {
            $leadStatuses = array_map(function ($status) {
                return "'" . addslashes($status) . "'";
            }, $extraConfig[QueryParamsDef::LEAD_STATUSES]);

            $retQ .= "AND ls.name IN (" . implode(",", $leadStatuses) . ") ";
        }
        if (isset($extraConfig[QueryParamsDef::LAST_CONTACT_DATE_FROM])) {
            $retQ .= "AND l.last_contact_date >= '" . $extraConfig[QueryParamsDef::LAST_CONTACT_DATE_FROM] . " 00:00:00'";
        }
        if (isset($extraConfig[QueryParamsDef::LAST_CONTACT_DATE_TO])) {
            $retQ .= "AND l.last_contact_date <= '" . $extraConfig[QueryParamsDef::LAST_CONTACT_DATE_TO] . " 23:59:59'";
        }
        if (isset($extraConfig[QueryParamsDef::LEAD_CREATION_DATE_FROM])) {
            $retQ .= "AND l.created_at >= '" . $extraConfig[QueryParamsDef::LEAD_CREATION_DATE_FROM] . " 00:00:00' ";
        }
        if (isset($extraConfig[QueryParamsDef::LEAD_CREATION_DATE_TO])) {
            $retQ .= "AND l.created_at <= '" . $extraConfig[QueryParamsDef::LEAD_CREATION_DATE_TO] . " 23:59:59' ";
        }
        if (isset($extraConfig[QueryParamsDef::LEAD_DECISION_DATE_FROM])) {
            $retQ .= "AND l.move_in_date >= '" . $extraConfig[QueryParamsDef::LEAD_DECISION_DATE_FROM] . " 00:00:00'";
        }
        if (isset($extraConfig[QueryParamsDef::LEAD_DECISION_DATE_TO])) {
            $retQ .= "AND l.move_in_date <= '" . $extraConfig[QueryParamsDef::LEAD_DECISION_DATE_TO] . " 23:59:59'";
        }
        if (isset($extraConfig[QueryParamsDef::LEAD_SOURCE])) {
            $retQ .= "AND lsou.id = '" . $extraConfig[QueryParamsDef::LEAD_SOURCE] . "'";
        }
        if (isset($extraConfig[QueryParamsDef::LEADS_CREATED_BY_AGENT])) {
            $retQ .= "AND l.created_by LIKE '" . $userId . "'";
        }
        if (isset($extraConfig[QueryParamsDef::LEADS_CREATED_BY_SOMEONE_ELSE])) {
            $retQ .= "AND (l.created_by IS NULL OR l.created_by NOT LIKE '" . $userId . "')";
        }
        if (isset($extraConfig[QueryParamsDef::PRICE_FROM])) {
            $retQ .= "AND l.filter_budget_min >= '" . $extraConfig[QueryParamsDef::PRICE_FROM] . "'";
        }
        if (isset($extraConfig[QueryParamsDef::PRICE_TO])) {
            $retQ .= "AND l.filter_budget_max <= '" . $extraConfig[QueryParamsDef::PRICE_TO] . "'";
        }
        if (isset($extraConfig[QueryParamsDef::LAST_CALL])) {
            $retQ .= "AND t.call_response = '" . $extraConfig[QueryParamsDef::LAST_CALL] . "'";
        }
        if (isset($extraConfig['needsUpdate']) && $extraConfig['needsUpdate']) {
            $retQ .= "AND DATEDIFF(NOW(), l.updated_at) > 5";
        }

        if (isset($extraConfig[QueryParamsDef::RATING])) {
            if ($extraConfig[QueryParamsDef::RATING] === 'non_rated') {
                $retQ .= "AND (l.rating IS NULL OR l.rating = '')";
            } else {
                $retQ .= "AND l.rating = '" . $extraConfig[QueryParamsDef::RATING] . "'";
            }
        }

        if (isset($extraConfig[QueryParamsDef::UTM_SOURCES]) && is_array($extraConfig[QueryParamsDef::UTM_SOURCES])) {
            $utmSources = array_map(function ($source) {
                return "'" . addslashes($source) . "'";
            }, $extraConfig[QueryParamsDef::UTM_SOURCES]);

            $retQ .= "AND l.utm_source IN (" . implode(",", $utmSources) . ") ";
        }

        if (isset($extraConfig[QueryParamsDef::UTM_CAMPAIGNS]) && is_array($extraConfig[QueryParamsDef::UTM_CAMPAIGNS])) {
            $utmCampaigns = array_map(function ($campaign) {
                return "'" . addslashes($campaign) . "'";
            }, $extraConfig[QueryParamsDef::UTM_CAMPAIGNS]);

            $retQ .= "AND l.utm_campaign IN (" . implode(",", $utmCampaigns) . ") ";
        }
        if (isset($extraConfig[QueryParamsDef::HAS_DEALS]) && $extraConfig[QueryParamsDef::HAS_DEALS] === 'true') {
            $retQ .= "AND EXISTS (SELECT 1 FROM deals d WHERE d.lead_id = l.id AND d.deleted_at IS NULL) ";
        }

        if (!empty($extraConfig['q'])) {
            $searchableFields = [
                'platform_from',
                'desired_area',
                'requirements',
                'filter_budget_min',
                'filter_budget_max',
                'filter_operation_type',
                'c.mobile_1',
                'c.mobile_2',
                'c.name',
                'c.email_1',
                'c.email_2',
                'u.name',
                'pt.name',
                'g.name',
                'lsou.name',
                'l.inquired_ref_no'
            ];
            $extraWhere = "";

            $q = $extraConfig['q'];
            foreach ($searchableFields as $f) {
                if ($f == 'l.inquired_ref_no') {
                    $extraWhere .= $f . " LIKE '%$q%' OR ";
                } else {
                    $extraWhere .= $f . " LIKE '$q%' OR ";
                }
            }

            $extraWhere = substr($extraWhere, 0, strlen($extraWhere) - 3);
            $retQ .= " AND ($extraWhere) ";
        }

        if (!$count) {
            $retQ .= " GROUP BY l.id ";
        }

        if (!$count) {
            if (!empty($extraConfig['sort'])) {
                if ($extraConfig['sort'] === 'l.rating') {
                    $retQ .= "ORDER BY
                    CASE
                        WHEN l.rating IS NULL OR l.rating = '' THEN 2
                        ELSE 1
                    END,
                    CASE
                        WHEN l.rating = 'A' THEN 1
                        WHEN l.rating = 'B' THEN 2
                        WHEN l.rating = 'C' THEN 3
                        WHEN l.rating = 'D' THEN 4
                        WHEN l.rating = 'E' THEN 5
                        WHEN l.rating = 'F' THEN 6
                        WHEN l.rating = 'X' THEN 7
                        ELSE 8
                    END";

                    if (!empty($extraConfig['dir'])) {
                        $retQ .= " " . strtoupper($extraConfig['dir']) . " ";
                    }
                } else {
                    $retQ .= "ORDER BY " . $extraConfig['sort'] . " ";

                    if (!empty($extraConfig['dir'])) {
                        $retQ .= " " . strtoupper($extraConfig['dir']) . " ";
                    }
                }
            } else {
                $retQ .= "ORDER BY
                CASE
                    WHEN l.lead_status_id = 11 THEN 1
                    ELSE 2
                END,
                CASE
                    WHEN l.rating IS NULL OR l.rating = '' THEN 2
                    ELSE 1
                END,
                CASE
                    WHEN l.rating = 'A' THEN 1
                    WHEN l.rating = 'B' THEN 2
                    WHEN l.rating = 'C' THEN 3
                    WHEN l.rating = 'D' THEN 4
                    WHEN l.rating = 'E' THEN 5
                    WHEN l.rating = 'F' THEN 6
                    WHEN l.rating = 'X' THEN 7
                    ELSE 8
                END
                ";
            }
        }

        return $retQ;
    }

    public function getLeadsSQL($user, $count = false, $offset = 0, $limit = 10, $extraConfig = [])
    {
        $userIsMatrixAgentManager = $user->hasRole(RolesDef::MATRIX_AGENT_MANAGER);
        $userIsTeamLeader = $user->hasRole(RolesDef::TEAM_LEADER);
        $userIsOfficeManager = $user->hasRole(RolesDef::OFFICE_MANAGER);
        $agentsForAssignment = [];

        $NOT_QUALIFIED_STATUS_ID = 13;
        $GHOSTED_STATUS_ID = 21;
        $CLOSED_LOSE_STATUS_ID = 19;
        $CLOSED_WON_STATUS_ID = 18;

        if ($userIsMatrixAgentManager) {
            $agentsForAssignment = User::where('created_by', $user->id)
                ->get()
                ->map(function ($item) {
                    return $item->id;
                })
                ->toArray();
            $agentsForAssignment[] = $user->id;
        }
        if ($userIsTeamLeader) {
            $agentsForAssignment = User::where('team_leader_id', $user->id)
                ->get()
                ->map(function ($item) {
                    return $item->id;
                })
                ->toArray();
            $agentsForAssignment[] = $user->id;
        }
        $leadsQueryHead = 'l.id, l.priority, l.platform_from, l.status, l.client_status,
            l.location_id, l.budget_min, l.budget_max, l.move_in_date,
            l.requirements, l.created_at, l.updated_at,
            l.filter_operation_type,
            l.filter_budget_min,
            l.filter_budget_max,
            l.last_contact_date,
            l.inquired_ref_no,
            l.leads_request,
            l.filter_bedrooms,
            l.utm_source,
            l.utm_medium,
            l.utm_campaign,
            l.utm_term,
            l.utm_content,
            pt.name,
            c.id contact_id,
            COALESCE(c.prefix_mobile_1, \'+974\') as contact_prefix_mobile_1, COALESCE(c.prefix_mobile_2, \'+974\') as contact_prefix_mobile_2,
            c.mobile_1 contact_mobile_1, c.mobile_2 contact_mobile_2,
            c.name contact_name,
            c.email_1 contact_email_1, c.email_2 contact_email_2,
            la.id assignment_id,
            la.deleted_at assignment_deleted_at,
            DATE_FORMAT(la.created_at, "%m/%d/%y %H:%i") assignment_created_at,
            u.id assignment_user_id,
            u.name assignment_user_name,
            u.color assignment_user_color,
            u.deleted_at assignment_user_deleted_at,
            us.name created_by,
            pt.name property_type,
            lsou.name platform_from,
            l.lead_status_id as status,
            l.rating as rating,
            g.name location_id,
            r.id reminder,
            ls.name lead_status_name,
            ls.background_color lead_status_background_color,
            t.call_response as last_call,
            t.updated_at as last_call_timing,
            lra2.user_id as reassignation_agent,
            ur.name as user_reassignation_name,
            us.name as agent_name,
            lra2.id as lead_reassignation_id,
            COALESCE(lit_count.interactions_count, 0) AS interactions_count ';

        $leadsQueryCountHead = 'COUNT(l.id) as records_no';
        $leadAssignmentClause = '';

        $extraClause = self::getExtraClause($extraConfig, $count);

        if (!isset($extraConfig['vt']) || empty($extraConfig['vt'])  || $extraConfig['vt'] === 'personal') {
            // NOT_QUALIFIED / GHOSTED should not appear on personal list as per https://fgreal.atlassian.net/browse/KAN-212
            $leadAssignmentClause = ' AND la.user_id = ' . $user->id . ' AND la.deleted_at IS NULL AND (l.lead_status_id IS NULL OR l.lead_status_id NOT IN ("' . $NOT_QUALIFIED_STATUS_ID . '", "' . $GHOSTED_STATUS_ID . '", "' . $CLOSED_LOSE_STATUS_ID . '"))';
        } elseif ($extraConfig['vt'] === 'master') {
            if (!$userIsOfficeManager) {
                if ($userIsMatrixAgentManager) {
                    $leadAssignmentClause = ' AND l.created_by IN (' . implode(", ", $agentsForAssignment) . ')';
                } else {
                    // masterlist shoud contain all personal leads + everything that is NOT_QUALIFIED, GHOSTED, CLOSED_LOSE, CLOSED_WON - https://fgreal.atlassian.net/browse/KAN-212 -  CLOSED_WON removed today as per Usama request 24.10.2024
                    $leadAssignmentClause = ' AND (la.lead_id IS NULL OR l.lead_status_id IN ("' . $NOT_QUALIFIED_STATUS_ID . '", "' . $GHOSTED_STATUS_ID . '", "' . $CLOSED_LOSE_STATUS_ID . '") OR la.deleted_at IS NOT NULL) ';
                }
            }
        } elseif ($extraConfig['vt'] === 'team_list') {
            if ($userIsTeamLeader) {
                $leadAssignmentClause = ' AND la.user_id IN (' . implode(", ", $agentsForAssignment) . ')';
            }
        } else if ($extraConfig['vt'] === 'reassigned') {
            $leadAssignmentClause = ' AND lra.user_id IN (' . $user->id . ')';
        }

        $isLeadsDashboard = array_key_exists('dashboardType', $extraConfig) && $extraConfig['dashboardType'] == 'leadsDashboard';
        if ($isLeadsDashboard && array_key_exists('status', $extraConfig)) {
            if ($extraConfig['status'] === 'NOT_QUALIFIED') {
                $leadAssignmentClause = ' AND (la.lead_id IS NULL OR la.deleted_at IS NOT NULL)';
                unset($extraConfig['status']);
            } else if ($extraConfig['vt'] === 'master' && in_array($extraConfig['status'], ['NEW', 'INITIAL_CONTACT', 'COLD_WARM_HOT', 'OFFER_SENT', 'MEETING_SCHEDULED', 'VIEWING_SCHEDULED', 'OTHER_OPTIONS', 'FOLLOW_UP', 'OFFER_NEGOTIATION', 'CONTRACT_PENDING', 'CLOSED_WON', 'CLOSED_LOSE', 'POST_CLOSURE', 'GENERAL_INQUIRY', 'GHOSTED', 'FAR_MOVING_DECISION'])) {
                $leadAssignmentClause = ' AND (la.lead_id IS NOT NULL AND la.deleted_at IS NULL)';
            }
        }

        // if ($extraConfig['vt'] != 'reassigned') {
        //     $leadAssignmentClause .= ' AND lra.id IS NULL ';
        // }

        $resultQ = 'SELECT ' . ($count ? $leadsQueryCountHead : $leadsQueryHead) . ' FROM leads l ' .
            ($extraConfig['vt'] === 'reassigned' ? 'JOIN lead_reassignations lra ON lra.lead_id = l.id ' : 'LEFT JOIN lead_reassignations lra ON lra.lead_id = l.id ') .
            (!$count ? 'LEFT JOIN lead_reassignations lra2 ON lra2.lead_id = l.id ' : '') .
            'LEFT JOIN contacts c ON c.id = l.contact_id ' .
            'LEFT JOIN users us ON us.id = l.created_by ' .
            'LEFT JOIN (
            SELECT MAX(id) as max_id, lead_id FROM lead_assignments GROUP BY lead_id
        ) la_max_id ON la_max_id.lead_id = l.id ' .
            'LEFT JOIN lead_assignments la ON la.id = la_max_id.max_id ' .
            'LEFT JOIN users u ON u.id = la.user_id ' .
            'LEFT JOIN users ur ON ur.id = lra.user_id ' .
            'LEFT JOIN lead_status ls ON ls.id = l.lead_status_id ' .
            'LEFT JOIN property_types pt ON pt.id = l.filter_property_type ' .
            'LEFT JOIN lead_sources lsou ON lsou.id = l.platform_from ' .
            'LEFT JOIN geography g ON g.id = l.location_id ' .
            (!$count ? 'LEFT JOIN reminders r ON r.object_id = l.id AND r.object_type = "leads" ' : '') .
            'LEFT JOIN (
            SELECT t1.* FROM tasks t1 WHERE t1.id = (
                SELECT MAX(t2.id) FROM tasks t2 WHERE t2.object_id = t1.object_id AND t2.object_type = "lead" AND t2.type = "Call Log"
            )
        ) t ON t.object_id = l.id ' .
            (!$count ? 'LEFT JOIN (
            SELECT lead_id, COUNT(*) AS interactions_count FROM lead_interactions_tracking GROUP BY lead_id
        ) lit_count ON lit_count.lead_id = l.id ' : '') .
            'WHERE ' . (isset($extraConfig[QueryParamsDef::HAS_DEALS]) && $extraConfig[QueryParamsDef::HAS_DEALS] === 'true' ? '' : 'l.state IS NULL AND ') . ' l.deleted_at IS NULL ' .
            $leadAssignmentClause . ' ' .
            $extraClause;

        if (!$count && $limit != -1) {
            $resultQ = "$resultQ LIMIT $offset, $limit";
        }

        return $resultQ;
    }

    public function getVisibleItems($itemClass, $user = null, $offset = 0, $limit = 10, $extraConfig = [])
    {
        if ($user == null) {
            $user = auth()->user();
        }

        $items = collect([]);

        switch ($itemClass) {
            case self::CATEG_LEADS:
                if ($limit == -1) {
                    // Get count first even for limit -1 to ensure we have the correct count
                    $count = DB::select(self::getLeadsSQL($user, true, $offset, $limit, $extraConfig));
                    $data = DB::select(self::getLeadsSQL($user, false, $offset, $limit, $extraConfig));
                    // Log::info("FILTERED LEADS FOR USER " . $user->email);
                    $items = ['items' => $data, 'count' => $count[0]->records_no];
                } else {
                    $count = DB::select(self::getLeadsSQL($user, true, $offset, $limit, $extraConfig));
                    $data = DB::select(self::getLeadsSQL($user, false, $offset, $limit, $extraConfig));
                    // Log::info("FILTERED LEADS FOR USER " . $user->email);
                    $items = ['items' => $data, 'count' => $count[0]->records_no];
                }

                break;
        }

        return $items;
    }

    public function filterReminders($reminders, $user = null)
    {
        if ($user == null) {
            $user = auth()->user();
        }

        if ($user->hasRole(RolesDef::OFFICE_MANAGER)) {
            return $reminders;
        }

        return $reminders->filter(function ($reminder) use ($user) {
            return $reminder->created_by == $user->id;
        });
    }

    public function getDuplicatesContactInformation($items)
    {
        $itemsMap = array_map(function ($item) {
            return $item->contact_mobile_1;
        }, $items['items']);

        $itemsMap = array_filter($itemsMap);

        $leads = Lead::with('contact', 'assignment.user', 'leadStatus', 'propertyType')
            ->whereHas('contact', function ($query) use ($itemsMap) {
                $query->whereNotNull('mobile_1')->whereIn('mobile_1', $itemsMap);
            })->get();

        $duplicateLeadIds = collect();

        $allLeads = $leads->filter(function ($lead) use ($duplicateLeadIds) {
            if (!$duplicateLeadIds->contains($lead->id)) {
                $duplicateLeadIds->push($lead->id);
                return true;
            }
            return false;
        });

        return $allLeads;
    }

    public function leadInteractionsTracking($leadId, $action, $userId = null)
    {
        LeadInteractionTracking::create([
            'lead_id' => $leadId,
            'user_id' => $userId,
            'action' => $action,
            'created_by' => auth()->user() ? auth()->user()->id : null,
        ]);
    }

    public function moveNotUpdatedLeads()
    {
        $newStatusId = LeadStatus::where('name', LeadStatus::STATUS_NEW)->first()->id;
        $newNotUpdatedStatusId = LeadStatus::where('name', LeadStatus::STATUS_NEW_NOT_UPDATED)->first()->id;

        $now = now();
        $oneHourAgo = $now->copy()->subHour();

        $leadsForTL = Lead::where('lead_status_id', $newStatusId)
            ->whereHas('latestAssignment', function ($query) use ($oneHourAgo) {
                $query->where('created_at', '<', $oneHourAgo)
                    ->whereNull('deleted_at')
                    ->whereHas('user', function ($userQuery) {
                        $userQuery->whereHas('roles', function ($roleQuery) {
                            $roleQuery->where('name', RolesDef::AGENT);
                        });
                    });
            })
            ->where(function ($query) {
                $query->whereDoesntHave('leadSource')
                    ->orWhereHas('leadSource', function ($subQuery) {
                        $subQuery->where('name', '!=', 'Personal Contact');
                    });
            })
            ->with(['latestAssignment.user.teamLeader', 'contact'])
            ->get();

        foreach ($leadsForTL as $lead) {
            $agent = $lead->latestAssignment->user;
            $teamLeader = $agent->teamLeader;

            if (!$teamLeader) {
                $lead->latestAssignment->delete();
                $lead->lead_status_id = $newNotUpdatedStatusId;
                $lead->updated_at = now();
                $lead->save();
                $this->operationHistoryService->addOperationHistory($lead, '[Automation] The lead was unassigned from [' . $agent->name . ']. Reassignment skipped');
                \Log::info('[Automation] The lead [' . $lead->id . '] was unassigned from [' . $agent->name . ']');
                continue;
            }

            try {
                $lead->latestAssignment->delete();

                $newAssignment = new LeadAssignment([
                    'user_id' => $teamLeader->id,
                    'lead_id' => $lead->id
                ]);
                $newAssignment->save();
                $lead->updated_at = now();
                $lead->save();
                $this->operationHistoryService->addOperationHistory($lead, '[Automation] The lead was unassigned from [' . $agent->name . '] and assigned to [' . $teamLeader->name . ']');
                \Log::info('[Automation] The lead [' . $lead->id . '] was unassigned from [' . $agent->name . '] and assigned to [' . $teamLeader->name . ']');
            } catch (\Exception $e) {
                \Log::error("Error reassigning lead ID {$lead->id} from agent to TL: " . $e->getMessage());
            }
        }

        $leadsForMasterlist = Lead::where('lead_status_id', $newStatusId)
            ->whereHas('latestAssignment', function ($query) use ($oneHourAgo) {
                $query->where('created_at', '<', $oneHourAgo)
                    ->whereNull('deleted_at')
                    ->whereHas('user', function ($userQuery) {
                        $userQuery->whereHas('roles', function ($roleQuery) {
                            $roleQuery->where('name', RolesDef::TEAM_LEADER);
                        });
                    });
            })
            ->where(function ($query) {
                $query->whereDoesntHave('leadSource')
                    ->orWhereHas('leadSource', function ($subQuery) {
                        $subQuery->where('name', '!=', 'Personal Contact');
                    });
            })
            ->with(['latestAssignment.user', 'contact'])
            ->get();


        foreach ($leadsForMasterlist as $lead) {
            $teamLeader = $lead->latestAssignment->user;

            try {
                $lead->lead_status_id = $newNotUpdatedStatusId;
                $lead->save();

                $lead->latestAssignment->delete();
                Log::info('[Automation] The lead [' . $lead->id . '] was unassigned from [' . $teamLeader->name . '] and moved to masterlist');
                $this->operationHistoryService->addOperationHistory($lead, '[Automation] The lead was unassigned from [' . $teamLeader->name . '] and moved to masterlist');
            } catch (\Exception $e) {
                \Log::error("Error moving lead ID {$lead->id} to masterlist: " . $e->getMessage());
            }
        }
    }

    // private function needsJoinForCount($extraConfig, $tableAlias)
    // {
    //     $filtersUsing = [
    //         'c' => ['c.mobile_1', 'c.name'],
    //         'la' => [QueryParamsDef::AGENT],
    //     ];

    //     if (!isset($filtersUsing[$tableAlias])) {
    //         return false;
    //     }

    //     foreach ($filtersUsing[$tableAlias] as $key) {
    //         if (isset($extraConfig[$key]) || (isset($extraConfig['wheres']) && array_key_exists($key, $extraConfig['wheres']))) {
    //             return true;
    //         }
    //     }

    //     return false;
    // }
}
