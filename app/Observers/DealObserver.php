<?php

namespace App\Observers;

use App\Models\Crm\Deal;
use App\Services\DealNotificationService;
use Illuminate\Support\Facades\Log;

class DealObserver
{
    protected $dealNotificationService;

    public function __construct(DealNotificationService $dealNotificationService)
    {
        $this->dealNotificationService = $dealNotificationService;
    }

    /**
     * Handle the Deal "created" event.
     *
     * @param  \App\Models\Crm\Deal  $deal
     * @return void
     */
    public function created(Deal $deal)
    {
        Log::info("Deal #" . $deal->id . " was created");
    }

    /**
     * Handle the Deal "updated" event.
     *
     * @param  \App\Models\Crm\Deal  $deal
     * @return void
     */
    public function updated(Deal $deal)
    {
        // Check if deal status changed to approved
        if ($deal->isDirty('deal_status') && $deal->deal_status === Deal::STATUS_APPROVED) {
            Log::info("Deal #" . $deal->id . " status changed to approved");

            // Check if the feature flag is enabled
            if (env('FEATURE_REMINDER_FOR_GOOGLE_REVIEW', false)) {
                Log::info("Feature flag enabled, triggering deal approval notifications for deal #" . $deal->id);
                $this->dealNotificationService->handleDealApprovalNotifications($deal);
            } else {
                Log::info("Feature flag disabled, skipping deal approval notifications for deal #" . $deal->id);
            }
        }
    }

    /**
     * Handle the Deal "deleted" event.
     *
     * @param  \App\Models\Crm\Deal  $deal
     * @return void
     */
    public function deleted(Deal $deal)
    {
        Log::info("Deal #" . $deal->id . " was deleted");
    }

    /**
     * Handle the Deal "restored" event.
     *
     * @param  \App\Models\Crm\Deal  $deal
     * @return void
     */
    public function restored(Deal $deal)
    {
        Log::info("Deal #" . $deal->id . " was restored");
    }

    /**
     * Handle the Deal "force deleted" event.
     *
     * @param  \App\Models\Crm\Deal  $deal
     * @return void
     */
    public function forceDeleted(Deal $deal)
    {
        Log::info("Deal #" . $deal->id . " was force deleted");
    }
}
