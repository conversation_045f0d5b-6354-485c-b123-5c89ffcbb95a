<?php

namespace App\Models\Email;

class NewPasswordEmail extends GenericEmail
{
    private $emailAddress;
    private $newPassword;
    private $userName;

    public function __construct($emailAddress, $newPassword, $userName)
    {
        $this->emailAddress = $emailAddress;
        $this->newPassword = $newPassword;
        $this->userName = $userName;
    }

    public function getSubject()
    {
        return "Your password has been reset";
    }

    public function getTemplateName()
    {
        return 'components.mail.new-password-email';
    }

    public function getTo()
    {
        return [$this->emailAddress];
    }

    public function getTemplateVars()
    {
        return [
            'newPassword' => $this->newPassword,
            'userName' => $this->userName,
        ];
    }
}
