<?php

use App\Http\Controllers\Admin\LeadSourcesController;
use App\Http\Controllers\API\LeadSourceController;
use App\Http\Controllers\API\MarketingCampaignsController;
use App\Http\Controllers\API\UserRatingController;
use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\Front\PropertiesController;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\API\APIPropertiesController;
use App\Http\Controllers\API\APIEnquiryController;
use App\Http\Controllers\API\FrontendProvisionController;
use App\Http\Controllers\API\ListingController;
use App\Http\Controllers\API\APINewsletterController;
use App\Http\Controllers\API\UserController;
use App\Http\Controllers\API\AgentController;
use App\Http\Controllers\API\AgentsController;
use App\Http\Controllers\API\APICountryController;
use App\Http\Controllers\API\TourController;
use App\Http\Controllers\API\OptionsController;
use App\Http\Controllers\API\APIGeographyController;
use App\Http\Controllers\API\APIGeographyTowerController;
use App\Http\Controllers\API\PropertyType as APIPropertyTypeController;
use App\Http\Controllers\API\PropertyView as APIPropertyViewController;
use App\Http\Controllers\API\ListingRequestsController;
use App\Http\Controllers\API\ReviewController;
use App\Http\Controllers\API\Inventory as APIInventoryController;
use App\Http\Controllers\API\InventoryWrite as APIInventoryWriteController;
use App\Http\Controllers\API\APIMobileInteractionController;
use App\Http\Controllers\API\MobileSettingsController;
use App\Http\Controllers\API\SEOContentController;
use App\Http\Controllers\API\APIFeedbackController;
use App\Http\Controllers\API\APIPropertyFinderCommController;
use App\Http\Controllers\API\BookingController;
use App\Http\Controllers\API\BrokerLandlord;
use App\Http\Controllers\API\ContactsListController;
use App\Http\Controllers\API\HotelRatePlanController;
use App\Http\Controllers\API\HotelsController;
use App\Http\Controllers\API\PasswordResetController;
use App\Http\Controllers\API\PublicLeadsController;
use App\Http\Controllers\API\SEOController as APISEOController;
use App\Http\Controllers\API\TrackController;
use App\Http\Controllers\Crm\InventoryControllerCrm;
use App\Http\Controllers\I18NController;
use App\Http\Controllers\API\RoomsController;
use App\Http\Controllers\API\RoomTypesController;
use App\Http\Controllers\API\APIDummyController;
use App\Http\Controllers\API\TasksController;
use App\Http\Controllers\Crm\AutomatizationController;
use App\Http\Controllers\Crm\ContactsHandlerController;
use App\Http\Controllers\Crm\MetricsController;
use App\Http\Controllers\LoggingController;
use App\Http\Controllers\SmsController;
use App\Http\Controllers\TwilioController;
use App\Http\Controllers\TwilioWebhookController;
use App\Http\Controllers\UserLoginController;
use App\Http\Controllers\WhatsAppWebhookController;
use App\Models\UserWidget;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::post('/login', [AuthController::class, 'login']);
Route::post('/user', [UserController::class, 'register']);
Route::get('/users', [UserController::class, 'getUsers']);
Route::get('/{locale}/property-details/{id}', [PropertiesController::class, 'preview']);
Route::post('agent-contact', [APIPropertiesController::class, 'agentContact'])->name('agentContact');
Route::post('enquiry', [APIEnquiryController::class, 'enquiry'])->name('enquiry.post');
Route::post('qatar-2022', [APIEnquiryController::class, 'qatar2022'])->name('qatar2022.post');
Route::post('feedback', [APIFeedbackController::class, 'collectFeedback'])->name('feedback.post');
Route::post('newsletter', [APINewsletterController::class, 'newsletter'])->name('newsletter.post');
Route::get('agent-rating/{userId}', [UserRatingController::class, 'getUserRating'])->name('agent-rating.index');
Route::post('agent-rating/connect-with-agent', [UserRatingController::class, 'connectWithAgent'])->name('agent-rating.connectWithAgent');
Route::post('user-rating/{userId}/rating/{ratingId}/switch', [UserRatingController::class, 'switchApprovalForRating'])->name('agent-rating.switch');
Route::post('agent-rating', [UserRatingController::class, 'store'])->name('agent-rating.store');
Route::post('reset-password', [PasswordResetController::class, 'resetPasswordEmail']);
Route::post('reset-password-check-code', [PasswordResetController::class, 'checkValidityCode']);
Route::post('change-password', [PasswordResetController::class, 'changePassword']);
Route::post('track', [TrackController::class, 'track']);
Route::post('sms-auth', [SmsController::class, 'authenticate']);
Route::post('sms', [SmsController::class, 'send']);

// new routes
Route::get('start-box', [FrontendProvisionController::class, 'searchBoxData'])->name('start-box.get');
Route::get('start-box-lite', [FrontendProvisionController::class, 'searchBoxDataLite'])->name('start-box-lite.get');
Route::post('users-dummy', [APIDummyController::class, 'index'])->name('api-dummy-controller-index.post');

// whatsapp
// Route::match(['GET', 'POST'], '/whatsapp/webhook', [WhatsAppWebhookController::class, 'handle']);
Route::prefix('webhooks')->group(function () {
    Route::get('whatsapp', [WhatsAppWebhookController::class, 'verify'])
        ->name('whatsapp.webhook.verify');
    
    Route::post('whatsapp', [WhatsAppWebhookController::class, 'handle'])
        ->name('whatsapp.webhook.handle');
});

// duplicate route, for mobile only
Route::get('autocomplete', [FrontendProvisionController::class, 'autocomplete'])->name('autocomplete');
Route::prefix('listing')->middleware(['sessions'])->group(function () {
    Route::get('m', [ListingController::class, 'indexMobile'])->name('listing.get');
    Route::get('', [ListingController::class, 'index'])->name('listing.get');
    Route::get('map', [ListingController::class, 'listingsMap'])->name('listing.getListingsMap');
    Route::prefix('{id}')->group(function () {
        Route::get('', [ListingController::class, 'showLite']);
        Route::get('related', [ListingController::class, 'related']);
        Route::post('share-payload', [ListingController::class, 'sharePayload']);
        Route::get('seo-content', [ListingController::class, 'seoContent']);
        Route::get('seo-object', [ListingController::class, 'seoObject']);
        Route::post('seo-object', [ListingController::class, 'postSeoObject']);
        Route::prefix('requests')->group(function () {
            Route::post('', [ListingRequestsController::class, 'create'])->name('listing.requests.post');
            Route::get('', [ListingRequestsController::class, 'get'])->name('listing.requests.get');
        });
    });
});
Route::prefix('hooks')->group(function () {
    Route::post('pf-property-new', [APIPropertyFinderCommController::class, 'hooksPropertyNew'])->name('hooks.pf.property.new');
    // Route::get('pf-property-new', [APIPropertyFinderCommController::class, 'hooksPropertyNewGet'])->name('hooks.pf.property.new.get');
    Route::post('pf-property-status', [APIPropertyFinderCommController::class, 'hooksPropertyStatus'])->name('hooks.pf.property.status');
    Route::post('pf-property-state', [APIPropertyFinderCommController::class, 'hooksPropertyState'])->name('hooks.pf.property.state');
});

Route::prefix('public-leads')->group(function () {
    Route::post('', [PublicLeadsController::class, 'store']);
});

Route::group(['middleware' => ['auth.api', 'verified']], function () {
    Route::prefix('profile')->group(function () {
        Route::get('', [UserController::class, 'profile'])->name('profile.get');
        Route::get('listing-saved', [ListingController::class, 'getProfileSavedListings'])->name('profile.listing-saved.get');
        Route::post('listing-saved', [ListingController::class, 'addProfileSavedListing'])->name('profile.listing-saved.add');
        Route::delete('listing-saved/{id}', [ListingController::class, 'deleteProfileSavedListing'])->name('profile.listing-saved.delete');
        Route::patch('', [UserController::class, 'profileUpdate']);
        Route::patch('edit', [UserController::class, 'profileEdit']);
        Route::get('notifications', [UserController::class, 'notifications'])->name('profile.notifications.get');
        Route::post('notifications/{id}/read', [UserController::class, 'markNotificationAsRead']);

        Route::get('recommended', [UserController::class, 'recommended'])->name('profile.recommended.get');
        Route::get('tours', [UserController::class, 'getTours'])->name('profile.tours.get');

        Route::get('dashboard/performance', [UserController::class, 'dashboardPerformance'])->name('profile.dashboard.performance.get');
    });

    Route::post('notification-tokens', [UserController::class, 'saveNotificationTokens'])->name('notification.tokens.post');
    Route::post('searches', [UserController::class, 'saveSearch'])->name('save.searches.post');
    Route::delete('user/searches/{id}', [UserController::class, 'deleteSearchId'])->name('search.id.delete');

    Route::prefix('agent')->group(function () {
        Route::get('{id}', [AgentController::class, 'show']);
        Route::get('{id}/tours', [AgentController::class, 'getTours']);
        Route::post('{id}/review', [ReviewController::class, 'store']);
    });
    Route::prefix('agents')->group(function () {
        Route::get('', [AgentsController::class, 'index'])->name('agents.index');
    });
    Route::prefix('crm')->group(function () {
        Route::get('landlord/check-exists', '\App\Http\Controllers\API\BrokerLandlord@getCheckLandlordExists');
        Route::get('landlord/search', [BrokerLandlord::class, 'search'])->name('broker-landlord.search');
        Route::post("landlord/{id}/connect", "\App\Http\Controllers\API\BrokerLandlord@connect")->name("crm.landlord.connect");
        Route::apiResource('landlord', '\App\Http\Controllers\API\BrokerLandlord');
        Route::apiResource('listing', APIInventoryController::class)->only([
            'index',
            'show'
        ]);
        Route::post('listing', [APIInventoryWriteController::class, 'store'])->name('crm.listing.store');
        Route::post('listing/{id}/image', [InventoryControllerCrm::class, 'uploadImage'])->name('crm.listing.writeImage');
        Route::delete('listing/{id}/image/{imageName}', [InventoryControllerCrm::class, 'deleteImageFromMobileApp'])->name('crm.listing.deleteImage');
        Route::patch('listing/{id}', [APIInventoryWriteController::class, 'update'])->name('crm.listing.update');
        Route::prefix('profile')->group(function () {
            Route::get('listing', [APIInventoryController::class, 'profileListing'])->name('profile.listing.get');
        });
        Route::post('inventory/{id}/duplicate', [APIInventoryController::class, 'duplicate'])->name('inventory.duplicate');
    });
    Route::apiResource('tour', TourController::class)->only([
        'store',
        'index',
        'update'
    ]);
    Route::prefix('tour')->group(function () {
        Route::get('{id}', [TourController::class, 'getOne'])->name('tour.getOne');
        Route::post('{id}/checkin', [TourController::class, 'agentCheckin'])->name('tour.agent.checkin');
    });
    Route::prefix('leads')->group(function () {
        Route::get('', '\App\Http\Controllers\API\LeadsController@index');
        Route::post('', '\App\Http\Controllers\API\LeadsController@store');

        Route::prefix('{leadId}')->group(function () {
            Route::get('', '\App\Http\Controllers\API\LeadsController@getOne');
            Route::patch('', '\App\Http\Controllers\API\LeadsController@edit');
            Route::delete('', '\App\Http\Controllers\API\LeadsController@delete');
            Route::delete('soft', '\App\Http\Controllers\API\LeadsController@deleteSoft');
            Route::delete('reassignation', '\App\Http\Controllers\API\LeadsController@deleteReassignation');
            Route::patch('assign/{userId}', '\App\Http\Controllers\API\LeadsController@leadsAssigne');
            Route::post('assign', '\App\Http\Controllers\API\LeadsController@assigneUserToLead');

            Route::get('activity', '\App\Http\Controllers\API\LeadsController@loadLeadActivity');
            Route::get('suggestions', '\App\Http\Controllers\API\LeadsController@leadSuggestions');
            Route::get('entries', '\App\Http\Controllers\API\LeadsController@entries');
            Route::prefix('tasks')->group(function () {
                Route::get('', '\App\Http\Controllers\API\LeadsController@leadTasks');
                Route::post('', '\App\Http\Controllers\API\LeadsController@recordTask');
                Route::patch('{taskId}', '\App\Http\Controllers\API\LeadsController@updateTask');
            });
        });
    });
    Route::prefix('lead-source')->group(function () {
        Route::get('', [LeadSourcesController::class, 'index']);
        Route::delete('{id}', '\App\Http\Controllers\Admin\LeadSourcesController@delete');
    });
    // Route::prefix('geography')->group(function() {
    //     Route::prefix('{geographyId}')->group(function() {
    //         Route::prefix('towers')->group(function() {
    //             Route::post('', [APIGeographyTowerController::class, 'create']);

    //             Route::prefix('{towerId}')->group(function() {
    //                 Route::patch('', [APIGeographyTowerController::class, 'edit']);
    //                 Route::delete('', [APIGeographyTowerController::class, 'delete']);
    //             });
    //         });
    //     });
    // });

    Route::prefix('mobile-action')->group(function () {
        Route::post('user-track', [APIMobileInteractionController::class, 'handleUserLocationTrack']);
        Route::post('subscription', [APIMobileInteractionController::class, 'subscribe']);
        Route::delete('subscription', [APIMobileInteractionController::class, 'unsubscribe']);
    });
    Route::prefix('settings')->group(function () {
        Route::get('', [MobileSettingsController::class, 'index'])->name('app.settings.get');
    });

    Route::prefix('seo')->group(function () {
        Route::get('links', [APISEOController::class, 'links'])->name('admin.seo.links');
        Route::get('menus', [APISEOController::class, 'menus'])->name('admin.seo.menus');
        Route::post('new', [SEOContentController::class, 'newSEOObject']);
        Route::patch('{id}', [SEOContentController::class, 'updateSEOObject']);
    });

    Route::group(['prefix' => 'seo-content'], function () {
        Route::get('', [SEOContentController::class, 'index']);
        Route::post('', [SEOContentController::class, 'save']);
        Route::patch('{id}', [SEOContentController::class, 'save']);
        Route::delete('{id}', [SEOContentController::class, 'delete']);
        Route::get('all', [SEOContentController::class, 'all']);
        Route::get('route', [SEOContentController::class, 'route']);

        //        Route::get('/seo/links', 'API\SEOContentController@links');
        //        Route::get('/seo/menus', 'API\MenusController@index');
        //        Route::patch('/seo/menus/{id}', 'API\MenusController@updateConfiguration');
        //        Route::delete('/seo-content/{id}', 'API\SEOContentController@delete');

        //        Route::get('/property-types', 'front\PropertiesController@propertyTypesWeb')->name('get-property-types');
    });
    Route::prefix('regions')->group(function () {
        Route::get('', [APIGeographyController::class, 'getAllRegions']);
        Route::get("active", [APIGeographyController::class, 'getActiveRegions'])->name("get.regions.active");
    });
    Route::get('users-not-logged-out', [UserLoginController::class, 'index']);
    Route::get('users-not-logged-out-data', [UserLoginController::class, 'handle']);
});
Route::prefix('geography')->group(function () {
    Route::get('', [APIGeographyController::class, 'index']);
    Route::prefix('{geographyId}')->group(function () {
        Route::prefix('towers')->group(function () {
            Route::get('', [APIGeographyTowerController::class, 'index']);
            Route::post('', [APIGeographyTowerController::class, 'create']);

            Route::prefix('{towerId}')->group(function () {
                Route::patch('', [APIGeographyTowerController::class, 'edit']);
                Route::delete('', [APIGeographyTowerController::class, 'delete']);
            });
        });
    });
});
Route::prefix('country')->group(function () {
    Route::get('', [APICountryController::class, 'index']);
    Route::get('{countryId}/locations', [APIGeographyController::class, 'getGeographiesByCountry']);
});
Route::prefix('contacts-list-duplicates')->group(function () {
    Route::get('', [ContactsListController::class, 'allDuplicates'])->name('api.contacts-list.duplicates');
    Route::get('{contactId}', [ContactsListController::class, 'getDuplicatesFor'])->name('api.contacts-list.duplicates-for');
    Route::post('{contactId}/mark-master', [ContactsListController::class, 'markContactAsMaster'])->name('api.contacts-list.mark-master');
});
Route::prefix('contacts-list')->group(function () {
    Route::get('', [ContactsListController::class, 'index'])->name('api.contacts-list.index');
    Route::post('', [ContactsListController::class, 'create'])->name('api.contacts-list.create');
    Route::post('import', [ContactsListController::class, 'import'])->name('api.contacts-list.import');
    Route::post('check-contact-exists', [ContactsListController::class, 'checkContactExists'])->name('api.contacts-list.check-exists');
    Route::post('check-contact-exists-phone', [ContactsListController::class, 'checkContactExistsPhone'])->name('api.contacts-list.check-exists-phone');
    Route::get('tags', [ContactsListController::class, 'tags'])->name('api.contacts-list.tags.index');
    Route::post('check', [ContactsHandlerController::class, 'processUpload'])->name('api.contacts-list.check');

    // Mailjet routes
    Route::get('mailjet/lists', [ContactsListController::class, 'getMailjetContactLists'])->name('api.contacts-list.mailjet.lists');
    Route::post('mailjet/lists', [ContactsListController::class, 'createMailjetContactList'])->name('api.contacts-list.mailjet.create-list');
    Route::post('mailjet/sync', [ContactsListController::class, 'syncContactsToMailjet'])->name('api.contacts-list.mailjet.sync');
    Route::prefix('{id}')->group(function () {
        Route::get('', [ContactsListController::class, 'getSingle'])->name('api.contacts-list.getSingle');
        Route::patch('', [ContactsListController::class, 'update'])->name('api.contacts-list.update');
        Route::delete('', [ContactsListController::class, 'delete'])->name('api.contacts-list.delete');
        Route::post('update-verified', [ContactsListController::class, 'updateVerified'])->name('api.contacts-list.update-verified');
        Route::get('tasks', [ContactsListController::class, 'contactTasks'])->name('crm.contacts-list.get.tasks');
        Route::post('tasks', [ContactsListController::class, 'recordTask'])->name('crm.contacts-list.get.tasks');
        Route::patch('tasks/{taskId}', '\App\Http\Controllers\API\LeadsController@updateTask');
        Route::get('operation-history', [ContactsListController::class, 'getOperationHistory'])->name('crm.contacts-list.operation-history');
        Route::get('state', [ContactsListController::class, 'contactState'])->name('crm.contacts-list.state');
    });
});
Route::prefix('options')->group(base_path('routes/api/options.php'));
Route::group(['middleware' => ['auth.api', 'verified']], function() {
    Route::prefix('contacts')->group(base_path('routes/api/contacts.php'));
});
Route::get('countries', [FrontendProvisionController::class, 'countries'])->name('countries.get');
Route::apiResource('listing-type', APIPropertyTypeController::class)->only(['index']);
Route::apiResource('listing-view', APIPropertyViewController::class)->only(['index']);
Route::get('nationality', [OptionsController::class, 'nationalities'])->name('nationalities.get');
Route::get('i18n-messages', [I18NController::class, 'getMessages'])->name('i18n.messages');
Route::get('route-data', [SEOContentController::class, 'routeData']);
Route::get('marketing-campaigns', [MarketingCampaignsController::class, 'index'])->name('marketing-campaigns.get.index');
Route::get('marketing-campaigns/{id}', [MarketingCampaignsController::class, 'show'])->name('marketing-campaigns.get.show');
Route::get('lead-source/{id}', [LeadSourceController::class, 'show']);
Route::put('lead-source/{id}', [LeadSourceController::class, 'update']);

Route::post('rooms/{roomId}/book', [BookingController::class, 'book'])->name('book.post');
Route::get('rooms/{roomId}/bookings', [BookingController::class, 'getRoomBookings'])->name('bookings.get');
Route::post('rooms/{roomId}/bookings/{bookingId}/approve', [BookingController::class, 'approve'])->name('bookings.approve');
Route::apiResource('rooms', RoomsController::class);
Route::get('short-stay-check', [BookingController::class, 'checkAvailability']);
Route::get('short-stay-room-check', [BookingController::class, 'checkRoomAvailabilityAndPrices']);


Route::prefix('hotels')->group(function () {
    Route::post('', [HotelsController::class, 'postHotel'])->name('api.hotels.post');
    Route::get('', [HotelsController::class, 'getHotels'])->name('api.hotels.getAll');
    Route::prefix('{id}')->group(function () {
        Route::get('', [HotelsController::class, 'getHotel'])->name('api.hotels.getOne');
        Route::patch('', [HotelsController::class, 'patchHotel'])->name('api.hotels.patch');
        Route::prefix('rate-plans')->group(function () {
            Route::get('', [HotelRatePlanController::class, 'getHotelRatePlans'])->name('api.hotels.get-hotel-rate-plans');
            Route::post('', [HotelRatePlanController::class, 'postHotelRatePlan'])->name('api.hotels.post-hotel-rate-plan');
            Route::prefix('{ratePlanId}')->group(function () {
                Route::get('', [HotelRatePlanController::class, 'getHotelRatePlan'])->name('api.hotels.get-hotel-rate-plans');
                Route::patch('', [HotelRatePlanController::class, 'patchHotelRatePlan'])->name('api.hotels.patch-hotel-rate-plan');
                Route::delete('', [HotelRatePlanController::class, 'deleteHotelRatePlan'])->name('api.hotels.delete-hotel-rate-plan');
            });
        });
        Route::prefix('room-types')->group(function () {
            Route::prefix('{roomTypeId}')->group(function () {
                Route::get('availability', [HotelsController::class, 'getHotelRoomsAvailability'])->name('api.hotels.get-hotel-rooms-availability');
                Route::post('availability', [HotelsController::class, 'postHotelRoomsAvailability'])->name('api.hotels.post-hotel-rooms-availability');
                Route::prefix('{ratePlanId}')->group(function () {
                    Route::get('prices', [HotelsController::class, 'getHotelRoomRatePlanPrices'])->name('api.hotels.get-hotel-rooms-rate-plan-prices');
                    Route::post('prices', [HotelsController::class, 'postHotelRoomsPlanPrice'])->name('api.hotels.post-hotel-rooms-rate-plan-prices');
                });
            });
        });
    });
});

Route::prefix('room-types')->group(function () {
    Route::post('', [RoomTypesController::class, 'createRoomType'])->name('api.room-types.createRoomType');
    Route::get('', [RoomTypesController::class, 'getRoomTypes'])->name('api.room-types.getAll');
    Route::get('{id}', [RoomTypesController::class, 'getRoomType'])->name('api.room-types.getOne');
    Route::patch('{id}', [RoomTypesController::class, 'updateRoomType'])->name('api.room-types.updateRoomType');
});

Route::patch('tour/{tourId}', [TourController::class, 'update'])->name('api.tour.update');
Route::patch('update-sidebar-collapsed-state', [UserController::class, 'updateSidebarCollapsedState']);

Route::prefix('tasks')->group(function () {
    Route::get('followup-tasks', [TasksController::class, 'userFollowupTasks'])->name('api.tasks.get.user-followup-tasks');
    Route::get('{id}', [TasksController::class, 'getTask'])->name('api.tasks.get.task');
});

Route::prefix('metrics')->group(function () {
    Route::get('dashboard/widgets', [MetricsController::class, 'getUserWidgets']);
    Route::get('dashboard/get-unselected-widgets', [MetricsController::class, 'getUserUnselectedWidgets']);
    Route::get('dashboard/add-user-widget/{widgetId}', [MetricsController::class, 'addUserWidget']);
    Route::get('dashboard/remove-user-widget/{widgetId}', [MetricsController::class, 'removeUserWidget']);
    Route::post('dashboard/save-new-layout', [MetricsController::class, 'saveNewLayout']);
    Route::get('widget-total-leads-no', [MetricsController::class, 'getTotalLeadsNo']);
    Route::get('widget-total-new-leads', [MetricsController::class, 'getNoOfNewLeads']);
    Route::get('widget-calls-meetings-property-viewings', [MetricsController::class, 'getCallsMeetingsPropertyViewings']);
    Route::get('widget-deals-closed-per-agent', [MetricsController::class, 'getDealsClosedPerAgent']);
    Route::get('closed-deals-timeline', [MetricsController::class, 'getClosedDealsPerTimeline']);

    Route::get('number-new-leads', [MetricsController::class, 'getNumberNewLeads']);
    Route::get('timeseries-new-leads', [MetricsController::class, 'getTimeseriesNewLeads']);

    Route::get('number-total-sales-volume', [MetricsController::class, 'getNumberTotalSalesVolume']);
    Route::get('timeseries-total-sales-volume', [MetricsController::class, 'getTimeseriesTotalSalesVolume']);

    Route::get('number-number-of-closed-deals', [MetricsController::class, 'getNumberNumberOfClosedDeals']);
    Route::get('timeseries-number-of-closed-deals', [MetricsController::class, 'getTimeseriesNumberOfClosedDeals']);

    Route::get('number-lead-to-client-conversion', [MetricsController::class, 'getNumberLeadToClientConversion']);

    Route::get('pie-leads-per-sources', [MetricsController::class, 'getPieLeadsPerSources']);
    Route::get('bar-leads-per-sources', [MetricsController::class, 'getBarLeadsPerSources']);
    Route::get('pie-leads-per-sources-per_utm_source', [MetricsController::class, 'getPieLeadsPerSourcesPerUtmSource']);

    Route::get('treemap-deals-per-projects-per-sources', [MetricsController::class, 'getTreemapDealsPerProjectsPerSources']);

    Route::get('pie-deals-per-agent', [MetricsController::class, 'getPieDealsPerAgent']);

    Route::get('number-pipeline-value', [MetricsController::class, 'getNumberPipelineValue']);

    Route::get('number-lead-response-time', [MetricsController::class, 'getNumberLeadResponseTime']);

    Route::get('number-exclusive-nonexclusive-listings', [MetricsController::class, 'getNumberExclusiveNonexclusiveListings']);

    Route::get('bar-calls-meetings-property-viewings', [MetricsController::class, 'getBarCallsMeetingsPropertyViewings']);

    Route::get('bar-average-commission-earned-per-agent', [MetricsController::class, 'getBarAverageCommissionEarnedPerAgent']);

    Route::get('widget-total-sales-volume', [MetricsController::class, 'getTotalSalesVolume']);
    Route::get('widget-closed-deals-count', [MetricsController::class, 'getClosedDealsCount']);
});

Route::get('/automatization/{automatizationId}/details', [AutomatizationController::class, 'getUsersMetricsOnAutomatization']);

Route::prefix('logs')->name('logs.')->group(function () {
    // Public logging endpoints (with optional API key middleware)
    // Route::middleware('api.key')->group(function () {
    Route::post('', [LoggingController::class, 'store'])->name('store');
    Route::post('batch', [LoggingController::class, 'storeBatch'])->name('storeBatch');
    // });

    // Admin/Dashboard endpoints (require authentication)
    Route::middleware('auth:api')->group(function () {
        Route::get('', [LoggingController::class, 'index'])->name('index');
        Route::get('{id}', [LoggingController::class, 'show'])->name('show');
        Route::delete('{id}', [LoggingController::class, 'destroy'])->name('destroy');
        Route::delete('', [LoggingController::class, 'destroyBatch'])->name('destroyBatch');
    });
});

Route::post('/twilio/call-tracking', [TwilioController::class, 'handleCall']);

// Optional route for health check/status of logging system
Route::get('logs/status', [LoggingController::class, 'status'])->name('logs.status');
