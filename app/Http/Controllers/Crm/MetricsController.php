<?php

namespace App\Http\Controllers\Crm;

use App\Helpers\NumberFormatter;
use App\Http\Controllers\CrmBaseController;
use App\Models\Crm\Deal;
use App\Models\Lead;
use App\Models\Property;
use App\Models\Task;
use App\Models\UserWidget;
use App\Models\Widget;
use App\Services\StatsReportService;
use Illuminate\Http\Request;
use DB;
use Carbon\Carbon;
use Log;

class MetricsController extends CrmBaseController
{
    protected $views = [
        'board' => 'crm.dashboards.metrics.board',
    ];

    private StatsReportService $reportService;

    public function __construct(
        StatsReportService $reportService
    ) {
        $this->reportService = $reportService;
    }
    public function board()
    {
        return view($this->views['board']);
    }

    public function getUserWidgets()
    {
        $userId = auth()->id();

        $widgets = UserWidget::where('user_id', $userId)
            ->select('id', 'height', 'widget_id', 'filters', 'position', 'width', 'x', 'y')
            ->with([
                'widget' => function ($query) {
                    $query->select('id', 'code', 'config', 'min_width', 'min_height', 'name', 'type');
                },
                'widget.widgets' => function ($query) {
                    $query->select('id', 'code', 'config', 'min_width', 'min_height', 'name', 'type');
                }
            ])
            ->get();

        // Convert string values to numbers for height, position, width, x, and y
        $widgets->transform(function ($widget) {
            // Use floatval instead of (int) to preserve decimal values if they exist
            $widget->height = is_null($widget->height) ? null : floatval($widget->height);
            $widget->position = is_null($widget->position) ? null : floatval($widget->position);
            $widget->width = is_null($widget->width) ? null : floatval($widget->width);
            $widget->x = is_null($widget->x) ? null : floatval($widget->x);
            $widget->y = is_null($widget->y) ? null : floatval($widget->y);
            if (!is_null($widget->widget)) {
                $widget->widget->min_width = is_null($widget->widget->min_width) ? null : floatval($widget->widget->min_width);
                $widget->widget->min_height = is_null($widget->widget->min_height) ? null : floatval($widget->widget->min_height);
            }
            if (!is_null($widget->widget->widgets)) {
                $widget->widget->widgets->transform(function ($childWidget) {
                    $childWidget->min_width = is_null($childWidget->min_width) ? null : floatval($childWidget->min_width);
                    $childWidget->min_height = is_null($childWidget->min_height) ? null : floatval($childWidget->min_height);
                    return $childWidget;
                });
            }
            return $widget;
        });

        return $widgets;
    }

    public function getUserUnselectedWidgets()
    {
        return Widget::with('widgets', 'userWidgets')->where('is_manually_selectable', true)->get();
    }

    public function removeUserWidget($widgetId)
    {
        $userId = auth()->id();
        UserWidget::where([['user_id', $userId], ['widget_id', $widgetId]])->delete();
        return response(['message' => 'widget removed'], 200);
    }

    public function addUserWidget($widgetId)
    {
        $userId = auth()->id();
        UserWidget::create([
            'user_id' => $userId,
            'widget_id' => $widgetId,
            'position' => UserWidget::where('user_id', $userId)->max('position') + 1,
        ]);
        return response(['message' => 'widget added'], 200);
    }

    // pair endpoints

    private function getCustomWeekPeriod(Carbon $date)
    {
        if ($date->dayOfWeek <= 4) {
            // For Sunday through Thursday, the week starts on the most recent Sunday.
            $start = $date->copy()->subDays($date->dayOfWeek)->startOfDay();
            $end = $start->copy()->addDays(4)->endOfDay();
        } else {
            // For Friday or Saturday, roll back to the most recent Thursday.
            $offset = $date->dayOfWeek - 4;
            $end = $date->copy()->subDays($offset)->endOfDay();
            $start = $end->copy()->subDays(4)->startOfDay();
        }
        return [$start, $end];
    }

    /**
     * Helper to get the "this month" period defined as all days after the last 25.
     *
     * If today is after the 25th, the period starts on the 26th of the current month;
     * otherwise, it starts on the 26th of the previous month.
     *
     * @param Carbon $date
     * @return array [start, end]
     */
    private function getThisMonthPeriod(Carbon $date)
    {
        if ($date->day > 25) {
            $start = $date->copy()->setDay(26)->startOfDay();
        } else {
            $start = $date->copy()->subMonth()->setDay(26)->startOfDay();
        }
        $end = $date->copy(); // Current time
        return [$start, $end];
    }

    /**
     * Returns a timeseries of new leads based on the specified period.
     *
     * Supported tags:
     * - today: Today only.
     * - this_week: Custom week (Sunday to Thursday).
     * - last_week: The custom week immediately preceding the current week.
     * - this_month: All days after the most recent 25.
     * - last_month: The period immediately preceding the current custom month.
     * - last_3_months: From 3 custom months ago until now.
     * - last_6_months: From 6 custom months ago until now.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTimeseriesNewLeads(Request $request)
    {
        $timeframe = $request->query("filters", "this_week");
        $previous = $request->query('previous', 'false');

        switch ($timeframe) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                // Get the custom week period for one week ago.
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                // Determine the current "this month" start date.
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                // Last month runs from one month before the current period start until the day before.
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                // Fallback to "today" if the provided tag is unsupported.
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        if ($previous == 'true') {
            $period = $startDate->diff($endDate);
            $startDate = $startDate->sub($period);
            $endDate = $endDate->sub($period);
            $name = 'Previous Period';
            $lineStyle = [
                'type' => 'dashed',
            ];
        } else {
            $name = 'Current Period';
            $lineStyle = ['width' => 3];
        }

        // Query the leads table for daily counts within the date range.
        $leadCountsSale = DB::table('leads')
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->where('filter_operation_type', 'sale')
            ->get();
        $leadCountsRent = DB::table('leads')
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->where('filter_operation_type', 'rent')
            ->get();


        // Prepare timeseries data with missing dates filled with 0.
        $dates = [];
        $countsSale = [];
        $countsRent = [];
        $previousPeriodCounts = []; // Placeholder for previous period data if needed

        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            $formattedDate = $currentDate->format('Y-m-d');
            $dates[] = $currentDate->format('M d');

            foreach ($leadCountsSale as $leadCountS) {
                if ($leadCountS->date == $formattedDate) {
                    $countsSale[] = $leadCountS->count;
                    break;
                } else {
                    $countsSale[] = 0;
                }
            }
            foreach ($leadCountsRent as $leadCount) {
                if ($leadCount->date == $formattedDate) {
                    $countsRent[] = $leadCount->count;
                    break;
                } else {
                    $countsRent[] = 0;
                }
            }

            $previousPeriodCounts[] = 0; // Adjust if you wish to compare with a previous period.
            $currentDate->addDay();
        }

        return response()->json([
            'data' => [
                'columns' => $dates,
                'series'  => [
                    [
                        'name'      => $name . ' Sale',
                        'data'      => $countsSale,
                        'type'      => "line",
                        // 'smooth'    => true,
                        "itemStyle" => [
                            "color" => "#FF0000"
                        ],
                        "lineStyle" => $lineStyle,
                    ],
                    [
                        'name'      => $name . ' Rent',
                        'data'      => $countsRent,
                        'type'      => "line",
                        // 'smooth'    => true,
                        "itemStyle" => [
                            "color" => "#5470C6"
                        ],
                        "lineStyle" => $lineStyle,
                    ],
                ]
            ]
        ]);
    }

    /**
     * Returns the number of new leads for the specified period.
     *
     * Supported tags:
     * - today: Today only.
     * - this_week: Custom week (Sunday to Thursday).
     * - last_week: The custom week immediately preceding the current week.
     * - this_month: All days after the most recent 25.
     * - last_month: The period immediately preceding the current custom month.
     * - last_3_months: From 3 custom months ago until now.
     * - last_6_months: From 6 custom months ago until now.
     *
     * @param Request $request
     * @return int
     */
    public function getNumberNewLeads(Request $request)
    {
        $this->updateUserWidgetFilters($request);
        $tag = $request->query('filters', 'today');

        switch ($tag) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                // Get the custom week period for one week ago.
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        $count = Lead::whereBetween('created_at', [$startDate, $endDate])->count();
        $countSale = Lead::whereBetween('created_at', [$startDate, $endDate])->where('filter_operation_type', 'sale')->count();
        $countRent = Lead::whereBetween('created_at', [$startDate, $endDate])->where('filter_operation_type', 'rent')->count();
        return ['data' => [
                'value' => $count,
                'children' => [
                    ['name'=>'', 'value'=>$countRent.' / '.$countSale]
                ],
            ]];
    }

    public function getNumberTotalSalesVolume(Request $request)
    {
        $tag = $request->query('filters', 'today');

        switch ($tag) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        // Query the deals table to count sale deals within the period.
        $result = DB::table('deals')
            ->where('type', 'SALE')
            ->whereNull('deleted_at')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->select(DB::raw('SUM(price) as data'))
            ->first();

        return response()->json(
            ['data' => [
                'value' => NumberFormatter::formatLargeNumber($result->data) ?? 0
            ]]
        );
    }

    /**
     * Returns a timeseries of total sales volume (number of sale deals)
     * for the specified period.
     *
     * Supported tags:
     * - today: Today only.
     * - this_week: Custom week (Sunday to Thursday).
     * - last_week: The custom week immediately preceding the current week.
     * - this_month: All days after the most recent 25.
     * - last_month: The period immediately preceding the current custom month.
     * - last_3_months: From 3 custom months ago until now.
     * - last_6_months: From 6 custom months ago until now.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTimeseriesTotalSalesVolume(Request $request)
    {
        $timeframe = $request->query('filters', 'today');
        $previous = $request->query('previous', 'false');

        switch ($timeframe) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        if ($previous == 'true') {
            $period = $startDate->diff($endDate);
            $startDate = $startDate->sub($period);
            $endDate = $endDate->sub($period);
            $name = 'Previous Period';
            $itemStyle = [
                "color" => "#5470C6"
            ];
            $lineStyle = [
                'type' => 'dashed',
                "color" => "#5470C6"
            ];
        } else {
            $name = 'Current Period';
            $itemStyle = [
                "color" => "#5470C6"
            ];
            $lineStyle = ['width' => 3];
        }

        // Query the deals table for daily counts of sale deals within the date range.
        $salesVolumeCounts = DB::table('deals')
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->where('type', 'SALE')
            ->whereNull('deleted_at')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->get();

        // Prepare timeseries data with any missing dates filled with 0.
        $dates = [];
        $counts = [];
        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            $formattedDate = $currentDate->format('Y-m-d');
            $dates[] = $currentDate->format('M d');
            $found = false;
            foreach ($salesVolumeCounts as $record) {
                if ($record->date == $formattedDate) {
                    $counts[] = $record->count;
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $counts[] = 0;
            }
            $currentDate->addDay();
        }

        return response()->json([
            'data' => [
                'columns' => $dates,
                'series'  =>
                [
                    'name'      => $name,
                    'data'      => $counts,
                    'type'      => "line",
                    'smooth'    => true,
                    "itemStyle" => $itemStyle,
                    "lineStyle" => $lineStyle,
                ]

            ]
        ]);
    }

    public function getNumberNumberOfClosedDeals(Request $request)
    {
        $tag = $request->query('filters', 'today');

        switch ($tag) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        $count = DB::table('deals')
            ->where('deal_status', 'approved')
            ->whereNull('deleted_at')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        return response()->json(['data' => [
            'value' => $count
        ]]);
    }

    /**
     * Returns a timeseries of the number of closed deals for the specified period.
     *
     * Supported tags:
     * - today: Today only.
     * - this_week: Custom week (Sunday to Thursday).
     * - last_week: The custom week immediately preceding the current week.
     * - this_month: All days after the most recent 25.
     * - last_month: The period immediately preceding the current custom month.
     * - last_3_months: From 3 custom months ago until now.
     * - last_6_months: From 6 custom months ago until now.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getTimeseriesNumberOfClosedDeals(Request $request)
    {
        $previous = $request->query('previous', 'false');
        $compare = $request->query('compare', 'false');

        $filterString = $request->query('filters');
        $parts =  explode('/', $filterString);
        $filter = $parts[0] ?? null;
        $dateFrom = $parts[1] ?? null;
        $dateTo = $parts[2] ?? null;
        $secondDateFrom = $parts[3] ?? null;
        $secondDateTo = $parts[4] ?? null;

        $tag = $filter ?? 'this_week'; 

        if ($tag == 'custom' || $tag == 'compare') {
            $startDate = Carbon::parse($dateFrom);
            $endDate   = Carbon::parse($dateTo);
        } else {

        switch ($tag) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }
    }

        if ($previous == 'true') {
            $period = $startDate->diff($endDate);
            $startDate = $startDate->sub($period);
            $endDate = $endDate->sub($period);
            $name = 'Previous Period';
            $itemStyle = [
                "color" => "#5470C6"
            ];
            $lineStyle = [
                'type' => 'dashed',
                "color" => "#5470C6"
            ];
        }elseif($compare == 'true'){
            $startDate = Carbon::parse($secondDateFrom);
            $endDate   = Carbon::parse($secondDateTo);
            $name = 'Second Period';
            $itemStyle = [
                "color" => "#5470C6"
            ];
            $lineStyle = [
                // 'type' => 'dashed',
                "color" => "#EE6666"
            ];
        } else {
            $name = 'Current Period';
            $itemStyle = [
                "color" => "#5470C6"
            ];
            $lineStyle = ['width' => 3]; // 2px
        }

        $closedDealsCounts = DB::table('deals')
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->where('deal_status', 'approved')
            ->whereNull('deleted_at')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date')
            ->get();

        // Build timeseries with missing dates filled with 0.
        $dates = [];
        $counts = [];
        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            $formattedDate = $currentDate->format('Y-m-d');
            $dates[] = $currentDate->format('M d');
            $found = false;
            foreach ($closedDealsCounts as $record) {
                if ($record->date == $formattedDate) {
                    $counts[] = $record->count;
                    $found = true;
                    break;
                }
            }
            if (!$found) {
                $counts[] = 0;
            }
            $currentDate->addDay();
        }

        return response()->json([
            'data' => [
                'columns' => $dates,
                'series'  =>
                [
                    'name'      => $name,
                    'data'      => $counts,
                    'type'      => "line",
                    "itemStyle" => $itemStyle,
                    "lineStyle" => $lineStyle,
                ]

            ]
        ]);
    }

    private function updateUserWidgetFilters(Request $request, $userWidgetId = null)
    {
        $filterString = $request->query('filters', 'today');
        $parts =  explode('/', $filterString);
        $filters = $parts[0] ?? null;
        $dateFrom = $parts[1] ?? null;
        $dateTo = $parts[2] ?? null;

        $userWidgetId = $request->query('userWidgetId', $userWidgetId);
        $userWidget = UserWidget::find($userWidgetId);
        if (!is_null($userWidget)) {
            $userWidgetFilters = $userWidget->filters;

            if (is_null($userWidgetFilters)) {
                $userWidgetFilters = (object)[];
            }
            $userWidgetFilters->onRenderFilters = (object)['value' => $filters, 'dateFrom' => $dateFrom, 'dateTo' => $dateTo,];
            $userWidget->filters = $userWidgetFilters;
            $userWidget->save();
        }
    }

    /**
     * Returns the number of lead-to-client conversions for the specified period.
     *
     * A lead is considered converted if there is at least one deal record (with a non-null client_id)
     * for that lead.
     *
     * Supported tags:
     * - today: Today only.
     * - this_week: Custom week (Sunday to Thursday).
     * - last_week: The custom week immediately preceding the current week.
     * - this_month: All days after the most recent 25.
     * - last_month: The period immediately preceding the current custom month.
     * - last_3_months: From 3 custom months ago until now.
     * - last_6_months: From 6 custom months ago until now.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getNumberLeadToClientConversion(Request $request)
    {
        $this->updateUserWidgetFilters($request);

        $tag = $request->query('filters', 'today');

        // Determine the date range based on the tag.
        switch ($tag) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        // Total number of leads created within the period.
        $totalLeads = DB::table('leads')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        // Number of converted leads is determined via deals that have a non-null client_id.
        $convertedLeads = DB::table('deals')
            ->whereNotNull('client_id')
            ->whereNull('deleted_at')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->distinct('lead_id')
            ->count('lead_id');

        $conversionRate = $totalLeads > 0 ? ($convertedLeads / $totalLeads) * 100 : 0;

        return response()->json(['data' => ['value' => round($conversionRate, 2) . "%"]]);
    }

    public function getPieLeadsPerSources(Request $request)
    {
        $this->updateUserWidgetFilters($request);

        $tag = $request->query('filters', 'this_week');

        switch ($tag) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        $results = DB::table('leads')
            ->select(DB::raw("
                CASE
                    WHEN platform_from REGEXP '^[0-9]+$'
                        THEN (SELECT name FROM lead_sources WHERE lead_sources.id = CAST(leads.platform_from AS UNSIGNED) LIMIT 1)
                    ELSE platform_from
                END AS source,
                COUNT(*) AS count
            "))
            ->whereBetween('created_at', [$startDate, $endDate])
            ->groupBy('source')
            ->get();

        $pieData = $results->map(function ($item) {
            return [
                'name'  => $item->source,
                'value' => $item->count,
            ];
        });

        return response()->json(['data' => $pieData]);
    }

    public function getPieLeadsPerSourcesPerUtmSource(Request $request)
    {
        $this->updateUserWidgetFilters($request);

        $tag = $request->query('filters', 'this_week');

        switch ($tag) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        $mainSources = DB::table('lead_sources')
            ->select(
                'lead_sources.id',
                'lead_sources.name as source',
                DB::raw('COUNT(leads.id) as count')
            )
            ->leftJoin('leads', function ($join) use ($startDate, $endDate) {
                $join->on(DB::raw('CAST(leads.platform_from AS UNSIGNED)'), '=', 'lead_sources.id')
                    ->whereBetween('leads.created_at', [$startDate, $endDate]);
            })
            ->groupBy('lead_sources.id', 'lead_sources.name')
            ->get();


        $utmSources = DB::table('leads')
            ->select(
                DB::raw('CAST(platform_from AS UNSIGNED) as lead_source_id'),
                'utm_source as source',
                DB::raw('COUNT(*) as count')
            )
            ->whereBetween('created_at', [$startDate, $endDate])
            ->whereNotNull('utm_source')
            ->groupBy(DB::raw('CAST(platform_from AS UNSIGNED)'), 'utm_source')
            ->get();


        $utmSourcesGrouped = $utmSources->groupBy('lead_source_id');

        $results = $mainSources->map(function ($source) use ($utmSourcesGrouped) {
            return [
                'name' => $source->source,
                'value' => $source->count,
                'children' => $utmSourcesGrouped->get($source->id, collect())->map(function ($utm) {
                    return [
                        'name' => $utm->source,
                        'value' => $utm->count,
                    ];
                })->values(),
            ];
        });


        return response()->json([
            'data' => $results,
            'config' => 'children'
        ]);
    }

    public function getPieDealsPerAgent(Request $request)
    {
        $this->updateUserWidgetFilters($request);
        $tag = $request->query('filters', 'this_week');

        // Determine the date range based on the tag.
        switch ($tag) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        // Format the dates for the SQL query.
        $start = $startDate->toDateTimeString();
        $end   = $endDate->toDateTimeString();

        // Build a union query that counts deals for closing_agent_id and referred_closing_agent_id.
        $unionQuery = "
            SELECT closing_agent_id AS agent_id, COUNT(*) AS deal_count
            FROM deals
            WHERE deleted_at IS NULL
              AND deal_status IN ('approved')
              AND created_at BETWEEN '$start' AND '$end'
              AND closing_agent_id IS NOT NULL
            GROUP BY closing_agent_id
            UNION ALL
            SELECT referred_closing_agent_id AS agent_id, COUNT(*) AS deal_count
            FROM deals
            WHERE deleted_at IS NULL
                AND deal_status IN ('approved')
              AND created_at BETWEEN '$start' AND '$end'
              AND referred_closing_agent_id IS NOT NULL
            GROUP BY referred_closing_agent_id
        ";

        // Wrap the union query and group by agent_id.
        $finalQuery = "
            SELECT agent_id, SUM(deal_count) AS count
            FROM ($unionQuery) AS sub
            GROUP BY agent_id
        ";

        // Join with the users table to get the agent names.
        $results = DB::table(DB::raw("($finalQuery) AS t"))
            ->join('users', 'users.id', '=', 't.agent_id')
            ->select('users.name AS source', 't.count')
            ->get();

        // Format the data for the pie chart.
        $pieData = $results->map(function ($item) {
            return [
                'name'  => $item->source,
                'value' => $item->count,
            ];
        });

        return response()->json(['data' => $pieData]);
    }

    // end pair endpoints

    public function getTotalLeadsNo()
    {
        $filters = request()->query();
        $userWidget = UserWidget::find($filters['userWidgetId']);
        $userWidget->filters = '{"onRenderFilters":"' . $filters['filters'] . '"}';
        $userWidget->save();
        $initial = 100;
        $total = $initial * (int)$filters['filters'];
        return json_encode(['data' => $total]);
    }

    public function saveNewLayout(Request $request)
    {
        $userId = auth()->id();
        $layout = json_decode($request->getContent(), true)['layout'];
        foreach ($layout as $item) {
            UserWidget::where([['user_id', $userId], ['id', $item['id']]])->update(['position' => $item['position'], 'x' => $item['x'] ?? 0, 'y' => $item['y'] ?? 0]);
        }
        return response(['message' => 'layout saved'], 200);
    }

    public function getTotalSalesVolume(Request $request)
    {
        $period = $request->get('filters', 'today');

        $userWidgetId = $request->query('userWidgetId'); // Default to 'Daily'
        $userWidget = UserWidget::find($userWidgetId);
        $userWidget->filters = '{"onRenderFilters":"' . $period . '"}';
        $userWidget->save();
        $query = DB::table('deals')
            ->where('type', 'SALE')
            ->whereNull('deleted_at'); // Exclude deleted deals
        $columns = [];
        $values = [];
        // Filter by date based on period
        switch ($period) {
            case 'today':
                $query->whereDate('created_at', Carbon::today());
                $columns[] = (new \DateTime())->format("d/m/Y");
                $sum = $query->sum(DB::raw('CAST(price AS DECIMAL(10, 2))'));
                $values[] = $sum;
                break;

            case 'thisWeek':
                $query->whereBetween('created_at', [
                    Carbon::now()->startOfWeek(),
                    Carbon::now()->endOfWeek()
                ]);
                for ($date = Carbon::now()->startOfWeek()->copy(); $date->lte(Carbon::now()->endOfWeek()); $date->addDay()) {
                    $columns[] = $date->format("d/m/Y");
                    $formattedDate = $date->format("Y-m-d");
                    $sum = $query->whereDate('created_at', $formattedDate)
                        ->sum(DB::raw('CAST(price AS DECIMAL(10, 2))'));
                    $values[] = $sum;
                }
                break;

            case 'thisMonth':
                $today = Carbon::today();
                if ($today->day <= 25) {
                    // If today is 25 or earlier, the monthly period starts from the 26th of the previous month
                    $startDate = Carbon::now()->subMonth()->day(26);
                } else {
                    // If today is after the 25th, the monthly period starts from the 26th of the current month
                    $startDate = Carbon::now()->day(26);
                }

                $query->whereBetween('created_at', [
                    $startDate->startOfDay(),
                    $today->endOfDay()
                ]);
                for ($date = $startDate->startOfDay()->copy(); $date->lte($today->endOfDay()); $date->addDay()) {
                    $columns[] = $date->format("d/m/Y");
                    $formattedDate = $date->format("Y-m-d");
                    $sum = $query->whereDate('created_at', $formattedDate)
                        ->sum(DB::raw('CAST(price AS DECIMAL(10, 2))'));
                    $values[] = $sum;
                }
                break;

            case 'last30Days':
                $query->where('created_at', '>=', now()->subMonth());
                for ($date = now()->subMonth()->copy(); $date->lte(now()); $date->addDay()) {
                    $columns[] = $date->format("d/m/Y");
                    $formattedDate = $date->format("Y-m-d");
                    $sum = $query->whereDate('created_at', $formattedDate)
                        ->sum(DB::raw('CAST(price AS DECIMAL(10, 2))'));
                    $values[] = $sum;
                }
                break;

            default:
                return response()->json(['error' => 'Invalid period parameter'], 400);
        }

        if ($userWidget->widget->type == 'Number') {
            $totalSales = $query->sum(DB::raw('CAST(price AS DECIMAL(10, 2))'));
            return response()->json([
                'data' => 'QAR ' . number_format($totalSales),
            ]);
        } else {
            return response()->json([
                'data' => [
                    'columns' => $columns,
                    'series' => [
                        json_encode([
                            'data' => $values,
                            // 'data' => [10,20,15,16,15,10],
                            'type' => "line",
                            'areaStyle' => json_encode([]),
                            "itemStyle" => [
                                "color" => "#5470C6"
                            ]
                        ]),
                        //preceding values
                        json_encode([
                            'data' => [1, 2, 3, 4, 10, 20],
                            'type' => "line",
                            // 'areaStyle' => json_encode([]),
                            "itemStyle" => [
                                "color" => "#A2A7AC"
                            ]
                        ]),
                    ]
                ]
            ]);
        }
    }

    public function getClosedDealsCount(Request $request)
    {
        $period = $request->query('filters', 'daily'); // Default to 'Daily'
        $userWidgetId = $request->query('userWidgetId'); // Default to 'Daily'
        $userWidget = UserWidget::find($userWidgetId);
        $userWidget->filters = '{"onRenderFilters":"' . $period . '"}';
        $userWidget->save();

        $query = DB::table('deals')
            ->whereNull('deleted_at')
            ->where('deal_status', 'approved');

        // Filter by date based on period
        switch (strtoupper($period)) {
            case 'DAILY':
                $query->whereDate('created_at', Carbon::today());
                break;

            case 'WEEKLY':
                $query->whereBetween('created_at', [
                    Carbon::now()->startOfWeek(),
                    Carbon::now()->endOfWeek()
                ]);
                break;

            case 'MONTHLY':
                $today = Carbon::today();
                if ($today->day <= 25) {
                    // If today is 25 or earlier, the monthly period starts from the 26th of the previous month
                    $startDate = Carbon::now()->subMonth()->day(26);
                } else {
                    // If today is after the 25th, the monthly period starts from the 26th of the current month
                    $startDate = Carbon::now()->day(26);
                }

                $query->whereBetween('created_at', [
                    $startDate->startOfDay(),
                    $today->endOfDay()
                ]);
                break;

            default:
                return response()->json(['error' => 'Invalid period parameter'], 400);
        }

        // Get count of closed rentals and sales
        $closedRentalsQuery = clone $query;
        $closedRentalsCount = $closedRentalsQuery->where('type', 'RENTAL')->count();

        $closedSalesQuery = clone $query;
        $closedSalesCount = $closedSalesQuery->where('type', 'SALE')->count();


        return response()->json([
            'data' => $closedRentalsCount . ' / ' . $closedSalesCount
        ]);
    }

    public function getNumberPipelineValue(Request $request)
    {
        $this->updateUserWidgetFilters($request);
        $timeframe = $request->query('filters', 'today');

        $q = Deal::whereIn('deal_status', [Deal::STATUS_PENDING, Deal::STATUS_APPROVED]);

        switch ($timeframe) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        $q->whereBetween('created_at', [$startDate, $endDate]);
        $noOfPendingDeals = $q->count();
        $valueOfPendingDeals = $q->sum('price');

        return response()->json([
            'data' => ['value' => 'QAR ' .  NumberFormatter::formatLargeNumber($valueOfPendingDeals) . ' / ' . $noOfPendingDeals . ' deals']
        ]);
    }

    public function getNoOfNewLeads(Request $request)
    {
        $selectedFilter = $request->query('filters', 'today');
        // $userWidgetId = $request->query('userWidgetId');
        // $userWidget = UserWidget::find($userWidgetId);
        // $userWidget->filters = '{"onRenderFilters":"' . $selectedFilter . '"}';
        // $userWidget->save();

        $query = Lead::latest();
        switch ($selectedFilter) {
            case 'today':
            case 'this_week':
            case '6 months':
            case '6 months':
            case '6 months':
                $query->where('created_at', '>=', now()->subMonths(6));
                break;
            case '3 months':
                $query->where('created_at', '>=', now()->subMonths(3));
                break;
            case '1 month':
                $query->where('created_at', '>=', now()->subMonth());
                break;
            case '1 week':
                $query->where('created_at', '>=', now()->subWeek());
                break;
            case 'today':
                $query->whereDate('created_at', '=', today());
                break;
        }
        $noOfNewLeads = $query->count();
        return response()->json([
            'data' => $noOfNewLeads
        ]);
    }

    public function getBarCallsMeetingsPropertyViewings(Request $request)
    {
        $this->updateUserWidgetFilters($request);
        $timeframe = $request->query('filters', 'today');
        $query = Task::where('status', 'completed');

        switch ($timeframe) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        $query->whereBetween('completion_date', [$startDate, $endDate]);

        // Clone the query for each count to avoid modifying the original query
        $callsQuery = clone $query;
        $calls = $callsQuery->where(function ($q) {
            $q->whereNotNull('automatization_id')->orWhere('type', 'Call Log');
        })->count();

        $meetingsQuery = clone $query;
        $meetings = $meetingsQuery->where('type', 'Meeting')->count();

        $viewingQuery = clone $query;
        $viewing = $viewingQuery->where('type', 'Viewing')->count();
        return response()->json([
            'data' => [
                'columns' => ['Calls', 'Meetings', 'Property Viewings'],
                'values' => [
                    [
                        "name" => 'Calls',
                        "value" => $calls,
                        "itemStyle" => [
                            "color" => "#FFEE35"
                        ]
                    ],
                    [
                        "name" => 'Meetings',
                        "value" => $meetings,
                        "itemStyle" => [
                            "color" => "#5470C6"
                        ]
                    ],
                    [
                        "name" => 'Property Viewings',
                        "value" => $viewing,
                        "itemStyle" => [
                            "color" => "#2FB12C"
                        ]
                    ]
                ]
            ]
        ]);
    }

    public function getNumberExclusiveNonexclusiveListings(Request $request)
    {
        $this->updateUserWidgetFilters($request);
        $timeframe = $request->query('filters', 'today');
        $query = Property::latest();

        switch ($timeframe) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        $query->whereBetween('created_at', [$startDate, $endDate]);
        $exclusive = $query->where('is_exclusive', true)->count();
        $nonExclusive = $query->where('is_exclusive', false)->count();
        return response()->json([
            'data' => ['value' => $exclusive . ' / ' . $nonExclusive]
        ]);
    }

    public function getNumberPendingReceivedCommissions(Request $request)
    {
        $this->updateUserWidgetFilters($request);
        $timeframe = $request->query('filters', 'today');
        $query = Deal::latest();

        switch ($timeframe) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        $sql = "SELECT
                SUM(
                    CASE WHEN d.client_commission_cashed = STATUS_CASHED AND d.listing_agent_cash_in = 1 AND (d.listing_agent_month BETWEEN '{$startDate}' AND {$endDate}) THEN d.listing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.client_commission_cashed = STATUS_CASHED AND d.referred_listing_agent_cash_in = 1 AND (d.referred_listing_agent_month BETWEEN '{$startDate}' AND {$endDate}) THEN d.referred_listing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.client_commission_cashed = STATUS_CASHED AND d.closing_agent_cash_in = 1 AND (d.closing_agent_month BETWEEN '{$startDate}' AND {$endDate}) THEN d.closing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.client_commission_cashed = STATUS_CASHED AND d.referred_closing_agent_cash_in = 1 AND (d.referred_closing_agent_month BETWEEN '{$startDate}' AND {$endDate}) THEN d.referred_closing_agent_shared_commission ELSE 0 END
                ) AS cashed_commission,
                 SUM(
                    CASE WHEN d.client_commission_cashed = STATUS_PENDING AND d.listing_agent_cash_in = 1  AND (d.listing_agent_month BETWEEN '{$startDate}' AND {$endDate}) THEN d.listing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.client_commission_cashed = STATUS_PENDING AND d.referred_listing_agent_cash_in = 1  AND (d.referred_listing_agent_month BETWEEN '{$startDate}' AND {$endDate}) THEN d.referred_listing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.client_commission_cashed = STATUS_PENDING AND d.closing_agent_cash_in = 1 AND (d.closing_agent_month BETWEEN '{$startDate}' AND {$endDate}) THEN d.closing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.client_commission_cashed = STATUS_PENDING AND d.referred_closing_agent_cash_in = 1 AND (d.referred_closing_agent_month BETWEEN '{$startDate}' AND {$endDate}) THEN d.referred_closing_agent_shared_commission ELSE 0 END
                ) AS pending_commission,

                FROM deals d";
        $cashInDBResult = DB::select(DB::raw($sql));

        return response()->json([
            'data' => ['value' => $cashInDBResult->pending_commission . ' QAR / ' . $cashInDBResult->cashed_commission . ' QAR']
        ]);
    }

    public function getClosedDealsPerTimeline()
    {
        return [];
    }

    public function getDealsClosedPerAgent(Request $request)
    {
        $period = $request->get('filters', 'today');
        $userWidgetId = $request->query('userWidgetId'); // Default to 'Daily'
        $userWidget = UserWidget::find($userWidgetId);
        $userWidget->filters = '{"onRenderFilters":"' . $period . '"}';
        $userWidget->save();

        $query = DB::table('deals');


        $allAgents = getAllFGRAgents(false, false, true);
        $series = [];
        $columns = [];
        foreach ($allAgents as $agent) {
            $values = [];
            switch ($period) {
                case 'today':
                    $query->whereDate('created_at', Carbon::today())
                        ->where(function ($q) use ($agent) {
                            $q->where('closing_agent_id', $agent->id);
                        });
                    if (!in_array((new \DateTime())->format("d/m/Y"), $columns)) {
                        $columns[] = (new \DateTime())->format("d/m/Y");
                    }
                    $values[] = $query->count();
                    break;

                case 'thisWeek':
                    for ($date = Carbon::now()->startOfWeek()->copy(); $date->lte(Carbon::now()->endOfWeek()); $date->addDay()) {
                        if (!in_array($date->format("d/m/Y"), $columns)) {
                            $columns[] = $date->format("d/m/Y");
                        }
                        $formattedDate = $date->format("Y-m-d");
                        $query->whereDate('created_at', $formattedDate)
                            ->where(function ($q) use ($agent) {
                                $q->where('closing_agent_id', $agent->id);
                            });
                        $values[] = $query->count();
                    }
                    break;

                case 'thisMonth':
                    $today = Carbon::today();
                    if ($today->day <= 25) {
                        // If today is 25 or earlier, the monthly period starts from the 26th of the previous month
                        $startDate = Carbon::now()->subMonth()->day(26);
                    } else {
                        // If today is after the 25th, the monthly period starts from the 26th of the current month
                        $startDate = Carbon::now()->day(26);
                    }
                    for ($date = $startDate->startOfDay()->copy(); $date->lte($today->endOfDay()); $date->addDay()) {
                        if (!in_array($date->format("d/m/Y"), $columns)) {
                            $columns[] = $date->format("d/m/Y");
                        }
                        $formattedDate = $date->format("Y-m-d");
                        $query->whereDate('created_at', $formattedDate)
                            ->where(function ($q) use ($agent) {
                                $q->where('closing_agent_id', $agent->id);
                            });
                        $values[] = $query->count();
                    }
                    break;

                case 'last30Days':
                    for ($date = now()->subMonth()->copy(); $date->lte(now()); $date->addDay()) {
                        if (!in_array($date->format("d/m/Y"), $columns)) {
                            $columns[] = $date->format("d/m/Y");
                        }
                        $formattedDate = $date->format("Y-m-d");
                        $query->whereDate('created_at', $formattedDate)
                            ->where(function ($q) use ($agent) {
                                $q->where('closing_agent_id', $agent->id);
                            });
                        $values[] = $query->count();
                    }
                    break;

                default:
                    return response()->json(['error' => 'Invalid period parameter'], 400);
            }
            $series[] = json_encode([
                'data' => $values,
                'type' => "line",
                // 'areaStyle' => json_encode([]),
                "itemStyle" => [
                    "color" => $agent->color
                ]
            ]);
        }

        return response()->json([
            'data' => [
                'columns' => $columns,
                'series' => $series
            ]
        ]);
    }

    public function getBarAverageCommissionEarnedPerAgent(Request $request)
    {
        $this->updateUserWidgetFilters($request);
        $timeframe = $request->query('filters', 'today');

        switch ($timeframe) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        $allAgents = getAllFGRAgents(false, false, true);
        $values = [];
        $columns = [];
        $data = $this->reportService->getAgentAverageCashIn(null, $startDate, null, $endDate);
        foreach ($allAgents as $agent) {
            $agentAverageCashIn = 0;
            foreach ($data as $cashInRow) {
                if (isset($cashInRow->id) && $agent->id == $cashInRow->id) {
                    $agentAverageCashIn += $cashInRow->avg_cashed_commission;
                }
            }
            $values[] = [
                "name" => $agent->name,
                "value" => round($agentAverageCashIn, 2),
                // "value" => $agent->id,
                "itemStyle" => [
                    "color" => $agent->color
                ]
            ];
            $columns[] = $agent->name;
        }

        return response()->json([
            'data' => [
                'columns' => $columns,
                'values' => $values,
                'dataZoom' => [
                    [
                        'type' => 'slider',
                        'xAxisIndex' => [0],
                        'start' => 0,
                        'end' => 10 * (100 / count($columns)),
                        'top' => '90%',
                        'height' => 20,
                    ],
                    [
                        'type' => 'inside',
                        'xAxisIndex' => [0],
                        'start' => 0,
                        'end' => 16 * (100 / count($columns)),
                    ]
                ]
            ]
        ]);
    }

    public function getNumberLeadResponseTime(Request $request)
    {
        $this->updateUserWidgetFilters($request);
        $timeframe = $request->query('filters', 'today');

        switch ($timeframe) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $dummy) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }
        $sql = "SELECT
            CONCAT(
                FLOOR(AVG(TIMESTAMPDIFF(SECOND, first_status.created_at, follow_up.created_at)) / 86400), 'd ',
                FLOOR(MOD(AVG(TIMESTAMPDIFF(SECOND, first_status.created_at, follow_up.created_at)), 86400) / 3600), 'h'
            ) AS avg_response_time
        FROM (
            SELECT
                lxs.lead_id,
                MIN(lxs.created_at) AS created_at
            FROM lead_x_status lxs
            GROUP BY lxs.lead_id
        ) AS first_status
        JOIN lead_x_status AS follow_up
            ON follow_up.lead_id = first_status.lead_id
        JOIN lead_status AS ls
            ON follow_up.lead_final_status_id = ls.id
            AND ls.name != 'NEW'
        WHERE first_status.created_at BETWEEN '" . $startDate->format('Y-m-d') . "'
            AND '" . $endDate->format('Y-m-d') . "'";

        $response = DB::select(DB::raw($sql));
        return response()->json([
            'data' => ['value' => $response[0]->avg_response_time ?? "-"]
        ]);
    }

    public function getBarLeadsPerSources(Request $request)
    {
        $this->updateUserWidgetFilters($request);

        $filterString = $request->query('filters');
        $parts =  explode('/', $filterString);
        $filter = $parts[0] ?? null;
        $dateFrom = $parts[1] ?? null;
        $dateTo = $parts[2] ?? null;

        $tag = $filter;

        if ($tag == 'custom') {
            $startDate = $dateFrom;
            $endDate   = $dateTo;
        } else {
            switch ($tag) {
                case 'today':
                    $startDate = Carbon::today();
                    $endDate   = Carbon::today()->endOfDay();
                    break;
                case 'this_week':
                    list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                    break;
                case 'last_week':
                    list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                    break;
                case 'this_month':
                    list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                    break;
                case 'last_month':
                    list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                    $startDate = $thisMonthStart->copy()->subMonth();
                    $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                    break;
                case 'last_3_months':
                    list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                    $startDate = $thisMonthStart->copy()->subMonth(3);
                    $endDate   = Carbon::now();
                    break;
                case 'last_6_months':
                    list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                    $startDate = $thisMonthStart->copy()->subMonth(6);
                    $endDate   = Carbon::now();
                    break;
                default:
                    $startDate = Carbon::today();
                    $endDate   = Carbon::today()->endOfDay();
                    break;
            }
        }

        $results = DB::table('lead_sources')
            ->select(
                'lead_sources.name as source',
                DB::raw('COUNT(leads.id) as count')
            )
            ->leftJoin('leads', function ($join) use ($startDate, $endDate) {
                $join->on(DB::raw('CAST(leads.platform_from AS UNSIGNED)'), '=', 'lead_sources.id')
                    ->whereBetween('leads.created_at', [$startDate, $endDate]);
            })
            ->groupBy('lead_sources.name')
            ->get();

        $columns = [];
        $barData = $results->map(function ($item) use (&$columns) {
            $columns[] = $item->source;
            return [
                'name'  => $item->source,
                'value' => $item->count,
            ];
        });


        return response()->json([
            'data' => [
                'columns' => $columns,
                'values' => $barData,
                'dataZoom' => [
                    [
                        'type' => 'slider',
                        'xAxisIndex' => [0],
                        'start' => 0,
                        'end' => 20 * (100 / count($columns)),
                        'top' => 5,
                        'height' => 20,
                    ],
                    [
                        'type' => 'inside',
                        'xAxisIndex' => [0],
                        'start' => 0,
                        'end' => 30 * (100 / count($columns)),
                    ]
                ],
                'config' => 'gradient'
            ]
        ]);
    }

    public function getTreemapDealsPerProjectsPerSources(Request $request){
        $this->updateUserWidgetFilters($request);

        $tag = $request->query('filters', 'this_week');

        switch ($tag) {
            case 'today':
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
            case 'this_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now());
                break;
            case 'last_week':
                list($startDate, $endDate) = $this->getCustomWeekPeriod(Carbon::now()->subWeek());
                break;
            case 'this_month':
                list($startDate, $endDate) = $this->getThisMonthPeriod(Carbon::now());
                break;
            case 'last_month':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth();
                $endDate   = $thisMonthStart->copy()->subDay()->endOfDay();
                break;
            case 'last_3_months':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(3);
                $endDate   = Carbon::now();
                break;
            case 'last_6_months':
                list($thisMonthStart, $_) = $this->getThisMonthPeriod(Carbon::now());
                $startDate = $thisMonthStart->copy()->subMonth(6);
                $endDate   = Carbon::now();
                break;
            default:
                $startDate = Carbon::today();
                $endDate   = Carbon::today()->endOfDay();
                break;
        }

        $campaignData = DB::table('marketing__projects as mp')
            ->join('marketing__campaigns as mc', 'mc.marketing_project_id', '=', 'mp.id')
            ->leftJoin('lead_sources as ls', 'mc.id', '=', 'ls.marketing_campaign_id')
            ->join('leads as l', 'l.utm_source', '=', 'mc.hash')
            ->join('deals as d', function ($join) use ($startDate, $endDate) {
                $join->on('d.lead_id', '=', 'l.id')
                    ->whereBetween('d.created_at', [$startDate, $endDate]);
            })
            ->select(
                'mp.id as project_id',
                'mp.name as project_name',
                DB::raw('COALESCE(ls.name, mc.name) as campaign_name'),
                'mc.id as campaign_id',
                DB::raw('COUNT(d.id) as deal_count')
            )
            ->groupBy('mp.id', 'mp.name', 'mc.id', 'mc.name', 'ls.name')
            ->get();

            $projects = $campaignData->groupBy('project_id')->map(function ($items, $projectId) {
                return (object)[
                    'id' => $projectId,
                    'name' => $items->first()->project_name,
                    'value' => $items->sum('deal_count'), // 👈 Sum of all campaign deals
                    'campaigns' => $items
                ];
            })->values();
                      
            $results = $projects->map(function ($project) {
                return [
                    'name' => $project->name,
                    'value' => $project->value,
                    'children' => $project->campaigns->map(function ($c) {
                        return [
                            'name' => $c->campaign_name,
                            'value' => $c->deal_count,
                        ];
                    })->values()
                ];
            }); 

        return response()->json([
            'data' => $results,
            'config' => 'children'
        ]);
    }
}
