<?php

namespace App\Models\Email;

class DealApprovalNotificationEmail extends GenericEmail
{
    private $deal;
    private $destinationEmail;

    public function __construct($deal, $destinationEmail)
    {
        $this->deal = $deal;
        $this->destinationEmail = $destinationEmail;
    }

    public function getSubject()
    {
        return "Deal Approved - Contact Client for Review [" . $this->deal->getRefNo() . "]";
    }

    public function getTemplateName()
    {
        return 'components.mail.deal-approval-notification';
    }

    public function getTo()
    {
        return $this->destinationEmail;
    }

    public function getTemplateVars()
    {
        return [
            'deal' => $this->deal,
        ];
    }
}
