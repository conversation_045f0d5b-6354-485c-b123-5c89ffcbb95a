<?php

namespace App\Services;

use Carbon\Carbon;
use DB;
use App\Helpers\NumberFormatter;

class PerformanceService
{
    private $taskService;
    private $statsReportService;

    public function __construct(TaskService $taskService, StatsReportService $statsReportService)
    {
        $this->taskService = $taskService;
        $this->statsReportService = $statsReportService;
    }
    private function getFGRPeriod($year, $month)
    {
        $endDate = Carbon::parse($year . '-' . $month . '-25')->format('Y-m-d');
        $startDate = Carbon::parse($endDate)->subMonth(1)->addDay()->format('Y-m-d');
        return [$startDate, $endDate];
    }

    public function getDataForAgents($userIds, $month, $year)
    {
        $officeUserIds = [54, 217, 334];
        $activeLeadStatusIds = [11, 12, 27, 28, 22, 23, 14, 24, 15, 16, 17, 20, 26, 5, 10]; // new, initial contact, not answered, continuing discussion, offer sent, meeting scheduled, viewing scheduled, other options, follow up, offer negotiation, contract pending, post closure, far moving decision, warm, hot
        $period = $this->getFGRPeriod($year, $month);
        $listingsInPeriodSql = "
            SELECT
                created_by,
            COUNT(*) as listings_count
            FROM
                properties
            WHERE
                deleted_at IS NULL AND
                created_by IN (" . implode(",", $userIds) . ") AND
                created_at BETWEEN '" . $period[0] . "' AND '" . $period[1] . "'
            GROUP BY
                created_by
        ";
        $listingsInPeriodRes = DB::select(DB::raw($listingsInPeriodSql));

        $totalListingsSql = "
            SELECT
                created_by,
            COUNT(*) as listings_count
            FROM
                properties
            WHERE
                created_by IN (" . implode(",", $userIds) . ")
                AND deleted_at IS NULL
            GROUP BY
                created_by
        ";
        // echo $totalListingsSql;
        // die;
        $totalListingsRes = DB::select(DB::raw($totalListingsSql));

        $contactsSql = "
            SELECT
                created_by,
            COUNT(*) as contacts_count
            FROM
                contacts
            WHERE
                deleted_at IS NULL AND
                created_by IN (" . implode(",", $userIds) . ")
            AND created_at BETWEEN '" . $period[0] . "' AND '" . $period[1] . "'
            GROUP BY
                created_by
        ";
        $contactsRes = DB::select(DB::raw($contactsSql));

        $selfGeneratedLeadsInPeriodSql = "
            SELECT
                l.created_by as user_id,
                COUNT(*) as leads_count
            FROM
                leads as l
                JOIN lead_status ls ON l.lead_status_id = ls.id
            WHERE
                l.deleted_at IS NULL AND
                ls.name NOT IN ('COLD', 'NOT_QUALIFIED', 'CLOSED_LOSE', 'GHOSTED', 'INACTIVE') AND 
                l.created_by IN (" . implode(",", $userIds) . ") AND 
                l.created_at BETWEEN '" . $period[0] . "' AND '" . $period[1] . "'
            GROUP BY
                l.id
        ";
        $selfGeneratedLeadsInPeriod = DB::select(DB::raw($selfGeneratedLeadsInPeriodSql));

        $officeAssignedLeadsInPeriodSql = "
            SELECT
                COUNT(l.id) as leads_count,
                la.user_id
            FROM
                leads l
                JOIN lead_assignments la ON la.lead_id = l.id
                JOIN lead_status ls ON l.lead_status_id = ls.id
            WHERE
                l.deleted_at IS NULL AND
                la.deleted_at IS NULL AND
                ls.name NOT IN ('COLD', 'NOT_QUALIFIED', 'CLOSED_LOSE', 'GHOSTED', 'INACTIVE') AND 
                l.created_by IN (" . implode(",", $officeUserIds) . ") AND 
                la.user_id IN (" . implode(",", $userIds) . ") AND
                l.created_at BETWEEN '" . $period[0] . "' AND '" . $period[1] . "'
            GROUP BY
                l.id
        ";
        $officeAssignedLeadsInPeriod = DB::select(DB::raw($officeAssignedLeadsInPeriodSql));

        $allActiveLeadsClause = "";
        foreach ($userIds as $index => $eachUserId) {
            $allActiveLeadsClause .= "( la.user_id = " . $eachUserId . " )";
            if ($index < count($userIds) - 1) {
                $allActiveLeadsClause .= " OR ";
            }
        }

        // self generated and kept under him or office assigned
        $totalLeadsSql = "
            SELECT
                l.created_by as user_id,
                COUNT(*) as leads_count
            FROM
                leads as l
                JOIN lead_assignments la ON la.lead_id = l.id
                JOIN lead_status ls ON l.lead_status_id = ls.id
            WHERE
                l.deleted_at IS NULL AND
                la.deleted_at IS NULL AND
                (" . $allActiveLeadsClause . ") AND
                ls.name NOT IN ('COLD', 'NOT_QUALIFIED', 'CLOSED_LOSE', 'GHOSTED', 'INACTIVE')
            GROUP BY
                l.id
        ";
        $totalLeadsRes = DB::select(DB::raw($totalLeadsSql));

        // self generated and kept under him or office assigned
        $activeLeadsSQL = "
            SELECT
                l.created_by as user_id,
                COUNT(*) as leads_count
            FROM
                leads as l
                JOIN lead_assignments la ON la.lead_id = l.id
                JOIN lead_status ls ON l.lead_status_id = ls.id
            WHERE
                l.deleted_at IS NULL AND
                la.deleted_at IS NULL AND
                (" . $allActiveLeadsClause . ") AND
                ls.name NOT IN ('COLD', 'NOT_QUALIFIED', 'CLOSED_LOSE', 'GHOSTED', 'INACTIVE') AND 
                l.created_at BETWEEN '" . $period[0] . "' AND '" . $period[1] . "'
            GROUP BY
                l.id
        ";
        $activeLeadsRes = DB::select(DB::raw($activeLeadsSQL));

        $dealsSql = "
            SELECT
                created_by,
            COUNT(*) as deals_count
            FROM
                deals
            WHERE
                (created_by IN (" . implode(",", $userIds) . ") OR closing_agent_id IN (" . implode(",", $userIds) . ") OR referred_closing_agent_id IN (" . implode(",", $userIds) . "))
            AND created_at BETWEEN '" . $period[0] . "' AND '" . $period[1] . "'
            GROUP BY
                created_by
        ";
        $dealsRes = DB::select(DB::raw($dealsSql));

        $tasksSummary = $this->taskService->getAllTasksSummaryForUsers($userIds, $period[0], $period[1]);

        $listingsInPeriod = ['count' => 0, 'agents_count' => count($userIds)];
        foreach ($listingsInPeriodRes as $row) {
            $listingsInPeriod['count'] += $row->listings_count;
        }
        $totalListings = ['count' => 0, 'agents_count' => count($userIds)];
        foreach ($totalListingsRes as $row) {
            $totalListings['count'] += $row->listings_count;
        }

        $officeAssignedLeads = ['count' => 0, 'agents_count' => count($userIds)];
        foreach ($officeAssignedLeadsInPeriod as $lead) {
            $officeAssignedLeads['count'] += $lead->leads_count;
        }
        $totalLeads = ['count' => 0, 'agents_count' => count($userIds), 'activeTotal' => null];
        foreach ($totalLeadsRes as $lead) {
            $totalLeads['count'] += $lead->leads_count;
        }
        foreach ($activeLeadsRes as $lead) {
            $totalLeads['activeTotal'] += $lead->leads_count;
        }
        $selfGeneratedLeads = ['count' => 0, 'agents_count' => count($userIds)];
        foreach ($selfGeneratedLeadsInPeriod as $lead) {
            $selfGeneratedLeads['count'] += $lead->leads_count;
        }
        $contacts = ['count' => 0, 'agents_count' => count($userIds)];
        foreach ($contactsRes as $row) {
            $contacts['count'] += $row->contacts_count;
        }
        $deals = ['count' => 0, 'agents_count' => count($userIds), 'cashedInThisMonth' => 0];
        foreach ($dealsRes as $row) {
            $deals['count'] += $row->deals_count;
        }

        // Calculate cashedInThisMonth using StatsReportService::getAgentCashIn
        $startDate = Carbon::parse($year . '-' . $month . '-01');
        $endDate = Carbon::parse($startDate)->endOfMonth();
        $totalCashedIn = 0;

        foreach ($userIds as $userId) {
            $cashInResults = $this->statsReportService->getAgentCashIn('monthly', $startDate, $userId, $endDate);
            foreach ($cashInResults as $result) {
                $totalCashedIn += $result->cashed_commission ?? 0;
            }
        }

        $deals['cashedInThisMonth'] = NumberFormatter::formatLargeNumber($totalCashedIn);

        $allData = [
            'listings' => $listingsInPeriod,
            'totalListings' => $totalListings,
            'contacts' => $contacts,
            'leads' => [
                'officeAssigned' => $officeAssignedLeads,
                'total' => $totalLeads,
                'allActive' => $totalLeads,
                'selfGenerated' => $selfGeneratedLeads,
            ],
            'deals' => $deals,
            'tasks' => $tasksSummary,
        ];

        return $allData;
    }
}
