<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('tasks:create-followup')->dailyAt('5:00');
        $schedule->command('tasks:create-from-automatization')->dailyAt('5:00');
        $schedule->command('landlords:handle-expiring')->dailyAt('5:30');
        $schedule->command('leads:handle-not-updated-in-1-h')->everyFiveMinutes();
        $schedule->command('leads:handle-not-updated-in-7days')->dailyAt('6:00');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
