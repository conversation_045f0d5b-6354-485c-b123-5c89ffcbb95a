<?php

namespace App\Services;

use App\Models\Crm\RolesDef;
use Illuminate\Http\Request;
use DB;
use Carbon\Carbon;

class AutomatizationService
{
    public $ajaxListMapper;
    public function __construct()
    {
        $this->ajaxListMapper = function ($item) {

            $data_item = [];
            $user = auth()->user();
            $createdAtDate = new \DateTime($item->created_at);
            $startDate = new \DateTime($item->start_date);
            $endDate = new \DateTime($item->end_date);
            $isTeamLeader = auth()->user()->hasAnyRole([RolesDef::TEAM_LEADER]) ? true : false;

            $actions = [];
            $actions['update'] = route('crm.automatizations.edit', ['automatizationId' => $item->id]);
            $actions['delete'] = '';
            $actions['canUpdate'] = $isTeamLeader ? false : true;
            $actions['details'] = route('crm.automatizations.details', ['automatizationId' => $item->id]);

            $data_item['DT_RowId'] = $item->id;
            $data_item[9] = ""; //Manage
            $data_item['actions'] = $actions; //Actions
            $data_item['id'] = $item->id;
            $data_item['no_of_tasks_per_day'] = $item->no_of_tasks_per_day;
            $data_item['status'] = ["name" => $item->status, "background_color" => $item->status == 'NEW' ? 'blue' :($item->status == 'ACTIVE' ? 'green' : 'red')];
            $data_item['agent_names'] = $item->agent_names;
            $data_item['tag_names'] = $item->tag_names;
            $data_item['created_at'] = $createdAtDate->format("d.m.Y H:i");
            $data_item['start_date'] = $startDate->format("d.m.Y");
            $data_item['end_date'] = $endDate->format("d.m.Y");

            return $data_item;
        };
    }

    public function fetchItems($offset = 0, $limit = 10)
    {
        $user = auth()->user();
        $isTeamLeader = $user->hasAnyRole([RolesDef::TEAM_LEADER]) ? true : false;
        $items = [];
        if($isTeamLeader){
            $userId = $user->id;
            $q = DB::table('automatizations as a')
                ->leftJoin('automatizations_x_users as au', 'a.id', '=', 'au.automatization_id')
                ->leftJoin('users as u', function ($join) use ($userId) {
                    $join->on('au.user_id', '=', 'u.id')
                        ->where('u.team_leader_id', '=', $userId);
                })
                ->leftJoin('automatizations_x_tags as at', 'a.id', '=', 'at.automatization_id')
                ->leftJoin('contacts_list_tags as clt', 'at.contacts_list_tag_id', '=', 'clt.id')
                ->select(
                    'a.id', 
                    'a.start_date',
                    'a.end_date',
                    'a.created_at',
                    'a.no_of_tasks_per_day',
                    'a.status',
                    DB::raw('GROUP_CONCAT(DISTINCT u.name SEPARATOR \', \') as agent_names'),
                    DB::raw('GROUP_CONCAT(DISTINCT clt.label SEPARATOR \', \') as tag_names')
                )
                ->whereExists(function ($sub) use ($userId) {
                    $sub->select(DB::raw(1))
                        ->from('automatizations_x_users as au2')
                        ->join('users as u2', 'au2.user_id', '=', 'u2.id')
                        ->whereColumn('au2.automatization_id', 'a.id')
                        ->where('u2.team_leader_id', $userId);
                })
                ->groupBy('a.id')
                ->orderBy('a.id', 'desc');

        }else{
        $q = DB::table('automatizations as a')
            ->leftJoin('automatizations_x_users as au', 'a.id', '=', 'au.automatization_id')
            ->leftJoin('users as u', 'au.user_id', '=', 'u.id')
            ->leftJoin('automatizations_x_tags as at', 'a.id', '=', 'at.automatization_id')
            ->leftJoin('contacts_list_tags as clt', 'at.contacts_list_tag_id', '=', 'clt.id')
            ->select(
                'a.id', 
                'a.start_date',
                'a.end_date',
                'a.created_at',
                'a.no_of_tasks_per_day',
                'a.status',
                DB::raw('GROUP_CONCAT(DISTINCT u.name SEPARATOR \', \') as agent_names'),
                DB::raw('GROUP_CONCAT(DISTINCT clt.label SEPARATOR \', \') as tag_names')
            )
            ->groupBy('a.id') 
            ->orderBy('a.id', 'desc'); 
        }

        $count = $q->count();

        if ($limit != -1) {
            $q = $q->offset($offset);
            $q = $q->limit($limit);
        }

        $items = $q->get();
        $items = ['items' => $items, 'count' => $count];
        return $items;
    }

    public function getExtraConfig(Request $request) {
        $columns = $request->query('columns');
        $order = $request->query('order');
        $search = $request->query('search');

        $extraConfig = ['q' => ''];

        if (!empty($columns) && is_array($columns)) {
            $wheres = [];
            foreach ($columns as $col) {
                if ($col['searchable'] && isset($col['search']) && isset($col['search']['value']) && !empty($col['search']['value'])) {
                    $wheres[$col['name']] = $col['search']['value'];
                }
            }

            $extraConfig['wheres'] = $wheres;
        }

        if (!empty($order) && is_array($order) && !empty($order[0])) {
            if (!empty($columns) && is_array($columns) && !empty($columns[$order[0]['column']])) {
                $sortBy = $columns[$order[0]['column']]['name'];
                $extraConfig['sort'] = $sortBy;
                $extraConfig['dir'] = $order[0]['dir'];
            }
        }

        if (!empty($search) && is_array($search) && !empty($search['value'])) {
            $extraConfig['q'] = $search['value'];
        }
        $extraConfig['searchParams'] = [];

        return $extraConfig;
    }

    public function syncStatus(){
        $automatizations = DB::table('automatizations')->whereIn('status', ['NEW','ACTIVE'])->get();
        $today = Carbon::today();
        foreach ($automatizations as $item) {
            if ($item->start_date <= $today && $item->end_date >= $today && $item->status == 'NEW') {
                DB::table('automatizations')
                    ->where('id', $item->id)
                    ->update(['status' => 'ACTIVE']);
            }elseif ($today > $item->end_date) {
                DB::table('automatizations')
                    ->where('id', $item->id)
                    ->update(['status' => 'INACTIVE']);
            }
        }
    }
}