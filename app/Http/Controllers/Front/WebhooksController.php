<?php

namespace App\Http\Controllers\Front;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;

use App\Services\ContactsService;
use App\Services\OperationHistoryService;
use App\Services\EmailService;
use App\Models\CacheKeys;
use App\Models\LeadSource;
use App\Models\LeadInteractionTracking;
use App\Models\Contact;
use App\Models\Country;
use App\Models\GoogleLead;
use App\Models\Lead;
use App\Models\User;
use App\Models\LeadAssignment;
use App\Models\MarketingParams;
use Illuminate\Support\Facades\Log;
use App\Services\AuthorizationService;

class WebhooksController extends FrontController
{
    private $contactsService;
    private $operationHistoryService;
    private $emailService;

    public function __construct(
        ContactsService $contactsService,
        OperationHistoryService $operationHistoryService,
        EmailService $emailService,
        AuthorizationService $authorizationService
    ) {
        $this->contactsService = $contactsService;
        $this->emailService = $emailService;
        $this->operationHistoryService = $operationHistoryService;
        $this->authorizationService = $authorizationService;
    }

    public function enquire(Request $request)
    {
        Log::info("Webhook enquire vars", $request->all());

        $marketingParams = array_fill_keys(MarketingParams::WHITELISTED_URL_PARAMS, '');

        $rules = array_merge([
            'Name' => 'required',
            'Phone' => 'required',
            'E_mail' => 'required|email',
            'Country' => 'required|exists:countries,id',
            'UnitType' => 'required',
            'Message' => 'required',
            'unit_no' => '',
            'network' => '',
            'gad_source' => '',
            'form_id' => '',
            'form_name' => 'required',
            'redirect_to' => '',
            'lookingTo' => 'sometimes',
            'resident' => 'sometimes',
            'ageGroup' => 'sometimes',
            'agentId' => 'sometimes',
        ], $marketingParams);

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 400);
        }

        $validData = $validator->validated();

        $existingContactByEmail = $this->contactsService->getContactsByEmail($validData['E_mail']);
        $existingContactByPhone = $this->contactsService->getContactsByPhoneNumber($validData['Phone']);

        $existingMasterContact = $existingContactByEmail->whereNull('master_contact_id')->first();
        if (is_null($existingMasterContact)) {
            $existingContact = $existingContactByEmail->first();
        } else {
            $existingContact = $existingMasterContact;
        }

        if (is_null($existingContact)) {
            $existingMasterContact = $existingContactByPhone->whereNull('master_contact_id')->first();
            if (is_null($existingMasterContact)) {
                $existingContact = $existingContactByPhone->first();
            } else {
                $existingContact = $existingMasterContact;
            }
        }

        $leadSource = LeadSource::firstWhere('name', $validData['form_name']);
        $country = Country::firstWhere('id', $validData['Country']);
        $broker = User::firstWhere('email', env('EMAIL_LOUAY'));

        $contact = Contact::create([
            'name' => $validData['Name'],
            'country' => $country->name,
            'email_1' => $validData['E_mail'],
            'prefix_mobile_1' => !is_null($country) ? $country->phone_prefix : null,
            'mobile_1' => $validData['Phone'],
            'master_contact_id' => is_null($existingContact) ? null : $existingContact->id,
            'is_master_contact' => is_null($existingContact) ? true : null
        ]);

        $leadsRequestObj = (object)[];
        $leadsRequestObj->be = intval($validData['UnitType']);
        $leadsRequestObj->ot = 'sale';
        // Collect all marketing parameters from the whitelisted parameters
        $leadMetadata = [];

        // Add all whitelisted marketing parameters
        foreach (MarketingParams::WHITELISTED_URL_PARAMS as $param) {
            if (isset($validData[$param]) && !empty($validData[$param])) {
                $leadMetadata[$param] = $validData[$param];
            }
        }

        // Add additional parameters
        $leadMetadata['gad_source'] = isset($validData['gad_source']) ? $validData['gad_source'] : '';
        $leadMetadata['network'] = isset($validData['network']) ? $validData['network'] : '';
        $leadMetadata['form_id'] = isset($validData['form_id']) ? $validData['form_id'] : '';
        $leadMetadata['form_name'] = isset($validData['form_name']) ? $validData['form_name'] : '';

        $historyMessage = "Lead added to system";
        $message = $validData['Message'];
        if (!is_null($leadSource)) {
            $historyMessage .= ". Source [" . $leadSource->name . "] ";
            $message .= "\r\n Source [" . $leadSource->name . "] ";
        }
        $unit_no = '';
        if (isset($validData['unit_no']) && !empty($validData['unit_no'])) {
            $historyMessage .= ". Unit No [" . $validData['unit_no'] . "] ";
            $message .= "\r\n Unit No [" . $validData['unit_no'] . "] ";
            $unit_no = $validData['unit_no'];
        }

        foreach (['lookingTo' => 'Looking To', 'resident' => 'Resident', 'ageGroup' => 'Age Group'] as $k => $label) {
            if (isset($validData[$k]) && !empty($validData[$k])) {
                $historyMessage .= "\r\n {$label}: {$validData[$k]} ";
                $message .= "\r\n {$label}: {$validData[$k]} ";
            }
        }

        $lead = Lead::create([
            'contact_id' => $contact->id,
            'platform_from' => is_null($leadSource) ? null : $leadSource->id,
            'lead_status_id' => '11',
            'filter_operation_type' => 'buy',
            'requirements' => $message,
            'filter_property_type' => 'buy',
            'filter_bedrooms' => $leadsRequestObj->be,
            'leads_request' => json_encode($leadsRequestObj),
            'lead_metadata' => $leadMetadata,

            // marketing parameters
            'utm_source' => isset($validData['utm_source']) ? $validData['utm_source'] : '',
            'utm_medium' => isset($validData['utm_medium']) ? $validData['utm_medium'] : '',
            'utm_campaign' => isset($validData['utm_campaign']) ? $validData['utm_campaign'] : '',
            'utm_term' => isset($validData['utm_term']) ? $validData['utm_term'] : '',
            'utm_content' => isset($validData['utm_content']) ? $validData['utm_content'] : '',
        ]);

        Cache::forget(CacheKeys::UTM_SOURCES);

        $brokerId = $broker->id;
        $targetAgent = null;
        if (isset($validData['agentId']) && !empty($validData['agentId'])) {
            $targetAgent = User::where('id', $validData['agentId'])->first();
            if (!is_null($targetAgent)) {
        //         $brokerId = $targetAgent->id;
                $historyMessage .= "\r\n Agent: {$targetAgent->name} ";
                $message .= "\r\n Agent: {$targetAgent->name} ";
            }
        }

        if ($validData['E_mail'] == '<EMAIL>') {
            $itdev = User::firstWhere('email', env('EMAIL_ADDRESS_ITDEV'));
            LeadAssignment::create([
                'user_id' => $itdev->id,
                'lead_id' => $lead->id,
            ]);
            $testingFormName = 'ITDEV_testing_emails';
            $this->emailService->sendJMJEnquiryNotificationToHaya($lead, $testingFormName);
        } else {
            LeadAssignment::create([
                'user_id' => $brokerId,
                'lead_id' => $lead->id,
            ]);

            $this->operationHistoryService->addOperationHistory($lead, $historyMessage);
            if ($validData['form_name'] == 'CView_Booking_Form') {
                $this->emailService->sendCViewBookConfirmationMessageToContact($contact);
                $this->emailService->sendCViewBookNotificationToHaya($lead, $unit_no);
            } else if ($validData['form_name'] == 'CView_enquiry_form') {
                $this->emailService->sendCViewEnquiryConfirmationMessageToContact($contact);
                $this->emailService->sendCViewEnquiryNotification($lead);
            } else {
                $this->emailService->sendJMJEnquiryConfirmationMessageToContact($contact);
                $this->emailService->sendJMJEnquiryNotificationToHaya($lead, $validData['form_name']);
            }

            // $action = LeadInteractionTracking::CREATED;
            // $this->authorizationService->leadInteractionsTracking($lead->id, $action, $brokerId);
        }

        if (!empty($validData['redirect_to'])) {
            return redirect($validData['redirect_to']);
        } else {
            return response()->json(['success' => true], 200);
        }
    }

    public function googleLead(Request $request, $leadType = null)
    {
        // Log the incoming request for debugging purposes
        Log::info('Google Lead Webhook received:', $request->all());

        // Validate the incoming request data
        $validated = $request->validate([
            'lead_id' => 'nullable|string',
            'user_column_data' => 'required|array',
            'api_version' => 'nullable|string',
            'form_id' => 'nullable|integer',
            'campaign_id' => 'nullable|integer',
            'google_key' => 'nullable|string',
            'is_test' => 'nullable|boolean',
            'gcl_id' => 'nullable|string',
            'adgroup_id' => 'nullable|integer',
            'creative_id' => 'nullable|integer',
        ]);

        // Check if the google_key matches the expected key
        $expectedKey = env('GOOGLE_LEAD_WEBHOOK_KEY');
        if (empty($validated['google_key']) || $validated['google_key'] !== $expectedKey) {
            Log::warning('Google Lead Webhook unauthorized access attempt', ['ip' => $request->ip()]);
            return response()->json(['status' => 'error', 'message' => 'Unauthorized'], 401);
        }

        try {
            // Insert the lead data with user_column_data as a JSON column
            GoogleLead::create([
                'lead_id' => $validated['lead_id'] ?? null,
                'user_column_data' => $validated['user_column_data'], // Store the entire array as JSON
                'api_version' => $validated['api_version'] ?? null,
                'form_id' => $validated['form_id'] ?? null,
                'campaign_id' => $validated['campaign_id'] ?? null,
                'google_key' => $validated['google_key'] ?? null,
                'is_test' => $validated['is_test'] ?? false,
                'gcl_id' => $validated['gcl_id'] ?? null,
                'adgroup_id' => $validated['adgroup_id'] ?? null,
                'creative_id' => $validated['creative_id'] ?? null,
                'lead_type' => $leadType,
            ]);

            // Return a response to acknowledge receipt
            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            Log::error('Error processing Google Lead:', ['error' => $e->getMessage()]);
            return response()->json(['status' => 'error', 'message' => 'An error occurred while processing the lead.'], 500);
        }
    }

    public function googleLeadBak(Request $request)
    {
        // Log the incoming request for debugging purposes
        Log::info('Google Lead Webhook received:', $request->all());

        // Validate the incoming request data
        $validated = $request->validate([
            'lead_id' => 'nullable|string',
            'user_column_data' => 'required|array',
            'api_version' => 'nullable|string',
            'form_id' => 'nullable|integer',
            'campaign_id' => 'nullable|integer',
            'google_key' => 'nullable|string',
            'is_test' => 'nullable|boolean',
            'gcl_id' => 'nullable|string',
            'adgroup_id' => 'nullable|integer',
            'creative_id' => 'nullable|integer',
        ]);

        // Check if the google_key matches the expected key
        $expectedKey = env('GOOGLE_LEAD_WEBHOOK_KEY');
        if (empty($validated['google_key']) || $validated['google_key'] !== $expectedKey) {
            Log::warning('Google Lead Webhook unauthorized access attempt', ['ip' => $request->ip()]);
            return response()->json(['status' => 'error', 'message' => 'Unauthorized'], 401);
        }

        // Extract common fields
        $leadId = $validated['lead_id'] ?? null;
        $apiVersion = $validated['api_version'] ?? null;
        $formId = $validated['form_id'] ?? null;
        $campaignId = $validated['campaign_id'] ?? null;
        $googleKey = $validated['google_key'] ?? null;
        $isTest = $validated['is_test'] ?? false;
        $gclId = $validated['gcl_id'] ?? null;
        $adgroupId = $validated['adgroup_id'] ?? null;
        $creativeId = $validated['creative_id'] ?? null;

        try {
            // Iterate over user_column_data and insert into the database
            foreach ($validated['user_column_data'] as $column) {
                $columnName = $column['column_name'] ?? null;
                $columnValue = $column['string_value'] ?? null;  // Adjust according to the type of data
                $columnId = $column['column_id'] ?? null;

                GoogleLead::create([
                    'lead_id' => $leadId,
                    'column_name' => $columnName,
                    'column_value' => $columnValue,
                    'column_id' => $columnId,
                    'api_version' => $apiVersion,
                    'form_id' => $formId,
                    'campaign_id' => $campaignId,
                    'google_key' => $googleKey,
                    'is_test' => $isTest,
                    'gcl_id' => $gclId,
                    'adgroup_id' => $adgroupId,
                    'creative_id' => $creativeId,
                ]);
            }

            // Return a response to acknowledge receipt
            return response()->json((object)[]);
        } catch (\Exception $e) {
            Log::error('Error processing Google Lead:', ['error' => $e->getMessage()]);
            return response()->json(['status' => 'error', 'message' => 'An error occurred while processing the lead.'], 500);
        }
    }
}
