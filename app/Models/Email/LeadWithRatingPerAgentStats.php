<?php

namespace App\Models\Email;

class LeadWithRatingPerAgentStats extends GenericEmail
{
    private $leadsStats;
    private $date;

    public function __construct($leadsStats)
    {
        $this->leadsStats = $leadsStats;
        $this->date = new \DateTime();
    }

    public function getSubject()
    {
        return "Leads with rating per agent report for " . ($this->date->format("d.m.Y"));
    }

    public function getTemplateName()
    {
        return 'components.mail.leads-with-rating-per-agent-stats';
    }

    public function getTo()
    {
        return [
            env('EMAIL_ADDRESS_SERBAN'), 
            env('EMAIL_ADDRESS_LEWIS'), 
            env('EMAIL_ADDRESS_NOHA')
        ];
    }

    public function getTemplateVars()
    {
        return [
            'leadsStats' => $this->leadsStats,
            'date' => $this->date,
        ];
    }
}
