<template>
    <div :class="{
        row: true,
        'g-2': true,
        filters: true,
        'filters--leadsVariant': containerVariant === 'leads',
    }">
        <div class="col-12 text-right mb-1 filter-lead">
            <a href="javascript:void(0)" @click="resetState()" class="small">Reset filters</a>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2">
            <label class="text-gray-50 small mb-0">Operation</label>
            <select v-model="state.ot" class="form-select form-select-sm">
                <option v-for="operationType in options.ot" :key="operationType.key" :value="operationType.key">
                    {{ operationType.value }}
                </option>
            </select>
            <p v-if="containerVariant === 'leads' && state.ot == ''" class="text-danger small mb-0">
                Required
            </p>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2">
            <label class="text-gray-50 small mb-0">Property Type</label>
            <FGVirtualSelect :selectorClassName="FGVirtualSelectClassNamePropertyType" :class="{
                'vscomp-ele': true,
                [FGVirtualSelectClassNamePropertyType]: true,
            }" :options="options.t" :config="propertyTypeSelectorConfig" :selectedValue="state.t"
                @change="onPropertyTypeSelectChange($event)" />
            <p v-if="state.t == []" class="text-danger small mb-0">
                Required
            </p>
        </div>
        <div v-if="containerVariant !== 'leads'" class="col-xs-12 col-sm-4 col-lg-2 mb-2 filter-lead">
            <label class="text-gray-50 small mb-0">Agent</label>
            <select v-model="state.agent" class="form-select form-select-sm">
                <option value="">Please select</option>
                <option v-for="agent in options.agents" :key="agent.key" :value="agent.key">
                    {{ agent.value }}
                </option>
            </select>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2 filter-lead">
            <label class="text-gray-50 small mb-0">Date Created</label>
            <div class="input-group input-group-sm">
                <input type="text" class="form-control form-control-sm" id="leadCreationDateRangeInput">
                <button @click="clearDaterange('leadCreationDateRangeInput')" class="btn btn-xs btn-outline-secondary">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2 filter-lead">
            <label class="text-gray-50 small mb-0">Lead source</label>
            <FGVirtualSelect :selectorClassName="FGVirtualSelectClassNameLeadSource" :class="{
                'vscomp-ele': true,
                [FGVirtualSelectClassNameLeadSource]: true,
            }" :options="options.leadSources" :config="leadSourcesSelectorConfig" :selectedValue="state.leadSources"
                @change="onLeadSourceSelectChange($event)" />
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2 filter-lead">
            <label class="text-gray-50 small mb-0">UTM Source</label>
            <FGVirtualSelect :selectorClassName="FGVirtualSelectClassNameUtmSource" :class="{
                'vscomp-ele': true,
                [FGVirtualSelectClassNameUtmSource]: true,
            }" :options="options.utmSources" :config="utmSourcesSelectorConfig" :selectedValue="state.utmSources"
                @change="onUtmSourceSelectChange($event)" />
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-3 mb-2 filter-lead">
            <label class="text-gray-50 small mb-0">UTM Campaign</label>
            <FGVirtualSelect :selectorClassName="FGVirtualSelectClassNameUtmCampaign" :class="{
                'vscomp-ele': true,
                [FGVirtualSelectClassNameUtmCampaign]: true,
            }" :options="options.utmCampaigns" :config="utmCampaignSelectorConfig" :selectedValue="state.utmCampaigns"
                @change="onUtmCampaignSelectChange($event)" />
        </div>
        <div v-if="options.leadStatuses.length > 0" class="col-xs-12 col-sm-4 col-lg-3 mb-2 filter-lead">
            <label class="text-gray-50 small mb-0">Lead status</label>
            <FGVirtualSelect :selectorClassName="FGVirtualSelectClassNameLeadStatus" :class="{
                'vscomp-ele': true,
                [FGVirtualSelectClassNameLeadStatus]: true,
            }" :options="options.leadStatuses" :config="leadStatusesSelectorConfig" :selectedValue="state.leadStatuses"
                @change="onLeadStatusSelectChange($event)" />
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2 filter-lead">
            <label class="text-gray-50 small mb-0">Last contacted</label>
            <div class="input-group input-group-sm">
                <input type="text" class="form-control form-control-sm" id="lastContactDateRangeInput">
                <button @click="clearDaterange('lastContactDateRangeInput')" class="btn btn-xs btn-outline-secondary">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2 filter-lead">
            <label class="text-gray-50 small mb-0">Decision date</label>
            <div class="input-group input-group-sm">
                <input type="text" class="form-control" id="leadDecisionDateRangeInput">
                <button @click="clearDaterange('leadDecisionDateRangeInput')" class="btn btn-xs btn-outline-secondary">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2 filter-lead">
            <label class="text-gray-50 small mb-0">Budget</label>
            <div class="input-group input-group-sm">
                <input v-model="state.pf" type="number" placeholder="From" class="form-control form-control-sm">
                <input v-model="state.pt" type="number" placeholder="To" class="form-control form-control-sm">
            </div>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2">
            <label class="text-gray-50 small mb-0">Bedrooms</label>
            <select v-model="state.be" class="form-select form-select-sm">
                <option value="">Please select</option>
                <option v-for="bedroom in options.be" :key="bedroom.key" :value="bedroom.key">
                    {{ bedroom.value }}
                </option>
            </select>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2">
            <label class="text-gray-50 small mb-0">Call log</label>
            <select v-model="state.callResponse" id="callResponseSelect" class="form-select form-select-sm">
                <option value="">Please select</option>
                <option v-for="cr of callResponses" :value="cr.value">{{ cr.label }}</option>
            </select>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2">
            <label class="text-gray-50 small mb-0">Rating</label>
            <select v-model="state.rating" class="form-select form-select-sm">
                <option v-for="ratingOption in options.rating" :key="ratingOption.key" :value="ratingOption.key">
                    {{ ratingOption.value }}
                </option>
            </select>
        </div>
        <div class="col-xs-12 col-sm-8 col-lg-6 mb-2 d-flex gap-3 align-items-center">
            <div class="form-check">
                <input v-model="state.createdByMe" type="checkbox" class="form-check-input" id="createdByMeCheckbox" />
                <label for="createdByMeCheckbox" class="form-check-label small">Created by me</label>
            </div>
            <div class="form-check">
                <input v-model="state.createdBySomeoneElse" type="checkbox" class="form-check-input"
                    id="createdBySomeoneElseCheckbox" />
                <label for="createdBySomeoneElseCheckbox" class="form-check-label small">Created by someone else</label>
            </div>
            <div class="form-check">
                <input v-model="state.hasDeals" type="checkbox" class="form-check-input" id="hasDealsCheckbox" />
                <label for="hasDealsCheckbox" class="form-check-label small">Has Deals</label>
            </div>
            <div class="form-check">
                <input v-model="state.needsUpdate" type="checkbox" class="form-check-input" id="needsUpdateCheckbox" />
                <label for="needsUpdateCheckbox" class="form-check-label small">Need to be updated</label>
            </div>
        </div>
    </div>
</template>

<script>
import { onMounted, ref, watch } from "vue";
import FGVirtualSelect from "../generic/FGVirtualSelect";
import { debounce } from "../../../helpers/all-kind";

const callResponses = [{
    value: 'not_answered',
    label: 'Not answered',
}, {
    value: 'answered',
    label: 'Answered',
}, {
    value: 'not_interested',
    label: 'Not interested',
}, {
    value: 'inactive',
    label: 'Inactive',
}];

const initialState = {
    llc: "",
    ot: "",
    loc: [],
    tower: [],
    t: [],
    at: 0,
    be: "",
    par: "",
    fu: "",
    bal: "",
    ki: "",
    ba: "",
    pf: "",
    pt: "",
    bills: "",
    comLl: "",
    comBu: "",
    comTe: "",
    status: "",
    leadStatuses: [],
    ps: "",
    pantry: "",
    agent: "",
    callResponse: [],
    fuOffice: "",
    view: [],
    prr: "",
    tid: "",
    hasPublishingRoles: false,
    af: "",
    a: [],
    leadCreationDateFrom: "",
    leadCreationDateTo: "",
    leadDecisionDateFrom: "",
    leadDecisionDateTo: "",
    lastContactDateFrom: "",
    lastContactDateTo: "",
    leadSources: "",
    createdByMe: false,
    createdBySomeoneElse: false,
    budgetFrom: null,
    budgetTo: null,
    utmSources: [],
    utmCampaigns: [],
    rating: "",
    hasDeals: false,
    needsUpdate: false,
};

export default {
    name: "InventoryFilters",
    emits: ['filtersChanged'],
    setup(props, { emit }) {
        function virtualSelectClassName() {
            return "r" + (Math.random() + 1).toString(36).substring(7);
        }
        const FGVirtualSelectClassNameLocations = virtualSelectClassName();
        const FGVirtualSelectClassNameLeadSource = virtualSelectClassName();
        const FGVirtualSelectClassNamePropertyType = virtualSelectClassName();
        const FGVirtualSelectClassNameUtmSource = virtualSelectClassName();
        const FGVirtualSelectClassNameUtmCampaign = virtualSelectClassName();
        const FGVirtualSelectClassNameLeadStatus = virtualSelectClassName();
        const containerVariant = ref("");

        const onLocationSelectChange = (locationIds) => {
            state.value.loc = locationIds;
        };
        const locationsSelectorConfig = {
            multiple: true,
            search: true,
        };

        const onPropertyTypeSelectChange = (propTypeIds) => {
            state.value.t = propTypeIds;
        };

        const onLeadSourceSelectChange = (leadSources) => {
            state.value.leadSources = leadSources;
        };

        const onUtmSourceSelectChange = (utmSourceIds) => {
            state.value.utmSources = utmSourceIds;
        };

        const onUtmCampaignSelectChange = (campaigns) => {
            state.value.utmCampaigns = campaigns;
        };

        const onLeadStatusSelectChange = (leadStatuses) => {
            state.value.leadStatuses = leadStatuses;
        };

        const propertyTypeSelectorConfig = {
            multiple: true,
            search: true,
        };
        const leadSourcesSelectorConfig = {
            multiple: false,
            search: true,
        };
        const leadStatusesSelectorConfig = {
            multiple: true,
            search: true,
        };
        const utmSourcesSelectorConfig = {
            multiple: true,
            search: true,
        };
        const utmCampaignSelectorConfig = {
            multiple: true,
            search: true,
            labelKey: 'label',
            valueKey: 'value',
            groupBy: 'utm_source',
            id: 'utm-campaigns-select'  // This is fine to keep now
        };

        const range = ref("");
        const options = ref({
            llc: [],
            t: [],
            agents: [],
            fu: [],
            fuOffices: [],
            ot: [],
            yesOrNo: [
                {
                    key: "",
                    value: "Please select",
                },
                {
                    key: "1",
                    value: "Yes",
                },
                {
                    key: "0",
                    value: "No",
                },
            ],
            rating: [
                {
                    key: "",
                    value: "All",
                },
                {
                    key: "non_rated",
                    value: "Non Rated",
                },
                {
                    key: "A",
                    value: "A (3 days)",
                },
                {
                    key: "B",
                    value: "B (5 days)",
                },
                {
                    key: "C",
                    value: "C (10 days)",
                },
                {
                    key: "D",
                    value: "D (15 days)",
                },
                {
                    key: "E",
                    value: "E (30 days)",
                },
                {
                    key: "F",
                    value: "F (60 days)",
                },
                {
                    key: "X",
                    value: "X",
                },
            ],
            status: [
                {
                    key: "",
                    value: "Please select",
                },
                {
                    key: "available",
                    value: "Available",
                },
                {
                    key: "occupied",
                    value: "Occupied",
                },
                {
                    key: "to-be-available",
                    value: "To be available",
                },
            ],
            ps: [
                {
                    key: "",
                    value: "Please select",
                },
                {
                    key: "request-for-change",
                    value: "Request for change",
                },
                {
                    key: "masterlist",
                    value: "Masterlist",
                },
                {
                    key: "published",
                    value: "Published",
                },
            ],
            pantry: [
                {
                    key: "1",
                    value: "All",
                },
                {
                    key: "private",
                    value: "Private",
                },
                {
                    key: "common",
                    value: "Common",
                },
            ],
            prmin: [],
            prmax: [],
            psmin: [],
            psmax: [],
            loc: [],
            views: [],
            bills: [],
            af: [
                {
                    key: "10",
                    value: "10 sqm",
                },
                {
                    key: "20",
                    value: "20 sqm",
                },
                {
                    key: "45",
                    value: "45 sqm",
                },
                {
                    key: "90",
                    value: "90 sqm",
                },
                {
                    key: "120",
                    value: "120 sqm",
                },
                {
                    key: "150",
                    value: "150 sqm",
                },
                {
                    key: "200",
                    value: "200 sqm",
                },
                {
                    key: "250",
                    value: "250 sqm",
                },
                {
                    key: "300",
                    value: "300 sqm",
                },
                {
                    key: "400",
                    value: "400 sqm",
                },
                {
                    key: "500",
                    value: "500 sqm",
                },
                {
                    key: "600",
                    value: "600 sqm",
                },
                {
                    key: "650",
                    value: "650 sqm",
                },
                {
                    key: "1000",
                    value: "1000 sqm",
                },
                {
                    key: "1500",
                    value: "1500 sqm",
                },
                {
                    key: "2000",
                    value: "2000 sqm",
                },
                {
                    key: "2500",
                    value: "2500 sqm",
                },
                {
                    key: "3000",
                    value: "3000 sqm",
                },
                {
                    key: "3500",
                    value: "3500 sqm",
                },
                {
                    key: "4000",
                    value: "4000 sqm",
                },
                {
                    key: "4500",
                    value: "4500 sqm",
                },
                {
                    key: "5000",
                    value: "5000 sqm",
                }
            ],
            at: [
                {
                    key: "150",
                    value: "150 sqm",
                },
                {
                    key: "200",
                    value: "200 sqm",
                },
                {
                    key: "350",
                    value: "350 sqm",
                },
                {
                    key: "450",
                    value: "450 sqm",
                },
                {
                    key: "500",
                    value: "500 sqm",
                },
                {
                    key: "1000",
                    value: "1000 sqm",
                },
                {
                    key: "2000",
                    value: "2000 sqm",
                },
                {
                    key: "3000",
                    value: "3000 sqm",
                },
                {
                    key: "4000",
                    value: "4000 sqm",
                },
                {
                    key: "5000",
                    value: "5000 sqm",
                },
                {
                    key: "6000",
                    value: "6000 sqm",
                },
                {
                    key: "7000",
                    value: "7000 sqm",
                },
                {
                    key: "8000",
                    value: "8000 sqm",
                },
                {
                    key: "9000",
                    value: "9000 sqm",
                },
                {
                    key: "10000",
                    value: "10000 sqm",
                },
            ],
            a: [],
            leadSources: [],
            leadStatuses: [],
            utmSources: [],
            utmCampaigns: [],
        });

        const state = ref({ ...initialState });

        const resetState = () => {
            state.value = { ...initialState };
            // reset locations and views in a hackish way
            for (const className of [
                FGVirtualSelectClassNameLocations,
                FGVirtualSelectClassNameLeadSource,
                FGVirtualSelectClassNamePropertyType,
                FGVirtualSelectClassNameUtmSource,
                FGVirtualSelectClassNameUtmCampaign,
                FGVirtualSelectClassNameLeadStatus,
            ]) {
                const domElem = document.querySelector(`.${className}`);
                if (domElem) {
                    domElem.reset();
                }
            }

            $("#leadCreationDateRangeInput").val("");
            $("#lastContactDateRangeInput").val("");
            $("#leadDecisionDateRangeInput").val("");
        };

        const doSearch = debounce((availableFilters) => {
            if (typeof onLeadsFiltersParamsChange != "undefined") {
                onLeadsFiltersParamsChange(availableFilters);
            } else {
                emit('filtersChanged', availableFilters);
            }
        });

        watch(
            () => ({ ...state.value }),
            (currentState, previousState) => {
                // if operation type changed, reset the prices value
                if (
                    previousState.ot !== currentState.ot &&
                    !!previousState.ot
                ) {
                    state.value.pf = "";
                    state.value.pt = "";
                }
        localStorage.setItem(
            "leadsTableFilters",
            JSON.stringify(currentState)
        );
        const availableFilters = {};
        
        if (currentState.needsUpdate) {
            availableFilters.needsUpdate = "1";
        }
                Object.keys(initialState).forEach((k) => {
                    if (!!state.value[k]) {
                        let mappedKey = k;
                        let mappedValue = state.value[k];
                        if (k == "comLl") {
                            mappedKey = "com-ll";
                        }
                        if (k == "comBu") {
                            mappedKey = "com-bu";
                        }
                        if (k == "comTe") {
                            mappedKey = "com-te";
                        }
                        if (k == "be") {
                            mappedValue = encodeURIComponent(state.value[k]);
                        }
                        availableFilters[mappedKey] = mappedValue;
                    }
                });
                doSearch(availableFilters);
            }
        );

        const dateRangeMap = {
            'leadCreationDateRangeInput': {
                from: 'leadCreationDateFrom',
                to: 'leadCreationDateTo'
            },
            'leadDecisionDateRangeInput': {
                from: 'leadDecisionDateFrom',
                to: 'leadDecisionDateTo'
            },
            'lastContactDateRangeInput': {
                from: 'lastContactDateFrom',
                to: 'lastContactDateTo'
            }
        }

        const clearDaterange = (daterangeId) => {
            $(`#${daterangeId}`).val('');
            state.value[dateRangeMap[daterangeId].from] = null;
            state.value[dateRangeMap[daterangeId].to] = null;
        }

        onMounted(() => {
            const currentStoredFilters =
                localStorage.getItem("leadsTableFilters");
            let leadCreationDateFrom = null;
            let leadCreationDateTo = null;
            let leadDecisionDateFrom = null;
            let leadDecisionDateTo = null;
            let lastContactDateFrom = null;
            let lastContactDateTo = null;

            if (!!currentStoredFilters) {
                const persistedFilters = JSON.parse(currentStoredFilters);
                state.value = { ...persistedFilters };
                leadCreationDateFrom = persistedFilters.leadCreationDateFrom ?? '';
                leadCreationDateTo = persistedFilters.leadCreationDateTo ?? '';
                leadDecisionDateFrom = persistedFilters.leadDecisionDateFrom ?? '';
                leadDecisionDateTo = persistedFilters.leadDecisionDateTo ?? '';
                lastContactDateFrom = persistedFilters.lastContactDateFrom ?? '';
                lastContactDateTo = persistedFilters.lastContactDateTo ?? '';
            }

            if (typeof variant != "undefined") {
                containerVariant.value = variant;
            }
            setTimeout(() => {
                options.value.agents = agents.map((agent) => {
                    return {
                        key: agent.id,
                        value: agent.selectName,
                    };
                });
                if (typeof leadSources != "undefined") {
                    options.value.leadSources = leadSources.sort((a, b) => a.label.localeCompare(b.label));;
                }
                if (typeof leadStatuses !== "undefined") {
                    options.value.leadStatuses = leadStatuses.map((item) => {
                        return {
                            value: item.name,
                            label: item.name,
                        };
                    });
                }
                options.value.loc = locations;
                const llcOptions = llc.map((item) => {
                    return {
                        key: item.value,
                        value: item.label,
                    };
                });
                options.value.llc = llcOptions;
                const otOptions = ot.map((item) => {
                    return {
                        key: item.value,
                        value: item.label,
                    };
                });
                options.value.ot = otOptions;
                const statusOp = statusOptions.map((item) => {
                    return {
                        key: item.name,
                        value: item.name,
                    };
                });
                options.value.statusLead = statusOp;
                Promise.all([
                    request("/api/listing-type").then((data) => {
                        const propertyTypeOptions = data.map((item) => {
                            return {
                                value: item.id,
                                label: item.label,
                            };
                        });
                        options.value.t = propertyTypeOptions;
                    }),
                    request("/api/options/bedroom").then((data) => {
                        const bedroomOptions = data.map((item) => {
                            return {
                                key: item.key,
                                value: item.value,
                            };
                        });
                        options.value.be = bedroomOptions;
                    }),
                    request("/api/options/utm-sources").then((data) => {
                        options.value.utmSources = data.map((source) => ({
                            value: source,
                            label: source
                        }));
                    }),
                    request("/api/options/utm-campaigns").then((data) => {
                        options.value.utmCampaigns = data.map((item) => {
                            return {
                                value: item.utm_campaign,
                                label: item.utm_campaign,
                                utm_source: item.utm_source
                            }
                        });
                    }),
                ]).then(() => {
                    const availableFilters = {};
                    Object.keys(initialState).forEach((k) => {
                        if (!!state.value[k]) {
                            let mappedKey = k;
                            if (k == "comLl") {
                                mappedKey = "com-ll";
                            }
                            if (k == "comBu") {
                                mappedKey = "com-bu";
                            }
                            if (k == "comTe") {
                                mappedKey = "com-te";
                            }
                            availableFilters[mappedKey] = state.value[k];
                        }
                    });
                    doSearch(availableFilters);
                });

                setTimeout(() => {
                    // Lead Creation Date Range Picker
                    const leadCreationConfigObj = {
                        opens: "left",
                        autoApply: true,
                    };
                    if (leadCreationDateFrom) {
                        leadCreationConfigObj.startDate = new Date(Date.parse(leadCreationDateFrom));
                    }
                    if (leadCreationDateTo) {
                        leadCreationConfigObj.endDate = new Date(Date.parse(leadCreationDateTo));
                    }

                    $("#leadCreationDateRangeInput").daterangepicker(
                        leadCreationConfigObj,
                        function (start, end) {
                            state.value.leadCreationDateFrom =
                                start.format("YYYY-MM-DD");
                            state.value.leadCreationDateTo =
                                end.format("YYYY-MM-DD");
                        }
                    );
                    if (!leadCreationDateFrom && !leadCreationDateTo) {
                        $("#leadCreationDateRangeInput").val('');
                    }

                    // Lead Decision Date Range Picker
                    const leadDecisionConfigObj = {
                        opens: "left",
                        autoApply: true,
                    };
                    if (leadDecisionDateFrom) {
                        leadDecisionConfigObj.startDate = new Date(Date.parse(leadDecisionDateFrom));
                    }
                    if (leadDecisionDateTo) {
                        leadDecisionConfigObj.endDate = new Date(Date.parse(leadDecisionDateTo));
                    }

                    $("#leadDecisionDateRangeInput").daterangepicker(
                        leadDecisionConfigObj,
                        function (start, end) {
                            state.value.leadDecisionDateFrom =
                                start.format("YYYY-MM-DD");
                            state.value.leadDecisionDateTo =
                                end.format("YYYY-MM-DD");
                        }
                    );
                    if (!leadDecisionDateFrom && !leadDecisionDateTo) {
                        $("#leadDecisionDateRangeInput").val('');
                    }

                    // Last Contact Date Range Picker
                    const lastContactConfigObj = {
                        opens: "left",
                        autoApply: true,
                    };
                    if (lastContactDateFrom) {
                        lastContactConfigObj.startDate = new Date(Date.parse(lastContactDateFrom));
                    }
                    if (lastContactDateTo) {
                        lastContactConfigObj.endDate = new Date(Date.parse(lastContactDateTo));
                    }

                    $("#lastContactDateRangeInput").daterangepicker(
                        lastContactConfigObj,
                        function (start, end) {
                            state.value.lastContactDateFrom =
                                start.format("YYYY-MM-DD");
                            state.value.lastContactDateTo =
                                end.format("YYYY-MM-DD");
                        }
                    );
                    if (!lastContactDateFrom && !lastContactDateTo) {
                        $("#lastContactDateRangeInput").val('');
                    }
                }, 500);
            });
        });

        watch(() => state.value.utmSources, async (newUtmSources) => {
            // Reset UTM campaigns selection first
            state.value.utmCampaigns = [];

            // Reset the virtual select component
            // const utmCampaignSelect = document.querySelector(`.${FGVirtualSelectClassNameUtmCampaign}`);
            // if (utmCampaignSelect) {
            //     utmCampaignSelect.reset();
            // }

            // Then fetch new campaign options based on selected sources
            if (newUtmSources && newUtmSources.length === 1) {
                const response = await request(`/api/options/utm-campaigns?utm_source=${newUtmSources[0]}`);
                options.value.utmCampaigns = response.map((item) => {
                    return {
                        value: item.utm_campaign,
                        label: item.utm_campaign,
                        utm_source: item.utm_source
                    }
                });
            } else {
                const response = await request('/api/options/utm-campaigns');
                options.value.utmCampaigns = response.map((item) => {
                    return {
                        value: item.utm_campaign,
                        label: item.utm_campaign,
                        utm_source: item.utm_source
                    }
                });
            }
        });

        return {
            options,
            state,
            range,
            locationsSelectorConfig,
            propertyTypeSelectorConfig,
            leadSourcesSelectorConfig,
            leadStatusesSelectorConfig,
            utmSourcesSelectorConfig,
            utmCampaignSelectorConfig,
            FGVirtualSelectClassNameLocations,
            FGVirtualSelectClassNameLeadSource,
            FGVirtualSelectClassNamePropertyType,
            FGVirtualSelectClassNameUtmSource,
            FGVirtualSelectClassNameUtmCampaign,
            FGVirtualSelectClassNameLeadStatus,
            containerVariant,
            onLocationSelectChange,
            onPropertyTypeSelectChange,
            onLeadSourceSelectChange,
            onLeadStatusSelectChange,
            onUtmSourceSelectChange,
            onUtmCampaignSelectChange,
            resetState,
            clearDaterange,
            callResponses
        };
    },
    components: {
        FGVirtualSelect,
    },
};
</script>

<style lang="scss">
.filters {
    &--leadsVariant {
        .filter-lead {
            display: none;
        }
    }
}

/* Compact styles for the filters */
.form-select-sm,
.form-control-sm {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    min-height: 30px;
}

/* Make virtual select more compact */
.vscomp-ele {
    --vscomp-font-size: 0.875rem;
    --vscomp-option-padding: 4px 8px;
    max-height: 32px;
    height: 32px;
}

.vscomp-toggle-button {
    max-height: 32px;
    height: 32px;
    line-height: 1;
    display: flex;
    align-items: center;
}
</style>
