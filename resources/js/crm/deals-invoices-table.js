window.accountantSearchParams = {};
let filtersInitialized = false;
let dealsAccountantTable;

$(document).ready(function () {
    let selectedRows = new Set();
    const addCustomFiltersAndSearch = (sSource, aoData, fnCallback) => {
        // if (filtersInitialized) {
        const urlStr = parseDTParams(aoData);

        Object.keys(window.accountantSearchParams).forEach((k) => {
            urlStr.push(`${k}=${window.accountantSearchParams[k]}`);
        });

        const data = urlStr.join("&");
        $.ajax({
            dataType: "json",
            url: `/crm/accountant`,
            data,
            success: (input) => {
                fnCallback(input);
            },
        });
        // }
    };

    $("#deals-invoices-list-filters th").each(function () {
        var title = $(this).text();
        var html =
            '<div class="input-group input-group-sm" >' +
            '<input type="text" class="form-control" id="deals-invoices-list-col-filter-' +
            $(this).data("col-index") +
            '" placeholder="' +
            title +
            '" />' +
            '<span class="input-group-addon"><span class="glyphicon glyphicon-filter"></span></span>' +
            "</div>";
        $(this).html(html);
    });

    dealsAccountantTable = $("#deals-invoices-list").DataTable({
        serverSide: true,
        scrollX: true,
        lengthMenu: [
            [10, 25, 50, -1],
            [10, 25, 50, "All"]
        ],
        pageLength: 50,
        responsive: false,
        scrollY: "calc(100vh - 350px)",
        oLanguage: {
            sProcessing: "<img width='20' height='20' src='/images/ajax_spinner.gif'>"
        },
        processing: true,
        searching: true,
        order: false,
        drawCallback: function () {
            $('.row-checkbox').each(function () {
                let rowId = $(this).data('id');
                $(this).prop('checked', selectedRows.has(rowId));
            });
            updateSelectionCount();
            updateSelectAllCheckbox();
        },
        columnDefs: [
            {
                class: "details-control",
                targets: 0,
                orderable: false,
                data: null,
                render: () => {
                    return '';
                }
            },
            {
                name: "selection",
                orderable: false,
                data: null,
                localIndex: 'selection',
                targets: 1,
                render(data, type, row) {
                    return `<input type="checkbox" class="row-checkbox" data-id="${row.DT_RowId}">`;
                },
            },
            {
                name: "d.id",
                data: "deal_ref_no",
                targets: 2,
                render: function (data) {
                    var text = data.display;
                    var url = data.url;
                    var html = '<a href="' + url + '" target="_blank">' + text + "</a>";
                    return html;
                },
            },
            {
                name: "tower_name",
                data: "tower_name",
                targets: 3,
            },
            {
                name: "d.unit_number",
                data: "unit_number",
                targets: 4,
            },
            {
                name: "owner_name",
                data: "landlord",
                targets: 5,
                render: function (data) {
                    var html = data.name;
                    return html;
                },
            },
            {
                name: "invoice",
                data: "landlord",
                targets: 6,
                render: function (data) {
                    var html = data.invoice;
                    return html;
                },
            },
            {
                data: "landlord_payment_status",
                name: "landlord_payment_status",
                targets: 7,
                render: function (data, _, row) {
                    const totalAmount = parseFloat(row.landlord_invoice_amount) || 0;
                    const cashedInAmount = parseFloat(row.landlord_cashed_in_amount) || 0;
                    const notCashedInAmount = parseFloat(row.landlord_not_cashed_in_amount) || 0;
                    const percentagePaid = totalAmount > 0 ? (cashedInAmount / totalAmount) * 100 : 0;

                    let color;
                    if (percentagePaid >= 100) {
                        color = '#28a745';
                    } else if (percentagePaid > 0) {
                        color = '#fd7e14';
                    } else {
                        color = '#000000';
                    }

                    const formattedCashedIn = Intl.NumberFormat('en-US').format(Math.round(cashedInAmount));
                    const formattedPending = Intl.NumberFormat('en-US').format(Math.round(notCashedInAmount));
                    const formattedTotal = Intl.NumberFormat('en-US').format(Math.round(totalAmount));

                    if (percentagePaid >= 100) {
                        return `<div class="text-nowrap" style="color: ${color}; font-weight: bold;">
                            ${formattedTotal}
                        </div>`;
                    } else {
                        return `<div class="text-nowrap" style="color: ${color}; font-weight: bold;">
                            ${formattedCashedIn} / ${formattedPending} / ${formattedTotal}
                        </div>`;
                    }
                }
            },
            {
                name: "d.listing_agent_id",
                data: "listing_agent_id",
                targets: 8,
            },
            {
                name: "client_name",
                data: "client",
                targets: 9,
                render: function (data) {
                    var html = data.name;
                    return html;
                },
            },
            {
                name: "invoice",
                data: "client",
                targets: 10,
                render: function (data) {
                    var html =data.invoice;
                    return html;
                },
            },
            {
                data: "client_payment_status",
                name: "client_payment_status",
                targets: 11,
                render: function (data, _, row) {
                    const totalAmount = parseFloat(row.client_invoice_amount) || 0;
                    const cashedInAmount = parseFloat(row.client_cashed_in_amount) || 0;
                    const notCashedInAmount = parseFloat(row.client_not_cashed_in_amount) || 0;
                    const percentagePaid = totalAmount > 0 ? (cashedInAmount / totalAmount) * 100 : 0;

                    let color;
                    if (percentagePaid >= 100) {
                        color = '#28a745';
                    } else if (percentagePaid > 0) {
                        color = '#fd7e14';
                    } else {
                        color = '#000000';
                    }

                    const formattedCashedIn = Intl.NumberFormat('en-US').format(Math.round(cashedInAmount));
                    const formattedPending = Intl.NumberFormat('en-US').format(Math.round(notCashedInAmount));
                    const formattedTotal = Intl.NumberFormat('en-US').format(Math.round(totalAmount));

                    if (percentagePaid >= 100) {
                        return `<div class="text-nowrap" style="color: ${color}; font-weight: bold;">
                            ${formattedTotal}
                        </div>`;
                    } else {
                        return `<div class="text-nowrap" style="color: ${color}; font-weight: bold;">
                            ${formattedCashedIn} / ${formattedPending} / ${formattedTotal}
                        </div>`;
                    }
                }
            },
            {
                name: "d.closing_agent_id",
                data: "closing_agent_id",
                targets: 12,
            },

            {
                name: "lead_source",
                data: "lead_source",
                targets: 13,
            },
            {
                targets: -1,
                data: "DT_RowId",
                className: "export-transient",
                render: function (data, type, full, row) {
                    const id = full.DT_RowId
                    const editLink =  `<li><a class="dropdown-item" href="/crm/accountant/${id}/edit"><i class="bi bi-pen"></i> Edit</a></li>`;
                    const goToDeal = `<li><a class="dropdown-item" href="/crm/deals/${id}/edit""><i class="bi bi-send"></i> Go to deal</a></li>`;

                    // Add invoice actions for landlord and client invoices
                    let invoiceActions = '';
                    if (full.landlord_invoice_id) {
                        invoiceActions += `<li><hr class="dropdown-divider"></li>`;
                        invoiceActions += `<li><a class="dropdown-item" href="/crm/deals/${id}/invoice/${full.landlord_invoice_id}/view" target="_blank"><i class="bi bi-eye"></i> View Landlord Invoice</a></li>`;
                        invoiceActions += `<li><a class="dropdown-item" href="/crm/deals/${id}/invoice/${full.landlord_invoice_id}/download"><i class="bi bi-download"></i> Download Landlord Invoice</a></li>`;
                    }
                    if (full.client_invoice_id) {
                        if (!invoiceActions) invoiceActions += `<li><hr class="dropdown-divider"></li>`;
                        invoiceActions += `<li><a class="dropdown-item" href="/crm/deals/${id}/invoice/${full.client_invoice_id}/view" target="_blank"><i class="bi bi-eye"></i> View Client Invoice</a></li>`;
                        invoiceActions += `<li><a class="dropdown-item" href="/crm/deals/${id}/invoice/${full.client_invoice_id}/download"><i class="bi bi-download"></i> Download Client Invoice</a></li>`;
                    }

                    return `<div class="dropdown" data-item-id="${id}">
                    <div onclick="javascript: toggleActionMenu(${id})" id="dropdownMenuButton${id}">
                        <button class="btn btn-sm btn-light dropdown-toggle" type="button" >
                            Actions
                        </button>
                    </div>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton${id}">
                        ${editLink}
                        ${goToDeal}
                        ${invoiceActions}
                    </ul>
                </div>`;
                },
                orderable: false,
                searchable: false,
                responsivePriority: 1
            },
        ],
        orderCellsTop: true,
        fnServerData: addCustomFiltersAndSearch,
        fnRowCallback: function (nRow, aData) {
        },
    });
    const detailRows = [];

    function format(dealData) {
        // const commentValue = dealData.comments ? dealData.comments : "-";
        const operationHistory = dealData.operation_history;
        const remarkMarkup = `
            <div style="max-width: 600px">
                <article>
                    <label style="font-weight: bold">Remarks</label>
                    <div class="d-flex flex-column align-items-end mb-2" style="width: 100%">
                        <textarea name="operation_history" class="form-control"></textarea>
                        <a role="button" class="btn btn-outline-secondary btn-sm mt-2 saveRemark" data-deal-id="${dealData.id}">Save</a>
                    </div>
                </article>

                <div style="${operationHistory.length > 0 ? 'height: 100px; overflow: scroll;' : ''}">
                ${operationHistory.length > 0 ?
                operationHistory.map(
                    (r) => {
                        return `<article class="mt-4" style="word-wrap: break-word; white-space: normal;"><section class="author"><span class="small d-flex justify-content-start"><em>${r.created_by}</em> - ${r.created_at}</section><section><p class="mb-0">${r.content}</p></section></article>`
                    }).join("") : "<article><p>No remarks</p></article>"
            }
                </div>
            </div>
        `

        return `
            <div class="mt-2 mb-2">
                ${remarkMarkup}
            </div>
        `;
    }

    document.body.addEventListener("click", function (event) {
        if (event.target.classList.contains("saveRemark")) {
            const dealId = event.target.getAttribute("data-deal-id");
            const textarea = event.target.closest("article").querySelector('[name=operation_history]');
            if (!textarea) {
                console.error("Textarea not found for dealId:", dealId);
                return;
            }
            const operationHistory = textarea.value
            if (!operationHistory.trim()) {
                return
            }
            const formData = {
                operationHistory
            };
            request(`/crm/accountant/${dealId}/details/save-remark`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
            })
                .then(response => {
                    showToast('The remark has been saved.', 'success');
                    textarea.value = "";
                    const remarksContainer = event.target.closest("article").nextElementSibling;
                    if (remarksContainer) {
                        const latestRemark = response.operation_history[response.operation_history.length - 1];

                        // const remarkMarkup = `<article class="mt-4" style="word-wrap: break-word; white-space: normal;"><section class="author"><span class="small d-flex justify-content-start"><em>${latestRemark.created_by}</em> - ${latestRemark.created_at}</section><section><p class="mb-0">${latestRemark.content}</p></section></article>`
                        const remarkMarkup = `<section class="author"><span class="small d-flex justify-content-start"><em>${latestRemark.created_by}</em> - ${latestRemark.created_at}</section><section><p class="mb-0">${latestRemark.content}</p></section>`;
                        const newRemark = document.createElement("article");
                        newRemark.classList.add("mt-4");
                        newRemark.style.wordWrap = "break-word";
                        newRemark.style.whiteSpace = "normal";
                        newRemark.innerHTML = remarkMarkup;

                        remarksContainer.prepend(newRemark);
                        const noRemarks = remarksContainer.querySelector("li");
                        if (noRemarks) {
                            noRemarks.remove();
                        }
                    }
                })
        }
    });


    $("#deals-invoices-list tbody").on(
        "click",
        "tr td.details-control",
        function () {
            const tr = $(this).closest("tr");
            const row = dealsAccountantTable.row(tr);
            const idx = $.inArray(tr.attr("id"), detailRows);

            if (row.child.isShown()) {
                tr.removeClass("details");
                row.child.hide();
                // Remove from the 'open' array
                detailRows.splice(idx, 1);
            } else {
                const rowData = row.data();
                request(`/crm/accountant/${rowData.DT_RowId}/details`)
                    .then((invoiceDetails) => {
                        tr.addClass("details");
                        row.child(
                            format(invoiceDetails)
                        ).show();
                        if (idx === -1) {
                            detailRows.push(tr.attr("id"));
                        }
                    })
                    .catch((err) => console.log(err));
            }
        }
    );

    function updateSelectionCount() {
        let selectedCount = selectedRows.size;
        $('#selectedCount').text(selectedCount);
        let hasSelection = selectedCount > 0;
        $('#bulkAction').prop('disabled', !hasSelection);
        $('#applyAction').prop('disabled', !hasSelection);
    }

    $('#selectAll').on('click', function () {
        let isChecked = this.checked;
        $('.row-checkbox').each(function () {
            let rowId = $(this).data('id');
            if (isChecked) {
                selectedRows.add(rowId);
            } else {
                selectedRows.delete(rowId);
            }
            $(this).prop('checked', isChecked);
        });
        updateSelectionCount();
    });


    $(document).on('change', '.row-checkbox', function () {
        let rowId = $(this).data('id');
        if ($(this).is(':checked')) {
            selectedRows.add(rowId);
        } else {
            selectedRows.delete(rowId);
        }
        updateSelectionCount();
        updateSelectAllCheckbox();
    });

    function updateSelectAllCheckbox() {
        let totalCheckboxes = $('.row-checkbox').length;
        let checkedCheckboxes = $('.row-checkbox:checked').length;
        $('#selectAll').prop('checked', totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes);
    }

    $('#applyAction').on('click', function () {
        let selectedIds = Array.from(selectedRows);
        let action = $('#bulkAction').val();
        if (!action) return alert("Please select an action!");

        if (action === "invoce_export") {
            invoceExport(selectedIds);
        }
    });
});

function invoceExport(selectedIds) {
    if (typeof XLSX === 'undefined') {
        console.error('XLSX library is not loaded');
        showToast("Error: Excel library not loaded. Please refresh the page and try again.", 'error');

        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js';
        script.onload = function() {
            console.log('XLSX library loaded dynamically');
            showToast("Excel library loaded. Please try exporting again.", 'info');
        };
        script.onerror = function() {
            console.error('Failed to load XLSX library dynamically');
            showToast("Failed to load Excel library. Please refresh the page.", 'error');
        };
        document.head.appendChild(script);
        return;
    }

    if (selectedIds && selectedIds.length > 0) {
        const loadingOverlay = $('<div id="excel-loading-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;"><div style="background-color: white; padding: 20px; border-radius: 5px;"><h3>Excel is being generated...</h3><p>Please wait. This process may take a few moments.</p></div></div>');
        $('body').append(loadingOverlay);
        $.ajax({
            url: '/crm/accountant/export-data',
            method: 'GET',
            data: {
                ids: selectedIds,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response && response.data) {
                    const excelData = [];
                    const headers = [
                        'Tower',
                        'Unit Number',
                        'Landlord',
                        'Landlord Invoice No.',
                        'Landlord Cash In (QAR)',
                        'Landlord Pending (QAR)',
                        'Landlord Total (QAR)',
                        'Landlord Agent',
                        'Client',
                        'Client Invoice Nr.',
                        'Client Cash In (QAR)',
                        'Client Pending (QAR)',
                        'Client Total (QAR)',
                        'Client Agent',
                        'Source'
                    ];
                    excelData.push(headers);
                    response.data.forEach(function(rowData) {
                        if (selectedIds.includes(Number(rowData.DT_RowId))) {
                            const row = [];

                            const columns = [
                                'tower_name',
                                'unit_number',
                                'landlord',
                                'owner_invoice_no',
                                'landlord_cashed_in_amount',
                                'landlord_not_cashed_in_amount',
                                'landlord_invoice_amount',
                                'listing_agent_id',
                                'client',
                                'client_invoice_no',
                                'client_cashed_in_amount',
                                'client_not_cashed_in_amount',
                                'client_invoice_amount',
                                'closing_agent_id',
                                'lead_source'
                            ];

                            columns.forEach(function(key) {
                                let cellData = rowData[key];

                                if (typeof cellData === 'object' && cellData !== null) {
                                    if (key === 'landlord' && cellData.name) {
                                        cellData = cellData.name;
                                    } else if (key === 'client' && cellData.name) {
                                        cellData = cellData.name;
                                    } else if (key === 'listing_agent_id' || key === 'closing_agent_id') {
                                        cellData = cellData.name || '';
                                    } else if (key === 'deal_payment_status') {
                                        if (cellData[0] === 'STATUS_PENDING' && cellData[1] === 'STATUS_PENDING') {
                                            cellData = "In Pending";
                                        } else if (cellData[0] === 'STATUS_CASHED' && cellData[1] === 'STATUS_CASHED') {
                                            cellData = "Cashed";
                                        } else if (cellData[0] === 'STATUS_PENDING' && cellData[1] === 'STATUS_CASHED') {
                                            cellData = "Pending Cashed";
                                        } else if (cellData[0] === 'STATUS_CASHED' && cellData[1] === 'STATUS_PENDING') {
                                            cellData = "Cashed Pending";
                                        } else if (cellData[0] === 'STATUS_CASHED') {
                                            cellData = "Cashed(landlord)";
                                        } else if (cellData[0] === 'STATUS_PENDING') {
                                            cellData = "In pending(landlord)";
                                        } else if (cellData[1] === 'STATUS_CASHED') {
                                            cellData = "Cashed(client)";
                                        } else if (cellData[1] === 'STATUS_PENDING') {
                                            cellData = "In pending(client)";
                                        } else {
                                            cellData = "N/A";
                                        }
                                    }
                                }

                                if(key == 'owner_invoice_no') {
                                    cellData = !!rowData['landlord'] ? rowData['landlord']['invoice'] ?? 'N/A' : 'N/A';
                                }
                                if(key == 'client_invoice_no') {
                                    cellData = !!rowData['client'] ? rowData['client']['invoice'] ?? 'N/A' : 'N/A';
                                }

                                if(key === 'landlord_cashed_in_amount' || key === 'landlord_not_cashed_in_amount' || key === 'landlord_invoice_amount' ||
                                   key === 'client_cashed_in_amount' || key === 'client_not_cashed_in_amount' || key === 'client_invoice_amount') {
                                    const amount = parseFloat(cellData) || 0;
                                    cellData = Math.round(amount).toLocaleString('en-US');
                                }

                                row.push(cellData || 'N/A');
                            });

                            excelData.push(row);
                        }
                    });

                    // Check if XLSX is defined
                    if (typeof XLSX === 'undefined') {
                        console.error('XLSX library is not loaded');
                        $('#excel-loading-overlay').remove();
                        showToast("Error: Excel library not loaded. Please refresh the page and try again.", 'error');
                        return;
                    }

                    try {
                        const wb = XLSX.utils.book_new();
                        const ws = XLSX.utils.aoa_to_sheet(excelData);

                        XLSX.utils.book_append_sheet(wb, ws, 'Deals Invoices');

                        const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });

                        function s2ab(s) {
                            const buf = new ArrayBuffer(s.length);
                            const view = new Uint8Array(buf);
                            for (let i = 0; i < s.length; i++) {
                                view[i] = s.charCodeAt(i) & 0xFF;
                            }
                            return buf;
                        }

                        const blob = new Blob([s2ab(wbout)], { type: 'application/octet-stream' });
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'deals-invoices-export.xlsx';
                        document.body.appendChild(a);
                        a.click();

                        $('#excel-loading-overlay').remove();

                        showToast("Export generated successfully!", 'success');

                        setTimeout(function() {
                            window.URL.revokeObjectURL(url);
                            document.body.removeChild(a);
                        }, 0);
                    } catch (error) {
                        console.error('Error generating Excel file:', error);
                        $('#excel-loading-overlay').remove();
                        showToast("Error generating Excel file. Please try again.", 'error');
                    }
                } else {
                    $('#excel-loading-overlay').remove();
                    showToast("Error", 'error');
                }
            },
            error: function(error) {
                console.error('Error fetching data for Excel export:', error);
                $('#excel-loading-overlay').remove();
                showToast("Error", 'error');
            }
        });
    } else {
        showToast("Error", 'error');
    }
}

function onDealsFiltersParamsChange(filterParams) {
    window.accountantSearchParams = filterParams;
    if (!filtersInitialized) {
        filtersInitialized = true;
    }
    if (!!dealsAccountantTable) {
        dealsAccountantTable.draw();
    }
}