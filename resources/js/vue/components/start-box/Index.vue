<template>
    <div class="start-box__tabs min-w-120">
        <a
            :class="{ active: operationType == 'buy' }"
            href="javascript:void(0)"
            @click="setOperationType('buy')"
            >{{ $translate("Buy") }}</a
        >
        <a
            :class="{ active: operationType == 'rent' }"
            href="javascript:void(0)"
            @click="setOperationType('rent')"
            >{{ $translate("Rent") }}</a
        >
    </div>

    <div class="start-box__content">
        <h1 class="">
            {{ $translate("Find Exclusive Properties in Qatar") }}
        </h1>
        <p class="mt-2 text-gray-60 text-normal">
            {{
                $translate(
                    "Discover the most attractive properties depending on your budget and preferences"
                )
            }}
        </p>
        <form autocomplete="off" onsubmit="javascript: return false;">
            <input
                autocomplete="false"
                name="hidden"
                type="text"
                style="display: none"
            />

            <div class="mt-3 search-container">
                <div class="input-group w-full">
                    <SearchBox
                        :config="searchBoxConfig"
                        @value-change="onSearchBoxValueChange($event)"
                        @control-focus="onSearchBoxInputFocus()"
                        @option-selected="onSearchBoxOptionSelected($event)"
                        @control-blur="onSearchBoxInputBlur()"
                    ></SearchBox>
                    <Dropdown2
                        :config="propertyTypesConfig"
                        @value-change="onPropertyTypeChange($event)"
                    />
                </div>
            </div>

            <div class="_actions flex justify-end mt-4">

                <label v-if="false" class="checkbox text-gray-90">
                    <input
                        type="checkbox"
                        v-model="mapViewModel"
                        @change="mapViewChange()"
                    />
                    {{ $translate("Show Results in Map View") }}
                </label>

                <button class="btn" @click="triggerSearch()">
                    {{ $translate("Search") }}
                </button>
            </div>
        </form>
    </div>
</template>

<script>
import { onMounted, ref, inject } from "vue";

import Dropdown2 from "../generic/Dropdown2";
import SearchBox from "../generic/SearchBox";

import { FrontendProvisionService } from "../../services/frontend-provision.service";
import { I18nService } from "../../services/i18n.service";

export default {
    name: "Index",

    setup() {
        const $translate = inject("translate");
        const initialOperationType = "buy";
        const operationType = ref(initialOperationType);
        const propertyTypesConfig = ref({
            defaultLabel: $translate("Property type"),
            options: [],
        });
        const searchBoxConfig = ref({
            debounceTime: 300,
            placeholder: $translate("Location, ref. no., keywords"),
            options: [],
        });
        const mapViewModel = ref(false);

        const pickedOptions = ref({ operationType: initialOperationType });

        const setOperationType = (nextOperationTypeValue) => {
            pickedOptions.value.operationType = nextOperationTypeValue;
            operationType.value = nextOperationTypeValue;
        };

        const onPropertyTypeChange = (option) => {
            pickedOptions.value.propertyType = option;
            propertyTypesConfig.value.selectedOption = { ...option };
        };

        const onSearchBoxValueChange = (term = null) => {
            if (!term) {
                pickedOptions.value.searchOption = null;
                searchBoxConfig.value.options = [];
                return;
            }

            FrontendProvisionService.getStartBoxSuggestions(term, (res) => {
                searchBoxConfig.value.options = (
                    res && res.data ? res.data : []
                ).map(({ value, data, isSingleListing = false, url = "" }) => ({
                    value: data,
                    label: value,
                    url,
                    isSingleListing,
                }));
            });
        };

        const onSearchBoxInputFocus = () => {
            pickedOptions.value.searchOption = null;
            searchBoxConfig.value.options = mainGeographies.map(
                ({ value, data, isSingleListing = false }) => ({
                    value: data,
                    label: value,
                    isSingleListing,
                })
            );

            // if(!term) {
            //     pickedOptions.value.searchOption = null;
            //     searchBoxConfig.value.options = [];
            //     return;
            // }
            //
            // FrontendProvisionService.getStartBoxSuggestions(term, (res) => {
            //     searchBoxConfig.value.options = (res && res.data ? res.data : []).map(({value, data, isSingleListing = false}) => ({ value: data, label: value, isSingleListing}));
            // })
        };

        const onSearchBoxInputBlur = () => {
            setTimeout(() => (searchBoxConfig.value.options = []), 300);
        };

        const onSearchBoxOptionSelected = (option) => {
            pickedOptions.value.searchOption = option;
            searchBoxConfig.value.options = [];
        };

        const mapViewChange = () => {
            pickedOptions.value.mapView = mapViewModel;
        };

        const triggerSearch = () => {
            // calculate next url and go
            // let slugParts = [];
            const locale = I18nService.getLocale();
            const operationType = pickedOptions.value.operationType;
            const propertyType = !!pickedOptions.value.propertyType
                ? pickedOptions.value.propertyType.value
                : "properties";
            // if(pickedOptions.value.searchOption) {
            //     slugParts = pickedOptions.value.searchOption.value.split('--');
            // }

            // let nextURL = `/${locale}/`;
            //

            //
            // if(slugParts.length) {
            //     nextURL = `${nextURL}${operationType}${slugParts.length ? '/' + slugParts[0] : ''}` +
            //     `${pickedOptions.value.propertyType ? '/' + pickedOptions.value.propertyType.value + '-for-' + (operationType === 'rent' ? 'rent' : 'sale') : ''}` +
            //     `${slugParts[1] ? '-' + slugParts[1] + (slugParts[2] ? '-' + slugParts[2] : '') : ''}`;
            // } else {
            //     nextURL = `${nextURL}${operationType}/${propertyType}-for-${operationType === 'rent' ? 'rent' : 'sale'}`
            // }
            //
            // if(pickedOptions.value.mapView) {
            //     nextURL += '?mapView=1';
            // }

            const params = new URLSearchParams();
            params.append("operationType", operationType);
            if (propertyType !== "properties") {
                params.append("propertyType", propertyType);
            }
            if (pickedOptions.value.searchOption) {
                const usedGeography = mainGeographies.find(
                    (mg) => mg.data == pickedOptions.value.searchOption.value
                );
                console.log("used geo: ", usedGeography);
                if (!!usedGeography) {
                    params.append("location", usedGeography.id);
                }
            }

            fetch(`/api/route-data?${params}`, {
                headers: {
                    Accept: "application/json",
                },
            })
                .then((res) => res.json())
                .then((routeData) => {
                    if (routeData && routeData.urls && routeData.urls[locale]) {
                        const url = new URL(routeData.urls[locale].url);
                        // Get current search params from browser URL
                        const currentParams = new URLSearchParams(window.location.search);
                        // Add all current params to the new URL
                        currentParams.forEach((value, key) => {
                            url.searchParams.append(key, value);
                        });
                        // Add mapView param if needed
                        if (pickedOptions.value.mapView) {
                            url.searchParams.append('mapView', '1');
                        }
                        window.location = url.toString();
                    }
                });

            // window.location = nextURL;
            return false;
        };

        onMounted(() => {
            propertyTypesConfig.value.options = propertyTypes;
        });

        return {
            operationType,
            searchBoxConfig,
            propertyTypesConfig,
            pickedOptions,
            mapViewModel,
            setOperationType,
            onPropertyTypeChange,
            onSearchBoxValueChange,
            onSearchBoxOptionSelected,
            onSearchBoxInputFocus,
            onSearchBoxInputBlur,
            triggerSearch,
            mapViewChange,
        };
    },

    components: {
        Dropdown2,
        SearchBox,
    },
};
</script>
