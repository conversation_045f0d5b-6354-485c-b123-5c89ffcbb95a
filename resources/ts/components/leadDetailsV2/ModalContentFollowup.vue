<template>
    <article>
        <form>
            <div v-if="state.form.automatization_id" class="container w-100 p-0 mb-3">
                <div class="p-3 bg-light rounded-1 shadow-xs">
                    <h5 class="mb-3">Contact Information</h5>
                    <div class="d-flex justify-content-between align-items-start">
                        <ul class="list-unstyled mb-0">
                            <li><strong>Name:</strong> <a target="_blank"
                                    :href="`/crm/contacts-list#/${state.form.contact_id}/edit`">{{
                                    state.form.contact_name }}</a></li>
                            <li><strong>Phone:</strong> {{ state.form.contact_phone }}</li>
                            <li><strong>Email:</strong> {{ state.form.contact_email }}</li>
                        </ul>
                    </div>
                    <div class="d-flex justify-content-end gap-2 pt-2">
                        <a role="button" class="btn btn-outline-primary btn-sm"
                            :href="`/crm/leads/create?source=contact&contactId=${state.form.contact_id}&taskId=${state.form.id}`">
                            <span class="ms-1">Create lead</span>
                        </a>
                        <a role="button" class="btn btn-outline-primary btn-sm"
                            :href="`/crm/broker-landlords/${state.form.contact_id}/edit?taskId=${state.form.id}`">
                            <span class="ms-1">Create Landlord</span>
                        </a>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="subject" class="form-label">Subject</label>
                <input v-model="state.form.subject" type="text" class="form-control">
            </div>
            <div class="mb-3 d-flex gap-2">
                <div class="flex-grow-1 flex-shrink-1">
                    <label for="actionSelect" class="form-label">Action *</label>
                    <select v-model="state.form.actionType" id="actionSelect" :class="['form-select', {'is-invalid': state.errors.actionType}]">
                        <option value="">Please select</option>
                        <option v-for="cr of actionTypes" :value="cr.value">{{ cr.label }}</option>
                    </select>
                    <div class="invalid-feedback" v-if="state.errors.actionType">{{ state.errors.actionType }}</div>
                </div>
                <div class="flex-grow-1 flex-shrink-1">
                    <label for="meetingStatus" class="form-label">Status</label>
                    <select v-model="state.form.status" id="status" class="form-select">
                        <option v-for="opt of statusOptions" :value="opt.value">{{ opt.label }}</option>
                    </select>
                </div>
            </div>
            <div class="mb-3">
                <label for="notes" class="form-label">Notes *</label>
                <textarea v-model="state.form.notes" :class="['form-control', {'is-invalid': state.errors.notes}]" id="notes" rows="3"></textarea>
                <div class="invalid-feedback" v-if="state.errors.notes">{{ state.errors.notes }}</div>
            </div>
        </form>
    </article>
</template>

<script setup lang="ts">
import { ActivityFollowupFormData, FETask, LeadMeetingStatus } from './../../model/fe-models.interface';
import { reactive, watch } from 'vue';

interface ModalContentFollowupState {
    form: ActivityFollowupFormData;
    errors: {
        actionType?: string;
        notes?: string;
    };
}

const actionTypes = [{
    value: 'call',
    label: 'Call',
}, {
    value: 'email',
    label: 'Email',
}, {
    value: 'nurture',
    label: 'Nurture',
}, {
    value: 'in person visit',
    label: 'In person visit',
},{
    value: 'whatsapp',
    label: 'Whatsapp',
},{
    value: 'text message',
    label: 'Text Message',
}];

const statusOptions = [{
    label: "Not Started",
    value: "not_started"
}, {
    label: "Completed",
    value: "completed"
}, {
    label: "In Progress",
    value: "in_progress"
}];

const getInitialFormData = (task: FETask): ActivityFollowupFormData => {
    const formData: ActivityFollowupFormData = {
        id: null,
        subject: 'Follow up',
        actionType: null,
        status: LeadMeetingStatus.NotStarted,
        notes: '',
        automatization_id: null,
        contact_id: null,
        contact_name: '-',
        contact_phone: '-',
        contact_email: '-'
    }
    if (task) {
        formData.id = task.id;
        formData.subject = task.subject;
        formData.status = task.status;
        formData.actionType = task.actionType;
        formData.notes = task.notes;
        formData.automatization_id = task.automatization_id;
        formData.contact_id = task.contact?.id;
        formData.contact_name = task.contact?.name;
        formData.contact_phone = task.contact?.mobile_1;
        formData.contact_email = task.contact?.email_1;
    }
    return formData;
}

const props = defineProps(['task'])

const emit = defineEmits<{
    (e: 'dataChange', payload: { formData: ActivityFollowupFormData, isValid: boolean }): void;
}>()

const initialFormData = getInitialFormData(props.task);

const state = reactive<ModalContentFollowupState>({
    form: initialFormData,
    errors: {}
})

const validate = (): boolean => {
    state.errors = {};
    let isValid = true;
    if (!state.form.actionType) {
        state.errors.actionType = 'Action is required';
        isValid = false;
    }
    if (typeof state.form.notes !== 'string' || !state.form.notes.trim()) {
        state.errors.notes = 'Notes field is required';
        isValid = false;
    }
    return isValid;
}

const emitValidation = () => {
    const isValid = validate();
    const safeFormData = { ...state.form, notes: typeof state.form.notes === 'string' ? state.form.notes : '' };
    emit('dataChange', { formData: safeFormData, isValid });
};

emitValidation();

watch(
    () => [state.form.subject, state.form.notes, state.form.actionType, state.form.status],
    () => {
        emitValidation();
    });
</script>

<style lang="scss" scoped></style>