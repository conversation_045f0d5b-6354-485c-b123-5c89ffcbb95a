<?php

// app/Services/WhatsAppMessageService.php
namespace App\Services;

use App\Models\Agent;
use App\Models\User;
use App\Models\WhatsappMessage;
use App\Notifications\NewWhatsAppMessage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class WhatsAppMessageService
{
    /**
     * Process incoming WhatsApp message
     */
    public function processMessage(array $message, array $messageData, ?string $entryId = null): void
    {
        try {
            // Skip if message already exists
            if (WhatsappMessage::where('message_id', $message['id'])->exists()) {
                Log::info('Message already processed', ['message_id' => $message['id']]);
                return;
            }

            // Extract message content
            $content = $this->extractMessageContent($message);
            $messageType = $message['type'] ?? 'unknown';
            
            // Get sender info - improved extraction from Facebook WhatsApp payload
            $senderPhone = $message['from'] ?? null;
            $senderName = null;

            // Extract sender name from contacts array if available
            if (isset($messageData['contacts']) && is_array($messageData['contacts'])) {
                foreach ($messageData['contacts'] as $contact) {
                    if (isset($contact['wa_id']) && $contact['wa_id'] === $senderPhone) {
                        $senderName = $contact['profile']['name'] ?? null;
                        break;
                    }
                }
            }

            // Fallback to first contact if no matching wa_id found
            if (!$senderName && isset($messageData['contacts'][0]['profile']['name'])) {
                $senderName = $messageData['contacts'][0]['profile']['name'];
            }

            // Extract reference number
            $refNumber = $this->extractReferenceNumber($content);
            
            // Find agent if reference number exists
            // $agent = $refNumber ? $this->findAgentByReference($refNumber) : null;
            $agent = null;

            // Create message record with improved metadata
            $whatsappMessage = WhatsappMessage::create([
                'message_id' => $message['id'],
                'sender_phone' => $senderPhone,
                'sender_name' => $senderName,
                'message_content' => $content,
                'message_type' => $messageType,
                'metadata' => [
                    'original_message' => $message,
                    'contacts' => $messageData['contacts'] ?? [],
                    'business_account_id' => $messageData['metadata']['phone_number_id'] ?? null,
                    'display_phone_number' => $messageData['metadata']['display_phone_number'] ?? null,
                    'messaging_product' => $messageData['messaging_product'] ?? null,
                    'entry_id' => $entryId,
                    'timestamp' => $message['timestamp'] ?? null
                ],
                'ref_number' => $refNumber,
                'agent_id' => $agent ? $agent->id : null,
                'message_timestamp' => isset($message['timestamp']) ?
                    \Carbon\Carbon::createFromTimestamp($message['timestamp']) : now()
            ]);

            // Notify agent if found
            if ($agent) {
                $this->notifyAgent($agent, $whatsappMessage);
            }

            Log::info('WhatsApp message processed successfully', [
                'message_id' => $message['id'],
                'sender' => $senderPhone,
                'ref_number' => $refNumber,
                'agent_id' => $agent ? $agent->id : null
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process WhatsApp message', [
                'message_id' => $message['id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Extract message content based on message type
     */
    protected function extractMessageContent(array $message): string
    {
        switch ($message['type']) {
            case 'text':
                return $message['text']['body'] ?? '';

            case 'image':
                $caption = $message['image']['caption'] ?? '';
                $mediaId = $message['image']['id'] ?? '';
                return $caption ?: "[Image received - ID: $mediaId]";

            case 'document':
                $filename = $message['document']['filename'] ?? 'Unknown file';
                $caption = $message['document']['caption'] ?? '';
                $mediaId = $message['document']['id'] ?? '';
                return $caption ? "$caption [Document: $filename - ID: $mediaId]" : "[Document: $filename - ID: $mediaId]";

            case 'audio':
                $mediaId = $message['audio']['id'] ?? '';
                return "[Audio message - ID: $mediaId]";

            case 'video':
                $caption = $message['video']['caption'] ?? '';
                $mediaId = $message['video']['id'] ?? '';
                return $caption ?: "[Video received - ID: $mediaId]";

            case 'location':
                $lat = $message['location']['latitude'] ?? '';
                $lng = $message['location']['longitude'] ?? '';
                $name = $message['location']['name'] ?? '';
                $address = $message['location']['address'] ?? '';
                $locationInfo = $name ? "Name: $name" : '';
                $locationInfo .= $address ? ($locationInfo ? ", Address: $address" : "Address: $address") : '';
                return "[Location shared: $lat, $lng" . ($locationInfo ? " - $locationInfo" : '') . "]";

            case 'contacts':
                $contactsCount = count($message['contacts'] ?? []);
                return "[Contact(s) shared - Count: $contactsCount]";

            case 'sticker':
                $mediaId = $message['sticker']['id'] ?? '';
                return "[Sticker sent - ID: $mediaId]";

            case 'button':
                $buttonText = $message['button']['text'] ?? '';
                $buttonPayload = $message['button']['payload'] ?? '';
                return "[Button pressed: $buttonText" . ($buttonPayload ? " - Payload: $buttonPayload" : '') . "]";

            case 'interactive':
                $interactiveType = $message['interactive']['type'] ?? '';
                if ($interactiveType === 'button_reply') {
                    $buttonId = $message['interactive']['button_reply']['id'] ?? '';
                    $buttonTitle = $message['interactive']['button_reply']['title'] ?? '';
                    return "[Interactive button: $buttonTitle - ID: $buttonId]";
                } elseif ($interactiveType === 'list_reply') {
                    $listId = $message['interactive']['list_reply']['id'] ?? '';
                    $listTitle = $message['interactive']['list_reply']['title'] ?? '';
                    return "[Interactive list: $listTitle - ID: $listId]";
                }
                return "[Interactive message - Type: $interactiveType]";

            default:
                return '[Unsupported message type: ' . $message['type'] . ']';
        }
    }

    /**
     * Extract reference number from message content
     */
    protected function extractReferenceNumber(string $content): ?string
    {
        // Common patterns for reference numbers
        $patterns = [
            '/REF[\s:]*([A-Z0-9]{6,12})/i',           // REF: ABC123456
            '/REF[\s#]*([A-Z0-9]{6,12})/i',           // REF #ABC123456
            '/REFERENCE[\s:]*([A-Z0-9]{6,12})/i',     // REFERENCE: ABC123456
            '/\b([A-Z]{2,3}[0-9]{4,8})\b/',           // ABC12345678
            '/\b([0-9]{6,10})\b/',                    // 1234567890
            '/#([A-Z0-9]{6,12})/i',                   // #ABC123456
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                return strtoupper(trim($matches[1]));
            }
        }

        return null;
    }

    /**
     * Find agent by reference number
     */
    protected function findAgentByReference(string $refNumber): ?User
    {
        // Get all active agents
        $agents = User::where('active', true)->get();

        foreach ($agents as $agent) {
            // Check if agent has specific patterns
            if ($agent->ref_patterns) {
                foreach ($agent->ref_patterns as $pattern) {
                    if (preg_match($pattern, $refNumber)) {
                        return $agent;
                    }
                }
            }

            // Default pattern matching based on agent ID or prefix
            $agentPrefix = strtoupper(substr($agent->name, 0, 2));
            if (str_starts_with($refNumber, $agentPrefix)) {
                return $agent;
            }
        }

        return null;
    }

    /**
     * Notify agent about new message
     */
    protected function notifyAgent(User $agent, WhatsappMessage $message): void
    {
        Log::info("[WhatsApp] - agent should be notified", ['agent_id' => $agent->id, 'message_id' => $message->message_id]);
        // try {
        //     Notification::send($agent, new NewWhatsAppMessage($message));
            
        //     $message->update(['agent_notified' => true]);
            
        //     Log::info('Agent notified successfully', [
        //         'agent_id' => $agent->id,
        //         'message_id' => $message->message_id
        //     ]);
        // } catch (\Exception $e) {
        //     Log::error('Failed to notify agent', [
        //         'agent_id' => $agent->id,
        //         'message_id' => $message->message_id,
        //         'error' => $e->getMessage()
        //     ]);
        // }
    }

    /**
     * Process WhatsApp message status updates (delivery receipts, read receipts, etc.)
     */
    public function processMessageStatus(array $status, array $messageData, ?string $entryId = null): void
    {
        try {
            $messageId = $status['id'] ?? null;
            $statusType = $status['status'] ?? null;
            $recipientId = $status['recipient_id'] ?? null;
            $timestamp = $status['timestamp'] ?? null;

            if (!$messageId || !$statusType) {
                Log::warning('[WhatsAppMessageService] Invalid status update', [
                    'status' => $status
                ]);
                return;
            }

            // Find the original message
            $whatsappMessage = WhatsappMessage::where('message_id', $messageId)->first();

            if (!$whatsappMessage) {
                Log::info('[WhatsAppMessageService] Status update for unknown message', [
                    'message_id' => $messageId,
                    'status' => $statusType
                ]);
                return;
            }

            // Update message metadata with status information
            $metadata = $whatsappMessage->metadata ?? [];
            $metadata['status_updates'] = $metadata['status_updates'] ?? [];
            $metadata['status_updates'][] = [
                'status' => $statusType,
                'timestamp' => $timestamp,
                'recipient_id' => $recipientId,
                'updated_at' => now()->toISOString()
            ];

            // Update the latest status
            $metadata['latest_status'] = $statusType;
            $metadata['latest_status_timestamp'] = $timestamp;

            $whatsappMessage->update(['metadata' => $metadata]);

            Log::info('[WhatsAppMessageService] Message status updated', [
                'message_id' => $messageId,
                'status' => $statusType,
                'recipient_id' => $recipientId
            ]);

        } catch (\Exception $e) {
            Log::error('[WhatsAppMessageService] Failed to process message status', [
                'status' => $status,
                'error' => $e->getMessage()
            ]);
        }
    }
}