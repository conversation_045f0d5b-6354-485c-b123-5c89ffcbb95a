<?php

use App\Services\AttachmentsService;
use App\Services\SlugifyService;
use Illuminate\Support\Str;
use App\Services\UserService;
use App\Models\User;
use App\Models\Crm\RolesDef;
use App\Models\Language;
use App\Models\MarketingParams;
use App\Services\DealsService;

function getImagesHost()
{
    return env("IMAGES_HOST", "https://www.fgrealty.qa");
}

function getCachedImagePath($listingId, $imgName)
{
    return "/images_cache/$listingId/$imgName";
}

function getPropertySlug($item)
{
    $slugifyServ = App::make(SlugifyService::class);
    $theSlug =
        $item->property_type_filter_value
        . '-for-'
        . $item->ad_type
        // $slugifyServ->slugify($item->title)
        . (isset($item->area_slug) ? '-' . $item->area_slug : ($item->area ? '-' . $slugifyServ->slugify($item->area) : ''))
        . (isset($item->city_slug) ? '-' . $item->city_slug : ($item->city ? '-' . $slugifyServ->slugify($item->city) : ''))
        . '--' . $item->id;
    return $theSlug;
}

function getPropertyImageHashes($property, $width = null, $height = null)
{
    $attachServ = App::make(AttachmentsService::class);

    $attachments = $attachServ->getObjectAttachments($property);

    $urls = [];
    foreach ($attachments as $att) {
        if (!empty($att)) {
            $hash = (object) null;
            $relativeUrl = $att->name;

            if (!empty($att->seo) && !empty($att->seo->img_path)) {
                $pi = pathinfo($att->name);
                $relativeUrl = $att->seo->img_path . '.' . $pi['extension'];
            }

            if (!is_null($width)) {
                $hash->url = getPropertyImageUrl($property->id . "/" . $relativeUrl, $width, $height);
            } else {
                $hash->url = env('CACHE_IMAGES_DIR') . "/" . $property->id . "/" . $relativeUrl;
            }
            $hash->originalURL = env('CACHE_IMAGES_DIR') . "/" . $property->id . "/" . $relativeUrl;
            $hash->bigURL = getPropertyImageUrl($property->id . "/" . $relativeUrl, 1900, 1080);

            // $hash->path = $att->seo->img_path;
            $hash->alt = empty($att->seo->img_alt) ? $property->getSEOGenericString() : $att->seo->img_alt;
            $hash->title = empty($att->seo->img_title) ? $property->getSEOGenericString() : $att->seo->img_title;
            $hash->attachmentId = $att->id;
            $urls[] = $hash;
        }
    }

    return $urls;
}

function getSnapshotImageHashes($snapshot, $width = null, $height = null)
{
    $attachServ = App::make(AttachmentsService::class);

    $attachments = $attachServ->getSnapshotAttachments([$snapshot->asset_id]);

    $urls = [];
    foreach ($attachments[$snapshot->asset_id] as $att) {
        if (!empty($att)) {
            $hash = (object) null;
            $relativeUrl = $att->img_path;

            if (!empty($att->seo) && !empty($att->seo->img_path)) {
                $pi = pathinfo($att->name);
                $relativeUrl = $att->seo->img_path . '.' . $pi['extension'];
            }

            if (!is_null($width)) {
                $hash->url = getPropertyImageUrl($snapshot->asset_id . "/" . $relativeUrl, $width, $height);
            } else {
                $hash->url = env('CACHE_IMAGES_DIR') . "/snapshots/" . $snapshot->asset_id . "/" . $relativeUrl;
            }
            $hash->originalURL = env('CACHE_IMAGES_DIR') . "/snapshots/" . $snapshot->asset_id . "/" . $relativeUrl;
            $hash->url = $snapshot->asset_id . "/" . $relativeUrl;
            $hash->bigURL = getPropertyImageUrl($snapshot->asset_id . "/" . $relativeUrl, 1900, 1080);

            // $hash->path = $att->seo->img_path;
            //            $hash->alt = empty($att->seo->img_alt) ? $snapshot->getSEOGenericString() : $att->seo->img_alt;
            //            $hash->title = empty($att->seo->img_title) ? $snapshot->getSEOGenericString() : $att->seo->img_title;
            //            $hash->attachmentId = $att->id;
            $urls[] = $hash;
        }
    }

    return $urls;
}


function getTitleSuffix()
{
    return '';
}

function getPropertyTitle($item)
{
    return $item->title;
}

function singlePropertyLocation($item)
{
    $parts = [];
    foreach (['location_name'] as $field) {
        if (!empty($item->$field) && count($parts) < 2) {
            $parts[] = $item->$field;
        }
    }

    return join(" | ", array_reverse($parts));
}

function getPropertyHtmlTitle($item)
{
    return getPropertyTitle($item)
        . getTitleSuffix();
}

function propertyPrice($property, $currency = "QAR")
{
    return $currency . " " . $property->price . ($property->ad_type == 'rent' ? " / month" : "");
}

function getRegionCachedImagePath($itemId, $imgName)
{
    return "/images_cache/regions/$itemId/$imgName";
}

function getCachedImageUrl($imagePath, $width, $height, $filter)
{
    $info = pathinfo($imagePath);
    $newPath = $info["dirname"] . "/" . $info['filename'] . "-image(" . $width . "x" . $height . "-" . $filter[0] . ")." . $info['extension'];

    return $newPath;
}

function getPropertyImageUrl($listingId, $relativeURL, $template)
{
    return getImagesHost() . '/ic/' . $template . '/snapshot-' . $listingId . '-' . $relativeURL;
}

function getAmenityLabel($amenityType)
{
    switch ($amenityType) {
        case 'build-up-area':
            return 'SQM';
        case 'bedrooms':
            return 'BE';
        case 'bathrooms':
            return 'BA';
    }
}

function routeOpType($opType)
{
    return ($opType == 'buy' ? 'sale' : $opType);
}

function getDynamicLinkURL($dynamicMenuItem, $locale = Language::EN)
{
    $service = App::make(App\Services\MenuHelperService::class);
    return $service->createURL($dynamicMenuItem, $locale);
}

function user_api_token()
{
    $userApiToken = "";
    $user = auth()->user();

    if (!is_null($user)) {
        $userApiToken = $user->api_token;

        if (empty($userApiToken)) {
            $userService = App::make(UserService::class);
            $userApiToken = $userService->getToken();

            $user->api_token = $userApiToken;
            $user->save();
        }
    }

    return $userApiToken;
}

function imageUrl($template, $objectId, $imageName, $imageType = 'snapshot')
{
    return env('IMAGES_HOST') . '/ic/' . $template . '/' . $imageType . '-' . $objectId . '-' . $imageName;
}

function imageRoute($template, $filename)
{
    return route('imagecache', ['template' => $template, 'filename' => $filename], true);
}

// eav
function getAttributeByDefId($attributes, $defId)
{
    $result = $attributes->where('attribute_definition_id', $defId)->first();
    return $result;
}

function getRandomString($length = 40)
{
    return Str::random($length);
}

function titleCase($value)
{
    return Str::title($value);
}

function prepareImageAlts($supposedAlts)
{
    return is_array($supposedAlts) ? $supposedAlts : json_decode(empty($supposedAlts) ? '[]' : $supposedAlts);
}

function getHighestRole($user)
{
    $orderedRoles = [
        \App\Models\Crm\RolesDef::OFFICE_MANAGER,
        \App\Models\Crm\RolesDef::AGENT,
    ];

    foreach ($orderedRoles as $role) {
        if ($user->hasRole($role)) {
            return $role;
        }
    }

    return null;
}

function role_agent()
{
    return \App\Models\Crm\RolesDef::AGENT;
}

function isSerban()
{
    return auth()->user()->email === '<EMAIL>';
}

function concatValues($val1, $val2, $delim = ";")
{
    $result = "";
    if ($val1 != null) {
        $result .= $val1;
    }

    if ($val2 != null) {
        if ($result != "") {
            $result .= $delim;
        }
        $result .= $val2;
    }

    return $result;
}

function logoPath()
{
    return "images/svg/FGREALTY.svg";
}

function agencyName()
{
    return env('COMPANY_NAME');
}

function agencyPhone()
{
    return env('QA_COMPANY_PHONE');
}

function agencyEmail()
{
    env('EMAIL_OFFICE');
}

function getCityFromExportItem($item)
{
    if (!empty($item->location_gparent)) {
        return $item->location_gparent;
    }
    if (!empty($item->location_parent)) {
        return $item->location_parent;
    }
    if (!empty($item->location)) {
        return $item->location;
    }
    return null;
}
function getRegionFromExportItem($item)
{
    if (!empty($item->location_gparent) && !empty($item->location_parent)) {
        return $item->location_parent;
    }
    if (!empty($item->location_parent) && !empty($item->location)) {
        return $item->location;
    }
    return null;
}

function getImageURLsFromExportItem($item)
{
    if (is_string($item->images)) {
        return empty(trim($item->images)) ? [] : json_decode($item->images);
    } else {
        return $item->images;
    }
}

function getAmenitiesFromExportItem($item)
{
    return explode(",", trim($item->amenities_group));
}

function agencyAddress() {}
function agencyCity()
{
    return "Doha";
}
function agencyWebsite()
{
    return "https://www.fgrealty.qa";
}

function getAllAgents()
{
    $users = User::orderBy('name', 'asc')
        ->whereHas('roles', function ($q) {
            $q->whereIn('name', [
                RolesDef::AGENT,
                RolesDef::MATRIX_AGENT,
                RolesDef::MATRIX_AGENT_MANAGER,
                RolesDef::TEAM_LEADER,
            ]);
        })->get();

    return $users;
}
function getTeamLeaderAgents()
{
    $users = User::orderBy('name', 'asc')
        ->whereHas('roles', function ($q) {
            $q->whereIn('name', [
                RolesDef::TEAM_LEADER
            ]);
        })->get();

    return $users;
}

function getTeamLeaderTeamAgents($teamLeader = null)
{
    if (is_null($teamLeader)) {
        $teamLeader = auth()->user();
    }
    $users = User::orderBy('name', 'asc')->where(['team_leader_id' => $teamLeader->id])->get();

    return $users;
}

function getAllFGRAgents($onlyPublicProfiles = false, $orderByRating = false, $ignoreTestUsers = false)
{
    if (!$orderByRating) {
        $users = User::orderBy('name', 'asc');
    } else {
        $users = User::orderBy('rating', 'desc')->orderBy('name', 'asc');
    }

    $users->whereHas('roles', function ($q) {
        $q->whereIn('name', [
            RolesDef::AGENT,
        ]);
    });
    if ($onlyPublicProfiles) {
        $users = $users->where('public_profile', 1)->whereNotNull('short_bio');
    }
    if ($ignoreTestUsers) {
        $users = $users->where(function ($qb) {
            return $qb->where('is_test_user', '!=', '1')->orWhereNull('is_test_user');
        });
    }

    return $users->get();
}

function getAllFGRAgentsForContest($onlyPublicProfiles = false, $orderByRating = false, $ignoreTestUsers = false)
{
    $extraUsersCompetition = ['<EMAIL>'];

    if (!$orderByRating) {
        $users = User::orderBy('name', 'asc');
    } else {
        $users = User::orderBy('rating', 'desc')->orderBy('name', 'asc');
    }

    $users->where(function ($query) use ($extraUsersCompetition) {
        $query->whereHas('roles', function ($q) {
            $q->whereIn('name', [RolesDef::AGENT]);
        })
            ->orWhereIn('email', $extraUsersCompetition);
    });
    // if ($onlyPublicProfiles) {
    //     $users = $users->where('public_profile', 1)->whereNotNull('short_bio');
    // }
    if ($ignoreTestUsers) {
        $users = $users->where(function ($qb) {
            return $qb->where('is_test_user', '!=', '1')->orWhereNull('is_test_user');
        });
    }

    return $users->get();
}

function getAllAgentsForContest()
{
    $extraUsersCompetition = ['<EMAIL>'];
    $users = User::orderBy('name', 'asc');

    $users->where(function ($query) use ($extraUsersCompetition) {
        $query->whereHas('roles', function ($q) {
            $q->whereIn('name', [RolesDef::AGENT]);
        })
            ->orWhereIn('email', $extraUsersCompetition);
    })
        ->where('is_active_for_competition', 1)
        ->where(function ($qb) {
            $qb->where('is_test_user', '!=', '1')
                ->orWhereNull('is_test_user');
        });

    return $users->get();
}


function getAllFGRAndMatrixAgents($onlyPublicProfiles = false, $orderByRating = false)
{
    if (!$orderByRating) {
        $users = User::orderBy('name', 'asc');
    } else {
        $users = User::orderBy('rating', 'desc')->orderBy('name', 'asc');
    }

    $users->whereHas('roles', function ($q) {
        $q->whereIn('name', [
            RolesDef::AGENT,
            RolesDef::MATRIX_AGENT,
            RolesDef::MATRIX_AGENT_MANAGER,
        ]);
    });
    if ($onlyPublicProfiles) {
        $users = $users->where('public_profile', 1)->whereNotNull('short_bio');
    }

    return $users->get();
}

function getRouteParamsNext($item)
{
    return [
        'operationType' => 'property',
        'slug' => $item->id,
        'refNo' => $item->ref_no
    ];
}

function cleanStr($content)
{
    $newContent = strip_tags($content);
    $newContent = preg_replace('/&(?!#?[a-z0-9]+;)/', '&amp;', $newContent);
    $newContent = preg_replace("/(^[\r\n]*|[\r\n]+)[\s\t]*[\r\n]+/", "\n", $newContent);
    $newContent = str_replace(' & ', ' &amp; ', html_entity_decode((htmlspecialchars_decode($newContent))));

    return $newContent;
}

function getDefaultWhatsappMessage($snapshotURL)
{
    return __("Hi, I want to know more details about this listing: listingURL", ['listingURL' => $snapshotURL]);
}

function createWhatsappLink($phoneNo, $message)
{
    $queryParams = http_build_query([
        "text" => $message
    ]);
    return "https://wa.me/" . str_replace(' ', '', $phoneNo) . "?" . $queryParams;
}

function getCompletePhoneNo($prefix, $phone)
{
    $mobilePhone = $phone;
    if (!empty($prefix) && !\Str::startsWith($phone, "+") && !\Str::startsWith($phone, "00")) {
        $mobilePhone = $prefix . $phone;
    }
    if(empty($prefix) && !\Str::startsWith($phone, "+") && !\Str::startsWith($phone, "00") && !\Str::startsWith($phone, "974")) {
        $mobilePhone = "+974" . $phone;
    }
    $mobilePhone = str_replace(' ', '', $mobilePhone);
    return $mobilePhone;
}

function resolveListingImageAlt($snapshot, $imageAlt, $index)
{
    if (!empty($imageAlt)) {
        return $imageAlt;
    } else {
        return $snapshot->title . " - Image " . sprintf("%02d", $index);
    }
}

// booking confirmation email helper;
function getBookingTableRow($leftContent, $rightContent, $rowBgColor = '')
{
    $headerLabelStyle = "font-size: 16px; color: #999";
    return "<tr style='border-bottom: 1px solid #ddd; $rowBgColor;'>
        <td style='min-width: 180px; max-width: 180px'><span style='$headerLabelStyle'>$leftContent</span></td>
        <td style='text-align: left; width: 100%'>$rightContent</td>
    </tr>";
}

function getDealRefNo($deal)
{
    $dealsService = App::make(DealsService::class);
    return $dealsService->getDealRefNo($deal);
}

function ordinal($number)
{
    $suffixes = ['th', 'st', 'nd', 'rd'];
    if ($number == "1") {
        return $number . $suffixes[1];
    }
    if ($number == "2") {
        return $number . $suffixes[2];
    }
    if ($number == "3") {
        return $number . $suffixes[3];
    }
    return $number . $suffixes[0];
}

// Moved to App\Models\MarketingParams

function persistQueryParamsToRoute($routeName, $params = [])
{
    $queryParams = array_filter(
        request()->query(),
        fn($key) => in_array($key, MarketingParams::WHITELISTED_URL_PARAMS),
        ARRAY_FILTER_USE_KEY
    );

    return route($routeName, array_merge($params, $queryParams));
}

function persistQueryParamsToURL($url)
{
    $queryParams = array_filter(
        request()->query(),
        fn($key) => in_array($key, MarketingParams::WHITELISTED_URL_PARAMS),
        ARRAY_FILTER_USE_KEY
    );

    if (!empty($queryParams)) {
        $query = http_build_query($queryParams);
        $separator = (strpos($url, '?') !== false) ? '&' : '?';
        return $url . $separator . $query;
    }

    return $url;
}
