<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Room;
use App\Models\RoomBooking;
use App\Services\BookingsService;
use App\Services\EmailService;
use App\Services\OperationHistoryService;
use App\Services\PropertiesService;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Log;

class BookingController extends Controller
{
    private $propertiesService;
    private $emailService;
    private $operationHistoryService;
    private $bookingsService;

    public function __construct(
        PropertiesService $propertiesService,
        EmailService $emailService,
        OperationHistoryService $operationHistoryService,
        BookingsService $bookingsService
    ) {
        $this->propertiesService = $propertiesService;
        $this->emailService = $emailService;
        $this->operationHistoryService = $operationHistoryService;
        $this->bookingsService = $bookingsService;
    }

    public function allElementsNotEmpty($arr) {
        foreach ($arr as $element) {
            if (empty($element->onePersonPrice) || empty($element->twoPersonPrice)) {
                return false;
            }
        }
        return true;
    }

    public function findAvailability()
    {
        return [];
    }

    public function book($roomId)
    {
        $validData = request()->validate([
            'startDate' => 'date',
            'endDate' => 'date|after:startDate',
            'name' => 'required',
            'email' => 'required|email',
            'phone' => 'required',
            'phonePrefix' => 'required',
            'nationalityId' => 'required',
            'taxes' => 'required',
            'totalPricePerStay' => 'required',
        ]);

        $validData['roomId'] = $roomId;

        $startDate = Carbon::parse($validData['startDate']);
        $endDate = Carbon::parse($validData['endDate']);
        $diffInDays = $startDate->diffInDays($endDate);
        $validData['nightsNo'] = $diffInDays;
        $validData['startDate'] = $startDate;
        $validData['endDate'] = $endDate;

        try {
            $createdBooking = RoomBooking::create($validData);
            $room = Room::with(['snapshot'])->firstWhere('id', $validData['roomId']);
            // notification email
            // $createdBooking->getRelation('room');
            $this->emailService->sendBookingNotificationToOffice($createdBooking, $room, $validData);
            $this->operationHistoryService->addOperationHistory($createdBooking, "The booking has been created");
            // $this->emailService->sendBookingNotificationToClient($createdBooking, $startDate, $endDate, $diffInDays, $validData);

            return response($createdBooking, 201);
        } catch (\Exception $Ex) {
            Log::error($Ex->getMessage());
            return response($Ex->getMessage(), 500);
        }
    }

    public function getRoomBookings($roomId)
    {
        $validData = request()->validate([
            'start' => 'date',
            'end' => 'date'
        ]);

        $bookings = RoomBooking::where('roomId', $roomId)->whereBetween('startDate', [$validData['start'], $validData['end']]);
        $events = [];

        $bookings->each(function ($book) use (&$events) {
            $evt = (object)['title' => $book->nightsNo . ' nights', 'start' => $book->startDate, 'end' => $book->endDate, 'backgroundColor' => '#3fd1cb', 'borderColor' => '#e70000'];
            $events[] = $evt;
        });

        return $events;
    }

    public function checkAvailability(Request $request)
    {
        $validData = $request->validate([
            'startDate' => 'required|date|after_or_equal:today',
            'endDate' => 'required|date|after_or_equal:startDate',
            'adults' => 'required|numeric',
            'children' => 'nullable|numeric',
            'infants' => 'nullable|numeric',
            'page' => 'integer',
            'rId' => 'integer',
            'locale' => Rule::in(['en', 'ar']),
        ]);

        $startDateStr = $request->input('startDate');
        $endDateStr = $request->input('endDate');
        $totalDays = 0;
        try {
            $startDate = Carbon::parse($startDateStr);
            $endDate = Carbon::parse($endDateStr);
            $totalDays = $startDate->diffInDays($endDate);
        } catch (\Exception $Ex) {
            Log::error($Ex->getMessage());
        }

        $perPage = 40;

        $roomTypesWithAvailabilityQ = DB::table('hotel_room_type as hrt')
            ->join('hotel_room_type_availability as availability', function ($qb) {
                $qb->on('availability.roomTypeId', '=', 'hrt.room_type_id')
                    ->on('availability.hotelId', '=', 'hrt.hotel_id');
            })
            ->join('property_snapshots as ps', 'ps.listing_id', '=', 'hrt.listing_id')
            ->join('hotels as h', 'h.id', '=', 'hrt.hotel_id')
            ->whereBetween('availability.date', [$startDate, $endDate])
            ->where('availability.totalAvailableUnits', '>', 0)
            ->groupBy('hrt.listing_id')
            ->havingRaw("COUNT(DISTINCT availability.date) = ?", [$totalDays + 1])
            ->where('hrt.max_people_no', '>=', $validData['adults'] + $validData['children'] + $validData['infants'])
            ->select(['h.id as hotel_id', 'h.name', 'hrt.room_type_id', 'ps.listing_id', 'ps.title']);

        $requestPeopleNo = $validData['adults'];
        $requestPeopleNo += $validData['children'];
        $requestPeopleNo += $validData['infants'];

        $roomTypesWithAvailabilityQ->where('hrt.max_people_no', '>=', $requestPeopleNo);

        $roomTypesWithAvailability = $roomTypesWithAvailabilityQ->paginate($perPage);

        $listingIds = [];
        $hotelRoomsData = [];
        $items = $roomTypesWithAvailability->items();
        $roomTypeIds = [];

        foreach ($items as $item) {
            if (!in_array($item->listing_id, $listingIds)) {
                $listingIds[] = $item->listing_id;
                $roomTypeIds[] = $item->room_type_id;
                $hotelRoomsData[] = [
                    'hotelId' => $item->hotel_id,
                    'roomTypeId' => $item->room_type_id
                ];
            }
        }

        // get all the rate plans for the selected rooms
        $roomTypesPricesArr = DB::table('hotel_room_type_prices as hrtp')
            ->whereBetween('hrtp.date', [$startDate, $endDate])
            ->whereIn('hrtp.roomTypeId', $roomTypeIds)
            ->orderBy('hrtp.hotelId', 'ASC')
            ->orderBy('hrtp.roomTypeId', 'ASC')
            ->orderBy('hrtp.ratePlanId', 'ASC')
            ->orderBy('hrtp.date', 'ASC')
            ->select(['hrtp.date', 'hrtp.hotelId', 'hrtp.roomTypeId', 'hrtp.ratePlanId', 'hrtp.onePersonPrice', 'hrtp.twoPersonPrice', 'hrtp.oneAdultExtraCharge', 'hrtp.pricePerChild0To2', 'hrtp.pricePerChild2To6', 'hrtp.pricePerChild6To12'])
            ->get();

        $roomTypesPricesRawMap = [];
        foreach ($roomTypesPricesArr as $roomTypePrice) {
            $currentKey = $roomTypePrice->roomTypeId . '--' . $roomTypePrice->ratePlanId;
            if (!array_key_exists($currentKey, $roomTypesPricesRawMap)) {
                $roomTypesPricesRawMap[$currentKey] = [
                    'priceLines' => [],
                    'totalPricePerRatePlan' => 0,
                    'priceOnFirstDay' => 0,
                ];
            }
            $roomTypePrice->calculatedPrice = $this->bookingsService->calculatePricePerDay($roomTypePrice, $validData['adults'], $validData['children'], $validData['infants']);
            if (count($roomTypesPricesRawMap[$currentKey]['priceLines']) == 0) {
                $roomTypesPricesRawMap[$currentKey]['priceOnFirstDay'] = $roomTypePrice->calculatedPrice;
            }
            $roomTypesPricesRawMap[$currentKey]['priceLines'][] = $roomTypePrice;
            $roomTypesPricesRawMap[$currentKey]['totalPricePerRatePlan'] += $roomTypePrice->calculatedPrice;
        }

        // remove invalid rate plans
        foreach($roomTypesPricesRawMap as $ratePlanKey => $data) {
            $shouldKeepRatePlan = $this->allElementsNotEmpty($data['priceLines']);

            if(!$shouldKeepRatePlan) {
                unset($roomTypesPricesRawMap[$ratePlanKey]);
            }
        }

        $roomTypesPricesMap = [];
        foreach ($roomTypesPricesRawMap as $key => $priceEntries) {
            [$rtId, $rpId] = explode("--", $key);
            if (!array_key_exists($rtId, $roomTypesPricesMap)) {
                $roomTypesPricesMap[$rtId] = [
                    'startPriceRatePlanId' => $rpId,
                    'startPrice' => $priceEntries['priceOnFirstDay']
                ];
            }
            if ($priceEntries['priceOnFirstDay'] < $roomTypesPricesMap[$rtId]['startPrice']) {
                $roomTypesPricesMap[$rtId] = [
                    'startPriceRatePlanId' => $rpId,
                    'startPrice' => $priceEntries['priceOnFirstDay']
                ];
            }
        }

        // dd($roomTypesPricesMap);

        $snapshotData = [];
        if (count($listingIds) > 0) {
            $snapshotData = $this->propertiesService->getSearchLiteQ(['ids' => $listingIds, 'is_short_stay' => 1], 0, [], 10000, $validData['locale'])->get()->toArray();
        }
        // dd($roomTypesPricesMap);
        foreach ($items as $item) {
            $snapshots = array_filter($snapshotData, function ($snapshotItem) use ($item) {
                return $snapshotItem->listing_id == $item->listing_id;
            });

            $startingPrice = 0;
            $startingPriceRatePlanId = null;
            if (array_key_exists($item->room_type_id, $roomTypesPricesMap)) {
                $startingPrice = $roomTypesPricesMap[$item->room_type_id]['startPrice'];
                $startingPriceRatePlanId = $roomTypesPricesMap[$item->room_type_id]['startPriceRatePlanId'];
            }

            $item->snapshot = count($snapshots) > 0 ? $this->propertiesService->mapSnapshotModelToListItem(reset($snapshots), 'list-item', $validData['locale']) : null;
            $item->snapshot['location'] = $item->name;
            $item->startingPrice = $startingPrice;
            $item->startingPriceRatePlanId = $startingPriceRatePlanId;
        }
        return response()->json($items);
    }

    public function checkRoomAvailabilityAndPrices(Request $request)
    {
        $request->validate([
            'startDate' => 'required|date|after_or_equal:today',
            'endDate' => 'required|date|after_or_equal:startDate',
            'hotelId' => 'integer',
            'roomTypeId' => 'integer',
            'ratePlanId' => 'integer',
            'adults' => 'required|numeric',
            'children' => 'nullable|numeric',
            'infants' => 'nullable|numeric',
        ]);

        $startDateStr = $request->input('startDate');
        $endDateStr = $request->input('endDate');
        $hotelId = $request->input('hotelId');
        $roomTypeId = $request->input('roomTypeId');
        $ratePlanId = $request->input('ratePlanId');
        $adults = $request->input('adults');
        $children = $request->input('children');
        $infants = $request->input('infants');

        $guestsConfig = [
            'adults' => $adults,
            'children' => $children,
            'infants' => $infants
        ];

        $totalDays = 0;
        try {
            $startDate = Carbon::parse($startDateStr);
            $endDate = Carbon::parse($endDateStr);
            $totalDays = $startDate->diffInDays($endDate);
        } catch (\Exception $Ex) {
            Log::error($Ex->getMessage());
        }

        // check availability
        $isRoomAvailableForPeriod = DB::table('hotel_room_type as hrt')
            ->join('hotel_room_type_availability as availability', function ($qb) {
                $qb->on('availability.roomTypeId', '=', 'hrt.room_type_id')
                    ->on('availability.hotelId', '=', 'hrt.hotel_id');
            })
            ->join('property_snapshots as ps', 'ps.listing_id', '=', 'hrt.listing_id')
            ->join('hotels as h', 'h.id', '=', 'hrt.hotel_id')
            ->whereBetween('availability.date', [$startDateStr, $endDateStr])
            ->where('availability.totalAvailableUnits', '>', 0)
            ->where('availability.roomTypeId', $roomTypeId)
            ->where('hrt.max_people_no', '>=', $adults + $children + $infants)
            ->groupBy('hrt.listing_id')
            ->havingRaw("COUNT(DISTINCT availability.date) = ?", [$totalDays + 1])
            ->select(['h.id as hotel_id', 'h.name', 'hrt.room_type_id', 'ps.listing_id', 'ps.title'])
            ->exists();

        if (!$isRoomAvailableForPeriod) {
            Log::info("Room accessed with unavailable spots [startDate: " . $startDateStr . " endDate: " . $endDateStr . " hotelId: " . $hotelId . " roomTypeId: " . $roomTypeId . " ratePlanId: " . $ratePlanId . "]");
            return abort(422, "No availability for selected dates");
        }

        $availableRatePlans = $this->bookingsService->getHotelRatePlansForRoomTypeForPeriod($hotelId, $startDateStr, $endDateStr, $roomTypeId, null, $guestsConfig);

        // clean rate plans
        foreach($availableRatePlans as $currRatePlanId => $ratePlanData) {
            $shouldKeepRatePlan = $this->allElementsNotEmpty($ratePlanData['priceLines']);

            if(!$shouldKeepRatePlan) {
                unset($availableRatePlans[$currRatePlanId]);
            }
        }

        $startDate = Carbon::parse($startDateStr);
        $endDate = Carbon::parse($endDateStr);
        $totalDays = $startDate->diffInDays($endDate);

        usort($availableRatePlans, function ($item1, $item2) {
            if ($item1['avgPricePerNight'] == $item2['avgPricePerNight']) {
                return 0;
            }
            return ($item1['avgPricePerNight'] < $item2['avgPricePerNight']) ? -1 : 1;
        });

        $totalPrice = 0;
        $averagePrice = 0;

        foreach ($availableRatePlans as $ratePlanData) {
            if ($ratePlanData['id'] == $ratePlanId) {
                $averagePrice += $ratePlanData['avgPricePerNight'];
                $totalPrice += $ratePlanData['totalPriceForRatePlan'];
            }
        }

        return response()->json([
            "totalPrice" => number_format($totalPrice, 0),
            "avgPricePerNight" => number_format(ceil($totalPrice / $totalDays), 0),
            "numberOfNights" => $totalDays,
            "availableRatePlans" => $availableRatePlans
        ]);
    }

    public function approve($roomId, $bookingId)
    {
        $booking = RoomBooking::firstWhere(['roomId' => $roomId, 'id' => $bookingId]);
        if (!is_null($booking)) {
            $booking->status = 'approved';
            $booking->save();

            $this->operationHistoryService->addOperationHistory($booking, "The booking was approved");
            return response(["message" => "Ok"], 200);
        }
        return response("Not found", 404);
    }
}
