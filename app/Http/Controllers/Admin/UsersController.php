<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\CrmBaseController;
use App\Models\Crm\ContactsListTag;
use App\Models\Crm\RolesDef;
use App\Models\Nationality;
use App\Models\User;
use App\Models\UserCheckIn;
use App\Services\AttachmentsService;
use App\Services\CountriesService;
use App\Services\LeadsService;
use App\Services\PropertiesService;
use App\Services\RolesAndPermissionsService;
use App\Services\SlugifyService;
use App\Services\UserService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role as SpatieRole;
use Spatie\Permission\Models\Permission as SpatiePermission;
use Log;
use Spatie\Permission\Contracts\Permission;

class UsersController extends CrmBaseController
{
    protected $views = [
        'index' => 'admin.users.index',
        'create' => 'admin.users.form.form',
        'edit' => 'admin.users.form.form',
    ];

    protected $routes = [
        'index' => 'admin.users.index',
        'edit' => 'admin.users.edit'
    ];

    private $originalCompetitionStatus;

    private $fieldsToValidate = [
        'email' => 'required|email|unique:users,email',
        'name' => 'required|string',
        'position' => 'required',
        'prefix_phone' => '',
        'phone' => 'required|min:4|max:40|regex:/^[0-9]*$/',
        'short_bio' => 'required',
        'image' => 'image',
        'image-remove' => '',
        'license' => 'file',
        'license-remove' => '',
        'team_leader_id' => '',
        'license_id' => '',
        'nationality_id' => '',
        'date_of_birth' => '',
        'languages' => '',
        'facebook' => 'nullable|url',
        'instagram' => 'nullable|url',
        'linkedin' => 'nullable|url',
        'public_profile' => '',
        'should_use_whatsapp_chatbot' => '',
        'brokerage_license_account_id' => '',
        'participate_to_competition' => '',

        'roles' => 'array',

        'password' => [
            'nullable',
            'confirmed',
            'min:6',             // must be at least 10 characters in length
            'regex:/[a-z]/',      // must contain at least one lowercase letter
            'regex:/[A-Z]/',      // must contain at least one uppercase letter
            'regex:/[0-9]/'
        ],
    ];

    protected $validationMessages = [
        'password.confirmed' => 'The password confirmation field does not match the password ',
        'password.min' => 'The password must have minimum 6 chars',
        'password.regex' => 'The password must have at least one lowercase letter, one uppercase letter and a number',
        'phone.regex' => 'The format of the field is invalid.',
        'phone.min' => 'The phone number should contain at least 4 chars and max 40 characters.',
        'phone.max' => 'The phone number should contain at least 4 chars and max 40 characters.'
    ];

    private $userService;
    private $attachmentsService;
    private $rolesAndPermissionsService;
    private $leadsService;
    private $propertiesService;
    private $slugifyService;
    private $countriesService;

    public function __construct(
        UserService $userService,
        AttachmentsService $attachmentsService,
        RolesAndPermissionsService $rolesAndPermissionsService,
        LeadsService $leadsService,
        PropertiesService $propertiesService,
        SlugifyService $slugifyService,
        CountriesService $countriesService
    ) {

        $this->userService = $userService;
        $this->attachmentsService = $attachmentsService;
        $this->rolesAndPermissionsService = $rolesAndPermissionsService;
        $this->leadsService = $leadsService;
        $this->propertiesService = $propertiesService;
        $this->slugifyService = $slugifyService;
        $this->countriesService = $countriesService;
    }

    protected function ajaxRequestIndex()
    {
        $userIsAdmin = auth()->user()->hasAnyRole([
            RolesDef::OFFICE_MANAGER
        ]);
        $userIsAgentMatrixManager = auth()->user()->hasAnyRole([RolesDef::MATRIX_AGENT_MANAGER]);
        $userIsTeamLeader = auth()->user()->hasAnyRole([RolesDef::TEAM_LEADER]);
        $userId = auth()->user()->id;

        $extraClauses = [];
        if (!$userIsAdmin && ($userIsAgentMatrixManager || $userIsTeamLeader)) {
            $extraClauses['created_by'] = $userId;
        }

        // Handle filter parameters
        if (request()->has('nationality')) {
            $extraClauses['nationality'] = request()->get('nationality');
        }
        if (request()->has('roles')) {
            $extraClauses['roles'] = request()->get('roles');
        }
        if (request()->has('teamLeader')) {
            $extraClauses['teamLeader'] = request()->get('teamLeader');
        }
        if (request()->has('competition')) {
            $extraClauses['competition'] = request()->get('competition') === 'true';
        }

        $items = $this->userService->getTableItems(request(), false, $extraClauses)->map(function ($item) use ($userIsAdmin, $userId) {
            return [
                'id' => $item->id,
                'name' => $item->name,
                'rating' => $item->rating,
                'email' => $item->email,
                'position' => $item->position,
                'brokerage_license_account' => $item->brokerage_license_agent ?? '-',
                'roles' => $item->roles,
                'public_profile' => $item->public_profile,
                'profile_image_url' => !empty($item->profile_image) ? imageRoute('square-150', $item->profile_image) : '',
                'canDelete' => in_array(auth()->user()->email, [env('EMAIL_ADDRESS_SERBAN'), env('EMAIL_ADDRESS_ITDEV'), env('EMAIL_ADDRESS_AMIRA')])
            ];
        });

        $count = $this->userService->getTableItemsCount(request(), $extraClauses);

        return [
            'data' => $items,
            'recordsTotal' => $count,
            'recordsFiltered' => $count
        ];
    }

    protected function getNewItem()
    {
        $user = new User();
        $user->created_by = auth()->user()->id;
        $user->uniq_id = md5(uniqid(rand(), true));
        $user->email_verified_at = now();

        return $user;
    }

    protected function updateObjectFieldsFromRequest($user, $validFields)
    {
        $user->should_use_whatsapp_chatbot = null;
        foreach ($validFields as $requestFieldName => $validValue) {
            if (!in_array($requestFieldName, ['image', 'image-remove', 'roles', 'password', 'license', 'license-remove', 'participate_to_competition'])) {
                $user->$requestFieldName = $validValue;
            }
        }
        if (!isset($validFields['public_profile'])) {
            $user->public_profile = false;
        }

        // Handle competition participation
        $user->is_active_for_competition = isset($validFields['participate_to_competition']) ? 1 : 0;
    }

    protected function viewVars(string $viewName, $assetId = null)
    {
        if ($viewName == 'edit' || $viewName == 'create') {
            return $this->getViewVars($assetId);
        }

        $agents = getAllFGRAgents();

        // Get roles for filters
        $roles = $this->rolesAndPermissionsService->allRoles();

        // Get team leaders for filters
        $teamLeaders = User::whereHas('roles', function ($q) {
            $q->where('name', RolesDef::TEAM_LEADER);
        })->orderBy('name')->get(['id', 'name']);

        return compact(['agents', 'roles', 'teamLeaders']);
    }

    private function getViewVars($id = null)
    {
        $empty = false;
        $delete = false;
        $readonly = false;

        $item = is_null($id) ? $this->getNewItem() : $this->getDbItem($id);
        if (!empty($id) && is_null($item)) {
            abort(404);
        }
        $formAction = is_null($id) ? route('admin.users.store') : route('admin.users.update', ['id' => $id]);
        $roles = $this->rolesAndPermissionsService->allRoles();

        if (!auth()->user()->hasRole(RolesDef::OFFICE_MANAGER)) {
            $roles = $roles->filter(function ($roleItem) {
                return in_array($roleItem->name, [RolesDef::MATRIX_AGENT]);
            });
        }

        $userRoles = $item->roles->map(function ($r) {
            return $r->id;
        })->toArray() ?? [];
        $countries = $this->countriesService->getCountries();
        $nationalities = Nationality::all();

        return compact([
            'empty',
            'delete',
            'readonly',
            'id',
            'item',
            'formAction',
            'roles',
            'userRoles',
            'countries',
            'nationalities'
        ]);
    }

    protected function getDbItem($id)
    {
        return User::find($id);
    }

    public function update($itemId)
    {
        $validFields = request()->validate($this->getFieldsToValidate('update', $itemId), $this->validationMessages ?? []);

        $originalItem = $this->getDbItem($itemId);
        $item = $this->getDbItem($itemId);
        $this->updateObjectFieldsFromRequest($item, $validFields);
        $this->executePreUpdate($item, $validFields, $originalItem);
        $item->save();
        $this->executePostUpdate($item, $validFields);

        return $this->handleRedirect($item);
    }

    protected function getFieldsToValidate(string $actionName, $itemId = null)
    {
        $validationRules = $this->fieldsToValidate;

        if ($actionName == 'store') {
            $validationRules['password'][] = 'required';
        }

        if ($actionName == 'update') {
            $validationRules['email'] = 'required|email|unique:users,email,' . $itemId;
        }
        return $validationRules;
    }

    protected function executePreCreate($item, $validFields)
    {
        if (!empty($validFields['password'])) {
            $item->password = Hash::make($validFields['password']);
        }
        $this->handleSlug($item);
    }

    protected function executePreUpdate($item, $validFields, $originalItem = null)
    {
        if (!empty($validFields['password'])) {
            $item->password = Hash::make($validFields['password']);
        }
        $this->handleSlug($item);

        // Store original competition status for comparison
        $this->originalCompetitionStatus = $originalItem ? $originalItem->is_active_for_competition : 0;
    }

    protected function executePostCreate($item, $validFields)
    {
        $this->handleUserRoles($item, $validFields);
        $this->handleProfileImage($item, $validFields);
        $this->handleLicense($item, $validFields);
    }

    protected function executePostUpdate($item, $validFields)
    {
        $this->handleUserRoles($item, $validFields);
        $this->handleProfileImage($item, $validFields);
        $this->handleLicense($item, $validFields);
        $this->handleCompetitionChange($item, $validFields);
    }

    private function handleUserRoles($item, $validFields)
    {
        $rolesArray = isset($validFields['roles']) && is_array($validFields['roles']) ? $validFields['roles'] : [];
        $item->roles()->sync($rolesArray);
    }

    private function handleProfileImage($item, $validFields)
    {
        $imageFile = $validFields['image'] ?? null;
        $imageFileDelete = $validFields['image-remove'] ?? false;
        $this->attachmentsService->syncItemImage($item, $imageFile, $imageFileDelete);
    }

    private function handleLicense($item, $validFields)
    {
        $licenseFile = $validFields['license'] ?? null;
        $licenseFileDelete = $validFields['license-remove'] ?? false;
        $this->attachmentsService->syncItemLicense($item, $licenseFile, $licenseFileDelete);
    }

    private function handleCompetitionChange($item, $validFields)
    {
        // Check if competition checkbox changed
        $currentCompetitionStatus = $item->is_active_for_competition;
        $originalStatus = $this->originalCompetitionStatus ?? 0;

        if ($originalStatus != $currentCompetitionStatus) {
            // Competition checkbox was changed - update this specific user
            $this->updateUserCompetitionStatus($item->id, $currentCompetitionStatus);
            $this->updatePermissionsForUser($item->id, $currentCompetitionStatus);
            app()->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
        }
    }

    private function updateUserCompetitionStatus($userId, $status)
    {
        DB::table('users')
            ->where('id', $userId)
            ->update(['is_active_for_competition' => $status]);
    }

    private function updatePermissionsForUser($userId, $status)
    {
        if ($status == 1) {
            // Add permission for this user
            DB::table('model_has_permissions')->upsert(
                [
                    'permission_id' => 49,
                    'model_type' => 'App\Models\User',
                    'model_id' => $userId,
                ],
                ['model_type', 'model_id', 'permission_id'],
                ['permission_id']
            );
        } else {
            // Remove permission for this user
            DB::table('model_has_permissions')
                ->where('model_type', 'App\Models\User')
                ->where('model_id', $userId)
                ->where('permission_id', 49)
                ->delete();
        }
    }

    public function downloadLicense($id)
    {
        $item = User::firstWhere('id', $id);
        if ($item->license_path) {
            $extension = pathinfo($item->license_path, PATHINFO_EXTENSION);
            $newFileName = 'Broker Card License.' . $extension;
            return Storage::download($item->license_path, $newFileName);
        }
        return response();
    }

    public function rolesPermissionsMatrix()
    {
        $roles = SpatieRole::with('permissions')->get();
        $permissions = SpatiePermission::all();
        return view('crm.users.roles-permissions-matrix', [
            'roles' => $roles,
            'permissions' => $permissions
        ]);
    }

    public function deletionStats($userId)
    {
        $leadsCreated = DB::table('lead_assignments')
            ->where('user_id', $userId)
            ->count();

        $leadsAssigned = DB::table('lead_assignments')
            ->where('user_id', $userId)
            ->whereNull('deleted_at')
            ->count();

        $listingsCreated = DB::table('properties')
            ->where('created_by', $userId)
            ->whereNull('deleted_at')
            ->count();

        $listingsUpdated = DB::table('property_snapshots')
            ->where('created_by', $userId)
            ->whereNull('deleted_at')
            ->count();

        $contactsCreated = DB::table('contacts')
            ->where('created_by', $userId)
            ->whereNull('deleted_at')
            ->count();

        $contactsConnections = DB::table('contacts_agents_connection')
            ->where('user_id', $userId)
            ->count();

        return [
            'leadsCreated' => $leadsCreated,
            'leadsAssigned' => $leadsAssigned,
            'listingsCreated' => $listingsCreated,
            'listingsUpdated' => $listingsUpdated,
            'contactsCreated' => $contactsCreated,
            'contactsConnections' => $contactsConnections
        ];
        // DB::table('leads')
        //     ->where('created_by', $fromUserId)
        //     ->update(['created_by' => $toUserId]);
    }

    public function delete($id)
    {
        if (!in_array(auth()->user()->email, [env('EMAIL_ADDRESS_SERBAN'), env('EMAIL_ADDRESS_ITDEV'), env('EMAIL_ADDRESS_AMIRA')])) {
            // if(auth()->user()->email !== '<EMAIL>'){
            return response(403);
        }
        $user = User::where('id', $id)->firstOrFail();
        $payloadData = request()->validate([
            'leadAssignationUserId' => '',
            'propertiesAssignationUserId' => '',
        ]);
        try {
            $userToAssignLeadsTo = $payloadData['leadAssignationUserId'];
            if (empty($userToAssignLeadsTo)) {
                $userToAssignLeadsTo = env('ON_DELETE_ASSIGN_ITEMS_USER_ID');
            }
            $userToAssignPropertiesTo = $payloadData['propertiesAssignationUserId'];
            if (empty($userToAssignPropertiesTo)) {
                $userToAssignPropertiesTo = env('ON_DELETE_ASSIGN_ITEMS_USER_ID');
            }
            $this->leadsService->moveLeadsToUser($id, $userToAssignLeadsTo);
            Log::info("Moved leads from " . $id . " to " . $userToAssignLeadsTo);
            $this->propertiesService->movePropertiesToUser($id, $userToAssignPropertiesTo);
            Log::info("Moved properties from " . $id . " to " . $userToAssignPropertiesTo);
            $user->delete();
            return response(["message" => "The user has been deleted"]);
        } catch (\Exception $Ex) {
            Log::warning($Ex->getMessage());
            return response(["message" => 'Cannot delete this user.']);
        }
    }

    public function handleSlug($item)
    {
        $slug = $this->slugifyService->slugify($item['name']);
        $item->slug = $slug;
    }

    public function interactiveUsersMap()
    {
        return view('admin.users.active-users-map');
    }

    /**
     * For OM, return all agents
     * For Team Leaders, return all users in team
     */
    public function userTeamList()
    {
        $user = auth()->user();
        $userIsOM = $user->hasRole(RolesDef::OFFICE_MANAGER);
        $userIsTL = $user->hasRole(RolesDef::TEAM_LEADER);
        $userMapper = function ($eachUser) use ($user) {
            return [
                'id' => $eachUser->id,
                'name' => $eachUser->name,
                'isCurrentUser' => $eachUser->id == $user->id,
                "teamLeaderId" => $eachUser->team_leader_id,
                "teamLeaderName" => $eachUser->teamLeader->name ?? null
            ];
        };
        if ($userIsOM) {
            $agents = getAllFGRAgents(false, false, true)->map($userMapper);
            return $agents;
        } elseif ($userIsTL) {
            $agents = getTeamLeaderTeamAgents($user);
            $agents->push($user);

            return $agents->map($userMapper);
        }
        return collect([$user])->map($userMapper);
    }

    /**
     * Bulk reset passwords for selected users
     */
    public function bulkResetPassword()
    {
        // Check permission
        if (!auth()->user()->can(\App\Models\Crm\PermissionsDef::CRM_ADMIN)) {
            return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
        }

        $validData = request()->validate([
            'userIds' => 'required|array',
            'userIds.*' => 'required|integer|exists:users,id'
        ]);

        $userIds = $validData['userIds'];
        $resetCount = 0;
        $errors = [];

        foreach ($userIds as $userId) {
            try {
                $user = User::findOrFail($userId);

                // Generate new random password
                $newPassword = $this->generateRandomPassword();

                // Update user password
                $user->password = Hash::make($newPassword);
                $user->save();

                // Send email with new password
                $emailService = app(\App\Services\EmailService::class);
                $emailService->sendNewPasswordEmail($user->email, $newPassword, $user->name);

                $resetCount++;
            } catch (\Exception $e) {
                $errors[] = "Failed to reset password for user ID {$userId}: " . $e->getMessage();
            }
        }

        if ($resetCount > 0) {
            return response()->json([
                'success' => true,
                'count' => $resetCount,
                'message' => "Passwords reset for {$resetCount} user(s)",
                'errors' => $errors
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'No passwords were reset',
                'errors' => $errors
            ], 400);
        }
    }

    /**
     * Generate a random password
     */
    private function generateRandomPassword($length = 12)
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';

        // Ensure at least one character from each type
        $password .= chr(rand(65, 90)); // Uppercase
        $password .= chr(rand(97, 122)); // Lowercase
        $password .= chr(rand(48, 57)); // Number
        $password .= '!@#$%^&*'[rand(0, 7)]; // Special character

        // Fill the rest randomly
        for ($i = 4; $i < $length; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }

        // Shuffle the password
        return str_shuffle($password);
    }

    public function userAssignedContactTags($userId)
    {
        $user = User::with(['contactListTags'])->where('id', $userId)->first();
        return $user->contactListTags->map(fn($item) => $item['id']);
    }

    public function contactTags()
    {

        return ContactsListTag::orderBy('label')->get();
    }

    public function userPermissions($id)
    {
        $user = User::where('id', $id)->firstOrFail();
        return view('admin.users.permissions.index', compact('user'));
    }

    public function updateUserAssignedContactTags($userId)
    {
        $validData = request()->validate([
            'tagsSelection' => 'array'
        ]);
        $user = User::where('id', $userId)->first();
        $user->contactListTags()->sync($validData['tagsSelection']);
        return ['msg' => 'Ok'];
    }

    public function checkInAdmin()
    {
        $dateStr = request()->get('date', (new \DateTime())->format('Y-m-d'));
        $checkinEntries = UserCheckIn::with(['user'])->whereRaw("DATE(check_in_date) LIKE '" . $dateStr . "'")->orderBy('check_in_date')->get();

        return view('admin.users.user-checkin', compact('checkinEntries', 'dateStr'));
    }
}
