<template>
    <div class="card card-body p-2 m-2 bg-light" :class="{
        'g-2': true,
        filters: true,
        'd-none': !moreFiltersVisible
    }" id="contactsFiltersPanel">
        <div class="row">
            <div class="col-12 text-right mb-1">
                <a href="javascript:void(0)" @click="resetFilters()" class="small">Reset filters</a>
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Name</label>
                <input v-model="nameFilter" type="text" placeholder="Search by name" class="form-control form-control-sm"
                    @input="onNameChange">
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Age</label>
                <select v-model="ageRangeFilter" class="form-select form-select-sm" @change="onAgeRangeChange">
                    <option value="">Please select</option>
                    <option value="20-40">20-40</option>
                    <option value="40-60">40-60</option>
                    <option value="60+">60+</option>
                </select>
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Nationality</label>
                <FGVirtualSelect :selectorClassName="nationalitySelectorClass" :class="{
                    'vscomp-ele': true,
                    [nationalitySelectorClass]: true,
                }" :options="nationalityOptions" :config="nationalitySelectorConfig"
                    :selectedValue="selectedNationalities" @change="onNationalityChange($event)" />
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Rent/Sale</label>
                <select v-model="operationType" class="form-select form-select-sm" @change="onOperationTypeChange">
                    <option value="">Please select</option>
                    <option value="rent">Rent</option>
                    <option value="sale">Sale</option>
                </select>
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Property Type</label>
                <FGVirtualSelect :selectorClassName="propertyTypeSelectorClass" :class="{
                    'vscomp-ele': true,
                    [propertyTypeSelectorClass]: true,
                }" :options="propertyTypeOptions" :config="propertyTypeSelectorConfig"
                    :selectedValue="selectedPropertyTypes" @change="onPropertyTypeChange($event)" />
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Date Created</label>
                <div class="input-group input-group-sm">
                    <input type="text" class="form-control form-control-sm" id="contactCreationDateRangeInput">
                    <button @click="clearDaterange('contactCreationDateRangeInput')"
                        class="btn btn-xs btn-outline-secondary">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Budget Range</label>
                <select v-model="budgetRangeFilter" class="form-select form-select-sm" @change="onBudgetRangeChange">
                    <option value="">Please select</option>
                    <option value="under-500k">Under 500K</option>
                    <option value="500k-1m">500K - 1M</option>
                    <option value="1m-2m">1M - 2M</option>
                    <option value="2m-5m">2M - 5M</option>
                    <option value="5m-10m">5M - 10M</option>
                    <option value="10m-20m">10M - 20M</option>
                    <option value="20m-30m">20M - 30M</option>
                    <option value="30m-50m">30M - 50M</option>
                    <option value="50m-75m">50M - 75M</option>
                    <option value="75m-100m">75M - 100M</option>
                    <option value="100m-150m">100M - 150M</option>
                    <option value="150m+">150M+</option>
                </select>
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Budget (Custom)</label>
                <div class="input-group input-group-sm">
                    <input v-model="budgetFrom" type="number" placeholder="From" class="form-control form-control-sm"
                        @input="onBudgetChange">
                    <input v-model="budgetTo" type="number" placeholder="To" class="form-control form-control-sm"
                        @input="onBudgetChange">
                </div>
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Interested in which project?</label>
                <FGVirtualSelect :selectorClassName="projectsSelectorClass" :class="{
                    'vscomp-ele': true,
                    [projectsSelectorClass]: true,
                }" :options="projectOptions" :config="projectsSelectorConfig"
                    :selectedValue="selectedProjects" @change="onProjectsChange($event)" />
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Tags</label>
                <FGVirtualSelect :selectorClassName="tagsFilterClass" :class="{
                    'vscomp-ele': true,
                    [tagsFilterClass]: true,
                }" :options="storeState.allTags" :config="filterTagsSelectorConfig" :selectedValue="selectedTags"
                    @change="updateTags($event)" />
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Call Response</label>
                <select v-model="storeState.callLog" class="form-select form-select-sm" @change="updateCallLog($event)">
                    <option value="">Please select</option>
                    <option v-for="cr of callResponses" :key="cr.value" :value="cr.value">{{ cr.label }}</option>
                </select>
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Lead Status</label>
                <FGVirtualSelect :selectorClassName="leadStatusSelectorClass" :class="{
                    'vscomp-ele': true,
                    [leadStatusSelectorClass]: true,
                }" :options="leadStatusOptions" :config="leadStatusSelectorConfig"
                    :selectedValue="selectedLeadStatuses" @change="onLeadStatusChange($event)" />
            </div>
            <div class="col-xs-12 col-sm-4 col-lg-3 mb-2">
                <label class="text-gray-50 small mb-0">Interest</label>
                <select v-model="interestFilter" class="form-select form-select-sm" @change="onInterestChange" disabled>
                    <option value="">Please select</option>
                    <option value="investors">Investors</option>
                    <option value="living">Living</option>
                </select>
            </div>
            <div class="col-xs-12 col-sm-8 col-lg-6 mb-2 d-flex gap-3 align-items-center">
                <div class="form-check">
                    <input v-model="storeState.verified" type="checkbox" class="form-check-input" id="verifiedCheckbox"
                        @change="(event: any) => updateVerified(event?.target?.checked)" />
                    <label for="verifiedCheckbox" class="form-check-label small">Verified</label>
                </div>
                <div class="form-check">
                    <input v-model="storeState.isLandlord" type="checkbox" class="form-check-input"
                        id="isLandlordCheckbox" @change="(event: any) => updateIsLandlord(event?.target?.checked)" />
                    <label for="isLandlordCheckbox" class="form-check-label small">Is Landlord</label>
                </div>
                <div class="form-check">
                    <input v-model="storeState.hasLeads" type="checkbox" class="form-check-input" id="hasLeadsCheckbox"
                        @change="(event: any) => updateHasLeads(event?.target?.checked)" />
                    <label for="hasLeadsCheckbox" class="form-check-label small">Has Leads</label>
                </div>
            </div>
            <div class="col-xs-12 col-sm-8 col-lg-6 mb-2 d-flex gap-3 align-items-center">
                <div class="form-check">
                    <input v-model="offplanFilter" type="checkbox" class="form-check-input" id="offplanCheckbox"
                        @change="onOffplanChange" disabled />
                    <label for="offplanCheckbox" class="form-check-label small">Offplan</label>
                </div>
                <div class="form-check">
                    <input v-model="internationalBuyersFilter" type="checkbox" class="form-check-input" id="internationalBuyersCheckbox"
                        @change="onInternationalBuyersChange" disabled />
                    <label for="internationalBuyersCheckbox" class="form-check-label small">International Buyers</label>
                </div>
                <div class="form-check">
                    <input v-model="hasPhoneFilter" type="checkbox" class="form-check-input" id="hasPhoneCheckbox"
                        @change="onHasPhoneChange" />
                    <label for="hasPhoneCheckbox" class="form-check-label small">Has Phone</label>
                </div>
                <div class="form-check">
                    <input v-model="hasEmailFilter" type="checkbox" class="form-check-input" id="hasEmailCheckbox"
                        @change="onHasEmailChange" />
                    <label for="hasEmailCheckbox" class="form-check-label small">Has Email</label>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref, watch } from "vue";
import { useStore } from "vuex";
import FGVirtualSelect from "../FGVirtualSelect.vue";
import { isEqual } from "lodash";

declare const request: Function;
declare const $: any;

export default defineComponent({
    name: "ContactsFilters",
    components: {
        FGVirtualSelect,
    },
    setup() {
        const store = useStore();

        // Reactive data
        const operationType = ref('');
        const budgetFrom = ref<number | undefined>(undefined);
        const budgetTo = ref<number | undefined>(undefined);
        const propertyTypeOptions = ref<Array<{ value: number; label: string }>>([]);
        const moreFiltersVisible = ref(false); // Start collapsed by default

        // New filter reactive data
        const nameFilter = ref('');
        const ageRangeFilter = ref('');
        const budgetRangeFilter = ref('');
        const interestFilter = ref('');
        const offplanFilter = ref(false);
        const internationalBuyersFilter = ref(false);
        const hasPhoneFilter = ref(false);
        const hasEmailFilter = ref(false);

        // Options for new filters
        const nationalityOptions = ref<Array<{ value: number; label: string }>>([]);
        const projectOptions = ref<Array<{ value: number; label: string }>>([]);
        const leadStatusOptions = ref<Array<{ value: number; label: string }>>([]);

        // Call responses data
        const callResponses = [{
            value: 'not_answered',
            label: 'Not answered',
        }, {
            value: 'answered',
            label: 'Answered',
        }, {
            value: 'not_interested',
            label: 'Not interested',
        }, {
            value: 'inactive',
            label: 'Inactive',
        }];

        // Virtual select class names
        function virtualSelectClassName(): string {
            return "r" + (Math.random() + 1).toString(36).substring(7);
        }
        const propertyTypeSelectorClass = virtualSelectClassName();
        const tagsFilterClass = virtualSelectClassName();
        const nationalitySelectorClass = virtualSelectClassName();
        const projectsSelectorClass = virtualSelectClassName();
        const leadStatusSelectorClass = virtualSelectClassName();

        // Computed properties
        const storeState = computed(() => store.state);

        const selectedPropertyTypes = computed(() => {
            return storeState.value.propertyTypes || [];
        });

        const propertyTypeSelectorConfig = computed(() => {
            return {
                multiple: true,
                search: true,
                showValueAsTags: true,
                maxHeight: '32px',
                dropboxWrapper: 'form-select-sm'
            };
        });

        // Tags computed properties
        const selectedTags = computed(() => {
            return storeState.value.tags?.map((eachTagId: string) => Number(eachTagId)) || [];
        });

        const filterTagsSelectorConfig = computed(() => {
            const allTags = storeState.value.allTags;
            const selectedTagIds = storeState.value?.tags ?? [];
            const selectedTags = [...allTags.filter((eachTag: any) => selectedTagIds.includes(eachTag.value))];
            return {
                multiple: true,
                search: true,
                showValueAsTags: true,
                selectedValue: selectedTags,
                maxHeight: '32px',
                dropboxWrapper: 'form-select-sm'
            };
        });

        // New filter computed properties
        const selectedNationalities = computed(() => {
            return storeState.value.nationalities || [];
        });

        const nationalitySelectorConfig = computed(() => {
            return {
                multiple: true,
                search: true,
                showValueAsTags: true,
                maxHeight: '32px',
                dropboxWrapper: 'form-select-sm'
            };
        });

        const selectedProjects = computed(() => {
            return storeState.value.interestedProjects || [];
        });

        const projectsSelectorConfig = computed(() => {
            return {
                multiple: true,
                search: true,
                showValueAsTags: true,
                maxHeight: '32px',
                dropboxWrapper: 'form-select-sm',
                disabled: true // readonly for the moment
            };
        });

        const selectedLeadStatuses = computed(() => {
            return storeState.value.leadStatuses || [];
        });

        const leadStatusSelectorConfig = computed(() => {
            return {
                multiple: true,
                search: true,
                showValueAsTags: true,
                maxHeight: '32px',
                dropboxWrapper: 'form-select-sm'
            };
        });

        // Methods
        const onOperationTypeChange = () => {
            store.dispatch('updateOperationType', operationType.value);
        };

        const onPropertyTypeChange = (propertyTypes: Array<number>) => {
            store.dispatch('updatePropertyTypes', propertyTypes);
        };

        const onBudgetChange = () => {
            store.dispatch('updateBudget', {
                budgetFrom: budgetFrom.value,
                budgetTo: budgetTo.value
            });
        };

        // New filter methods
        const onNameChange = () => {
            store.dispatch('updateName', nameFilter.value);
        };

        const onAgeRangeChange = () => {
            store.dispatch('updateAgeRange', ageRangeFilter.value);
        };

        const onNationalityChange = (nationalities: Array<number>) => {
            store.dispatch('updateNationalities', nationalities);
        };

        const onBudgetRangeChange = () => {
            store.dispatch('updateBudgetRange', budgetRangeFilter.value);
        };

        const onProjectsChange = (projects: Array<number>) => {
            store.dispatch('updateInterestedProjects', projects);
        };

        const onOffplanChange = () => {
            store.dispatch('updateOffplan', offplanFilter.value);
        };

        const onLeadStatusChange = (leadStatuses: Array<number>) => {
            store.dispatch('updateLeadStatuses', leadStatuses);
        };

        const onInterestChange = () => {
            store.dispatch('updateInterest', interestFilter.value);
        };

        const onInternationalBuyersChange = () => {
            store.dispatch('updateInternationalBuyers', internationalBuyersFilter.value);
        };

        const onHasPhoneChange = () => {
            store.dispatch('updateHasPhone', hasPhoneFilter.value);
        };

        const onHasEmailChange = () => {
            store.dispatch('updateHasEmail', hasEmailFilter.value);
        };

        const clearDaterange = (inputId: string) => {
            const input = document.getElementById(inputId) as HTMLInputElement;
            if (input) {
                input.value = '';
                store.dispatch('updateDateCreated', { dateFrom: '', dateTo: '' });
            }
        };

        const resetFilters = () => {
            // Reset existing filters
            operationType.value = '';
            budgetFrom.value = undefined;
            budgetTo.value = undefined;

            // Reset new filters
            nameFilter.value = '';
            ageRangeFilter.value = '';
            budgetRangeFilter.value = '';
            interestFilter.value = '';
            offplanFilter.value = false;
            internationalBuyersFilter.value = false;
            hasPhoneFilter.value = false;
            hasEmailFilter.value = false;

            // Clear date range picker
            clearDaterange('contactCreationDateRangeInput');

            // Reset store - both new and existing filters
            store.dispatch('updateOperationType', '');
            store.dispatch('updatePropertyTypes', []);
            store.dispatch('updateDateCreated', { dateFrom: '', dateTo: '' });
            store.dispatch('updateBudget', { budgetFrom: undefined, budgetTo: undefined });
            store.dispatch('updateTags', []);
            store.dispatch('updateCallLog', '');
            store.dispatch('updateVerified', false);
            store.dispatch('updateIsLandlord', false);
            store.dispatch('updateHasLeads', false);

            // Reset new filters in store
            store.dispatch('updateName', '');
            store.dispatch('updateAgeRange', '');
            store.dispatch('updateNationalities', []);
            store.dispatch('updateBudgetRange', '');
            store.dispatch('updateInterestedProjects', []);
            store.dispatch('updateOffplan', false);
            store.dispatch('updateLeadStatuses', []);
            store.dispatch('updateInterest', '');
            store.dispatch('updateInternationalBuyers', false);
            store.dispatch('updateHasPhone', false);
            store.dispatch('updateHasEmail', false);
        };

        // Methods for existing filters
        const updateTags = (tags: string) => {
            if (!isEqual(tags, storeState.value.tags)) {
                store.dispatch('updateTags', tags);
            }
        };

        const updateCallLog = (event: any) => {
            const selectedValue = event.target.value;
            store.dispatch('updateCallLog', selectedValue);
        };

        const updateVerified = (data: boolean) => {
            store.dispatch('updateVerified', data);
        };

        const updateIsLandlord = (data: boolean) => {
            store.dispatch('updateIsLandlord', data);
        };

        const updateHasLeads = (data: boolean) => {
            store.dispatch('updateHasLeads', data);
        };

        // Load property types
        const loadPropertyTypes = async () => {
            try {
                const data = await request("/api/listing-type");
                propertyTypeOptions.value = data.map((item: any) => ({
                    value: item.id,
                    label: item.label,
                }));
            } catch (error) {
                console.error('Failed to load property types:', error);
            }
        };

        // Load nationalities
        const loadNationalities = async () => {
            try {
                const data = await request("/api/nationality");
                nationalityOptions.value = data.map((item: any) => ({
                    value: item.key,
                    label: item.value,
                }));
            } catch (error) {
                console.error('Failed to load nationalities:', error);
            }
        };

        // Load developments/projects
        const loadProjects = async () => {
            try {
                const response = await request("/crm/developments", {
                    headers: {
                        "X-Requested-With": "XMLHttpRequest",
                    },
                });
                projectOptions.value = response.data.map((item: any) => ({
                    value: item.id,
                    label: item.title,
                }));
            } catch (error) {
                console.error('Failed to load projects:', error);
            }
        };

        // Load lead statuses
        const loadLeadStatuses = async () => {
            try {
                const data = await request("/api/options/lead-statuses");
                leadStatusOptions.value = data.map((item: any) => ({
                    value: item.id,
                    label: item.name,
                }));
            } catch (error) {
                console.error('Failed to load lead statuses:', error);
            }
        };

        // Initialize date range picker
        const initializeDateRangePicker = () => {
            setTimeout(() => {
                if (typeof $ !== 'undefined' && typeof $.fn.daterangepicker !== 'undefined') {
                    $("#contactCreationDateRangeInput").daterangepicker(
                        {
                            opens: "left",
                            autoApply: true,
                        },
                        function (start: any, end: any) {
                            store.dispatch('updateDateCreated', {
                                dateFrom: start.format("YYYY-MM-DD"),
                                dateTo: end.format("YYYY-MM-DD")
                            });
                        }
                    );
                }
            }, 500);
        };

        // Watch store state to sync local values
        watch(() => storeState.value.operationType, (newValue) => {
            operationType.value = newValue || '';
        });

        watch(() => storeState.value.budgetFrom, (newValue) => {
            budgetFrom.value = newValue;
        });

        watch(() => storeState.value.budgetTo, (newValue) => {
            budgetTo.value = newValue;
        });

        // Watch new filter values
        watch(() => storeState.value.name, (newValue) => {
            nameFilter.value = newValue || '';
        });

        watch(() => storeState.value.ageRange, (newValue) => {
            ageRangeFilter.value = newValue || '';
        });

        watch(() => storeState.value.budgetRange, (newValue) => {
            budgetRangeFilter.value = newValue || '';
        });

        watch(() => storeState.value.interest, (newValue) => {
            interestFilter.value = newValue || '';
        });

        watch(() => storeState.value.offplan, (newValue) => {
            offplanFilter.value = newValue || false;
        });

        watch(() => storeState.value.internationalBuyers, (newValue) => {
            internationalBuyersFilter.value = newValue || false;
        });

        watch(() => storeState.value.hasPhone, (newValue) => {
            hasPhoneFilter.value = newValue || false;
        });

        watch(() => storeState.value.hasEmail, (newValue) => {
            hasEmailFilter.value = newValue || false;
        });

        // Lifecycle
        onMounted(async () => {
            // Load all data
            await Promise.all([
                loadPropertyTypes(),
                loadNationalities(),
                loadProjects(),
                loadLeadStatuses()
            ]);

            initializeDateRangePicker();

            // Initialize values from store
            operationType.value = storeState.value.operationType || '';
            budgetFrom.value = storeState.value.budgetFrom;
            budgetTo.value = storeState.value.budgetTo;

            // Initialize new filter values from store
            nameFilter.value = storeState.value.name || '';
            ageRangeFilter.value = storeState.value.ageRange || '';
            budgetRangeFilter.value = storeState.value.budgetRange || '';
            interestFilter.value = storeState.value.interest || '';
            offplanFilter.value = storeState.value.offplan || false;
            internationalBuyersFilter.value = storeState.value.internationalBuyers || false;
            hasPhoneFilter.value = storeState.value.hasPhone || false;
            hasEmailFilter.value = storeState.value.hasEmail || false;
        });

        return {
            // Reactive data
            operationType,
            budgetFrom,
            budgetTo,
            propertyTypeOptions,
            moreFiltersVisible,
            callResponses,
            // New filter reactive data
            nameFilter,
            ageRangeFilter,
            budgetRangeFilter,
            interestFilter,
            offplanFilter,
            internationalBuyersFilter,
            hasPhoneFilter,
            hasEmailFilter,
            nationalityOptions,
            projectOptions,
            leadStatusOptions,
            // Virtual select classes
            propertyTypeSelectorClass,
            tagsFilterClass,
            nationalitySelectorClass,
            projectsSelectorClass,
            leadStatusSelectorClass,
            // Computed properties
            storeState,
            selectedPropertyTypes,
            propertyTypeSelectorConfig,
            selectedTags,
            filterTagsSelectorConfig,
            selectedNationalities,
            nationalitySelectorConfig,
            selectedProjects,
            projectsSelectorConfig,
            selectedLeadStatuses,
            leadStatusSelectorConfig,
            // Methods
            onOperationTypeChange,
            onPropertyTypeChange,
            onBudgetChange,
            clearDaterange,
            resetFilters,
            updateTags,
            updateCallLog,
            updateVerified,
            updateIsLandlord,
            updateHasLeads,
            // New filter methods
            onNameChange,
            onAgeRangeChange,
            onNationalityChange,
            onBudgetRangeChange,
            onProjectsChange,
            onOffplanChange,
            onLeadStatusChange,
            onInterestChange,
            onInternationalBuyersChange,
            onHasPhoneChange,
            onHasEmailChange,
        };
    },
});
</script>

<style>

/* Compact styles for the filters */
.form-select-sm,
.form-control-sm {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
    min-height: 30px;
}

/* Make virtual select more compact */
.vscomp-ele {
    --vscomp-font-size: 0.875rem;
    --vscomp-option-padding: 4px 8px;
    max-height: 32px;
    height: 32px;
}

.vscomp-toggle-button {
    max-height: 32px;
    height: 32px;
    line-height: 1;
    display: flex;
    align-items: center;
}
</style>
