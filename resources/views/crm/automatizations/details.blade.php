@extends('crm.layout')

@section('title')
FG Realty CRM - Automation Details
@endsection

@section('content')
<div class="d-flex flex-column mt-3 pl-2">
    <h4 class="text-left">Automation #{{$automatization->id}}</h4>

    <div class="d-flex flex-column w-100 p-0 py-3">
        <div class="p-4 bg-light rounded-1 shadow-xs">
            <h4 class="mb-3">Project Details</h4>
            <div class="row">
                <div class="col-md-6"><strong>Start Date:</strong> {{$automatization->start_date->format('d/m/Y')}}</div>
                <div class="col-md-6"><strong>End Date:</strong> {{$automatization->end_date->format('d/m/Y')}}</div>
            </div>
            <div class="row">
                <div class="col-md-6"><strong>Status:</strong> 
                <div class="text-nowrap font-small badge p-2 ps-2 pe-2" style="background-color: {{$automatization->status == 'ACTIVE' ? 'green' : ($automatization->status == 'NEW' ? 'blue' : 'red') }}">{{$automatization->status}}</div> 
            </div>
                <div class="col-md-6"><strong>Tasks per day (per user):</strong> {{$automatization->no_of_tasks_per_day}}</div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <strong>Tags:</strong> @foreach($automatization->tags as $t){{$t->label}}@if($t != $automatization->tags->last()),@endif @endforeach <br>
                    <strong>Upcoming Tasks:</strong>  {{count($automatization->users) * $interval->days * $automatization->no_of_tasks_per_day - count($automatization->tasks)}}
                </div>
                <div class="col-md-6"><strong>Users:</strong> 
                    @if($isTeamLeader)
                        @foreach($automatization->users->where('team_leader_id', auth()->user()->id) as $u)
                            {{ $u->name }}@if(!$loop->last),@endif
                        @endforeach
                    @else
                        @foreach($automatization->users as $u)
                            {{ $u->name }}@if(!$loop->last),@endif
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@if($automatization->status == 'NEW')
<div class="text-center my-5">
    <div class="alert alert-info">
        <h4 class="alert-heading">Upcoming Tasks</h4>
        <p>This table will be populated with data once the automatization starts running.</p>
    </div>
</div>
@endif
<div class="table-responsive">
    <table class="table table-bordered table-striped @if($automatization->status == 'NEW') table-light opacity-50 @endif">
        <thead>
            <tr>
                <th></th>
                <th>Total Number of Tasks</th>
                <th>Created Tasks</th>
                <th>Completed Tasks</th>
                <th>Average Response Time</th>
                <th>Follow-up Rate</th>
                <th>Lead Conversion Rate</th>
                <th>Viewing Rate</th>
                <th>Upcoming Tasks</th>
            </tr>
        </thead>
        <tbody>
            @if($automatization->status == 'NEW')
            <tr>
                <td colspan="9" class="text-center">No data available yet.</td>
            </tr>
            @else
                @foreach($datas as $data)
                <tr class="summary-row" data-bs-toggle="collapse" data-bs-target="#collapse{{$loop->index}}" aria-expanded="false" aria-controls="collapse{{$loop->index}}" data-date="{{$data->the_date}}" data-index="{{$loop->index}}">
                    <th><i class="bi bi-plus-circle-fill text-primary me-2"></i>{{\Carbon\Carbon::parse($data->the_date)->format('d/m/Y')}}</th>
                    <td>{{$data->totalTasks}}</td>
                    <td>{{$data->created_tasks}}</td>
                    <td>{{$data->completed_tasks}}</td>
                    <td>{{$data->avg_response_time}}</td>
                    <td>{{$data->follow_up_rate . '%'}}</td>
                    <td>{{$data->lead_conversion_rate . '%'}}</td>
                    <td>{{$data->viewing_rate . '%'}}</td>
                    <td>{{$data->totalTasks - $data->created_tasks}}</td>

                </tr>
                <tr class="collapse" id="collapse{{$loop->index}}">
                    <td colspan="9" class="details-container p-4" data-index="{{$loop->index}}">
                        Loading...
                    </td>
                </tr>
                @endforeach
            @endif
        </tbody>
    </table>
</div>
<style>
.summary-row td,
.summary-row th {
  cursor: pointer;
}
</style>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        document.querySelectorAll('.summary-row').forEach(row => {
            row.addEventListener('click', function () {
                const date = this.dataset.date;
                const index = this.dataset.index;
                const container = document.querySelector(`#collapse${index} .details-container`);
    
                // Avoid fetching again if it's already loaded
                if (container.dataset.loaded === "true") return;
                const automatizationId = '{{ $automatization->id }}';
                request(`/api/automatization/${automatizationId}/details?date=${encodeURIComponent(date)}`)
                    // .then(response => response.json())
                    .then(data => {
                        let html = `
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Total Number of Tasks</th>
                                        <th>Created Tasks</th>
                                        <th>Completed Tasks</th>
                                        <th>Average Response Time</th>
                                        <th>Follow-up Rate</th>
                                        <th>Lead Conversion Rate</th>
                                        <th>Viewing Rate</th>
                                        <th>Upcoming Tasks</th>
                                    </tr>
                                </thead>
                                <tbody>`;
    
                        data.users.forEach(user => {
                            html += `
                                <tr>
                                    <th>${user.name}</th>
                                    <td>${user.totalTasks}</td>
                                    <td>${user.created_tasks}</td>
                                    <td>${user.completed_tasks}</td>
                                    <td>${user.avg_response_time}</td>
                                    <td>${user.follow_up_rate}%</td>
                                    <td>${user.lead_conversion_rate}%</td>
                                    <td>${user.viewing_rate}%</td>
                                    <td>${user.totalTasks - user.created_tasks}</td>
                                </tr>`;
                        });
    
                        html += '</tbody></table>';
                        container.innerHTML = html;
                        container.dataset.loaded = "true"; // prevent re-fetching
                    })
                    .catch(error => {
                        container.innerHTML = '<div class="text-danger">Error loading details.</div>';
                        console.error(error);
                    });
            });
        });
    });
    </script>
    

<style>
    /* Remove the default focus border */
    .btn-link:focus {
        box-shadow: none;
        outline: none;
    }

    .btn-link,
    .btn-link i,
    .btn-link:hover {
        color: black;
    }
</style>
@endsection
