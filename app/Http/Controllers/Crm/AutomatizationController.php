<?php

namespace App\Http\Controllers\Crm;

use App\Http\Controllers\Controller;
use App\Models\Crm\Automatization;
use App\Models\Crm\ContactsListTag;
use App\Models\Task;
use App\Models\Crm\RolesDef;
use App\Models\User;
use App\Services\AutomatizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;
use Log;
use DB;
use DateTime;

class AutomatizationController extends Controller
{
    protected $indexView = "crm.automatizations.index";
    protected $detailsView = "crm.automatizations.details";
    protected $createView = "crm.automatizations.form.create";
    protected $editView = "crm.automatizations.form.edit";
    protected $automatizationService;
    protected $ajaxListMapper;
    protected $hasEnvelope = true;

    public function __construct(
        AutomatizationService $automatizationService,
        Request $request
    ) {
        $this->automatizationService = $automatizationService;
        $extraConfig = $this->automatizationService->getExtraConfig($request);
        $this->itemFetcher = function ($offset = 0, $limit = 10) use ($extraConfig) {
            return $this->automatizationService->fetchItems($extraConfig, $offset, $limit);
        };
        $this->itemFetcher = function ($offset = 0, $limit = 10) {
            return $this->automatizationService->fetchItems($offset, $limit);
        };
        $this->ajaxListMapper = $this->automatizationService->ajaxListMapper;
    }
  

    public function index(Request $request) {
        if ($request->ajax()) {
            if ($this->itemFetcher) {
                $offset = empty($request->query('start')) ? 0 : $request->query('start');
                $limit = empty($request->query('length')) ? 0 : $request->query('length');
                $items = call_user_func($this->itemFetcher, $offset, $limit);
            } else {
                $items = call_user_func($this->modelClass . '::all');
            }

            if (isset($items['items'])) {
                if (is_array($items['items'])) {
                    $data = array_map($this->ajaxListMapper, $items['items']);
                } else {
                    $data = $items['items']->map($this->ajaxListMapper);
                }
            } else {
                if (is_array($items)) {
                    $data = array_map($this->ajaxListMapper, $items);
                } else {
                    $data = $items->map($this->ajaxListMapper);
                }
            }
            if (isset($this->hasEnvelope)) {
                $payload = ['data' => $data, 'recordsTotal' => $items['count'] ?? 0, 'recordsFiltered' => $items['count'] ?? 0];
                return response()->json($payload);
            }
            return response()->json(["data" => $data]);
        }

        return view($this->indexView);

    }   

    public function create(){
        $tags = ContactsListTag::get()->map(function ($tag) {
            return ['value' => $tag->id, 'label' => $tag->label];
        })->toArray();

        $users = getAllFGRAgents(false,false,true)->map(function ($user) {
            return ['value' => $user->id, 'label' => $user->name];
        })->toArray();

        return view($this->createView, compact(['tags', 'users']));
    }

    public function store(){
        $validData = request()->validate([
            'tag_ids' => '',
            'user_ids' => '',
            'start_date' => 'required',
            'end_date' => 'required',
            'no_of_tasks_per_day' => 'required',
        ]);
        $tagIdsArray = [];
        if(!empty(trim($validData['tag_ids']))) {
            $tagIdsArray = explode(',', $validData['tag_ids']);
        }
        
        $userIdsArray = [];
        if(!empty($validData['user_ids'])) {
            $userIdsArray = explode(',', $validData['user_ids']);
        }
        $startDate = new DateTime($validData['start_date']);
        $endDate = new DateTime($validData['end_date']);
        $today = new DateTime();
        if($startDate > $today){
            $status = 'NEW';
        }elseif($endDate > $today){
            $status = 'ACTIVE';
        }else{
            $status = 'INACTIVE';
        }

        $automatization = Automatization::create([
            'start_date' => $validData['start_date'],
            'end_date' => $validData['end_date'],
            'no_of_tasks_per_day' => $validData['no_of_tasks_per_day'],
            'status' => $status,
            'created_by' => auth()->user()->id
        ]);

        foreach($tagIdsArray as $tagId){
            DB::table('automatizations_x_tags')->insert([
                'automatization_id' => $automatization->id,
                'contacts_list_tag_id' => $tagId,
            ]);
        }

        foreach($userIdsArray as $userId){
            DB::table('automatizations_x_users')->insert([
                'automatization_id' => $automatization->id,
                'user_id' => $userId,
            ]);
        }
    
        return redirect()->route('crm.automatizations.index')->with('message.success', 'The item was successfully saved');
    }

    public function details($automatizationId){
        $isTeamLeader = auth()->user()->hasAnyRole([RolesDef::TEAM_LEADER]) ? true : false;
        $automatization = Automatization::with('tasks.leadFromTask')->find($automatizationId);
        $startDate = new DateTime($automatization->start_date);
        $endDate = new DateTime($automatization->end_date);
        $interval = $startDate->diff($endDate);
        
        $sql = "SELECT
                    DATE(t.created_at) AS the_date,
                    COUNT(t.id) AS created_tasks,
                    SUM(CASE WHEN t.status = 'completed' THEN 1 ELSE 0 END) AS completed_tasks,
                    AVG(CASE 
                        WHEN t.status = 'completed' AND t.completion_date IS NOT NULL 
                        THEN TIMESTAMPDIFF(SECOND, t.created_at, t.completion_date)
                        ELSE NULL 
                    END) AS avg_response_time,
                    ROUND(SUM(CASE WHEN t.status != 'not_started' THEN 1 ELSE 0 END) / COUNT(t.id) * 100, 2) AS follow_up_rate,
                    ROUND(SUM(CASE WHEN l.task_id IS NOT NULL THEN 1 ELSE 0 END) / COUNT(t.id) * 100, 2) AS lead_conversion_rate,
                    ROUND(SUM(CASE WHEN l.task_id IS NOT NULL AND EXISTS (
                        SELECT 1 FROM tasks i 
                        WHERE i.object_type = 'lead' 
                        AND i.object_id = l.id 
                        AND i.type = 'Viewing'
                    ) THEN 1 ELSE 0 END ) / COUNT(t.id) * 100, 2) AS viewing_rate,
                    (a.no_of_tasks_per_day * uc.user_count) AS totalTasks
                FROM tasks t
                JOIN automatizations a ON a.id = t.automatization_id
                JOIN (SELECT automatization_id, COUNT(*) AS user_count
                    FROM automatizations_x_users
                    GROUP BY automatization_id
                ) uc ON uc.automatization_id = a.id
                LEFT JOIN leads l ON l.task_id = t.id
                WHERE a.id = :automatization_id
                GROUP BY the_date
                ORDER BY the_date ASC";

        $datas = DB::select(DB::raw($sql),[
            'automatization_id' => $automatizationId,
        ]);

        collect($datas)->map(function ($item) {
            $item->avg_response_time = self::secondsToTime($item->avg_response_time);
            return $item;
        })->all();

        return view($this->detailsView, compact(['automatization', 'datas', 'interval', 'isTeamLeader']));
    }

    public function getUsersMetricsOnAutomatization($automatizationId, Request $request){
        $date = $request->query('date');
        $isTeamLeader = auth()->user()->hasAnyRole([RolesDef::TEAM_LEADER]) ? true : false;

        $sql = "SELECT
                    u.id AS user_id,
                    u.name AS user_name,
                    COUNT(t.id) AS created_tasks,
                    SUM(CASE WHEN t.status = 'completed' THEN 1 ELSE 0 END) AS completed_tasks,
                    AVG(CASE 
                        WHEN t.status = 'completed' AND t.completion_date IS NOT NULL 
                        THEN TIMESTAMPDIFF(SECOND, t.created_at, t.completion_date)
                        ELSE NULL 
                    END) AS avg_response_time,
                    ROUND(SUM(CASE WHEN t.status != 'not_started' THEN 1 ELSE 0 END) / COUNT(t.id) * 100, 2) AS follow_up_rate,
                    ROUND(SUM(CASE WHEN l.task_id IS NOT NULL THEN 1 ELSE 0 END) / COUNT(t.id) * 100, 2) AS lead_conversion_rate,
                    ROUND(SUM(CASE WHEN l.task_id IS NOT NULL AND EXISTS (
                        SELECT 1 FROM tasks i 
                        WHERE i.object_type = 'lead' 
                        AND i.object_id = l.id 
                        AND i.type = 'Viewing'
                    ) THEN 1 ELSE 0 END ) / COUNT(t.id) * 100, 2) AS viewing_rate,
                    (a.no_of_tasks_per_day * 1) AS totalTasks
                FROM users u
                JOIN automatizations_x_users au ON au.user_id = u.id
                JOIN automatizations a ON a.id = au.automatization_id
                LEFT JOIN tasks t ON t.assigned_to = u.id AND DATE(t.created_at) = :date AND t.automatization_id = a.id
                LEFT JOIN leads l ON l.task_id = t.id
                WHERE a.id = :automatization_id";

        $params = [
            'automatization_id' => $automatizationId,
            'date' => $date,
        ];

        if ($isTeamLeader) {
            $sql .= " AND u.team_leader_id = :team_leader_id";
            $params['team_leader_id'] = auth()->user()->id;
        }

        $sql .= " GROUP BY u.id";

        $datas = DB::select(DB::raw($sql), $params);

        $users = collect($datas)->map(function($item) {
                return [
                    'name' => $item->user_name,
                    'totalTasks' => $item->totalTasks,
                    'created_tasks' => $item->created_tasks,
                    'completed_tasks' => $item->completed_tasks,
                    'avg_response_time' => self::secondsToTime($item->avg_response_time),
                    'follow_up_rate' => $item->follow_up_rate??0,
                    'lead_conversion_rate' => $item->lead_conversion_rate??0,
                    'viewing_rate' => $item->viewing_rate??0,
                ];
            });

        return response()->json(['users' => $users]);
    }

    function secondsToTime($seconds) {
        if($seconds){
            $dtF = new \DateTime('@0');
            $dtT = new \DateTime("@$seconds");
            return $dtF->diff($dtT)->format('%a days, %h hours, %i minutes');
        }else{
            return '-';
        }
    }

    public function edit($automatizationId){
        $automatization = Automatization::find($automatizationId);
        $tags = ContactsListTag::get()->map(function ($tag) {
            return ['value' => $tag->id, 'label' => $tag->label];
        })->toArray();

        $users = getAllFGRAgents(false,false,true)->map(function ($user) {
            return ['value' => $user->id, 'label' => $user->name];
        })->toArray();

        return view($this->editView, compact(['automatization', 'tags', 'users']));
    }

    public function update($id){
        $validData = request()->validate([
            'tag_ids' => '',
            'user_ids' => '',
            'start_date' => 'required',
            'end_date' => 'required',
            'no_of_tasks_per_day' => 'required',
        ]);
        $tagIdsArray = [];
        if(!empty(trim($validData['tag_ids']))) {
            $tagIdsArray = explode(',', $validData['tag_ids']);
        }
        
        $userIdsArray = [];
        if(!empty($validData['user_ids'])) {
            $userIdsArray = explode(',', $validData['user_ids']);
        }
        $startDate = new DateTime($validData['start_date']);
        $endDate = new DateTime($validData['end_date']);
        $today = new DateTime();
        if($startDate > $today){
            $status = 'NEW';
        }elseif($endDate > $today){
            $status = 'ACTIVE';
        }else{
            $status = 'INACTIVE';
        }

        $automatization = Automatization::find($id);
        $automatization->update([
            'start_date' => $validData['start_date'],
            'end_date' => $validData['end_date'],
            'no_of_tasks_per_day' => $validData['no_of_tasks_per_day'],
            'status' => $status,
            'created_by' => auth()->user()->id
        ]);

        $automatization->tags()->sync($tagIdsArray);
        $automatization->users()->sync($userIdsArray);

        return redirect()->route('crm.automatizations.index')->with('message.success', 'The item was successfully saved');
    }

}