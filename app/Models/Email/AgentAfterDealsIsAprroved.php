<?php

namespace App\Models\Email;

class AgentAfterDealsIsAprroved extends GenericEmail
{
    private $deal;

    public function __construct($deal)
    {
        $this->deal = $deal;
    }

    public function getSubject()
    {
        return "The deal was approved";
    }

    public function getTemplateName()
    {
        return 'components.mail.agent-deal-approved';
    }

    public function getTo()
    {
        if(env('APP_ENV') == 'local') {
            return env('EMAIL_ADDRESS_ITDEV');
        }
        return $this->deal->author->email;
    }

    public function getTemplateVars()
    {
        return [
            'deal' => $this->deal,
        ];
    }
}
