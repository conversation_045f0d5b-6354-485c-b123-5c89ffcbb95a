// Basic monthly data example
export const monthlyLeadData = {
  labels: ['January', 'February', 'March', 'April', 'May', 'June'],
  datasets: [
    {
      label: 'Target Leads',
      backgroundColor: '#FF6384',
      data: [50, 60, 70, 80, 90, 100]
    },
    {
      label: 'Self Generated Leads',
      backgroundColor: '#36A2EB',
      data: [30, 40, 50, 60, 70, 80]
    },
    {
      label: 'All Leads',
      backgroundColor: '#FFCE56',
      data: [80, 100, 120, 140, 160, 180]
    }
  ]
};

// Quarterly data example with custom colors
export const quarterlyLeadData = {
  labels: ['Q1', 'Q2', 'Q3', 'Q4'],
  datasets: [
    {
      label: 'Target Leads',
      backgroundColor: 'rgba(255, 99, 132, 0.7)',
      borderColor: 'rgb(255, 99, 132)',
      borderWidth: 1,
      data: [120, 150, 180, 210]
    },
    {
      label: 'Self Generated Leads',
      backgroundColor: 'rgba(54, 162, 235, 0.7)',
      borderColor: 'rgb(54, 162, 235)',
      borderWidth: 1,
      data: [90, 110, 130, 150]
    },
    {
      label: 'All Leads',
      backgroundColor: 'rgba(255, 206, 86, 0.7)',
      borderColor: 'rgb(255, 206, 86)',
      borderWidth: 1,
      data: [210, 260, 310, 360]
    }
  ]
};

// Weekly data example with different colors and pattern
export const weeklyLeadData = {
  labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
  datasets: [
    {
      label: 'Target Leads',
      backgroundColor: '#8e5ea2',
      data: [12, 19, 15, 17]
    },
    {
      label: 'Self Generated Leads',
      backgroundColor: '#3cba9f',
      data: [8, 15, 12, 11]
    },
    {
      label: 'All Leads',
      backgroundColor: '#e8c3b9',
      data: [20, 34, 27, 28]
    }
  ]
};

// Data by region
export const regionalLeadData = {
  labels: ['North', 'South', 'East', 'West', 'Central'],
  datasets: [
    {
      label: 'Target Leads',
      backgroundColor: 'rgba(220, 20, 60, 0.7)',
      data: [45, 52, 38, 60, 42]
    },
    {
      label: 'Self Generated Leads',
      backgroundColor: 'rgba(0, 128, 128, 0.7)',
      data: [32, 48, 30, 40, 35]
    },
    {
      label: 'All Leads',
      backgroundColor: 'rgba(106, 90, 205, 0.7)',
      data: [77, 100, 68, 100, 77]
    }
  ]
};

// Data with custom styling options
export const customStyledData = {
  labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
  datasets: [
    {
      label: 'Target Leads',
      backgroundColor: 'rgba(255, 99, 132, 0.7)',
      hoverBackgroundColor: 'rgba(255, 99, 132, 1)',
      borderColor: 'rgb(255, 99, 132)',
      borderWidth: 2,
      borderRadius: 5,
      data: [50, 60, 70, 80, 90, 100]
    },
    {
      label: 'Self Generated Leads',
      backgroundColor: 'rgba(54, 162, 235, 0.7)',
      hoverBackgroundColor: 'rgba(54, 162, 235, 1)',
      borderColor: 'rgb(54, 162, 235)',
      borderWidth: 2,
      borderRadius: 5,
      data: [30, 40, 50, 60, 70, 80]
    },
    {
      label: 'All Leads',
      backgroundColor: 'rgba(255, 206, 86, 0.7)',
      hoverBackgroundColor: 'rgba(255, 206, 86, 1)',
      borderColor: 'rgb(255, 206, 86)',
      borderWidth: 2,
      borderRadius: 5,
      data: [80, 100, 120, 140, 160, 180]
    }
  ]
};


export const tasksChartConstants = {
  labels: ['Total', 'Auto', 'Auto Done', 'Self', 'Self Done', 'Overdue'],
  colors: ['rgb(33, 150, 243)', 'rgb(255, 193, 7)', 'rgb(76, 175, 80)', 'rgb(156, 39, 176)', 'rgb(0, 150, 136)', 'rgb(244, 67, 54)']
}