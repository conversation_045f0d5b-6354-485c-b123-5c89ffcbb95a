<template>
    <div class="row g-2 filters">
        <div class="col-12 text-right mb-1">
            <a href="javascript:void(0)" @click="resetState()" class="small">Reset filters</a>
        </div>
        
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2">
            <label class="text-gray-50 small mb-0">Nationality</label>
            <FGVirtualSelect 
                :selectorClassName="nationalitySelectorClass" 
                :class="{
                    'vscomp-ele': true,
                    [nationalitySelectorClass]: true,
                }" 
                :options="options.nationalities" 
                :config="nationalitySelectorConfig"
                :selectedValue="state.nationality" 
                @change="onNationalitySelectChange($event)" 
            />
        </div>

        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2">
            <label class="text-gray-50 small mb-0">Roles</label>
            <FGVirtualSelect 
                :selectorClassName="rolesSelectorClass" 
                :class="{
                    'vscomp-ele': true,
                    [rolesSelectorClass]: true,
                }" 
                :options="options.roles" 
                :config="rolesSelectorConfig"
                :selectedValue="state.roles" 
                @change="onRolesSelectChange($event)" 
            />
        </div>

        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2">
            <label class="text-gray-50 small mb-0">Team Leader</label>
            <FGVirtualSelect 
                :selectorClassName="teamLeaderSelectorClass" 
                :class="{
                    'vscomp-ele': true,
                    [teamLeaderSelectorClass]: true,
                }" 
                :options="options.teamLeaders" 
                :config="teamLeaderSelectorConfig"
                :selectedValue="state.teamLeader" 
                @change="onTeamLeaderSelectChange($event)" 
            />
        </div>

        <div class="col-xs-12 col-sm-4 col-lg-2 mb-2 d-flex align-items-center">
            <div class="form-check">
                <input v-model="state.competition" type="checkbox" class="form-check-input" id="competitionCheckbox" />
                <label for="competitionCheckbox" class="form-check-label small">Competition</label>
            </div>
        </div>
    </div>
</template>

<script>
import { onMounted, ref, watch } from "vue";
import FGVirtualSelect from "../generic/FGVirtualSelect";
import { debounce } from "../../../helpers/all-kind";

const initialState = {
    nationality: "",
    roles: [],
    teamLeader: "",
    competition: false,
};

export default {
    name: "UsersFilters",
    emits: ['filtersChanged'],
    setup(props, { emit }) {
        function virtualSelectClassName() {
            return "r" + (Math.random() + 1).toString(36).substring(7);
        }

        // Generate unique class names for virtual selects
        const nationalitySelectorClass = virtualSelectClassName();
        const rolesSelectorClass = virtualSelectClassName();
        const teamLeaderSelectorClass = virtualSelectClassName();

        const options = ref({
            nationalities: [],
            roles: [],
            teamLeaders: [],
        });

        const state = ref({ ...initialState });

        // Virtual select configurations
        const nationalitySelectorConfig = {
            multiple: false,
            search: true,
            maxHeight: '32px',
            dropboxWrapper: 'form-select-sm'
        };

        const rolesSelectorConfig = {
            multiple: true,
            search: true,
            showValueAsTags: true,
            maxHeight: '32px',
            dropboxWrapper: 'form-select-sm'
        };

        const teamLeaderSelectorConfig = {
            multiple: false,
            search: true,
            maxHeight: '32px',
            dropboxWrapper: 'form-select-sm'
        };

        // Change handlers for virtual selects
        const onNationalitySelectChange = (nationality) => {
            state.value.nationality = nationality;
        };

        const onRolesSelectChange = (roles) => {
            state.value.roles = roles;
        };

        const onTeamLeaderSelectChange = (teamLeader) => {
            state.value.teamLeader = teamLeader;
        };

        const resetState = () => {
            state.value = { ...initialState };
        };

        const doSearch = debounce((availableFilters) => {
            window.usersSearchParams = availableFilters;
            if (typeof window.onUsersFiltersParamsChange === 'function') {
                window.onUsersFiltersParamsChange(availableFilters);
            }
            emit('filtersChanged', availableFilters);
        }, 300);

        onMounted(() => {
            // Load nationalities
            if (typeof request !== "undefined") {
                request("/api/nationality").then((nationalityData) => {
                    options.value.nationalities = nationalityData.map((item) => ({
                        value: item.key,
                        label: item.value,
                    }));
                }).catch((error) => {
                    console.error('Failed to load nationalities:', error);
                });
            }

            // Load roles from global variable if available
            if (typeof window.rolesData !== "undefined") {
                options.value.roles = window.rolesData.map((item) => ({
                    value: item.id,
                    label: item.name,
                }));
            }

            // Load team leaders from global variable if available
            if (typeof window.teamLeadersData !== "undefined") {
                options.value.teamLeaders = window.teamLeadersData.map((item) => ({
                    value: item.id,
                    label: item.name,
                }));
            }

            // Load saved filters from localStorage
            const savedFilters = localStorage.getItem("usersTableFilters");
            if (savedFilters) {
                try {
                    const parsedFilters = JSON.parse(savedFilters);
                    state.value = { ...initialState, ...parsedFilters };
                } catch (error) {
                    console.error('Failed to parse saved filters:', error);
                }
            }
        });

        watch(
            () => ({ ...state.value }),
            (currentState) => {
                localStorage.setItem("usersTableFilters", JSON.stringify(currentState));
                
                const availableFilters = {};
                Object.keys(initialState).forEach((k) => {
                    if (state.value[k] !== null && state.value[k] !== "" && 
                        !(Array.isArray(state.value[k]) && state.value[k].length === 0) &&
                        state.value[k] !== false) {
                        availableFilters[k] = state.value[k];
                    }
                });

                doSearch(availableFilters);
            }
        );

        return {
            options,
            state,
            nationalitySelectorClass,
            rolesSelectorClass,
            teamLeaderSelectorClass,
            nationalitySelectorConfig,
            rolesSelectorConfig,
            teamLeaderSelectorConfig,
            onNationalitySelectChange,
            onRolesSelectChange,
            onTeamLeaderSelectChange,
            resetState,
        };
    },
    components: {
        FGVirtualSelect,
    },
};
</script>

<style lang="scss">
.filters {
    .vscomp-ele {
        max-height: 32px;
    }
}
</style>
