body {
    @apply min-w-375 bg-white;
}

header.main {
    @apply w-full flex justify-center bg-white z-20;
}

header.main nav {
    @apply w-full
    /*max-w-1200*/
    ;
}

// main {
//     @apply w-full max-w-1200 mx-auto;
// }

div.wrapper-main {
    @apply w-full max-w-1200 mx-auto;
}

footer.main {
    @apply w-full;
}

footer.main .main-sections {
    @apply max-w-1200;
}

svg {
    display: inline-block;
    vertical-align: baseline;
    margin-bottom: -2px;

    /* yes, I'm that particular about formatting */
    &:focus {
        outline: none;
    }
}

.svg-icon {
    fill: currentColor;
}

.lang-ar {
    header.main {
        min-height: 64px;
    }
}

@mixin footer_h2 {
    font-size: 1.33em;
    line-height: 125%;
    font-weight: 300;
    letter-spacing: -0.25px;
    color: white;
}

/* new footer*/
.mainFooter {
    @apply flex flex-col px-3 pt-7.5 pb-5;
    background-color: #0D0D0D;

    &__firstRow {
        @apply mt-0 flex flex-col gap-10;

        &-shortcuts {
            @apply flex;
        }

        &-content {
            display: flex;
            flex-direction: column;
            gap: 30px;

            &-apps {
                @apply flex flex-col gap-2.5;

                h2 {
                    @include footer_h2;
                    width: 100%;
                    max-width: 400px;
                }

                &-ctas {
                    display: flex;
                    gap: 12px;
                }
            }

            &-newsletter {
                @apply flex flex-col gap-2.5;

                h2 {
                    @include footer_h2;
                    width: 100%;
                    max-width: 400px;
                }

                &-form {
                    display: flex;
                    flex-direction: column;
                    gap: 12px;

                    button {
                        background-color: #fff;
                        color: #0D0D0D;
                        font-weight: 500;
                        min-height: 47px;
                        width: 100%;
                        &:hover {
                            background-color: #7B828A;
                            color: #FFFFFF;
                        }
                    }

                    &-input[type=email] {
                        background-color: #FFFFFF25;
                        border: none;
                        border-radius: 6px;
                        color: #fff !important;
                        padding: 6px 16px;
                        font-size: 16px;
                        min-height: 47px;
                        min-width: 100%;
                        width: 350px;
                    }

                    &-input[type=email]:active {
                        color: #fff;
                    }
                }
                &-message {
                    display: none;
                    color: green !important;
                }
            }
        }
    }
    &__brandsRow {
        @apply mt-10 mb-3 mx-0 flex flex-wrap gap-5;

        &-brand {
            @apply flex flex-col gap-3;
            p {
                margin-top: 0px !important;
                display: inline-block;
                font-size: 0.9rem;
                max-width: 640px;
                line-height: 1.5;
                color: white;
            }
        }

        &-awards {
            @apply flex flex-col gap-3;
            p {
                margin-top: 0px !important;
                display: inline-block;
                font-size: .8rem;
                letter-spacing: -0.25px;
                max-width: 640px;
                line-height: 1.5;
                color: #E7E7E7;
            }
        }
    }
    &__bottomLine {
        @apply flex flex-col-reverse items-start gap-3 flex-nowrap mt-0 pt-3;
        border: 1px solid #333438;
        border-left: 0px;
        border-right: 0px;
        border-bottom: 0px;

        &-links {
            font-size: 0.9rem;

            a {
                color: white;
            }
        }

        &-socialMedia {
            @apply flex gap-2;
        }
    }

    @media screen and (min-width: 641px) {
        padding: 80px 80px 40px;
        &__firstRow-content-newsletter {
            &-form {
                flex-direction: row;
                button {
                    width: auto;
                }
            }
        }
    }

    @media screen and (min-width: 1201px) {
        @apply mt-15;
        padding: 120px 120px 40px;

        &__firstRow {
            @apply flex-row gap-0;

            &-shortcuts {
                width: 50%;
            }

            &-content {
                @apply pl-10;
                width: 50%;
            }
        }
        &__brandsRow {
            @apply gap-0;
            &-brand {
                width: 50%;
            }
    
            &-awards {
                @apply pl-10;
                width: 50%;
            }
        }
        &__bottomLine {
            @apply flex-row justify-between;
        }
    }
}

.colHalf {
    @apply flex flex-col;
    width: 45%;
    gap: 40px 0;

    h2 {
        @include footer_h2;
    }

    ul {
        list-style: none;
        margin: 0;

        li {
            display: flex;
            align-items: center;
            margin-top: 6px;
            padding-bottom: 6px;
        }
    }

    a {
        @apply text-white;
        font-weight: 300;
    }
}

.footerFormGroup {}