<?php

use App\Http\Controllers\Admin\CompaniesController;
use App\Http\Controllers\Admin\MarketingPlatforms;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\FormsController;
use App\Http\Controllers\Admin\PerformanceController;
use App\Http\Controllers\Admin\PoliciesController;
use App\Http\Controllers\Admin\ReportsController;
use App\Http\Controllers\Admin\UserRatingController;
use App\Http\Controllers\Admin\UsersController;
use App\Http\Controllers\Admin\LeadSourcesController;

Route::middleware(['permission:' . \App\Models\Crm\PermissionsDef::ADMIN_USERS])->group(function () {
    Route::prefix('users')->group(function () {
        Route::get('', [UsersController::class, 'index'])->name('admin.users.index');
        Route::get('create', [UsersController::class, 'create'])->name('admin.users.create');
        Route::post('store', [UsersController::class, 'store'])->name('admin.users.store');
        Route::post('bulk-reset-password', [UsersController::class, 'bulkResetPassword'])->name('admin.users.bulk-reset-password');
        Route::middleware(['permission:' . \App\Models\Crm\PermissionsDef::CHECK_IN_ADMIN])->group(function () {
            Route::get('check-in-admin', [UsersController::class, 'checkInAdmin'])->name('admin.users.check-in-admin');
        });
        Route::prefix('{id}')->group(function () {
            Route::get('edit', [UsersController::class, 'edit'])->name('admin.users.edit');
            Route::get('deletion-stats', [UsersController::class, 'deletionStats'])->name('admin.users.deletionStats');
            Route::post('update', [UsersController::class, 'update'])->name('admin.users.update');
            Route::post('delete', [UsersController::class, 'delete'])->name('admin.users.delete');

            // performance
            Route::get('rating', [UserRatingController::class, 'userRating'])->name('admin.user-rating.user-rating');
            Route::get('permissions', [UsersController::class, 'userPermissions'])->name('admin.user-permissions.index');
            Route::get('user-performance', [PerformanceController::class, 'userPerformance'])->name('admin.user.performance');
            Route::get('user-reports', [PerformanceController::class, 'userReports'])->name('admin.user.reports');
            Route::get('user-reports-details', [PerformanceController::class, 'userReportDetails'])->name('admin.user.report.details');
            Route::post('user-performance-save', [PerformanceController::class, 'save'])->name('user.performance.save');
            Route::get('download-documents', [PerformanceController::class, 'downloadDocuments'])->name('performance.documents.download');
            Route::get('download-payslips', [PerformanceController::class, 'downloadPayslips'])->name('performance.payslips.download');
            Route::post('contact-tags', [UsersController::class, 'updateUserAssignedContactTags'])->name('user.contact-tags.update');
            Route::get('contact-tags', [UsersController::class, 'userAssignedContactTags'])->name('user.contact-tags.get');
            Route::get('user-license', [UsersController::class, 'downloadLicense'])->name('admin.user.license.download');
            Route::get('user-payslips', [PerformanceController::class, 'userPayslips'])->name('admin.user.payslips');
            Route::post('user-payslips-save', [PerformanceController::class, 'save'])->name('user.payslips.save');
        });
    });
});
Route::middleware(['permission:' . \App\Models\Crm\PermissionsDef::CRM_ADMIN . '|' . \App\Models\Crm\PermissionsDef::MATRIX_AGENT_MANAGEMENT . '|' . \App\Models\Crm\PermissionsDef::TEAM_LEADER])->group(function () {
    Route::prefix('')->group(function () {
        Route::get('', [DashboardController::class, 'index'])->name('admin.index');

        Route::prefix('marketing-platforms')->group(function () {
            Route::get('', [MarketingPlatforms::class, 'index'])->name('admin.marketing-platforms.index');
            Route::get('search', [MarketingPlatforms::class, 'index'])->name("admin.marketing-platforms.search");
            Route::patch('{platformId}', [MarketingPlatforms::class, 'switchActive'])->name("admin.marketing-platforms.active");
        });

        Route::prefix('lead-sources')->group(function () {
            Route::get('', [LeadSourcesController::class, 'index'])->name('admin.lead-sources.index');
            Route::get('search', [LeadSourcesController::class, 'search'])->name('admin.lead-sources.search');
            Route::get('new', [LeadSourcesController::class, 'new'])->name('admin.lead-sources.new');
            Route::post('create', [LeadSourcesController::class, 'create'])->name('admin.lead-sources.create');
        });

        Route::prefix('companies')->group(function () {
            Route::get('', [CompaniesController::class, 'index'])->name('admin.companies.index');
            Route::get('search', [CompaniesController::class, 'search'])->name('admin.companies.search');
        });

        Route::prefix('reports')->group(function () {
            Route::get('', [ReportsController::class, 'index'])->name('admin.reports.index');
        });
        Route::prefix('reports-new')->group(function () {
            Route::get('', [ReportsController::class, 'reportsIndexNew'])->name('admin.reports.new.index');
            Route::get('reports-data', [ReportsController::class, 'getData'])->name('admin.reports.new.getData');
        });
        Route::prefix('performance')->group(function () {
            Route::get('', [PerformanceController::class, 'index'])->name('admin.performance.index');
        });

        Route::prefix('policies')->group(function () {
            Route::post('store', [PoliciesController::class, 'store'])->name('policies.create.post');
        });

        Route::prefix('forms')->group(function () {
            Route::post('store', [FormsController::class, 'store'])->name('forms.create.post');
        });

        Route::middleware(['permission:' . \App\Models\Crm\PermissionsDef::CRM_ADMIN])->group(function () {
            Route::get('roles-and-permissions-matrix', [UsersController::class, 'rolesPermissionsMatrix'])->name('admin.users.roles-permissions-matrix');
            Route::get('interactive-users-map', [UsersController::class, 'interactiveUsersMap'])->name('admin.users.interactive-users-map');
        });

        Route::prefix('contact-tags')->group(function () {
            Route::get('', [UsersController::class, 'contactTags'])->name('admin.contact_tags');
        });
    });
});

Route::get('policies/download-policy/{id}', [PoliciesController::class, 'downloadPolicy'])->name('policies.policy.download');
Route::get('policies', [PoliciesController::class, 'index'])->name('admin.policies.index');
Route::get('forms/download-form/{id}', [FormsController::class, 'downloadForm'])->name('forms.form.download');
Route::get('forms', [FormsController::class, 'index'])->name('admin.forms.index');
Route::post('competition', [ReportsController::class, 'competitionIndex'])->name('admin.competition.index');
