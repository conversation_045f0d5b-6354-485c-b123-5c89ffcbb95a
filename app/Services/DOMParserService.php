<?php

namespace App\Services;

class DOMParserService
{
    private $nodesToExport = [
        'h1',
        'h2',
        'h3',
        'h4',  
        'p',
        'ul',
        'li',
    ];

    private $nodesToExportOld = [
        'h1',
        'h2',
        'h3',
        'p',
        'ul',
        'li',
    ];

    public function parseContent($htmlContent, $nodesList = null)
    {
        if (is_null($nodesList)) {
            $nodesList = $this->nodesToExport;
        }
        $elementsToReturn = [];
        if (empty($htmlContent)) {
            return $elementsToReturn;
        }
        
        $d = new \DOMDocument();
        libxml_use_internal_errors(true); 
        $d->loadHTML(mb_convert_encoding($htmlContent, 'HTML-ENTITIES', 'UTF-8'));
        libxml_clear_errors();
        
        $it = $d->getElementsByTagName("*");

        foreach ($it as $item) {
            if (in_array($item->tagName, $nodesList)) {
                if ($item->tagName === 'ul') {
                    $listItems = [];
                    foreach ($item->childNodes as $child) {
                        if ($child->nodeName === 'li') {
                            $listItems[] = trim($child->nodeValue);
                        }
                    }
                    
                    $elementsToReturn[] = [
                        'type' => 'list',
                        'items' => $listItems
                    ];
                } 
                else if ($item->tagName === 'li' && $item->parentNode->tagName !== 'ul') {
                    $elementsToReturn[] = [
                        'type' => 'list_item',
                        'content' => trim($item->nodeValue)
                    ];
                }
                else if (in_array($item->tagName, ['h1', 'h2', 'h3', 'h4'])) {
                    $elementsToReturn[] = [
                        'type' => 'heading',
                        'level' => (int)substr($item->tagName, 1), 
                        'content' => trim($item->nodeValue)
                    ];
                }
                else if ($item->tagName === 'p') {
                    $elementsToReturn[] = [
                        'type' => 'paragraph',
                        'content' => trim($item->nodeValue)
                    ];
                }
            }
        }
        
        return $elementsToReturn;
    }

    public function parseContentOld($htmlContent, $nodesList = null)
    {
        if (is_null($nodesList)) {
            $nodesList = $this->nodesToExportOld;
        }
        $elementsToReturn = [];
        if (empty($htmlContent)) {
            return $elementsToReturn;
        }
        $d = new \DOMDocument();
        $d->loadHTML($htmlContent);
        $it = $d->getElementsByTagName("*");

        foreach ($it as $item) {
            if (in_array($item->tagName, $nodesList)) {
                $elementsToReturn[] = ['tag_name' => $item->tagName, 'content' => utf8_decode($item->nodeValue)];
            }
        }
        return $elementsToReturn;
    }
}