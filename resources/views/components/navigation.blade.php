@php
$routeName = '';
$route = request()->route();
if(!is_null($route)) {
$routeName = $route->getName();
}

$currentURL = url()->current();
$isProblematicMobile = $mobileDetect->isMobile() && ($mobileDetect->isSamsung());
@endphp

<nav role="navigation ">
    <div class="leftContent">
        <div title="Mobile menu switcher" class="leftContent__hamburger"></div>
        @if($locale != 'en')
        <a class="logo" href="{{persistQueryParamsToURL(@route('home.localized', ['locale' => $locale]))}}">
            @else
            <a class="logo" href="{{persistQueryParamsToURL(@route('home'))}}">
                @endif
                @if(app()->isLocale('ar'))
                <img width="106.92" height="15.99" class="logo__img-short" src="{{ asset('images/svg/FGREALTY_mobile_v2.svg') }}" alt="FGREALTY Logo">
                <img width="97" height="48" class="logo__img-tall" src="{{ asset('images/svg/FGREALTY_v2.svg') }}" alt="FGREALTY Logo" loading="lazy">
                {{--<img width="110.84" height="71.88" class="logo__img-tall" src="{{ asset('images/svg/FGREALTY_ar.svg') }}" alt="FGREALTY Qatar Logo">
                <img width="106.17" height="34.93" class="logo__img-short" src="{{ asset('images/svg/FGREALTY_mobile_ar.svg') }}" alt="FGREALTY Logo">--}}
                @else
                {{--
            <img width="110.84" height="71.88" class="logo__img-tall" src="{{ asset('images/svg/FGREALTY.svg') }}" alt="FGREALTY Qatar Logo">
                <img width="106.17" height="34.93" class="logo__img-short" src="{{ asset('images/svg/FGREALTY_mobile.svg') }}" alt="FGREALTY Logo">
                --}}
                <img width="106.92" height="15.99" class="logo__img-short" src="{{ asset('images/svg/FGREALTY_mobile_v2.svg') }}" alt="FGREALTY Logo">
                <img width="97" height="48" class="logo__img-tall" src="{{ asset('images/svg/FGREALTY_v2.svg') }}" alt="FGREALTY Logo">
                @endif
            </a>
    </div>

    <div class="flex flex-1 justify-end">
        <ul class="hidden principal">
            <li class="submenu">
                @php
                $buyPropertyLink = (object) null;
                $buyPropertyLink->op_type = 'buy';
                $buyPropertyUrl = $menuHelperService->createURL($buyPropertyLink, $locale);
                @endphp
                <a href="{{persistQueryParamsToURL($buyPropertyUrl)}}" class="{{ $buyPropertyUrl == $currentURL ? 'active' : '' }}">
                    <span>{{__('Buy')}}</span>
                </a>
                <ul>
                    @foreach($headerMenus['Header Menu - BUY']->categories[0]->links as $link)
                    @if(!empty($link))
                    <li><a href="{{persistQueryParamsToURL($menuHelperService->createURL($link, $locale))}}">{{$link->link_label}}</a>
                    </li>
                    @endif
                    @endforeach
                </ul>
            </li>
            <li class="submenu">
                @php
                $rentPropertyLink = (object) null;
                $rentPropertyLink->op_type = 'rent';
                $rentPropertyUrl = $menuHelperService->createURL($rentPropertyLink, $locale);
                @endphp
                <a href="{{persistQueryParamsToURL($rentPropertyUrl)}}" class="{{ $rentPropertyUrl == $currentURL ? 'active' : '' }}">
                    <span>{{__('Rent')}}</span>
                </a>
                <ul>
                    @foreach($headerMenus['Header Menu - RENT']->categories[0]->links as $link)
                    @if(!empty($link))
                    <li><a href="{{persistQueryParamsToURL($menuHelperService->createURL($link, $locale))}}">{{$link->link_label}}</a>
                    </li>
                    @endif
                    @endforeach
                </ul>
            </li>
            <li class="submenu submenu--tabbed">
                <a href="#" onclick="javascript:void(0)">
                    <span>{{__('Commercial')}}</span>
                </a>
                <ul>
                    <li>
                        <article class="submenuMegaMenu">
                            <span class="submenuMegaMenu__item">
                                <span class="submenuMegaMenu__item-headerRent">{{__('Rent')}}</span>
                                <div class="submenuMegaMenuLinks">
                                    @foreach($rentCategories as $category)
                                    <a href="{{persistQueryParamsToURL($menuHelperService->createURL($category, $locale))}}">{{$category->link_label}}</a>
                                    @endforeach
                                </div>
                            </span>
                            <span class="submenuMegaMenu__itemBordered">
                                <span class="submenuMegaMenu__itemBordered-headerSale">{{__('Buy')}}</span>
                                <div class="submenuMegaMenuLinks submenuMegaMenuLinks--bordered">
                                    @foreach($saleCategories as $category)
                                    <a href="{{persistQueryParamsToURL($menuHelperService->createURL($category, $locale))}}">{{$category->link_label}}</a>
                                    @endforeach
                                </div>
                            </span>
                        </article>
                    </li>
                </ul>
            </li>
            <li class="one-line">
                <a href="{{persistQueryParamsToURL(route('properties.search.exclusive', ['searchWord' => __('search'), 'exclusiveWord' => __('exclusive'), 'locale' => $locale]))}}" class="{{ $routeName == 'properties.search.exclusive' ? 'active' : '' }}">{{__('Exclusive Properties')}}</a>
            </li>
            @if(isset($extraMenuEntries['SHORT_STAY_APARTMENT'.($locale === 'en' ? '' : '_'.strtoupper($locale))]))
            @php
            $shortStayURL = $extraMenuEntries['SHORT_STAY_APARTMENT'.($locale === 'en' ? '' : '_'.strtoupper($locale))]['url'];
            if($locale == 'ar') {
            $shortStayURL = implode('/', array_map('urlencode', explode('/', $extraMenuEntries['SHORT_STAY_APARTMENT_'.strtoupper($locale)]['url'])));
            }
            @endphp
            <li><a href="{{persistQueryParamsToURL($shortStayURL)}}">{{__($extraMenuEntries['SHORT_STAY_APARTMENT'.($locale === 'en' ? '' : '_'.strtoupper($locale))]['title'])}}</a></li>
            @endif
            @php
            $shouldDisplayInternationalLink = false;
            if(
            (isset($countryStatNumbers['77']) && $countryStatNumbers['77'] > 0) ||
            (isset($countryStatNumbers['2']) && $countryStatNumbers['2'] > 0 && $cityStatNumbers['1001'] && $cityStatNumbers['1001'] > 0) ||
            (isset($countryStatNumbers['193']) && $countryStatNumbers['193'] > 0) ||
            (isset($countryStatNumbers['189']) && $countryStatNumbers['189'] > 0) ||
            (isset($countryStatNumbers['127']) && $countryStatNumbers['127'] > 0) ||
            (isset($countryStatNumbers['68']) && $countryStatNumbers['68'] > 0) ||
            (isset($countryStatNumbers['38']) && $countryStatNumbers['38'] > 0) ||
            (isset($countryStatNumbers['75']) && $countryStatNumbers['75'] > 0) ||
            (isset($countryStatNumbers['218']) && $countryStatNumbers['218'] > 0) ||
            (isset($countryStatNumbers['225']) && $countryStatNumbers['225'] > 0) 
            ) {
            $shouldDisplayInternationalLink = true;
            }
            @endphp

            <li class="submenu">
                @if($shouldDisplayInternationalLink)
                <a href="#">{{__('International')}}</a>
                <ul>
                    @if(isset($countryStatNumbers['77']) && $countryStatNumbers['77'] > 0)
                    <li>
                        <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('uk')]))}}">{{__('UK')}}</a>
                    </li>
                    @endif
                    @if(isset($countryStatNumbers['2']) && $countryStatNumbers['2'] > 0 && $cityStatNumbers['1001'] && $cityStatNumbers['1001'] > 0)
                    <li>
                        <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('dubai')]))}}">{{__('Dubai')}}</a>
                    </li>
                    @endif
                    @if(isset($countryStatNumbers['193']) && $countryStatNumbers['193'] > 0)
                    <li>
                        <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('saudi-arabia')]))}}">{{__('Saudi Arabia')}}</a>
                    </li>
                    @endif
                    @if(isset($countryStatNumbers['189']) && $countryStatNumbers['189'] > 0)
                    <li>
                        <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('romania')]))}}">{{__('Romania')}}</a>
                    </li>
                    @endif
                    @if(isset($countryStatNumbers['127']) && $countryStatNumbers['127'] > 0)
                    <li>
                        <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('lebanon')]))}}">{{__('Lebanon')}}</a>
                    </li>
                    @endif
                    @if(isset($countryStatNumbers['172']) && $countryStatNumbers['172'] > 0)
                    <li>
                        <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('oman')]))}}">{{__('Oman')}}</a>
                    </li>
                    @endif
                    @if(isset($countryStatNumbers['68']) && $countryStatNumbers['68'] > 0)
                    <li>
                        <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('spain')]))}}">{{__('Spain')}}</a>
                    </li>
                    @endif
                    @if(isset($countryStatNumbers['38']) && $countryStatNumbers['38'] > 0)
                    @if($cityStatNumbers['1067'] && $cityStatNumbers['1067'] > 0)
                    <li class="submenu submenu--secondTier">
                        <a href="#">{{__('Canada')}}</a>
                        <ul>
                            <li>
                                <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('ontario')]))}}">{{__('Ontario')}}</a>
                            </li>
                        </ul>
                    </li>
                    @endif
                    @endif
                    @if(isset($countryStatNumbers['75']) && $countryStatNumbers['75'] > 0)
                    <li class="submenu submenu--secondTier">
                        <a href="#">{{__('France')}}</a>
                        <ul>
                            <li>
                                <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('paris')]))}}">{{__('Paris')}}</a>
                            </li>
                        </ul>
                    </li>
                    @endif
                    @if(isset($countryStatNumbers['89']) && $countryStatNumbers['89'] > 0)
                    @if($cityStatNumbers['1084'] && $cityStatNumbers['1084'] > 0)
                    <li class="submenu submenu--secondTier">
                        <a href="#">{{__('Greece')}}</a>
                        <ul>
                            <li>
                                <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('kamena-vourla')]))}}">{{__('Kamena Vourla')}}</a>
                            </li>
                        </ul>
                    </li>
                    @endif
                    @endif
                    @if(isset($countryStatNumbers['218']) && $countryStatNumbers['218'] > 0)
                    @if($cityStatNumbers['1088'] && $cityStatNumbers['1088'] > 0)
                    <li class="submenu submenu--secondTier">
                        <a href="#">{{__('Thailand')}}</a>
                        <ul>
                            <li>
                                <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('uluwatu')]))}}">{{__('Uluwatu')}}</a>
                            </li>
                        </ul>
                    </li>
                    @endif
                    @endif
                    @if(isset($countryStatNumbers['225']) && $countryStatNumbers['225'] > 0)
                    @if($cityStatNumbers['1090'] && $cityStatNumbers['1090'] > 0)
                    <li class="submenu submenu--secondTier">
                        <a href="#">{{__('Turkey')}}</a>
                        <ul>
                            <li>
                                <a href="{{persistQueryParamsToURL(route('international', ['locale' => $locale, 'internationalWord' => __('international'), 'section' => __('dibektas-hills')]))}}">{{__('Dibektas Hills')}}</a>
                            </li>
                        </ul>
                    </li>
                    @endif
                    @endif
                </ul>
                @else
                <span>&nbsp;</span>
                @endif
            </li>

            <li class="submenu">
                <a href="#">{{__('Find your agent')}}</a>
                <ul>
                    <li>
                        <a href="{{persistQueryParamsToURL(route('agents.index', ['locale' => 'en']))}}">{{__('Qatar agents')}}</a>
                    </li>
                    <li>
                        <a href="/learn/en">{{__('Learning hub')}}</a>
                    </li>
                </ul>
            </li>

            <li class="mobile-only settings">
                <div class="settings-wrapper">
                    <div class="widget-settings">
                        <div class="widget-settings__currencies">
                            <a href="#" class="dropbtn"> {{ $userCurrency}}
                                <svg class="currencies-icon" xmlns="http://www.w3.org/2000/svg" width="9" height="7" viewBox="0 0 9 7">
                                    <path d="M3.659,1.308a1,1,0,0,1,1.682,0L8.01,5.459A1,1,0,0,1,7.168,7H1.832A1,1,0,0,1,.99,5.459Z" />
                                </svg>
                            </a>
                        </div>
                    </div>

                    <div class="settings__options">
                        <div class="settings__options-language">
                            <a href="/en" class="btn-secondary btn-inverted @if($userLanguage === 'en') btn-secondary--active @endif">
                                <img class="w-3 h-3 lazyload" src="{{asset('images/svg/english.svg')}}" alt="english" loading="lazy">
                                <span>English (US)</span>
                            </a>
                            <a href="/ar" class="btn-secondary btn-inverted @if($userLanguage === 'ar') btn-secondary--active @endif">
                                <img class="w-3 h-3" src="{{asset('images/qa-flag.png')}}" alt="arabic" loading="lazy">
                                <span>Arabic (عربى)</span>
                            </a>
                        </div>
                        <div class="settings__options-currency">
                            <button class="btn-secondary btn-inverted @if($userCurrency === 'qar') btn-secondary--active @endif" data-currency="qar">
                                <img class="w-3 h-3" src="{{asset('images/svg/qar.svg')}}" alt="qar" loading="lazy">
                                <span>QAR (Qatari Riyal)</span>
                            </button>
                            <button class="btn-secondary btn-inverted @if($userCurrency === 'eur') btn-secondary--active @endif" data-currency="eur">
                                <img class="w-3 h-3" src="{{asset('images/svg/eur.svg')}}" alt="eur" loading="lazy">
                                <span>EUR (Euro)</span>
                            </button>
                            <button class="btn-secondary btn-inverted @if($userCurrency === 'usd') btn-secondary--active @endif" data-currency="usd">
                                <img class="w-3 h-3" src="{{asset('images/svg/usd.svg')}}" alt="usd" loading="lazy">
                                <span>USD (United States Dollar)</span>
                            </button>
                            <button class="btn-secondary btn-inverted @if($userCurrency === 'gbp') btn-secondary--active @endif" data-currency="gbp">
                                <img class="w-3 h-3" src="{{asset('images/svg/gbp.svg')}}" alt="gbp" loading="lazy">
                                <span>GBP (Pound Sterling)</span>
                            </button>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <div class="actions-box {{$isProblematicMobile ? 'actions-box--wide' : ''}}">
        <div class="actionCta actionCta--signIn">
            <a href="{{route('login')}}" class="actionCta__a">
                <span class="actionCta__a-contentWrapper">
                    <span class="actionCta__a-contentWrapper-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                            <g transform="translate(-151 -255)">
                                <path d="M48.879,7.419A4.059,4.059,0,0,0,50.627,4.1a4.245,4.245,0,0,0-8.485,0,4.059,4.059,0,0,0,1.748,3.316,6.792,6.792,0,0,0-4.506,6.325A2.3,2.3,0,0,0,41.718,16h9.333a2.3,2.3,0,0,0,2.333-2.256A6.792,6.792,0,0,0,48.879,7.419ZM43.415,4.1a2.971,2.971,0,0,1,5.939,0,2.971,2.971,0,0,1-5.939,0Zm7.636,10.667H41.718a1.045,1.045,0,0,1-1.061-1.026,5.731,5.731,0,0,1,11.455,0A1.045,1.045,0,0,1,51.052,14.769Z" transform="translate(112.615 255)" fill="#1a1a1a"></path>
                                <rect width="16" height="16" transform="translate(151 255)" fill="none"></rect>
                            </g>
                        </svg>
                    </span>
                    <span class="actionCta__a-contentWrapper-text">
                        {{__('Sign In')}}
                    </span>
                </span>
            </a>
        </div>
        <div class="actionCta">
            <a href="{{route('home.localized', ['locale' => $locale == 'ar' ? 'en' : 'ar'])}}" class="actionCta__a" title="{{$locale == 'ar' ? __('English') : __('Arabic')}}">
                <span class="actionCta__a-contentWrapper">
                    <span class="actionCta__a-contentWrapper-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                            <path d="M12,4a8,8,0,1,0,8,8A8.009,8.009,0,0,0,12,4Zm6.4,5.333H15.534A9.8,9.8,0,0,0,14.17,5.419,6.964,6.964,0,0,1,18.4,9.333ZM14.667,12c0,.559-.028,1.092-.077,1.6H9.41c-.049-.508-.077-1.041-.077-1.6s.028-1.092.077-1.6H14.59C14.638,10.908,14.667,11.441,14.667,12ZM12,18.933c-.963,0-2.008-1.663-2.449-4.267h4.9C14.008,17.27,12.963,18.933,12,18.933Zm-2.449-9.6C9.992,6.73,11.037,5.067,12,5.067s2.008,1.663,2.449,4.267ZM9.83,5.419A9.8,9.8,0,0,0,8.466,9.333H5.6A6.964,6.964,0,0,1,9.83,5.419ZM5.26,10.4H8.338c-.045.515-.071,1.048-.071,1.6s.026,1.085.071,1.6H5.26a6.7,6.7,0,0,1,0-3.2ZM5.6,14.667H8.466A9.8,9.8,0,0,0,9.83,18.581,6.964,6.964,0,0,1,5.6,14.667Zm8.568,3.914a9.8,9.8,0,0,0,1.364-3.914H18.4a6.963,6.963,0,0,1-4.229,3.914ZM18.74,13.6H15.662c.045-.515.071-1.048.071-1.6s-.026-1.085-.071-1.6h3.077a6.706,6.706,0,0,1,0,3.2Z" transform="translate(-4 -4)" fill="#1a1a1a"></path>
                        </svg>
                    </span>
                    <span class="actionCta__a-contentWrapper-text">
                        {{ $locale == 'ar' ? __('AR') : __('EN')}}
                    </span>
                </span>
            </a>
        </div>
    </div>
</nav>

<script>
    window.addEventListener('load', () => {
        const navCurrenciesCtrl = document.querySelector('nav .widget-settings__currencies');
        const navCurrenciesWidget = document.querySelector('.settings__options-currency')

        const showCurrenciesWidget = () => {
            const btn = navCurrenciesCtrl.querySelector('a.dropbtn');
            btn.classList.add('active');
            navCurrenciesWidget.classList.add('settings__options-currency--visible');
        }

        const hideCurrenciesWidget = () => {
            const btn = navCurrenciesCtrl.querySelector('a.dropbtn');
            btn.classList.remove('active');
            navCurrenciesWidget.classList.remove('settings__options-currency--visible');
        }

        navCurrenciesCtrl.addEventListener('click', () => {
            const widgetVisible = navCurrenciesCtrl.querySelector('a.dropbtn').classList.contains('active');
            if (!widgetVisible) {
                showCurrenciesWidget();
            } else {
                hideCurrenciesWidget();
            }
        })
    })

    const openSellModal = () => {
        return document.querySelector('list-with-us button').click();
    }
</script>