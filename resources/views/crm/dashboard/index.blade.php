@extends('crm.layout')
@php
if(auth()->user()->email == '<EMAIL>') {
    $isSerban = true;
} else {
    $isSerban = false;
}
if(auth()->user()->email == '<EMAIL>') {
    $isItDev = true;
} else {
    $isItDev = false;
}
@endphp

@section('title')
    FG Realty CRM
@endsection

@section('head-css')
    <link rel="stylesheet" href="{{asset('/css/crm-virtual-select.min.css')}}">
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css' rel='stylesheet'>
@endsection

@section('content')
<script>
        const isSerbanConnected = JSON.parse('{!! json_encode($isSerban) !!}');
        const isITDevConnected = JSON.parse("{!! json_encode($isItDev) !!}");
        const agents = JSON.parse('{!! addslashes(json_encode($agents->map(function($agent){ return $agent->only("id", "name", "profile_image", "is_active_for_competition"); })->values()->toArray())) !!}')
</script>
    <div class="px-md-4">
        @if(session()->has('checkin-succesfully'))
        <div class="alert alert-success mt-4">You have been succesfully checked in!</div>
        @endif
        @role(App\Models\Crm\RolesDef::SHORT_STAY_USER)
        <x-short-stay-user-dashboard-metrics></x-short-stay-user-dashboard-metrics>
        @endrole
        @can(\App\Models\Crm\PermissionsDef::SECTION_DASHBOARD_KPI)
            <div class="my-5 py-2">
                <kpi-performance></kpi-performance>
            </div>
        @endcan
        @can(\App\Models\Crm\PermissionsDef::SECTION_DASHBOARD_CALENDAR)
            <div
                class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Upcoming events</h1>
                <div class="btn-toolbar mb-2 mb-md-0 gap-1">
                    @can(\App\Models\Crm\PermissionsDef::GLOBAL_REPORTS_ACCESS)
                        <a href="{{ route('metrics.dashboard') }}" class="btn btn-sm btn-outline-secondary"> <i
                                class="bi bi-bar-chart"></i> Metrics dashboard</a>
                    @endcan
                    @can(\App\Models\Crm\PermissionsDef::GLOBAL_REPORTS_ACCESS)
                        {{--<a href="{{ route('admin.reports.new.index') }}" class="btn btn-sm btn-outline-secondary"> <i
                                class="bi bi-bar-chart"></i> Reports</a>--}}
                    @endcan
                    <button type="button" onclick="triggerAddReminderModal('no-asset-id', 'dashboard')"
                        class="btn btn-sm btn-outline-secondary" href="#addNoteModal">
                        <i class="bi bi-bell"></i>
                        Reminders</button>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div id="calendar"></div>
                </div>
            </div>
        @endcan
        @if(auth()->user()->can(\App\Models\Crm\PermissionsDef::CAN_VIEW_CONTEST) || auth()->user()->can(\App\Models\Crm\PermissionsDef::CAN_PREVIEW_FEATURES) || auth()->user()->can(\App\Models\Crm\PermissionsDef::EXTRA_USER_VIEW_CONTEST)) 
            <div class="mt-4">
                <h3>Agent Competition</h3>
                <agent-competition-container></agent-competition-container>
            </div>
        @endif
        @can(\App\Models\Crm\PermissionsDef::SECTION_DASHBOARD_PERFORMANCE)
            <div class="row mb-4">
                <div class="mb-3 border-bottom">
                    <h1 class="h2 mt-4">Performance</h1>
                </div>
                <div>
                    @php
                        $docs = auth()->user()->performanceDocuments;
                    @endphp
                    @if ($docs->count())
                        <ol>
                            @foreach ($docs as $d)
                                <li>
                                    <a
                                        href="{{ route('dashboard.performance.documents.download', ['id' => $d->id]) }}">{{ $d->file_name }}</a>
                                </li>
                            @endforeach
                        </ol>
                    @else
                        No documents
                    @endif
                </div>
            </div>
        @endcan
    </div>

    <!-- Modal -->
    <div class="modal fade" id="viewCalendarReminderModal" tabindex="-1" aria-labelledby="viewCalendarReminderModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewCalendarReminderModalLabel"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <dl>
                        <dt>Reminder</dt>
                        <dd id="reminderText"></dd>
                        <dt>Added By</dt>
                        <dd id="reminderAddedBy"></dd>
                        <dt>Due date</dt>
                        <dd id="reminderDueDate"></dd>
                        <dt>Priority</dt>
                        <dd id="reminderPriority"></dd>
                        <dt>Reminder Email</dt>
                        <dd id="reminderEmail"></dd>
                        <dt>Item</dt>
                        <dd id="reminderItemLink">-</dd>
                    </dl>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    @include('crm.components.notes-modal')
@endsection

@section('body-js')
    <script type="text/javascript" src="/js/virtual-select.min.js"></script>
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js'></script>
    <script src="https://cdn.jsdelivr.net/npm/@fullcalendar/bootstrap5@6.1.8/index.global.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
    <script src="{{ mix('js/kpi-performance.js') }}"></script>
    <script src="{{ mix('js/agent-competition-container.js') }}"></script>

    <!-- <script src="{{ mix('js/dashboard-stats.js') }}"></script> -->
    <script>
        const queryString = window.location.search;
        const urlParams = new URLSearchParams(queryString);
        const idReminderFromUrl = urlParams.get('fromEmail');

        document.addEventListener('DOMContentLoaded', function() {
            var calendarEl = document.getElementById('calendar');
            if (!!calendarEl) {


                var calendar = new FullCalendar.Calendar(calendarEl, {
                    timeZone: 'UTC',
                    themeSystem: 'bootstrap5',
                    initialView: 'dayGridMonth',
                    header: {
                        left: 'prev,next today',
                        center: 'title',
                        right: 'month,agendaWeek,agendaDay'
                    },
                    height: 600,
                    aspectRatio: 1,
                    businessHours: true,
                    editable: false,
                    displayEventTime: false,
                    events: {
                        url: "/crm/dashboard/events",
                    },
                    dayMaxEvents: 3,
                    eventClick: function(calEvent, jsEvent, view) {
                        $('#viewCalendarReminderModalLabel').text(calEvent.event.title);
                        if (!!calEvent.event.extendedProps) {
                            const {
                                priority,
                                object_id,
                                object_type,
                                text,
                                send_email,
                                send_email_date,
                                user_name,
                                user_profile_image_url,
                                due_date,
                                object_url
                            } = calEvent.event.extendedProps;
                            $('#reminderText').text(text);
                            const userInfoContainer =
                                `<div style="display: flex; align-items: flex-start; gap: 10px; justify-content: flex-start;"><img class="border rounded" style="width: 50px" src="${user_profile_image_url}"></img>${user_name}</div>`
                            $('#reminderAddedBy').html(userInfoContainer);
                            $('#reminderDueDate').text(due_date);
                            $('#reminderPriority').text(priority);
                            const reminderEmailStr =
                                `${!!send_email ? 'Yes' + ' / ' + send_email_date : 'No'}`
                            $('#reminderEmail').text(reminderEmailStr);
                            const reminderItemLink = !!object_url ?
                                `<a class="link" href="${object_url}">Visit</a>` : '-';
                            $('#reminderItemLink').html(reminderItemLink);

                            $('#viewCalendarReminderModal').modal('toggle')
                        }
                    },

                    viewDidMount: function(view) {

                        if (urlParams.has('fromEmail')) {
                            request(`/crm/inventory/notes-by-id/${idReminderFromUrl}`, {
                                method: "GET",
                                headers: {
                                    'Content-Type': 'application/json',
                                    'Accept': 'application/json'
                                }
                            }).then(data => {
                                // var currentDate = new Date();
                                var targetDate = new Date(data.due_date);
                                targetDate.toDateString()
                                calendar.gotoDate(targetDate);
                                const {
                                    priority,
                                    object_id,
                                    object_type,
                                    text,
                                    send_email,
                                    send_email_date,
                                    user_name,
                                    user_profile_image_url,
                                    due_date,
                                    object_url
                                } = data;
                                $('#reminderText').text(text);
                                const userInfoContainer =
                                    `<div style="display: flex; align-items: flex-start; gap: 10px; justify-content: flex-start;"><img class="border rounded" style="width: 50px" src="${user_profile_image_url}"></img>${user_name}</div>`
                                $('#reminderAddedBy').html(userInfoContainer);
                                $('#reminderDueDate').text(due_date);
                                $('#reminderPriority').text(priority);
                                const reminderEmailStr =
                                    `${!!send_email ? 'Yes' + ' / ' + send_email_date : 'No'}`
                                $('#reminderEmail').text(reminderEmailStr);
                                const reminderItemLink = !!object_url ?
                                    `<a class="link" href="${object_url}">Visit</a>` : '-';
                                $('#reminderItemLink').html(reminderItemLink);
                                $('#viewCalendarReminderModal').modal('toggle')
                            })
                        }
                    }
                });

                calendar.render();
            }
        });
    </script>
@endsection

@section('footer')
@endsection
