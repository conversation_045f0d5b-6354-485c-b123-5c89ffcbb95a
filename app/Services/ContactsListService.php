<?php

namespace App\Services;

use DB;
use App\Models\Crm\ContactsList;
use App\Models\Crm\RolesDef;
use App\Models\Lead;
use Illuminate\Http\Request;

class ContactsListService extends GenericService
{
    protected function getTableItemsBaseQ(Request $request, $extraClauses = [], $groupByClause = null)
    {
        $user = auth()->user();
        $userTagIdsAccess = $user->contactListTags->map(function ($item) {
            return $item->id;
        })->toArray();
        $isOfficeManager = $user->hasRole(RolesDef::OFFICE_MANAGER);
        $isCallCenterAgent = $user->hasRole(RolesDef::CALL_CENTER_AGENT);
        $isTeamLeader = $user->hasRole(RolesDef::TEAM_LEADER);
        // $userCanManageDubaiInvestors = $user->hasPermissionTo(PermissionsDef::CAN_MANAGE_DUBAI_INVESTORS);
        // $userCanManagePearlOwners = $user->hasPermissionTo(PermissionsDef::CAN_MANAGE_PEARL_OWNERS);
        $params = $request->all();

        $contactDuplicationType = $request->get('duplicationType', 'master');

        $qb = DB::table('contacts as c')
            ->select([
                'c.id',
                'c.name',
                'c.gender',
                'c.nationality_id',
                'c.date_of_birth',
                'c.qatar_id_no',
                'c.occupation',
                'c.company_name',
                'c.position',
                'c.residency',
                'c.email_1',
                'c.prefix_mobile_1',
                'c.mobile_1',
                'c.mobile_2',
                'c.verified',
                'r.id as reminders',
                DB::raw("DATE_FORMAT(c.created_at, '%Y-%m-%d') as created_at"),
                'u.name as agent_name',
                DB::raw('GROUP_CONCAT(DISTINCT ct.id) as tags'),
                DB::raw('GROUP_CONCAT(DISTINCT ct.label SEPARATOR "||") as tag_labels'),
                'cl.id as landlord_id',
                DB::raw('GROUP_CONCAT(l.id) as leads_ids'),
                DB::raw('GROUP_CONCAT(d.id) as deals_ids'),
                'ctasks.call_response as last_call_response',
                'ctasks.call_notes as last_call_notes',
                'ctasks.created_at as last_call_timing',
            ])
            ->leftJoin('nationalities as n', 'c.nationality_id', '=', 'n.id')
            ->leftJoin('contacts_list_x_tags as cxt', 'cxt.contacts_list_id', '=', 'c.id')
            ->leftJoin('contacts_list_tags as ct', 'ct.id', '=', 'cxt.contacts_list_tag_id')
            ->leftJoin('contacts_landlords as cl', 'cl.contact_id', '=', 'c.id')
            ->leftJoin('leads as l', 'l.contact_id', '=', 'c.id')
            ->leftJoin('deals as d', 'd.lead_id', '=', 'l.id')
            ->leftJoin('contacts_agents_connection as cac', 'cac.contact_id', '=', 'c.id')
            ->leftJoin('users as u', 'u.id', '=', 'c.created_by')
            ->leftJoin('reminders as r', function ($qb) {
                $qb->on('c.id', '=', 'r.object_id')
                    ->where('r.object_type', '=', 'contacts_list');
            })
            ->leftJoin(DB::raw("(SELECT MAX(created_at) as max_created_at, object_id, call_response FROM tasks WHERE object_type LIKE 'contact' GROUP BY object_id) as tmd"), 'tmd.object_id', '=', 'c.id')
            ->leftJoin('tasks as ctasks', function ($qb) {
                $qb->on('ctasks.created_at', '=', 'tmd.max_created_at')->on('ctasks.object_id', '=', 'c.id')
                    ->where('ctasks.object_type', '=', 'contact');
            })
            ->offset(0)
            ->orderBy('c.updated_at', 'DESC')
            ->groupBy('c.id')
            ->limit($params['limit'] ?? 20);

        $term = '';
        if ($request->has('q')) {
            $term = strtolower(trim($request->get('q'))) ?? '';
        }

        $exportType = $request->get('exportType');
        if ($exportType == 'whatsapp') {
            $qb->whereRaw(DB::raw('((c.prefix_mobile_1 IS NULL AND c.mobile_1 LIKE "+%") OR (c.prefix_mobile_1 IS NOT NULL AND c.mobile_1 NOT LIKE "+%"))'));
            // Exclude phone numbers that are in failed_messages (using getCompletePhoneNo logic)
            // $qb->whereRaw("
            //     CASE
            //         WHEN c.prefix_mobile_1 IS NOT NULL AND c.mobile_1 NOT LIKE '+%' AND c.mobile_1 NOT LIKE '00%' THEN
            //             CONCAT(c.prefix_mobile_1, REPLACE(REPLACE(REPLACE(c.mobile_1, '(', ''), ')', ''), ' ', ''))
            //         WHEN c.prefix_mobile_1 IS NULL AND c.mobile_1 NOT LIKE '+%' AND c.mobile_1 NOT LIKE '00%' AND c.mobile_1 NOT LIKE '974%' THEN
            //             CONCAT('+974', REPLACE(REPLACE(REPLACE(c.mobile_1, '(', ''), ')', ''), ' ', ''))
            //         ELSE
            //             REPLACE(REPLACE(REPLACE(c.mobile_1, '(', ''), ')', ''), ' ', '')
            //     END NOT IN (SELECT phone_number FROM failed_messages)
            // ");
        }
        if ($exportType == 'whatsapp_qatar') {
            // Filter for Qatari numbers based on the specified logic
            // $qb->whereRaw(DB::raw('((c.prefix_mobile_1 IS NULL AND (c.mobile_1 LIKE "974%" OR c.mobile_1 LIKE "974%")) OR (c.prefix_mobile_1 IS NOT NULL AND c.mobile_1 NOT LIKE "+%"))'));
            $qb->where(function($query) {
                $query->where(function($q) {
                    // Case 1: prefix_mobile_1 is +974
                    $q->where('c.prefix_mobile_1', '+974')
                      ->where(function($subQ) {
                          $subQ->where(function($innerQ) {
                              // mobile_1 starts with 974 AND length is 11
                              $innerQ->whereRaw('c.mobile_1 LIKE "974%"')
                                     ->whereRaw('LENGTH(c.mobile_1) = 11');
                          })->orWhere(function($innerQ) {
                              // mobile_1 NOT start with 974 AND length is 8
                              $innerQ->whereRaw('c.mobile_1 NOT LIKE "974%"')
                                     ->whereRaw('LENGTH(c.mobile_1) = 8');
                          });
                      });
                })->orWhere(function($q) {
                    // Case 2: prefix_mobile_1 is null
                    $q->whereNull('c.prefix_mobile_1')
                      ->where(function($subQ) {
                          $subQ->where(function($innerQ) {
                              // mobile_1 starts with 974 AND length is 11
                              $innerQ->whereRaw('c.mobile_1 LIKE "974%"')
                                     ->whereRaw('LENGTH(c.mobile_1) = 11');
                          })->orWhere(function($innerQ) {
                              // mobile_1 NOT start with 974 AND length is 8
                              $innerQ->whereRaw('c.mobile_1 NOT LIKE "974%"')
                                     ->whereRaw('LENGTH(c.mobile_1) = 8');
                          });
                      });
                });
            });
            // Exclude phone numbers that are in failed_messages (ensure all numbers start with +974)
            $qb->whereRaw("
                CASE
                    WHEN c.prefix_mobile_1 = '+974' AND c.mobile_1 LIKE '974%' THEN
                        c.mobile_1
                    WHEN c.prefix_mobile_1 = '+974' AND c.mobile_1 NOT LIKE '974%' THEN
                        CONCAT('974', c.mobile_1)
                    WHEN c.prefix_mobile_1 IS NULL AND c.mobile_1 LIKE '974%' THEN
                        c.mobile_1
                    WHEN c.prefix_mobile_1 IS NULL AND c.mobile_1 NOT LIKE '974%' THEN
                        CONCAT('974', c.mobile_1)
                END NOT IN (SELECT phone_number FROM failed_messages)
            ");
        }
        if ($exportType == 'marketing_platform') {
            $qb->whereNotNull('c.email_1')->where('c.email_1', '!=', '');
            // $qb->whereRaw(DB::raw('(c.email_1 IS NOT NULL OR c.email_2 IS NOT NULL)'));
        }

        if (!empty($term)) {
            $percentTerm = "%" . $term . "%";
            $qb->where(function ($sql) use ($term, $percentTerm) {
                $sql->where('c.id', '=', $term)
                    ->orWhere('c.name', 'LIKE', $percentTerm)
                    ->orWhere('c.company_name', 'LIKE', $percentTerm)
                    ->orWhere('c.position', 'LIKE', $percentTerm)
                    ->orWhere('c.occupation', 'LIKE', $percentTerm)
                    ->orWhere('c.residency', 'LIKE', $percentTerm)
                    ->orWhere('c.qatar_id_no', 'LIKE', $percentTerm)
                    ->orWhere('c.mobile_1', 'LIKE', $percentTerm)
                    ->orWhere('c.mobile_2', 'LIKE', $percentTerm)
                    ->orWhere('c.email_1', 'LIKE', $percentTerm)
                    ->orWhere('n.name', 'LIKE', $percentTerm);
            });
        }
        if ($contactDuplicationType == 'duplicate') {
            $qb->whereNotNull('master_contact_id');
        } else {
            $qb->whereNull('master_contact_id');
        }
        if (isset($params['tags']) && !empty(trim($params['tags']))) {
            $tags = explode(',', $params['tags']);
            if (count($tags) > 0) {
                foreach ($tags as $tagIndex => $tagId) {
                    $qb->join('contacts_list_x_tags as cxt' . $tagIndex, 'cxt' . $tagIndex . '.contacts_list_id', '=', 'c.id');
                    $qb->where('cxt' . $tagIndex . '.contacts_list_tag_id', $tagId);
                }
                // $qb->whereIn('cxt.contacts_list_tag_id', $tags);
            }
        }
        if (isset($params['master_contact_id'])) {
            $qb->where('c.master_contact_id', $params['master_contact_id']);
        }
        if (isset($params['verified']) && $params['verified'] == 1) {
            $qb->where('c.verified', 1);
        }
        if (isset($params['isLandlord']) && $params['isLandlord'] == 1) {
            $qb->whereNotNull('cl.id');
        }
        if (isset($params['hasLeads']) && $params['hasLeads'] == 1) {
            $qb->whereNotNull('l.id');
        }
        if (isset($params['callLog'])) {
            $qb->where('ctasks.call_response', $params['callLog']);
        }

        // New filter parameters - join with leads table when needed
        $needsLeadsJoin = false;
        if (isset($params['operationType']) && !empty($params['operationType'])) {
            $needsLeadsJoin = true;
        }
        if (isset($params['propertyTypes']) && !empty($params['propertyTypes'])) {
            $needsLeadsJoin = true;
        }
        if (isset($params['budgetFrom']) || isset($params['budgetTo'])) {
            $needsLeadsJoin = true;
        }

        // Add additional leads join if needed for filtering
        if ($needsLeadsJoin) {
            // We already have a left join with leads, but we need to ensure we filter properly
            // When filtering by lead properties, we only want contacts that have leads
            $qb->whereNotNull('l.id');

            if (isset($params['operationType']) && !empty($params['operationType'])) {
                $qb->where('l.filter_operation_type', $params['operationType']);
            }

            if (isset($params['propertyTypes']) && !empty($params['propertyTypes'])) {
                $propertyTypes = explode(',', $params['propertyTypes']);
                $qb->whereIn('l.filter_property_type', $propertyTypes);
            }

            if (isset($params['budgetFrom']) && is_numeric($params['budgetFrom'])) {
                $qb->where(function($query) use ($params) {
                    $query->where('l.filter_budget_min', '>=', $params['budgetFrom'])
                          ->orWhere('l.filter_budget_max', '>=', $params['budgetFrom'])
                          ->orWhere('l.budget', '>=', $params['budgetFrom']);
                });
            }

            if (isset($params['budgetTo']) && is_numeric($params['budgetTo'])) {
                $qb->where(function($query) use ($params) {
                    $query->where('l.filter_budget_min', '<=', $params['budgetTo'])
                          ->orWhere('l.filter_budget_max', '<=', $params['budgetTo'])
                          ->orWhere('l.budget', '<=', $params['budgetTo']);
                });
            }
        }

        // Date created filters
        if (isset($params['dateCreatedFrom']) && !empty($params['dateCreatedFrom'])) {
            $qb->whereDate('c.created_at', '>=', $params['dateCreatedFrom']);
        }

        if (isset($params['dateCreatedTo']) && !empty($params['dateCreatedTo'])) {
            $qb->whereDate('c.created_at', '<=', $params['dateCreatedTo']);
        }

        // Additional new filter parameters
        if (isset($params['name']) && !empty($params['name'])) {
            $nameFilter = '%' . strtolower(trim($params['name'])) . '%';
            $qb->where('c.name', 'LIKE', $nameFilter);
        }

        if (isset($params['ageRange']) && !empty($params['ageRange'])) {
            $ageRange = $params['ageRange'];
            $currentDate = now();

            switch ($ageRange) {
                case '20-40':
                    $qb->whereRaw('TIMESTAMPDIFF(YEAR, c.date_of_birth, ?) BETWEEN 20 AND 40', [$currentDate]);
                    break;
                case '40-60':
                    $qb->whereRaw('TIMESTAMPDIFF(YEAR, c.date_of_birth, ?) BETWEEN 40 AND 60', [$currentDate]);
                    break;
                case '60+':
                    $qb->whereRaw('TIMESTAMPDIFF(YEAR, c.date_of_birth, ?) >= 60', [$currentDate]);
                    break;
            }
        }

        if (isset($params['nationalities']) && !empty($params['nationalities'])) {
            $nationalities = explode(',', $params['nationalities']);
            $qb->whereIn('c.nationality_id', $nationalities);
        }

        if (isset($params['budgetRange']) && !empty($params['budgetRange'])) {
            // For budget range, we need to join with leads table to check lead budgets
            $needsLeadsJoin = true;
            $qb->whereNotNull('l.id');

            switch ($params['budgetRange']) {
                case 'under-500k':
                    $qb->where(function($query) {
                        $query->where('l.budget', '<', 500000)
                              ->orWhere('l.filter_budget_max', '<', 500000);
                    });
                    break;
                case '500k-1m':
                    $qb->where(function($query) {
                        $query->whereBetween('l.budget', [500000, 1000000])
                              ->orWhere(function($q) {
                                  $q->where('l.filter_budget_min', '>=', 500000)
                                    ->where('l.filter_budget_max', '<=', 1000000);
                              });
                    });
                    break;
                case '1m-2m':
                    $qb->where(function($query) {
                        $query->whereBetween('l.budget', [1000000, 2000000])
                              ->orWhere(function($q) {
                                  $q->where('l.filter_budget_min', '>=', 1000000)
                                    ->where('l.filter_budget_max', '<=', 2000000);
                              });
                    });
                    break;
                case '2m-5m':
                    $qb->where(function($query) {
                        $query->whereBetween('l.budget', [2000000, 5000000])
                              ->orWhere(function($q) {
                                  $q->where('l.filter_budget_min', '>=', 2000000)
                                    ->where('l.filter_budget_max', '<=', 5000000);
                              });
                    });
                    break;
                case '5m-10m':
                    $qb->where(function($query) {
                        $query->whereBetween('l.budget', [5000000, 10000000])
                              ->orWhere(function($q) {
                                  $q->where('l.filter_budget_min', '>=', 5000000)
                                    ->where('l.filter_budget_max', '<=', 10000000);
                              });
                    });
                    break;
                case '10m-20m':
                    $qb->where(function($query) {
                        $query->whereBetween('l.budget', [10000000, 20000000])
                              ->orWhere(function($q) {
                                  $q->where('l.filter_budget_min', '>=', 10000000)
                                    ->where('l.filter_budget_max', '<=', 20000000);
                              });
                    });
                    break;
                case '20m-30m':
                    $qb->where(function($query) {
                        $query->whereBetween('l.budget', [20000000, 30000000])
                              ->orWhere(function($q) {
                                  $q->where('l.filter_budget_min', '>=', 20000000)
                                    ->where('l.filter_budget_max', '<=', 30000000);
                              });
                    });
                    break;
                case '30m-50m':
                    $qb->where(function($query) {
                        $query->whereBetween('l.budget', [30000000, 50000000])
                              ->orWhere(function($q) {
                                  $q->where('l.filter_budget_min', '>=', 30000000)
                                    ->where('l.filter_budget_max', '<=', 50000000);
                              });
                    });
                    break;
                case '50m-75m':
                    $qb->where(function($query) {
                        $query->whereBetween('l.budget', [50000000, 75000000])
                              ->orWhere(function($q) {
                                  $q->where('l.filter_budget_min', '>=', 50000000)
                                    ->where('l.filter_budget_max', '<=', 75000000);
                              });
                    });
                    break;
                case '75m-100m':
                    $qb->where(function($query) {
                        $query->whereBetween('l.budget', [75000000, 100000000])
                              ->orWhere(function($q) {
                                  $q->where('l.filter_budget_min', '>=', 75000000)
                                    ->where('l.filter_budget_max', '<=', 100000000);
                              });
                    });
                    break;
                case '100m-150m':
                    $qb->where(function($query) {
                        $query->whereBetween('l.budget', [100000000, 150000000])
                              ->orWhere(function($q) {
                                  $q->where('l.filter_budget_min', '>=', 100000000)
                                    ->where('l.filter_budget_max', '<=', 150000000);
                              });
                    });
                    break;
                case '150m+':
                    $qb->where(function($query) {
                        $query->where('l.budget', '>=', 150000000)
                              ->orWhere('l.filter_budget_min', '>=', 150000000);
                    });
                    break;
            }
        }

        if (isset($params['interestedProjects']) && !empty($params['interestedProjects'])) {
            // TODO: Implement proper project interest filtering
            // This requires clarification on how leads are associated with developments/projects
            // For now, we'll filter by location_id as a proxy for development interest
            $projects = explode(',', $params['interestedProjects']);
            $qb->whereNotNull('l.id');
            $qb->whereIn('l.location_id', $projects);
        }

        if (isset($params['offplan']) && $params['offplan'] == 1) {
            // Filter by ad_type or property_type to identify offplan properties
            // This might need adjustment based on your actual business logic
            $qb->whereNotNull('l.id');
            $qb->where(function($query) {
                $query->where('l.ad_type', 'LIKE', '%offplan%')
                      ->orWhere('l.ad_type', 'LIKE', '%off-plan%')
                      ->orWhere('l.ad_type', 'LIKE', '%off plan%');
            });
        }

        if (isset($params['leadStatuses']) && !empty($params['leadStatuses'])) {
            $leadStatuses = explode(',', $params['leadStatuses']);
            $needsLeadsJoin = true;
            $qb->whereNotNull('l.id');
            $qb->whereIn('l.lead_status_id', $leadStatuses);
        }

        if (isset($params['interest']) && !empty($params['interest'])) {
            // Filter by request_action or ad_type to identify investment vs living interest
            // This might need adjustment based on your actual business logic
            $qb->whereNotNull('l.id');

            switch ($params['interest']) {
                case 'investors':
                    $qb->where(function($query) {
                        $query->where('l.request_action', 'LIKE', '%invest%')
                              ->orWhere('l.ad_type', 'LIKE', '%invest%')
                              ->orWhere('l.requirements', 'LIKE', '%invest%');
                    });
                    break;
                case 'living':
                    $qb->where(function($query) {
                        $query->where('l.request_action', 'LIKE', '%rent%')
                              ->orWhere('l.request_action', 'LIKE', '%live%')
                              ->orWhere('l.ad_type', 'LIKE', '%rent%')
                              ->orWhere('l.requirements', 'LIKE', '%live%');
                    });
                    break;
            }
        }

        if (isset($params['internationalBuyers']) && $params['internationalBuyers'] == 1) {
            // International buyers are those with non-Qatari nationality
            $qatarNationalityId = 151; // Qatar nationality ID based on import mapping
            $qb->where(function($query) use ($qatarNationalityId) {
                $query->where('c.nationality_id', '!=', $qatarNationalityId)
                      ->orWhereNull('c.nationality_id');
            });
        }

        if (isset($params['hasPhone']) && $params['hasPhone'] == 1) {
            $qb->where(function($query) {
                $query->whereNotNull('c.mobile_1')
                      ->where('c.mobile_1', '!=', '')
                      ->orWhere(function($q) {
                          $q->whereNotNull('c.mobile_2')
                            ->where('c.mobile_2', '!=', '');
                      });
            });
        }

        if (isset($params['hasEmail']) && $params['hasEmail'] == 1) {
            $qb->whereNotNull('c.email_1')
               ->where('c.email_1', '!=', '');
        }

        if (!isset($params['master_contact_id'])) {
            if ((!$isOfficeManager && !$isCallCenterAgent) || !request()->has('vt') || (request()->has('vt') && request()->get('vt') != 'master')) {
                if ($isTeamLeader) {
                    $qb->where(function ($sqlqb) {
                        $sqlqb->where('c.created_by', auth()->user()->id)
                            ->orWhere('u.team_leader_id', auth()->user()->id)
                            ->orWhere(function ($sqlqb1) {
                                $sqlqb1->orWhereNotNull('cac.id')->where('cac.user_id', auth()->user()->id);
                            });
                    });
                } else {
                    $qb->where(function ($sqlqb) use ($user, $userTagIdsAccess) {
                        $sqlqb->where('c.created_by', $user->id)
                            ->orWhere(function ($sqlqb1) {
                                $sqlqb1->orWhereNotNull('cac.id')->where('cac.user_id', auth()->user()->id);
                            });
                        if (count($userTagIdsAccess)) {
                            $sqlqb->orWhereIn('ct.id', $userTagIdsAccess);
                        }
                        // if($userCanManageDubaiInvestors) {
                        //     $sqlqb->orWhere('ct.label', 'Dubai Investors');
                        // }
                        // if($userCanManagePearlOwners) {
                        //     $sqlqb->orWhere('ct.label', 'Pearl Owner');
                        // }
                    });
                }
                // else {
                //     $qb->where(function ($sqlqb) {
                //         $sqlqb->where('c.created_by', auth()->user()->id)
                //             ->orWhere(function ($sqlqb1) {
                //                 $sqlqb1->orWhereNotNull('cac.id')->where('cac.user_id', auth()->user()->id);
                //             });
                //     });
                // }
            }
        }

        if (isset($extraClauses['id'])) {
            $qb->where('c.id', $extraClauses['id']);
        }
        // dd($qb->toSql());
        // die;
        return $qb;
    }

    public function toggleStar($validFields)
    {
        $contact = ContactsList::where('id', $validFields['contact_id'])->first();
        if (!is_null($contact)) {
            $contact->verified = $validFields['verified'] == false ? false : true;
            $contact->save();
            return $contact->toArray();
        }
        return $contact;
    }

    protected function mapRecords($records)
    {
        return $records;
    }

    public function getSingleContact($id, $leadId)
    {
        $user = auth()->user();
        $userTagIdsAccess = $user->contactListTags->map(function ($item) {
            return $item->id;
        })->toArray();
        $isAgent = $user->hasRole(RolesDef::AGENT);
        $isOfficeManager = $user->hasRole(RolesDef::OFFICE_MANAGER);
        $isCallCenterAgent = $user->hasRole(RolesDef::CALL_CENTER_AGENT);
        // $userCanManageDubaiInvestors = $user->hasPermissionTo(PermissionsDef::CAN_MANAGE_DUBAI_INVESTORS);
        // $userCanManagePearlOwners = $user->hasPermissionTo(PermissionsDef::CAN_MANAGE_PEARL_OWNERS);

        $isLeadAgent = false;

        if (!$isOfficeManager && !$isCallCenterAgent && $isAgent) {
            // check if the agent is assigned to one of the leads of the contact
            $leads = Lead::with('latestAssignment')->where('contact_id', $id)->get();
            foreach ($leads as $l) {
                if (!is_null($l->latestAssignment) && $l->latestAssignment->user_id == $user->id) {
                    $isLeadAgent = true;
                } else if ($l->id == $leadId) {
                    $isLeadAgent = true;
                }
            }
        }

        $qb = DB::table('contacts as c')
            ->select([
                'c.id',
                'c.name',
                'c.gender',
                'c.nationality_id',
                'c.date_of_birth',
                'c.qatar_id_no',
                'c.occupation',
                'c.company_name',
                'c.position',
                'c.residency',
                'c.email_1',
                'c.prefix_mobile_1',
                'c.mobile_1',
                'c.prefix_mobile_2',
                'c.mobile_2',
                'c.verified',
                'c.metadata',
                DB::raw('GROUP_CONCAT(DISTINCT ct.id) as tags'),
                DB::raw('GROUP_CONCAT(DISTINCT ct.label SEPARATOR "||") as tag_labels'),
                'cl.id as landlord_id',
                DB::raw('GROUP_CONCAT(l.id) as leads_ids'),
                DB::raw('GROUP_CONCAT(d.id) as deals_ids')
            ])
            ->leftJoin('nationalities as n', 'c.nationality_id', '=', 'n.id')
            ->leftJoin('contacts_list_x_tags as cxt', 'cxt.contacts_list_id', '=', 'c.id')
            ->leftJoin('contacts_list_tags as ct', 'ct.id', '=', 'cxt.contacts_list_tag_id')
            ->leftJoin('contacts_landlords as cl', 'cl.contact_id', '=', 'c.id')
            ->leftJoin('leads as l', 'l.contact_id', '=', 'c.id')
            ->leftJoin('deals as d', 'd.lead_id', '=', 'l.id')
            ->leftJoin('contacts_agents_connection as cac', 'cac.contact_id', '=', 'c.id')
            ->offset(0)
            ->groupBy('c.id')
            ->where('c.id', $id);

        if (!$isOfficeManager && !$isCallCenterAgent && !$isLeadAgent) {
            $qb->where(function ($iqb) use ($user, $userTagIdsAccess) {
                $iqb->where(function ($iiqb) use ($user) {
                    $iiqb->where('c.created_by', $user->id)
                        ->orWhere('cac.user_id', $user->id);
                });
                if (count($userTagIdsAccess)) {
                    $iqb->orWhereIn('ct.id', $userTagIdsAccess);
                }
                // if ($userCanManageDubaiInvestors) {
                //     $iqb->orWhere('ct.label', 'Dubai Investors');
                // }
                // if ($userCanManagePearlOwners) {
                //     $iqb->orWhere('ct.label', 'Pearl Owner');
                // }
            });
        }
        // else {
        //     if (!$isLeadAgent) {
        //         if ($userCanManageDubaiInvestors || $userCanManagePearlOwners) {
        //             if (!$userCanManagePearlOwners) {
        //                 $qb->where('ct.label', 'Dubai Investors');
        //             } else if (!$userCanManageDubaiInvestors) {
        //                 $qb->where('ct.label', 'Pearl Owner');
        //             } else {
        //                 $qb->where(function ($sqlq) {
        //                     $sqlq->where('ct.label', 'Dubai Investors')
        //                         ->orWhere('ct.label', 'Pearl Owner');
        //                 });
        //             }
        //         }
        //     }
        // }


        return $qb->first();
    }
}
