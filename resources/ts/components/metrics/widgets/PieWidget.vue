<template>
  <article class="pieWidget">
    <section class="pieWidget__content">
      <div ref="dom" class="pieCard"></div>
    </section>
  </article>
</template>

<script setup lang="ts">
import echarts from "../index";
import { ECharts, EChartsCoreOption } from "echarts/core";
import { ref, defineProps, onMounted, watch, computed, onBeforeUnmount } from "vue";
declare const request: any;

const props = defineProps({
  widget: Object,
  userWidgetId: Number,
  filters: String,
  variant: String,
});

const dom = ref<HTMLElement | null>(null);
let ins: ECharts | null = null;
const data = ref(null);
const config = ref(null);

const option = computed<EChartsCoreOption>(() => {

  if (config.value == 'children') {
    return {
      tooltip: { trigger: 'item', formatter: '{b}: {c} ({d}%)' },
      visualMap: {
        type: 'continuous',
        min: 0,
        max: data.value?.length || 0,
        inRange: { color: ['#2F93C8', '#AEC48F', '#FFDB5C', '#F98862'] },
      },
      series: {
        type: 'sunburst',
        data: data.value || [],
        radius: [0, '90%'],
        label: { rotate: 'radial' },
      },
    };
  } else {
    return {
      tooltip: { trigger: 'item', formatter: '{b}: {c} ({d}%)' },
      series: [
        {
          name: props.widget?.data?.name || 'Data Distribution',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          avoidLabelOverlap: true,
          itemStyle: { borderRadius: 2, borderColor: '#fff', borderWidth: 1 },
          label: { show: false, position: 'center' },
          emphasis: { focus: 'series', scaleSize: 10 },
          data: data.value || [],
        },
      ],
    };
  }
});

watch(
  () => props.filters,
  () => {
    fetchWidgetData();
  },
);

watch(() => config.value, () => {
  if (ins && config.value) {
    ins.setOption(option.value);
  }
});

// Add a watch on data to update the chart when data changes
watch(
  () => data.value,
  (newData) => {
    if (ins && newData) {
      // Update the chart with new data
      ins.setOption({
        series: [
          {
            data: newData
          }
        ]
      });
      // Force a resize after data update
      setTimeout(() => ins?.resize(), 0);
    }
  }
);

const fetchWidgetData = async () => {
  try {
    const filters = props.filters ? { filters: props.filters } : null;
    const queryParams: any = { ...filters };
    if (props.userWidgetId) {
      queryParams.userWidgetId = props.userWidgetId;
    }
    const endpoint = !!props.variant
      ? props.widget?.data?.config?.variants?.[props.variant]?.endpoint
      : props.widget?.data?.config?.endpoint;

    const response = await request(`${endpoint}?${new URLSearchParams(queryParams)}`);
    data.value = response.data;
    config.value = response.config;

    // Debug: Log the config value
    console.log("Fetched config value:", config.value);

    if (ins) {
      ins.setOption(option.value);
    }
  } catch (error) {
    console.error("Error fetching data for widget:", error);
  }
};

const resizeChart = () => {
  if (ins && dom.value) {
    ins.resize({
      width: 'auto',
      height: 'auto'
    });
  }
};

onMounted(() => {
  if (dom.value && !ins) {
    ins = echarts.init(dom.value, null, {
      renderer: 'canvas',
      useDirtyRect: false
    });

    ins.setOption(option.value);
    fetchWidgetData();

    // Add resize listener
    window.addEventListener('resize', resizeChart);

    // Force initial resize after a short delay
    setTimeout(resizeChart, 100);
  }
});

// Clean up event listeners
onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeChart);
  if (ins) {
    ins.dispose();
    ins = null;
  }
});
</script>

<style lang="scss">
.pieWidget {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  &__content {
    flex: 1;
    display: flex;
    width: 100%;
    height: 100%;
  }

  .pieCard {
    flex: 1;
    width: 100%;
    height: 100%;
    min-height: 300px;
  }
}
</style>