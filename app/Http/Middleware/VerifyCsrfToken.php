<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $except = [
        '/login',
        '/settings',
        '/en/contact',
        '/webhooks/enquire',
        '/webhooks/google-lead',
        '/webhooks/google-lead/jmj',
        '/webhooks/google-lead/seef',
        '/webhooks/google-lead/blossom'
    ];
}
