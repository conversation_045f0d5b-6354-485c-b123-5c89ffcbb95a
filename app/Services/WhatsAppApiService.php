<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class WhatsAppApiService
{
    private ?string $accessToken;
    private ?string $phoneNumberId;
    private string $baseUrl = 'https://graph.facebook.com/v18.0';

    public function __construct()
    {
        $this->accessToken = Config::get('services.whatsapp.access_token');
        $this->phoneNumberId = Config::get('services.whatsapp.phone_number_id');

        // Validate required configuration
        if (empty($this->accessToken) || empty($this->phoneNumberId)) {
            Log::warning('[WhatsAppApiService] Missing WhatsApp configuration', [
                'access_token_set' => !empty($this->accessToken),
                'phone_number_id_set' => !empty($this->phoneNumberId)
            ]);
        }
    }

    /**
     * Send a text message via WhatsApp Business API
     */
    public function sendTextMessage(string $recipientPhone, string $message): array
    {
        try {
            // Validate configuration
            if (empty($this->accessToken) || empty($this->phoneNumberId)) {
                Log::error('[WhatsAppApiService] Missing WhatsApp configuration for sending message', [
                    'to' => $recipientPhone,
                    'access_token_set' => !empty($this->accessToken),
                    'phone_number_id_set' => !empty($this->phoneNumberId)
                ]);

                return [
                    'success' => false,
                    'error' => 'WhatsApp API configuration is missing. Please check WHATSAPP_ACCESS_TOKEN and WHATSAPP_PHONE_NUMBER_ID environment variables.'
                ];
            }

            $url = "{$this->baseUrl}/{$this->phoneNumberId}/messages";

            // Format the phone number
            $formattedPhone = $this->formatPhoneNumber($recipientPhone);

            $payload = [
                'messaging_product' => 'whatsapp',
                'to' => $formattedPhone,
                'type' => 'text',
                'text' => [
                    'body' => $message
                ]
            ];

            Log::info('[WhatsAppApiService] Sending WhatsApp message', [
                'to' => $formattedPhone,
                'original_phone' => $recipientPhone,
                'message' => $message
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
            ])->post($url, $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                Log::info('[WhatsAppApiService] WhatsApp message sent successfully', [
                    'to' => $formattedPhone,
                    'original_phone' => $recipientPhone,
                    'message_id' => $responseData['messages'][0]['id'] ?? null,
                    'response' => $responseData
                ]);

                return [
                    'success' => true,
                    'message_id' => $responseData['messages'][0]['id'] ?? null,
                    'response' => $responseData
                ];
            } else {
                $errorData = $response->json();
                Log::error('[WhatsAppApiService] Failed to send WhatsApp message', [
                    'to' => $formattedPhone,
                    'original_phone' => $recipientPhone,
                    'status' => $response->status(),
                    'error' => $errorData
                ]);

                return [
                    'success' => false,
                    'error' => $errorData,
                    'status' => $response->status()
                ];
            }

        } catch (\Exception $e) {
            Log::error('[WhatsAppApiService] Exception while sending WhatsApp message', [
                'to' => $recipientPhone,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Send a template message via WhatsApp Business API
     */
    public function sendTemplateMessage(string $recipientPhone, string $templateName, array $parameters = []): array
    {
        try {
            // Validate configuration
            if (empty($this->accessToken) || empty($this->phoneNumberId)) {
                Log::error('[WhatsAppApiService] Missing WhatsApp configuration for sending template message', [
                    'to' => $recipientPhone,
                    'template' => $templateName,
                    'access_token_set' => !empty($this->accessToken),
                    'phone_number_id_set' => !empty($this->phoneNumberId)
                ]);

                return [
                    'success' => false,
                    'error' => 'WhatsApp API configuration is missing. Please check WHATSAPP_ACCESS_TOKEN and WHATSAPP_PHONE_NUMBER_ID environment variables.'
                ];
            }

            $url = "{$this->baseUrl}/{$this->phoneNumberId}/messages";

            // Format the phone number
            $formattedPhone = $this->formatPhoneNumber($recipientPhone);

            $payload = [
                'messaging_product' => 'whatsapp',
                'to' => $formattedPhone,
                'type' => 'template',
                'template' => [
                    'name' => $templateName,
                    'language' => [
                        'code' => 'en'
                    ]
                ]
            ];

            if (!empty($parameters)) {
                $payload['template']['components'] = [
                    [
                        'type' => 'body',
                        'parameters' => $parameters
                    ]
                ];
            }

            Log::info('[WhatsAppApiService] Sending WhatsApp template message', [
                'to' => $recipientPhone,
                'template' => $templateName,
                'parameters' => $parameters
            ]);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Content-Type' => 'application/json',
            ])->post($url, $payload);

            if ($response->successful()) {
                $responseData = $response->json();
                Log::info('[WhatsAppApiService] WhatsApp template message sent successfully', [
                    'to' => $recipientPhone,
                    'template' => $templateName,
                    'message_id' => $responseData['messages'][0]['id'] ?? null,
                    'response' => $responseData
                ]);

                return [
                    'success' => true,
                    'message_id' => $responseData['messages'][0]['id'] ?? null,
                    'response' => $responseData
                ];
            } else {
                $errorData = $response->json();
                Log::error('[WhatsAppApiService] Failed to send WhatsApp template message', [
                    'to' => $recipientPhone,
                    'template' => $templateName,
                    'status' => $response->status(),
                    'error' => $errorData
                ]);

                return [
                    'success' => false,
                    'error' => $errorData,
                    'status' => $response->status()
                ];
            }

        } catch (\Exception $e) {
            Log::error('[WhatsAppApiService] Exception while sending WhatsApp template message', [
                'to' => $recipientPhone,
                'template' => $templateName,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Format phone number for WhatsApp API
     */
    public function formatPhoneNumber(string $phone): string
    {
        // Remove any non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // If phone doesn't start with country code, assume it's a Qatar number
        if (!str_starts_with($phone, '974') && strlen($phone) === 8) {
            $phone = '974' . $phone;
        }
        
        return $phone;
    }
}
