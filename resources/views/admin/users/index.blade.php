@extends('crm.layout')

@section('content')
<div class="px-md-2">
    @if(request()->session()->has('success') || request()->session()->has('info') || request()->session()->has('danger'))
    <div class="messages-container pt-4">
        @include('crm.components.session-alerts')
    </div>
    @endif
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Users</h1>
        <div class="btn-toolbar mb-2 mb-md-0 d-flex justify-content-end gap-2 align-items-center">
            @role(\App\Models\Crm\RolesDef::CHECK_IN_ADMIN)
            <a role="button" class="btn btn-sm btn-outline-secondary" href="{{route('admin.users.check-in-admin')}}">
                <span data-feather="key"></span>
                User Checkins</a>
            @endrole
            @can(\App\Models\Crm\PermissionsDef::CRM_ADMIN)
            @if(in_array(strtolower(auth()->user()->email), ['<EMAIL>', '<EMAIL>', '<EMAIL>']))
            <a href="{{route('admin.users.interactive-users-map')}}" class="link">Current Users</a>
            @endif
            <a href="{{route('admin.users.roles-permissions-matrix')}}" class="link">Roles and permissions matrix</a>
            @endcan
            <a href="{{route('admin.users.create')}}" role="button" class="btn btn-sm btn-outline-secondary">
                <span data-feather="plus"></span>
                Add new user
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12 pb-2">
            <a role="button" class="btn btn-sm btn-outline-secondary" href="javascript:toggleExtraFilters()">
                <span data-feather="chevrons-right"></span>
                More filters
            </a>
        </div>
        <div class="card card-body p-2 m-2 bg-light d-none" id="extraFiltersPanel">
            <div class="col-xs-12 filters-container">
                <div class="row">
                    <users-filters></users-filters>
                </div>
            </div>
        </div>
    </div>

    @can(\App\Models\Crm\PermissionsDef::CRM_ADMIN)
    <div class="bulk-actions">
        <div class="input-group input-group-sm">
            <span class="input-group-text">With selection (<span id="selectedCount">0</span>):</span>
            <select id="bulkAction" class="form-select form-select-sm" disabled>
                <option value="">Select an action</option>
                <option value="reset_password">Reset Password</option>
            </select>
        </div>
        <button id="applyAction" class="btn btn-primary btn-sm" disabled>Apply</button>
    </div>
    @endcan

    <div class="col mt-2">
        <table id="usersTable" class="table table-sm dataTable fw-light align-middle">
            <thead>
                <tr>
                    @can(\App\Models\Crm\PermissionsDef::CRM_ADMIN)
                    <th>
                        <input type="checkbox" name="" id="selectAll">
                    </th>
                    @endcan
                    <th>&nbsp;</th>
                    <th>Full Name</th>
                    <th>Rating</th>
                    <th>Username</th>
                    <th>Position</th>
                    <th>Brokerage license account</th>
                    <th>Roles</th>
                    <th>Public Profile</th>
                    <th>Actions</th>
                </tr>
            </thead>
        </table>
    </div>
</div>
@include('crm.components.admin-user-delete-modal')

<!-- Reset Password Confirmation Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetPasswordModalLabel">Reset Password Confirmation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>You are about to reset the password for <strong><span id="selectedUsersCount">0</span></strong> user(s).</p>
                <p><strong>Impact:</strong></p>
                <ul>
                    <li>New random passwords will be generated for the selected users</li>
                    <li>Each user will receive an email with their new password</li>
                    <li>Users will need to use the new password to log in</li>
                </ul>
                <p>Are you sure you want to proceed?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmResetPassword">Reset Passwords</button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('head-css')
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/dataTables.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css"
    href="https://cdn.datatables.net/fixedheader/3.1.9/css/fixedHeader.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css"
    href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="/css/virtual-select.min.css" />
<style>
    .vscomp-ele {
        max-width: 100%;
        display: block;
        width: 100%;
        max-height: 32px;
    }

    td.details-control {
        background: url('{{asset(' /images/datatable/details_open.png')}}') no-repeat center center;
        cursor: pointer;
    }

    tr.shown td.details-control {
        background: url('{{asset(' /images/datatable/details_close.png')}}') no-repeat center center;
    }

    tr.child td.child {
        background-color: #f1f5fd;
    }

    tr.child td.child ul.dtr-details {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
        grid-column-gap: 5px;
        grid-row-gap: 5px;
    }

    tr.child td.child ul.dtr-details li {
        display: flex;
    }

    tr.child td.child ul.dtr-details li span:first-child {
        display: inline-block;
        margin-right: 10px;
    }

    tr.child td.child ul.dtr-details li span:first-child:after {
        content: ':';
    }

    .bulk-actions {
        display: flex;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 15px;
        width: 100%;
        max-width: 440px;
    }
</style>
@endsection


@section('body-js')
<script>
    // Set permission flag for JavaScript
    window.userCanManageUsers = @json(auth()->user()->can(\App\Models\Crm\PermissionsDef::CRM_ADMIN));

    // Data for filters
    window.rolesData = @json($roles->map(function($role) { return ['id' => $role->id, 'name' => $role->name]; }));
    window.teamLeadersData = @json($teamLeaders->map(function($user) { return ['id' => $user->id, 'name' => $user->name]; }));

    const toggleExtraFilters = () => {
        const classList = document.querySelector('#extraFiltersPanel').classList;
        if (classList.contains('d-none')) {
            classList.remove('d-none');
        } else {
            classList.add('d-none');
        }
    }
</script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/fixedheader/3.1.9/js/dataTables.fixedHeader.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.js"></script>
<script type="text/javascript" src="/js/virtual-select.min.js"></script>
<script type="text/javascript" src="{{mix('/js/crm/libs/datatables-util.js')}}"></script>
<script type="text/javascript" src="{{mix('/js/admin/users-table.js')}}"></script>
<script type="text/javascript" src="{{mix('/js/users-filters.js')}}"></script>
@endsection