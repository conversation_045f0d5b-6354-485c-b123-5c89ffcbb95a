<?php

namespace App\Http\Controllers\Crm;

use App\Helpers\NumberFormatter;
use App\Http\Controllers\CrmBaseController;
use App\Models\Crm\Deal;
use App\Models\InvoiceDocument;
use App\Models\InvoicePayment;
use App\Models\Invoice;
use App\Services\AccountantService;
use App\Services\AttachmentsService;
use App\Services\EmailService;
use App\Services\LeadsService;
use Illuminate\Http\Request;
use App\Services\OperationHistoryService;
use App\Services\UserService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class AccountantController extends CrmBaseController
{
    protected $accountantService;
    protected $leadsService;
    protected $userService;
    protected $ajaxListMapper;
    protected $hasEnvelope = true;
    protected $operationHistoryService;
    protected $attachmentsService;
    protected $emailService;

    protected $views = [
        'index' => 'crm.accountant.index',
        'edit' => 'crm.accountant.edit',
    ];

    protected $routes = [
        'index' => 'crm.accountant.index',
        'edit' => 'crm.accountant.edit',
        'update' => 'crm.accountant.update',
    ];

    protected $messages = [
        'edit.success' => 'Invoice details updated successfully.',
        'delete.success' => 'Document deleted successfully.',
        'upload.success' => 'Document uploaded successfully.'
    ];

    public function __construct(
        AccountantService $accountantService,
        LeadsService $leadsService,
        UserService $userService,
        Request $request,
        OperationHistoryService $operationHistoryService,
        AttachmentsService $attachmentsService,
        EmailService $emailService
    ) {
        $this->accountantService = $accountantService;
        $this->leadsService = $leadsService;
        $this->userService = $userService;
        $this->operationHistoryService = $operationHistoryService;
        $this->attachmentsService = $attachmentsService;
        $extraConfig = $this->accountantService->getExtraConfig($request);
        $this->itemFetcher = function ($offset = 0, $limit = 10) use ($extraConfig) {
            return $this->accountantService->fetchItems($extraConfig, $offset, $limit);
        };
        $this->ajaxListMapper = $this->accountantService->ajaxListMapper;
        $this->emailService = $emailService;
    }

    protected function getFieldsToValidate(string $actionName, $itemId = null)
    {
        return [
            'landlord_invoice_number' => '',
            'landlord_invoice_amount' => '',
            'landlord_invoice_date' => '',
            'landlord_amount' => '',
            'landlord_payment_date' => '',
            'landlord_payment_type' => '',
            'landlord_document' => '',
            'landlord_document_remove' => '',
            'landlord_documents' => 'array',
            'landlord_documents.*' => 'required|file|max:10240', // 10MB max per file
            'landlord_documents_options' => 'array',
            'landlord_documents_options.*' => 'required',
            'landlord_documents_receipt_no' => 'array',
            'landlord_documents_receipt_no.*' => 'string|nullable',
            'landlord_documents_cheque_number' => 'array',
            'landlord_documents_cheque_number.*' => 'string|nullable',
            'landlord_documents_observations' => 'array',
            'landlord_documents_observations.*' => 'string|nullable',
            'landlord_documents_remove' => 'array',
            'landlord_documents_remove.*' => 'integer',
            'client_invoice_number' => '',
            'client_invoice_amount' => '',
            'client_invoice_date' => '',
            'client_amount' => '',
            'client_payment_date' => '',
            'client_payment_type' => '',
            'client_document' => '',
            'client_document_remove' => '',
            'client_documents' => 'array',
            'client_documents.*' => 'required|file|max:10240', // 10MB max per file
            'client_documents_options' => 'array',
            'client_documents_options.*' => 'required',
            'client_documents_receipt_no' => 'array',
            'client_documents_receipt_no.*' => 'string|nullable',
            'client_documents_cheque_number' => 'array',
            'client_documents_cheque_number.*' => 'string|nullable',
            'client_documents_observations' => 'array',
            'client_documents_observations.*' => 'string|nullable',
            'client_documents_remove' => 'array',
            'client_documents_remove.*' => 'integer',
            // Commission fields
            'listing_agent_id' => 'nullable|integer|exists:users,id',
            'referred_listing_agent_id' => 'nullable|integer|exists:users,id',
            'closing_agent_id' => 'nullable|integer|exists:users,id',
            'referred_closing_agent_id' => 'nullable|integer|exists:users,id',
            'listing_agent_shared_commission' => 'nullable|numeric|min:0',
            'referred_listing_agent_shared_commission' => 'nullable|numeric|min:0',
            'closing_agent_shared_commission' => 'nullable|numeric|min:0',
            'referred_closing_agent_shared_commission' => 'nullable|numeric|min:0',
            'listing_agent_cash_in' => '',
            'referred_listing_agent_cash_in' => '',
            'closing_agent_cash_in' => '',
            'referred_closing_agent_cash_in' => '',
            'listing_agent_month' => '',
            'referred_listing_agent_month' => '',
            'closing_agent_month' => '',
            'referred_closing_agent_month' => '',
            'operation-history' => '',
        ];
    }


    public function index()
    {
        $request = request();
        if ($request->ajax()) {
            if ($this->itemFetcher) {
                $offset = empty($request->query('start')) ? 0 : $request->query('start');
                $limit = empty($request->query('length')) ? 0 : $request->query('length');
                $items = call_user_func($this->itemFetcher, $offset, $limit);
            } else {
                $items = call_user_func($this->modelClass . '::all');
            }

            if (isset($items['items'])) {
                if (is_array($items['items'])) {
                    $data = array_map($this->ajaxListMapper, $items['items']);
                } else {
                    $data = $items['items']->map($this->ajaxListMapper);
                }
            } else {
                if (is_array($items)) {
                    $data = array_map($this->ajaxListMapper, $items);
                } else {
                    $data = $items->map($this->ajaxListMapper);
                }
            }
            if (isset($this->hasEnvelope)) {
                $payload = ['data' => $data, 'recordsTotal' => $items['count'] ?? 0, 'recordsFiltered' => $items['count'] ?? 0];
                return response()->json($payload);
            }
            return response()->json(["data" => $data]);
        }
        $agents = $this->userService->getPossibleListingOwners();
        $teamLeaderAgents = getTeamLeaderAgents();
        $sources = $this->leadsService->getCachedSources();


        return view($this->views['index'], compact('agents', 'sources', 'teamLeaderAgents'));
    }

    public function getInvocesDetails($dealId)
    {
        $deal = Deal::with(['operationHistory.author'])->findOrFail($dealId);

        $dealArray = $deal->toArray();

        usort($dealArray['operation_history'], function ($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        foreach ($dealArray['operation_history'] as &$history) {
            $history['created_by'] = $history['author']['name'] ?? 'Unknown';
            $history['created_at'] = Carbon::parse($history['created_at'])->format('d.m.Y H:i');
        }
        return $dealArray;
    }

    public function saveRemark($dealId)
    {
        $deal = Deal::findOrFail($dealId);
        $authUser = auth()->user();

        $validData = request()->validate([
            'operationHistory' => '',
        ]);

        if (!empty($validData['operationHistory'])) {
            $this->operationHistoryService->addOperationHistory($deal, $validData['operationHistory'], $authUser);
        }

        $remarks = $deal->operationHistory->map(function ($remark) {
            $createdAt = Carbon::parse($remark['created_at'])->format('d.m.Y H:i');
            $authUser = auth()->user();
            return [
                'content'            => $remark->content,
                'created_by'         => $authUser->name,
                'created_at'         => $createdAt,
            ];
        });
        $deal->save();
        return response()->json(['operation_history' => $remarks], 200);
    }

    public function edit($id)
    {
        $deal = Deal::with(['invoices', 'invoices.payments'])->findOrFail($id);

        $landlordPayments = collect([]);
        $clientPayments = collect([]);

        $landlordInvoiceDetails = $deal->invoices
            ->where('is_client', false)
            ->first();

        $clientInvoiceDetails = $deal->invoices
            ->where('is_client', true)
            ->first();

        if ($landlordInvoiceDetails) {
            $landlordPayments = $landlordInvoiceDetails->payments ?? collect([]);
        }
        if ($clientInvoiceDetails) {
            $clientPayments = $clientInvoiceDetails->payments ?? collect([]);
        }

        // Load documents from the new invoice_documents table
        $landlordDocuments = collect([]);
        $clientDocuments = collect([]);

        if ($landlordInvoiceDetails) {
            $landlordDocuments = InvoiceDocument::where('invoice_id', $landlordInvoiceDetails->id)
                ->orderBy('upload_date', 'desc')
                ->get();
        }

        if ($clientInvoiceDetails) {
            $clientDocuments = InvoiceDocument::where('invoice_id', $clientInvoiceDetails->id)
                ->orderBy('upload_date', 'desc')
                ->get();
        }

        $paymentMethods = $this->accountantService->paymentMethods;

        $operationHistory = $deal->operationHistory()->with('author')->orderBy('created_at', 'desc')->get();

        return view($this->views['edit'], [
            'deal' => $deal,
            'landlordInvoiceDetails' => $landlordInvoiceDetails,
            'clientInvoiceDetails' => $clientInvoiceDetails,
            'landlordPayments' => $landlordPayments,
            'clientPayments' => $clientPayments,
            'landlordDocuments' => $landlordDocuments,
            'clientDocuments' => $clientDocuments,
            'paymentMethods' => $paymentMethods,
            'operationHistory' => $operationHistory,
        ]);
    }

    public function uploadDocument($id, $landlordRecord = null, $clientRecord = null)
    {
        $request = request();
        $deal = Deal::findOrFail($id);

        $landlordInvoiceDetails = Invoice::where('deal_id', $deal->id)
            ->where('is_client', false)
            ->first();

        $clientInvoiceDetails = Invoice::where('deal_id', $deal->id)
            ->where('is_client', true)
            ->first();

        try {
            $uploadedDocuments = [];

            if ($request->hasFile('landlord_payment_documents')) {
                $file = $request->file('landlord_payment_documents');

                $this->attachmentsService->processInvoicePaymentProof($landlordRecord, $file, 'landlord_payment_documents');

                $landlordRecord->save();

                $uploadedDocuments[] = $landlordRecord;
            }

            if ($request->hasFile('client_payment_documents')) {
                $file = $request->file('client_payment_documents');


                $this->attachmentsService->processInvoicePaymentProof($clientRecord, $file, 'client_payment_documents');
                $clientRecord->save();

                $uploadedDocuments[] = $clientRecord;
            }

            $message = count($uploadedDocuments) > 0
                ? (count($uploadedDocuments) > 1 ? count($uploadedDocuments) . ' documents uploaded successfully.' : 'Document uploaded successfully.')
                : 'No documents were uploaded.';

            return redirect()->route('crm.accountant.edit', ['id' => $id])
                ->with('success', $message);
        } catch (\Exception $e) {
            Log::error('Error uploading documents: ' . $e->getMessage());
            return redirect()->route('crm.accountant.edit', ['id' => $id])
                ->with('error', 'An error occurred while uploading documents: ' . $e->getMessage());
        }
    }


    public function downloadDocument($id, $documentId)
    {
        $payment = InvoicePayment::findOrFail($documentId);

        if (is_string($payment->payment_documents)) {
            $payment->payment_documents = json_decode($payment->payment_documents, true);
        }
        if (!empty($payment->payment_documents) && is_array($payment->payment_documents)) {
            foreach ($payment->payment_documents as $doc) {
                if (isset($doc['file_path']) && isset($doc['file_name'])) {
                    $filePath = $doc['file_path'];
                    $fileName = $doc['file_name'];
                    break;
                }
            }
        }

        if ($filePath && $fileName) {
            return Storage::download($filePath, $fileName);
        }

        abort(404, 'Document not found.');
    }

    public function update($id)
    {
        $deal = Deal::findOrFail($id);
        $request = request();
        $validData = $request->validate($this->getFieldsToValidate('update', $id));

        $landlordInvoice = Invoice::where('deal_id', $id)
            ->where('is_client', false)
            ->first();

        $clientInvoice = Invoice::where('deal_id', $id)
            ->where('is_client', true)
            ->first();


        if (!$landlordInvoice) {
            $landlordInvoice = new Invoice();
            $landlordInvoice->deal_id = $id;
        }
        $landlordInvoice->ref_no = $validData['landlord_invoice_number'] ?? null;
        $landlordInvoice->amount = $validData['landlord_invoice_amount'] ?? null;
        $landlordInvoice->issue_date = $validData['landlord_invoice_date'] ?? null;
        $landlordInvoice->is_client = '0';

        if (!$clientInvoice) {
            $clientInvoice = new Invoice();
            $clientInvoice->deal_id = $id;
        }

        $clientInvoice->ref_no = $validData['client_invoice_number'] ?? null;
        $clientInvoice->amount = $validData['client_invoice_amount'] ?? null;
        $clientInvoice->issue_date = $validData['client_invoice_date'] ?? null;
        $clientInvoice->is_client = '1';


        if (isset($validData['landlord_document']) || isset($validData['landlord_document_remove'])) {
            $landlordDocumentInvoice = $validData['landlord_document'] ?? null;
            $deleteOldLandlordDocumentInvoicePdfFile = isset($validData['landlord_document_remove']);
            $this->attachmentsService->syncLandlordInvoiceDocument($landlordInvoice, $landlordDocumentInvoice, $deleteOldLandlordDocumentInvoicePdfFile);
        }

        if (isset($validData['client_document']) || isset($validData['client_document_remove'])) {
            $clientDocumentInvoice = $validData['client_document'] ?? null;
            $deleteOldClientDocumentInvoicePdfFile = isset($validData['client_document_remove']);
            $this->attachmentsService->syncClientInvoiceDocument($clientInvoice, $clientDocumentInvoice, $deleteOldClientDocumentInvoicePdfFile);
        }

        $landlordInvoice->save();
        $clientInvoice->save();

        // Update commission fields in the deal
        $this->updateCommissionFields($deal, $validData);

        $landlordInvoicePayment = null;
        if ($validData['landlord_amount'] || $validData['landlord_payment_date'] || $validData['landlord_payment_type']) {
            $landlordInvoicePayment = new InvoicePayment();
            $landlordInvoicePayment->invoice_id = $landlordInvoice ? $landlordInvoice->id : null;
            $landlordInvoicePayment->amount = $validData['landlord_amount'] ?? null;
            $landlordInvoicePayment->payment_date = $validData['landlord_payment_date'] ?? null;
            $landlordInvoicePayment->payment_type = $validData['landlord_payment_type'] ?? null;
            $landlordInvoicePayment->cashed_in = false; // Default to not cashed in
            $landlordInvoicePayment->save();
        }


        $clientInvoicePayment = null;
        if ($validData['client_amount'] || $validData['client_payment_date'] || $validData['client_payment_type']) {
            $clientInvoicePayment = new InvoicePayment();
            $clientInvoicePayment->invoice_id = $clientInvoice ? $clientInvoice->id : null;
            $clientInvoicePayment->amount = $validData['client_amount'] ?? null;
            $clientInvoicePayment->payment_date = $validData['client_payment_date'] ?? null;
            $clientInvoicePayment->payment_type = $validData['client_payment_type'] ?? null;
            $clientInvoicePayment->cashed_in = false; // Default to not cashed in
            $clientInvoicePayment->save();
        }

        // Handle new landlord documents upload
        if ($request->hasFile('landlord_documents')) {
            $this->handleLandlordDocumentsUpload($landlordInvoice, $validData);
        }

        // Handle landlord documents removal
        if (isset($validData['landlord_documents_remove']) && !empty($validData['landlord_documents_remove'])) {
            $this->handleLandlordDocumentsRemoval($landlordInvoice, $validData['landlord_documents_remove']);
        }

        // Handle new client documents upload
        if ($request->hasFile('client_documents')) {
            $this->handleClientDocumentsUpload($clientInvoice, $validData);
        }

        // Handle client documents removal
        if (isset($validData['client_documents_remove']) && !empty($validData['client_documents_remove'])) {
            $this->handleClientDocumentsRemoval($clientInvoice, $validData['client_documents_remove']);
        }

        if ($request->hasFile('landlord_payment_documents') || $request->hasFile('client_payment_documents')) {
            $this->uploadDocument($id, $landlordInvoicePayment, $clientInvoicePayment);
        } else {
            Log::info('No payment proof uploads to process');
        }

        if (!empty($validData['operation-history'])) {
            $this->operationHistoryService->addOperationHistory($deal, $validData['operation-history'], auth()->user());
        }

        return redirect()->route($this->routes['edit'], ['id' => $id])
            ->with('success', $this->messages['edit.success']);
    }

    private function handleLandlordDocumentsUpload($landlordInvoice, $validData)
    {
        $files = request()->file('landlord_documents');
        $options = $validData['landlord_documents_options'] ?? [];
        $observations = $validData['landlord_documents_observations'] ?? [];
        $receiptNumbers = $validData['landlord_documents_receipt_no'] ?? [];
        $chequeNumbers = $validData['landlord_documents_cheque_number'] ?? [];

        foreach ($files as $index => $file) {
            $filePath = $file->store("landlord_documents/{$landlordInvoice->deal_id}");
            $fileName = $file->getClientOriginalName();
            $documentType = $options[$index] ?? '';
            $documentObservations = $observations[$index] ?? '';

            // Get the appropriate number based on document type
            $receiptNo = null;
            $chequeNumber = null;
            if ($documentType === 'receipt') {
                $receiptNo = $receiptNumbers[$index] ?? null;
            } elseif ($documentType === 'cheque') {
                $chequeNumber = $chequeNumbers[$index] ?? null;
            }

            InvoiceDocument::create([
                'invoice_id' => $landlordInvoice->id,
                'file_name' => $fileName,
                'file_path' => $filePath,
                'document_type' => $documentType,
                'receipt_no' => $receiptNo,
                'cheque_number' => $chequeNumber,
                'observations' => $documentObservations,
                'upload_date' => now(),
            ]);
        }
    }

    private function handleLandlordDocumentsRemoval($landlordInvoice, $documentIdsToRemove)
    {
        foreach ($documentIdsToRemove as $documentId) {
            $document = InvoiceDocument::where('invoice_id', $landlordInvoice->id)
                ->where('id', $documentId)
                ->first();

            if ($document) {
                // Delete file from storage
                if ($document->file_path) {
                    Storage::delete($document->file_path);
                }

                // Delete document record
                $document->delete();
            }
        }
    }

    private function handleClientDocumentsUpload($clientInvoice, $validData)
    {
        $files = request()->file('client_documents');
        $options = $validData['client_documents_options'] ?? [];
        $observations = $validData['client_documents_observations'] ?? [];
        $receiptNumbers = $validData['client_documents_receipt_no'] ?? [];
        $chequeNumbers = $validData['client_documents_cheque_number'] ?? [];

        foreach ($files as $index => $file) {
            $filePath = $file->store("client_documents/{$clientInvoice->deal_id}");
            $fileName = $file->getClientOriginalName();
            $documentType = $options[$index] ?? '';
            $documentObservations = $observations[$index] ?? '';

            // Get the appropriate number based on document type
            $receiptNo = null;
            $chequeNumber = null;
            if ($documentType === 'receipt') {
                $receiptNo = $receiptNumbers[$index] ?? null;
            } elseif ($documentType === 'cheque') {
                $chequeNumber = $chequeNumbers[$index] ?? null;
            }

            InvoiceDocument::create([
                'invoice_id' => $clientInvoice->id,
                'file_name' => $fileName,
                'file_path' => $filePath,
                'document_type' => $documentType,
                'receipt_no' => $receiptNo,
                'cheque_number' => $chequeNumber,
                'observations' => $documentObservations,
                'upload_date' => now(),
            ]);
        }
    }

    private function handleClientDocumentsRemoval($clientInvoice, $documentIdsToRemove)
    {
        foreach ($documentIdsToRemove as $documentId) {
            $document = InvoiceDocument::where('invoice_id', $clientInvoice->id)
                ->where('id', $documentId)
                ->first();

            if ($document) {
                // Delete file from storage
                if ($document->file_path) {
                    Storage::delete($document->file_path);
                }

                // Delete document record
                $document->delete();
            }
        }
    }

    private function updateCommissionFields($deal, $validData)
    {
        // Update basic commission fields
        $basicCommissionFields = [
            'listing_agent_id',
            'referred_listing_agent_id',
            'closing_agent_id',
            'referred_closing_agent_id',
            'listing_agent_shared_commission',
            'referred_listing_agent_shared_commission',
            'closing_agent_shared_commission',
            'referred_closing_agent_shared_commission'
        ];

        foreach ($basicCommissionFields as $field) {
            if (array_key_exists($field, $validData)) {
                $deal->$field = $validData[$field];
            }
        }

        // Handle cash_in fields (checkboxes) - exactly like DealsController
        $deal->listing_agent_cash_in = isset($validData['listing_agent_cash_in']) && $validData['listing_agent_cash_in'] == '1' ? 1 : 0;
        $deal->referred_listing_agent_cash_in = isset($validData['referred_listing_agent_cash_in']) && $validData['referred_listing_agent_cash_in'] == '1' ? 1 : 0;
        $deal->closing_agent_cash_in = isset($validData['closing_agent_cash_in']) && $validData['closing_agent_cash_in'] == '1' ? 1 : 0;
        $deal->referred_closing_agent_cash_in = isset($validData['referred_closing_agent_cash_in']) && $validData['referred_closing_agent_cash_in'] == '1' ? 1 : 0;

        // Handle month fields - exactly like DealsController (convert Y-m to Y-m-d)
        $listingAgentMonth = !empty($validData['listing_agent_month']) ? $validData['listing_agent_month'] . '-01' : null;
        $referredListingAgentMonth = !empty($validData['referred_listing_agent_month']) ? $validData['referred_listing_agent_month'] . '-01' : null;
        $closingAgentMonth = !empty($validData['closing_agent_month']) ? $validData['closing_agent_month'] . '-01' : null;
        $referredClosingAgentMonth = !empty($validData['referred_closing_agent_month']) ? $validData['referred_closing_agent_month'] . '-01' : null;

        $deal->listing_agent_month = $listingAgentMonth;
        $deal->referred_listing_agent_month = $referredListingAgentMonth;
        $deal->closing_agent_month = $closingAgentMonth;
        $deal->referred_closing_agent_month = $referredClosingAgentMonth;

        $deal->save();
    }

    public function exportData(Request $request)
    {
        $ids = $request->input('ids', []);

        $validator = validator(['ids' => $ids], [
            'ids' => 'array',
            'ids.*' => 'integer'
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 422);
        }

        $selectedIds = $ids;

        $extraConfig = $this->accountantService->getExtraConfig($request);
        $extraConfig['limit'] = 10000; // Set a high limit to get all data
        $extraConfig['ids'] = $selectedIds; // Filter by selected IDs

        $items = $this->accountantService->fetchItems($extraConfig, 0, 10000);

        if (isset($items['items'])) {
            if (is_array($items['items'])) {
                $data = array_map($this->ajaxListMapper, $items['items']);
            } else {
                $data = $items['items']->map($this->ajaxListMapper);
            }
        } else {
            if (is_array($items)) {
                $data = array_map($this->ajaxListMapper, $items);
            } else {
                $data = $items->map($this->ajaxListMapper);
            }
        }

        return response()->json(['data' => $data]);
    }

    public function deleteDocument($dealId, $documentId)
    {
        try {
            $payment = InvoicePayment::findOrFail($documentId);

            if (!empty($payment->payment_documents)) {
                if (is_string($payment->payment_documents)) {
                    $payment->payment_documents = json_decode($payment->payment_documents, true) ?? [];
                }

                foreach ($payment->payment_documents as $doc) {
                    if (isset($doc['file_path']) && isset($doc['file_name'])) {
                        Storage::delete($doc['file_path']);
                        break;
                    }
                }
            }
            $payment->delete();
            $currentUser = auth()->user();

            $remarks = request()->input('remarks');
            if ($remarks) {
                $this->operationHistoryService->addOperationHistory($payment, $remarks, $currentUser);
            }

            $this->emailService->sendEmailOnDeletingPaymentProof($payment, $currentUser);
            return response()->json(['success' => true, 'message' => $this->messages['delete.success']]);
        } catch (\Exception $e) {
            if (request()->ajax()) {
                return response()->json(['success' => false, 'message' => 'An error occurred while deleting the document: ' . $e->getMessage()], 500);
            }

            return redirect()->route('crm.accountant.edit', ['id' => $dealId])
                ->with('error', 'An error occurred while deleting the document: ' . $e->getMessage());
        }
    }

    public function downloadLandlordInvoice($dealId, $docId)
    {
        $invoice = Invoice::firstWhere('id', $docId);
        if ($invoice->documents_path) {
            return Storage::download($invoice->documents_path, $invoice->documents_title);
        }
        return response();
    }

    public function downloadClientInvoice($dealId, $docId)
    {
        $invoice = Invoice::firstWhere('id', $docId);
        if ($invoice->documents_path) {
            return Storage::download($invoice->documents_path, $invoice->documents_title);
        }
        return response();
    }

    public function downloadLandlordDocument($id, $documentId)
    {
        $deal = Deal::findOrFail($id);
        $landlordInvoice = Invoice::where('deal_id', $deal->id)
            ->where('is_client', false)
            ->first();

        if (!$landlordInvoice) {
            abort(404, 'Invoice not found');
        }

        $document = InvoiceDocument::where('invoice_id', $landlordInvoice->id)
            ->where('id', $documentId)
            ->first();

        if (!$document) {
            abort(404, 'Document not found');
        }

        if (!Storage::exists($document->file_path)) {
            abort(404, 'File not found');
        }

        return Storage::download($document->file_path, $document->file_name);
    }

    public function previewLandlordDocument($id, $documentId)
    {
        $deal = Deal::findOrFail($id);
        $landlordInvoice = Invoice::where('deal_id', $deal->id)
            ->where('is_client', false)
            ->first();

        if (!$landlordInvoice) {
            abort(404, 'Invoice not found');
        }

        $document = InvoiceDocument::where('invoice_id', $landlordInvoice->id)
            ->where('id', $documentId)
            ->first();

        if (!$document) {
            abort(404, 'Document not found');
        }

        if (!Storage::exists($document->file_path)) {
            abort(404, 'File not found');
        }

        $fileExtension = strtolower(pathinfo($document->file_name, PATHINFO_EXTENSION));
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];

        if (in_array($fileExtension, $imageExtensions)) {
            return response(Storage::get($document->file_path))
                ->header('Content-Type', Storage::mimeType($document->file_path));
        }

        abort(404, 'Preview not available for this file type');
    }

    public function downloadClientDocument($id, $documentId)
    {
        $deal = Deal::findOrFail($id);
        $clientInvoice = Invoice::where('deal_id', $deal->id)
            ->where('is_client', true)
            ->first();

        if (!$clientInvoice) {
            abort(404, 'Invoice not found');
        }

        $document = InvoiceDocument::where('invoice_id', $clientInvoice->id)
            ->where('id', $documentId)
            ->first();

        if (!$document) {
            abort(404, 'Document not found');
        }

        if (!Storage::exists($document->file_path)) {
            abort(404, 'File not found');
        }

        return Storage::download($document->file_path, $document->file_name);
    }

    public function previewClientDocument($id, $documentId)
    {
        $deal = Deal::findOrFail($id);
        $clientInvoice = Invoice::where('deal_id', $deal->id)
            ->where('is_client', true)
            ->first();

        if (!$clientInvoice) {
            abort(404, 'Invoice not found');
        }

        $document = InvoiceDocument::where('invoice_id', $clientInvoice->id)
            ->where('id', $documentId)
            ->first();

        if (!$document) {
            abort(404, 'Document not found');
        }

        if (!Storage::exists($document->file_path)) {
            abort(404, 'File not found');
        }

        $fileExtension = strtolower(pathinfo($document->file_name, PATHINFO_EXTENSION));
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];

        if (in_array($fileExtension, $imageExtensions)) {
            return response(Storage::get($document->file_path))
                ->header('Content-Type', Storage::mimeType($document->file_path));
        }

        abort(404, 'Preview not available for this file type');
    }

    public function summary()
    {
        $allInvoicesSummed = Invoice::sum('amount');
        $allTimesCashedIn = InvoicePayment::where('cashed_in', true)->sum('amount');
        $pendingPayments = InvoicePayment::where('cashed_in', false)->sum('amount');

        $thisMonth = Carbon::now();
        [$startDate, $endDate] = $this->getThisMonthPeriod($thisMonth);

        $cashInThisMonth = InvoicePayment::whereBetween('payment_date', [$startDate, $endDate])
            ->where('cashed_in', true)
            ->sum('amount');

        return response()->json([
            'cash_in' => NumberFormatter::formatLargeNumber($cashInThisMonth),
            'pending' => NumberFormatter::formatLargeNumber($pendingPayments),
            'to_collect' => NumberFormatter::formatLargeNumber($allInvoicesSummed - $allTimesCashedIn)
        ]);
    }

    private function getThisMonthPeriod(Carbon $date)
    {
        if ($date->day > 25) {
            $start = $date->copy()->setDay(26)->startOfDay();
        } else {
            $start = $date->copy()->subMonth()->setDay(26)->startOfDay();
        }
        $end = $date->copy(); // Current time
        return [$start, $end];
    }

    public function updateCashedIn($paymentId)
    {
        $request = request();
        $validData = $request->validate([
            'cashed_in' => 'required|boolean'
        ]);

        try {
            $payment = InvoicePayment::findOrFail($paymentId);
            $payment->cashed_in = $validData['cashed_in'];
            $payment->save();

            return response()->json([
                'success' => true,
                'message' => 'Payment status updated successfully.',
                'cashed_in' => $payment->cashed_in
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while updating payment status: ' . $e->getMessage()
            ], 500);
        }
    }
}
