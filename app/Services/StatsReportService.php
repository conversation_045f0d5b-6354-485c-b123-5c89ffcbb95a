<?php

namespace App\Services;

use App\Models\Crm\Deal;
use App\Models\Lead;
use App\Models\Property;
use Carbon\Carbon;
use DB;

class StatsReportService
{
    public function getPropertiesCreated($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(p.id) AS created_listings
            FROM
                properties p
            JOIN users u ON
                p.created_by = u.id
            JOIN model_has_roles mhr ON
                mhr.model_id = p.created_by
            JOIN roles r ON
                r.id = mhr.role_id
            WHERE
                p.publishing_status IN ('published', 'masterlist') AND
                (u.is_test_user IS NULL OR u.is_test_user = 0)
                AND r.name IN('Agent') AND p.deleted_at IS NULL AND
            ";

        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(p.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(p.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(p.created_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY
                u.id
        ";
        $agentPropertiesCreated = DB::select(DB::raw($sql));



        return $agentPropertiesCreated;
    }

    public function getPropertiesUpdated($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(p.id) AS updated_listings
            FROM
                properties p
            JOIN users u ON
                p.updated_by = u.id
            JOIN model_has_roles mhr ON
                mhr.model_id = p.updated_by
            JOIN roles r ON
                r.id = mhr.role_id
            WHERE
                (u.is_test_user IS NULL OR u.is_test_user = 0) AND
                r.name IN('Agent') AND
            ";

        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(p.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= " DATE(p.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "r.name IN('Agent') AND DATE(p.updated_at) >=  $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY
                u.id
        ";
        $agentPropertiesUpdated = DB::select(DB::raw($sql));

        return $agentPropertiesUpdated;
    }

    public function getPropertiesUpdatedNull($intervalType, $startDate = null, $userId = null)
    {
        // @TODO - update query
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $agentPropertiesUpdatedNull = DB::select(DB::raw("
            SELECT
                COUNT(*) AS modified_listings_null
            FROM
                properties
            WHERE
                DATE(updated_at) >= $mysqlTimeStatement
            AND
                (u.is_test_user IS NULL OR u.is_test_user = 0) AND
                r.name IN('Agent') AND updated_by IS NULL
        "));

        return $agentPropertiesUpdatedNull;
    }

    public function getLeadsPerAuthor($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(l.id) AS created_leads
            FROM
                leads l
            JOIN lead_assignments la ON
                la.lead_id = l.id
            JOIN users u ON
                l.created_by = u.id
            JOIN model_has_roles mhr ON
                mhr.model_id = l.created_by
            JOIN roles r ON
                r.id = mhr.role_id
            WHERE
                l.deleted_at IS NULL AND
                la.user_id = u.id AND
                (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent')  AND
                
            ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(l.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(l.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(l.created_at) >= $mysqlTimeStatement";
        }
        $sql .= "
            GROUP BY
                u.id
        ";
        $agentLeadCreated = DB::select(DB::raw($sql));

        return $agentLeadCreated;
    }

    public function getAssignedLeads($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);

        $sql = "
            SELECT
                u.id, COUNT(l.id) AS assigned_leads
            FROM
                leads l
            JOIN users u ON
                l.created_by = u.id
            JOIN model_has_roles mhr ON
                mhr.model_id = l.created_by
            JOIN roles r ON
                r.id = mhr.role_id
            JOIN lead_assignments la ON
                la.lead_id = l.id
            WHERE
                l.created_by != la.user_id AND
                la.deleted_at IS NULL AND
                (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND
            ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(la.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(la.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(la.created_at) >= $mysqlTimeStatement";
        }
        $sql .= "
            GROUP BY
                la.id

        ";

        $agentLeadAssigned = DB::select(DB::raw($sql));

        return $agentLeadAssigned;
    }

    public function getLastAssignedLeads($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(l.id) AS reassigned_leads
            FROM
                leads l
            JOIN users u ON
                l.created_by = u.id
            JOIN model_has_roles mhr ON
                mhr.model_id = l.created_by
            JOIN roles r ON
                r.id = mhr.role_id
            JOIN lead_assignments la ON
                la.lead_id = l.id
            WHERE
                (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND
            ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(la.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(la.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(la.created_at) >= $mysqlTimeStatement";
        }
        $sql .= "
            GROUP BY
            la.id
            DESC LIMIT 1
        ";
        $agenLastLeadAssigned = DB::select(DB::raw($sql));

        return $agenLastLeadAssigned;
    }

    public function getLeadsFromWebsite($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(ac.id) AS website_leads
            FROM
                agent_contact ac
                JOIN properties p ON p.id = ac.listing_id
                JOIN users u ON p.created_by = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE 
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND
                    ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(ac.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(ac.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(ac.created_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY u.id
        ";

        $agentLeadCreatedWebsite = DB::select(DB::raw($sql));

        return $agentLeadCreatedWebsite;
    }

    public function getAssignedLeadsPerUser($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id as user_id, COUNT(la.id) AS assigned_leads
            FROM
                leads l
                JOIN lead_assignments la ON la.lead_id = l.id
                JOIN users u ON la.user_id = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND
                ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(la.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(la.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(la.created_at) >= $mysqlTimeStatement";
        }

        $sql .= " 
            GROUP BY u.id
        ";

        $agentLeadAssigned = DB::select(DB::raw($sql));

        return $agentLeadAssigned;
    }

    public function getReassignedLeadsPerUser($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id as user_id, COUNT(la.id) AS reassigned_leads
            FROM
                leads l
                JOIN lead_assignments la ON la.lead_id = l.id
                JOIN users u ON la.user_id = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    la.lead_id NOT IN (SELECT id FROM leads WHERE created_by = user_id) AND r.name IN('Agent') AND 
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND
                ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(la.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(la.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(la.created_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY u.id
        ";

        $agentReassignedLeadsPerUser = DB::select(DB::raw($sql));
        return $agentReassignedLeadsPerUser;
    }

    public function getLeadsPerUserByStatus($intervalType, $startDate = null, $userId = null, $leadStatus)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(l.id) AS leads_by_status_no
            FROM
                leads l
                JOIN lead_assignments la ON la.lead_id = l.id
                JOIN users u ON la.user_id = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    r.name IN('Agent') AND
                    l.lead_status_id = '$leadStatus' AND
                    la.deleted_at IS NULL AND
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(la.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(la.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(la.created_at) >= $mysqlTimeStatement";
        }

        $sql .= "
                GROUP BY u.id
        ";
        $sql = DB::raw($sql);
        $agentLeadsByStatus = DB::select(DB::raw($sql));
        return $agentLeadsByStatus;
    }

    public function getConvertedLeadsPerUser($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT 
                u.id, COUNT(l.id) AS converted_leads_no
            FROM leads l
            JOIN lead_x_status lxs ON l.id = lxs.lead_id
            JOIN users u ON lxs.created_by = u.id
            JOIN model_has_roles mhr ON mhr.model_id = u.id
            JOIN roles r ON r.id = mhr.role_id
            WHERE 
                r.name IN ('Agent')
                AND l.deleted_at IS NULL
                AND (u.is_test_user IS NULL OR u.is_test_user = 0)
                AND lxs.lead_initial_status_id IS NOT NULL
                AND lxs.lead_final_status_id IS NOT NULL
                AND lxs.lead_initial_status_id IN (1, 5, 10, 13, 21, 19)
                AND lxs.lead_final_status_id NOT IN (1, 5, 10, 13, 21, 19)
                AND lxs.deleted_at IS NULL
                AND ";

        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(lxs.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(lxs.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(lxs.created_at) >= $mysqlTimeStatement ";
        }
        $sql .= "GROUP BY u.id
        ";

        $convertedLeadsResult = DB::select(DB::raw($sql));
        return $convertedLeadsResult;
    }

    public function getDealsPerAgent($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);

        $rawSql = "
            SELECT
                u.id, u.name, COUNT(*) AS created_deals_no
            FROM
                deals d
                JOIN users u ON d.created_by = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND 
                ";

        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $rawSql .= "u.id = '" . $userId . "' AND DATE(d.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $rawSql .= "DATE(d.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $rawSql .= "DATE(d.created_at) >= $mysqlTimeStatement";
        }
        $rawSql .= "
            GROUP BY u.id
        ";
        $sql = DB::raw($rawSql);
        $dealsNo = DB::select($sql);

        return $dealsNo;
    }

    public function getCreatedLandlordsPerUser($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
        SELECT
            u.id as user_id, COUNT(*) AS landlords_no
        FROM
            contacts_landlords cl
            JOIN contacts c ON c.id = cl.contact_id
            JOIN users u ON c.created_by = u.id
            JOIN model_has_roles mhr ON
                mhr.model_id = u.id
            JOIN roles r ON
                r.id = mhr.role_id
            WHERE 
                (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND 
            ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(c.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(c.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(c.created_at) >= $mysqlTimeStatement";
        }
        $sql .= "
                GROUP BY u.id
            ";

        $dealsNo = DB::select(DB::raw($sql));

        return $dealsNo;
    }

    public function getTopSnapshotViews($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                count(iv.id) countNo, item_id
            FROM
                item_views iv
                JOIN properties p on iv.item_id = p.id
                JOIN users u ON p.created_by = u.id
            WHERE 
                operation_type = 'snapshot_view' AND
                (u.is_test_user IS NULL OR u.is_test_user = 0) AND 
            ";

        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(iv.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(iv.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(iv.updated_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY item_id
            ORDER BY countNo DESC
            LIMIT 10
        ";

        $snapshotViewsResultSet = DB::select(DB::raw($sql));
        return $snapshotViewsResultSet;
    }

    public function getContactsCreated($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(c.id) AS created_landlords
            FROM
                contacts c
            JOIN users u ON
                c.created_by = u.id
            JOIN model_has_roles mhr ON
                mhr.model_id = c.created_by
            JOIN roles r ON
                r.id = mhr.role_id
            WHERE
                (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND 
            ";

        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(c.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(c.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(c.created_at) >= $mysqlTimeStatement";
        }
        $sql .= " 
                GROUP BY u.id
            ";
        $agentLandlordsCreated = DB::select(DB::raw($sql));

        return $agentLandlordsCreated;
    }

    public function getAgentCommissionForClient($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, SUM(IF(d.client_commission_cashed LIKE 'STATUS_CASHED', COALESCE(d.commission_client, 0), 0) + IF(d.landlord_commission_cashed LIKE 'STATUS_CASHED', COALESCE(d.commission_landlord, 0), 0)) AS cashed_commission_client
            FROM
                deals d
                JOIN users u ON d.created_by = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND 
                ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(d.updated_at) >= $mysqlTimeStatement";
        }

        $sql .= " 
                GROUP BY u.id
            ";
        // echo $sql;
        // die;

        $agentCommissionForClient = DB::select(DB::raw($sql));

        return $agentCommissionForClient;
    }

    public function getAgentCommissionForLandlord($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, SUM(d.commission_landlord) AS cashed_commission_landlord
            FROM
                deals d
                JOIN users u ON d.created_by = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND 
                ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(d.updated_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY u.id
        ";

        $agentCommissionForLandlord = DB::select(DB::raw($sql));

        return $agentCommissionForLandlord;
    }


    public function getAgentCommissionListingAgent($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, SUM(d.listing_agent_shared_commission) AS listing_agent_shared_commission
            FROM
                deals d
                JOIN users u ON d.listing_agent_id = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND 
                ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(d.updated_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY u.id
        ";

        $agentCommissionListingAgent = DB::select(DB::raw($sql));

        return $agentCommissionListingAgent;
    }

    public function getAgentCommissionReferredListingAgent($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, SUM(d.referred_listing_agent_shared_commission) AS referred_listing_agent_shared_commission
            FROM
                deals d
                JOIN users u ON d.referred_listing_agent_id = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND 
                ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(d.updated_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY u.id
        ";

        $agentCommissionReferredListingAgent = DB::select(DB::raw($sql));

        return $agentCommissionReferredListingAgent;
    }

    public function getAgentCommissionClosingAgent($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, SUM(d.closing_agent_shared_commission) AS closing_agent_shared_commission
            FROM
                deals d
                JOIN users u ON d.closing_agent_id = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND 
                ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(d.updated_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY u.id
        ";

        $agentCommissionClosingAgent = DB::select(DB::raw($sql));

        return $agentCommissionClosingAgent;
    }

    public function getAgentCommissionReferredClosingAgent($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, SUM(d.referred_closing_agent_shared_commission) AS referred_closing_agent_shared_commission
            FROM
                deals d
                JOIN users u ON d.referred_closing_agent_id = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND 
                ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(d.updated_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY u.id
        ";

        $agentCommissionReferredClosingAgent = DB::select(DB::raw($sql));

        return $agentCommissionReferredClosingAgent;
    }

    public function getAgentCashIn($intervalType, $startDate = null, $userId = null, $endDate = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate, $endDate);
        $sql = "
            SELECT
                u.id,
                SUM(
                    CASE WHEN d.listing_agent_cash_in = 1 AND d.listing_agent_id = u.id AND (d.listing_agent_month BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ") THEN d.listing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.referred_listing_agent_cash_in = 1 AND d.referred_listing_agent_id = u.id AND (d.referred_listing_agent_month BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ") THEN d.referred_listing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.closing_agent_cash_in = 1 AND d.closing_agent_id = u.id AND (d.closing_agent_month BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ") THEN d.closing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.referred_closing_agent_cash_in = 1 AND d.referred_closing_agent_id = u.id AND (d.referred_closing_agent_month BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ") THEN d.referred_closing_agent_shared_commission ELSE 0 END
            ) AS cashed_commission
            FROM
                deals d
            JOIN 
                users u ON 
                (d.listing_agent_id = u.id OR
                d.referred_listing_agent_id = u.id OR
                d.closing_agent_id = u.id OR
                d.referred_closing_agent_id = u.id)
            JOIN 
                model_has_roles mhr ON mhr.model_id = u.id
            JOIN 
                roles r ON r.id = mhr.role_id
            WHERE
                (u.is_test_user IS NULL OR u.is_test_user = 0)
                AND r.name IN('Agent') ";

        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "AND u.id = '" . $userId . "' ";
            }
            $sql .= "AND (
                        (DATE(d.listing_agent_month) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ") OR
                        (DATE(d.referred_listing_agent_month) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ") OR
                        (DATE(d.closing_agent_month) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ") OR
                        (DATE(d.referred_closing_agent_month) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ")
                    ) ";
        }

        $sql .= "
            GROUP BY u.id
        ";
        $cashInDBResult = DB::select(DB::raw($sql));
        return $cashInDBResult;
    }

    public function getAgentAverageCashIn($intervalType, $startDate = null, $userId = null, $endDate = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate, $endDate);
        $sql = "
            SELECT
                u.id,
                -- Total cashed commission
                SUM(
                    CASE WHEN d.listing_agent_cash_in = 1 AND d.listing_agent_id = u.id AND (d.listing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}) THEN d.listing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.referred_listing_agent_cash_in = 1 AND d.referred_listing_agent_id = u.id AND (d.referred_listing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}) THEN d.referred_listing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.closing_agent_cash_in = 1 AND d.closing_agent_id = u.id AND (d.closing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}) THEN d.closing_agent_shared_commission ELSE 0 END +
                    CASE WHEN d.referred_closing_agent_cash_in = 1 AND d.referred_closing_agent_id = u.id AND (d.referred_closing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}) THEN d.referred_closing_agent_shared_commission ELSE 0 END
                ) AS cashed_commission,

                -- Count of deals where the agent got cashed commission
                COUNT(
                    DISTINCT CASE
                        WHEN (d.listing_agent_cash_in = 1 AND d.listing_agent_id = u.id AND (d.listing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}))
                        OR (d.referred_listing_agent_cash_in = 1 AND d.referred_listing_agent_id = u.id AND (d.referred_listing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}))
                        OR (d.closing_agent_cash_in = 1 AND d.closing_agent_id = u.id AND (d.closing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}))
                        OR (d.referred_closing_agent_cash_in = 1 AND d.referred_closing_agent_id = u.id AND (d.referred_closing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}))
                        THEN d.id
                        ELSE NULL
                    END
                ) AS deal_count,

                -- Average cashed commission
                (
                    SUM(
                        CASE WHEN d.listing_agent_cash_in = 1 AND d.listing_agent_id = u.id AND (d.listing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}) THEN d.listing_agent_shared_commission ELSE 0 END +
                        CASE WHEN d.referred_listing_agent_cash_in = 1 AND d.referred_listing_agent_id = u.id AND (d.referred_listing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}) THEN d.referred_listing_agent_shared_commission ELSE 0 END +
                        CASE WHEN d.closing_agent_cash_in = 1 AND d.closing_agent_id = u.id AND (d.closing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}) THEN d.closing_agent_shared_commission ELSE 0 END +
                        CASE WHEN d.referred_closing_agent_cash_in = 1 AND d.referred_closing_agent_id = u.id AND (d.referred_closing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}) THEN d.referred_closing_agent_shared_commission ELSE 0 END
                    ) /
                    NULLIF(
                        COUNT(
                            DISTINCT CASE
                                WHEN (d.listing_agent_cash_in = 1 AND d.listing_agent_id = u.id AND (d.listing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}))
                                OR (d.referred_listing_agent_cash_in = 1 AND d.referred_listing_agent_id = u.id AND (d.referred_listing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}))
                                OR (d.closing_agent_cash_in = 1 AND d.closing_agent_id = u.id AND (d.closing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}))
                                OR (d.referred_closing_agent_cash_in = 1 AND d.referred_closing_agent_id = u.id AND (d.referred_closing_agent_month BETWEEN '{$mysqlTimeStatement[0]}' AND {$mysqlTimeStatement[1]}))
                                THEN d.id
                                ELSE NULL
                            END
                        ), 0
                    )
                ) AS avg_cashed_commission

            FROM
                deals d
            JOIN 
                users u ON 
                (d.listing_agent_id = u.id OR
                d.referred_listing_agent_id = u.id OR
                d.closing_agent_id = u.id OR
                d.referred_closing_agent_id = u.id)
            JOIN 
                model_has_roles mhr ON mhr.model_id = u.id
            JOIN 
                roles r ON r.id = mhr.role_id
            WHERE
                (u.is_test_user IS NULL OR u.is_test_user = 0)
                AND r.name IN ('Agent')";

        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "AND u.id = '" . $userId . "' ";
            }
            $sql .= "AND (
                        (DATE(d.listing_agent_month) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ") OR
                        (DATE(d.referred_listing_agent_month) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ") OR
                        (DATE(d.closing_agent_month) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ") OR
                        (DATE(d.referred_closing_agent_month) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . ")
                    ) ";
        }

        $sql .= "
            GROUP BY u.id
        ";
        $cashInDBResult = DB::select(DB::raw($sql));
        return $cashInDBResult;
    }


    public function getAgentApprovedDeals($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate = null);
        $sql = "
            SELECT
                u.id, COUNT(d.id) AS approved_deals_no
            FROM
                deals d
                JOIN users u ON d.created_by = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND 
                    d.deal_status = 'approved' AND ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(d.updated_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(d.updated_at) >= $mysqlTimeStatement";
        }
        $sql .= "
            GROUP BY u.id
        ";
        $sql = DB::raw($sql);

        $agentApprovedDeals = DB::select(DB::raw($sql));
        return $agentApprovedDeals;
    }

    public function getVisitScheduledPerUser($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(l.id) AS leads_visit_scheduled
            FROM
                leads l
            JOIN reminders rem ON rem.object_id = l.id AND rem.object_type = 'leads'
            JOIN users u ON rem.created_by = u.id
            JOIN model_has_roles mhr ON
                mhr.model_id = u.id
            JOIN roles r ON
                r.id = mhr.role_id
            WHERE
                r.name IN('Agent') AND
                rem.object_id = l.id AND
                rem.deleted_at IS NULL AND
                rem.reminder_type = 'VIEWING_SCHEDULED' AND
                (u.is_test_user IS NULL OR u.is_test_user = 0) AND ";

        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(rem.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(rem.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(rem.created_at) >= $mysqlTimeStatement";
        }

        $sql .= "
                GROUP BY u.id
        ";

        $agentLeadsByVisitScheduled = DB::select(DB::raw($sql));

        return $agentLeadsByVisitScheduled;
    }

    public function getRatingByAgent($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(ure.id) AS agent_rating
            FROM
                user_rating_entries ure
            JOIN users u ON
                ure.user_id = u.id
            JOIN model_has_roles mhr ON
                mhr.model_id = u.id
            JOIN roles r ON
                r.id = mhr.role_id
            WHERE
                (u.is_test_user IS NULL OR u.is_test_user = 0)
                AND ure.approved_at IS NOT NULL
                AND r.name IN('Agent') AND
            ";

        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(ure.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(ure.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(ure.created_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY
                u.id
        ";
        $agentsRating = DB::select(DB::raw($sql));

        return $agentsRating;
    }

    public function getVerifiedListings($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(p.id) AS verified_listings
            FROM
                properties p
            JOIN contacts c ON
                p.contact_id = c.id
            JOIN users u ON
                p.created_by = u.id
            JOIN model_has_roles mhr ON
                mhr.model_id = p.created_by
            JOIN roles r ON
                r.id = mhr.role_id
            WHERE
                (u.is_test_user IS NULL OR u.is_test_user = 0)
                AND c.verified = 1
                AND c.deleted_at IS NULL
                AND r.name IN('Agent') AND p.deleted_at IS NULL AND
            ";

        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(p.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(p.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(p.created_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY
                u.id
        ";
        $verifiedListings = DB::select(DB::raw($sql));

        return $verifiedListings;
    }

    public function getAgentThatReassignLeads($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id as user_id, COUNT(lr.id) AS agent_reassigned_leads
            FROM
                leads l
                JOIN lead_reassignations lr ON lr.lead_id = l.id
                JOIN users u ON lr.user_id = u.id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE
                    r.name IN('Agent') AND 
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND
                ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(lr.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(lr.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(lr.created_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY u.id
        ";

        $agentReassignedLeads = DB::select(DB::raw($sql));
        return $agentReassignedLeads;
    }

    public function getLeadsFromWebsitePerUser($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(la.user_id) AS website_leads_per_user
            FROM
                leads l
                JOIN lead_assignments la ON la.lead_id = l.id
                JOIN users u ON u.id = la.user_id
                JOIN model_has_roles mhr ON
                    mhr.model_id = u.id
                JOIN roles r ON
                    r.id = mhr.role_id
                WHERE 
                    l.platform_from = 1 AND
                    la.lead_id = l.id AND
                    la.user_id = u.id AND
                    l.deleted_at IS NULL AND
                    (u.is_test_user IS NULL OR u.is_test_user = 0) AND r.name IN('Agent') AND
                    ";
        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(l.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(l.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(l.created_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY u.id
        ";

        $agentLeadCreatedWebsitePerUser = DB::select(DB::raw($sql));

        return $agentLeadCreatedWebsitePerUser;
    }

    public function getSelfGeneratedLeadsPerUser($intervalType, $startDate = null, $userId = null)
    {
        $mysqlTimeStatement = $this->translateIntervalTypeToMysqlStatement($intervalType, $startDate);
        $sql = "
            SELECT
                u.id, COUNT(DISTINCT l.id) AS generated_leads_per_user
            FROM
                leads l
                LEFT JOIN lead_assignments la ON la.lead_id = l.id
                JOIN users u ON u.id = la.user_id OR u.id = l.created_by
                JOIN model_has_roles mhr ON mhr.model_id = u.id
                JOIN roles r ON r.id = mhr.role_id
                JOIN lead_status ls ON l.lead_status_id = ls.id
            WHERE
                (l.created_by = u.id OR (l.platform_from = 1 AND la.user_id = u.id)) AND
                l.deleted_at IS NULL AND
                (u.is_test_user IS NULL OR u.is_test_user = 0) AND
                ls.name NOT IN ('COLD', 'NOT_QUALIFIED', 'CLOSED_LOSE', 'GHOSTED', 'INACTIVE') AND 
                r.name IN ('Agent') AND
        ";


        if (!is_null($startDate) && is_array($mysqlTimeStatement)) {
            if (!is_null($userId)) {
                $sql .= "u.id = '" . $userId . "' AND DATE(l.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            } else {
                $sql .= "DATE(l.created_at) BETWEEN '" . $mysqlTimeStatement[0] . "' AND " . $mysqlTimeStatement[1] . " ";
            }
        } else {
            $sql .= "DATE(l.created_at) >= $mysqlTimeStatement";
        }

        $sql .= "
            GROUP BY u.id
        ";

        $selfGeneratedLeadsPerUser = DB::select(DB::raw($sql));

        return $selfGeneratedLeadsPerUser;
    }

    public function getLeadsPerCategoriesPerPeriod($startDate, $endDate)
    {
        $leads = $this->getLeadsPerPeriod($startDate, $endDate);
        $residentials = [1, 2, 14, 15, 16, 17, 18];
        $nonResidentials = [3, 7, 8, 9, 10, 11, 12, 13, 19, 20, 21, 22, 23, 24, 25];

        $stats = [
            'total' => $leads->count(),
            'rent_residential' => 0,
            'rent_commercial' => 0,
            'rent_other' => 0,
            'sale' => 0,
            'source_of_leads' => []
        ];

        $leadPlatforms = [];
        foreach ($leads as $l) {
            if (!isset($leadPlatforms[$l->platform_from_name])) {
                $leadPlatforms[$l->platform_from_name] = 0;
            }
            $leadPlatforms[$l->platform_from_name]++;
            $request = $l->leads_request;
            $jsonData = json_decode($request);
            if (isset($jsonData->t)) {
                if ($l->filter_operation_type == 'rent') {
                    $isResidential = false;
                    $isCommercial = false;
                    if (is_array($jsonData->t)) {
                        foreach ($residentials as $rId) {
                            if (in_array($rId, $jsonData->t)) {
                                $isResidential = true;
                            }
                        }
                        foreach ($nonResidentials as $rId) {
                            if (in_array($rId, $jsonData->t)) {
                                $isCommercial = true;
                            }
                        }
                    }
                    if ($isResidential) {
                        $stats['rent_residential']++;
                    }
                    if ($isCommercial) {
                        $stats['rent_commercial']++;
                    }
                    if (!$isResidential && !$isCommercial) {
                        $stats['rent_other']++;
                    }
                } else if ($l->filter_operation_type == 'sale') {
                    $stats['sale']++;
                }
            }
        }

        $stats['source_of_leads'] = $leadPlatforms;

        return $stats;
    }

    private function getLeadsPerPeriod($startDate, $endDate)
    {
        return DB::table('leads as l')
            ->leftJoin('lead_sources as ls', 'ls.id', '=', 'l.platform_from')
            ->whereBetween('l.created_at', [$startDate, $endDate])
            ->select(['l.filter_operation_type', 'l.filter_property_type', 'l.platform_from', 'l.leads_request', DB::raw('ls.name as platform_from_name')])
            ->get();
    }

    private function translateIntervalTypeToMysqlStatement($intervalType, $startDate = null, $endDate = null)
    {
        if (!is_null($startDate)) {
            if (!is_null($endDate)) {
                return [
                    $startDate->format('Y-m-d'),
                    "'".$endDate->format('Y-m-d')."'",
                ];
            }
            return [
                $startDate->format('Y-m-d'),
                'DATE_SUB(DATE_ADD("' . $startDate->format('Y-m-d') . '", INTERVAL 1 MONTH), INTERVAL 1 DAY)'
            ];
        } else {
            if ($intervalType === 'monthly') {
                return 'DATE_FORMAT(NOW(), "%Y-%m-01 00:00:00")';
            } else if ($intervalType === "weekly") {
                return 'DATE_ADD(CURRENT_DATE(), INTERVAL(-DAYOFWEEK(CURRENT_DATE())) DAY)';
            } else if ($intervalType === "quarterly") {
                return 'last_day(now()) + interval 1 day - interval 3 month';
            } else {
                // yearly
                return 'DATE_ADD(CURRENT_DATE(), INTERVAL -1 YEAR)';
            }
        }
        // return $intervalType === 'monthly' ? 'DATE_FORMAT(NOW(), "%Y-%m-01 00:00:00")' : 'DATE_ADD(CURRENT_DATE(), INTERVAL(-DAYOFWEEK(CURRENT_DATE())) DAY)';
    }

    public function getAgentLeadsStats()
    {
        $data = DB::table('lead_assignments as la')
            ->join('users as u', 'la.user_id', '=', 'u.id')
            ->join('leads as l', 'la.lead_id', '=', 'l.id')
            ->select(
                'u.name as agent_name',
                DB::raw("SUM(CASE WHEN l.rating IN ('Hot', 'A') THEN 1 ELSE 0 END) as A"),
                DB::raw("SUM(CASE WHEN l.rating IN ('Warm', 'B') THEN 1 ELSE 0 END) as B"),
                DB::raw("SUM(CASE WHEN l.rating IN ('Luke Warm', 'C') THEN 1 ELSE 0 END) as C"),
                DB::raw("SUM(CASE WHEN l.rating IN ('Cool', 'D') THEN 1 ELSE 0 END) as D"),
                DB::raw("SUM(CASE WHEN l.rating IN ('Cold', 'E') THEN 1 ELSE 0 END) as E"),
                DB::raw("SUM(CASE WHEN l.rating IN ('F') THEN 1 ELSE 0 END) as F"),
                DB::raw("SUM(CASE WHEN l.rating IN ('X') THEN 1 ELSE 0 END) as X"),
                DB::raw("SUM(CASE WHEN l.rating = '' THEN 1 ELSE 0 END) as NonRated")
            )
            ->whereNull('la.deleted_at')
            ->whereNull('l.deleted_at')
            ->whereNull('u.deleted_at')
            ->groupBy('u.name')
            ->orderBy('u.name')
            ->get();


        return $data;
    }

    function generateAgentDailyTasksReport($date = null)
    {
        // Default date to current date if not provided
        $date = $date ?? Carbon::today()->toDateString();

        $report = DB::table('tasks')
            ->leftJoin('leads', 'tasks.object_id', '=', 'leads.id')
            ->leftJoin('lead_assignments', 'leads.id', '=', 'lead_assignments.lead_id')
            ->leftJoin('users as creators', 'tasks.created_by', '=', 'creators.id')
            ->leftJoin('users as assignees', 'lead_assignments.user_id', '=', 'assignees.id')
            ->selectRaw("
                CASE 
                    WHEN tasks.created_by != 1 THEN creators.name 
                    ELSE assignees.name 
                END as agent_name,
                SUM(CASE WHEN DATE(tasks.created_at) = ? THEN 1 ELSE 0 END) as tasks_created_today,
                SUM(CASE WHEN DATE(tasks.completion_date) = ? THEN 1 ELSE 0 END) as tasks_completed_today,
                SUM(CASE WHEN tasks.status != 'completed' AND DATE(tasks.due_date) < ? THEN 1 ELSE 0 END) as overdue_tasks
            ", [$date, $date, $date])
            ->groupBy('agent_name')
            ->get();

        return $report;
    }

    public function getDailyShortStats(\DateTime $date)
    {
        $listings = Property::whereDate('created_at', $date)->get();
        $leads = Lead::with(['contact', 'leadSource'])->whereDate('created_at', $date)->get();
        $deals = Deal::with(['property', 'client', 'owner', 'listingAgent'])->whereDate('created_at', $date)->get();

        return [
            'date' => $date,
            'listings' => $listings,
            'leads' => $leads,
            'deals' => $deals
        ];
    }
}
