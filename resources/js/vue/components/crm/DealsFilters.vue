<template>
    <div class="row">
        <div class="col-12 text-right mb-2 filter-deal">
            <a href="javascript:void(0)" @click="resetState()">Reset filters</a>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-3 filter-deal">
            <label class="text-gray-50 mb-1">Sale/Rent</label>
            <select v-model="state.dealType" class="form-select">
                <option value="">Please select</option>
                <option
                    v-for="dealType in typeArr"
                    :key="dealType.key"
                    :value="dealType.key"
                >
                    {{ dealType.value }}
                </option>
            </select>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-3 filter-deal">
            <label class="text-gray-50 mb-1">Agent</label>
            <select v-model="state.agent" class="form-select">
                <option value="">Please select</option>
                <option
                    v-for="agent in options.agents"
                    :key="agent.key"
                    :value="agent.key"
                >
                    {{ agent.value }}
                </option>
            </select>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-3 filter-deal">
            <label class="text-gray-50 mb-1">Team Leader</label>
            <select v-model="state.teamLeaderAgents" class="form-select">
                <option value="">Please select</option>
                <option
                    v-for="agent in options.teamLeaderAgents"
                    :key="agent.key"
                    :value="agent.key"
                >
                    {{ agent.value }}
                </option>
            </select>
        </div>
        <div class="col-xs-12 col-sm-6 col-lg-3 mb-3">
            <label class="text-gray-50 mb-1">Date Created</label>
            <div class="input-group">
                <input
                    type="text"
                    class="form-control"
                    id="createdAtRangeInput"
                />
                <button
                    @click="clearDaterange('createdAtRangeInput')"
                    class="btn btn-xs btn-outline-secondary"
                >
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-3 filter-deal">
            <label class="text-gray-50 mb-1">End Month</label>
            <select v-model="state.dealEndMonth" class="form-select">
                <option value="">Please select</option>
                <option
                    v-for="month in monthsArr"
                    :key="month.key"
                    :value="month.key"
                >
                    {{ month.value }}
                </option>
            </select>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-3 filter-deal">
            <label class="text-gray-50 mb-1">End Year</label>
            <select v-model="state.dealEndYear" class="form-select">
                <option value="">Please select</option>
                <option v-for="year in years" :key="year" :value="year">
                    {{ year }}
                </option>
            </select>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-3 filter-deal">
            <label class="text-gray-50 mb-1">Listing (Ref No)</label>
            <input v-model="state.refNo" class="form-control" type="text" />
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-3 filter-lead">
            <label class="text-gray-50 small mb-0">Lead source</label>
            <FGVirtualSelect :selectorClassName="leadSourceSelectorClass" :class="{
                'vscomp-ele': true,
                [leadSourceSelectorClass]: true,
            }" :options="options.leadSources" :config="leadSourcesSelectorConfig"
                :selectedValue="state.leadSource" @change="onLeadSourceSelectChange($event)" />
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-3 filter-deal">
            <label class="text-gray-50 small mb-0">Nationality</label>
            <FGVirtualSelect :selectorClassName="nationalitySelectorClass" :class="{
                'vscomp-ele': true,
                [nationalitySelectorClass]: true,
            }" :options="options.nationalities" :config="nationalitySelectorConfig"
                :selectedValue="state.nationalities" @change="onNationalitySelectChange($event)" />
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-3 filter-deal">
            <label class="text-gray-50 mb-1">Age</label>
            <select v-model="state.ageRange" class="form-select">
                <option value="">Please select</option>
                <option value="20-40">20-40</option>
                <option value="40-60">40-60</option>
                <option value="60+">60+</option>
            </select>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-3 filter-deal">
            <label class="text-gray-50 mb-1">Interest</label>
            <select v-model="state.interest" class="form-select" disabled>
                <option value="">Please select</option>
                <option value="Investor">Investor</option>
                <option value="Living">Living</option>
            </select>
        </div>
        <div class="col-xs-12 col-sm-4 col-lg-2 mb-3 filter-deal">
            <label class="text-gray-50 mb-1">International</label>
            <div class="form-check">
                <input
                    v-model="state.international"
                    class="form-check-input"
                    type="checkbox"
                    id="internationalCheck"
                    disabled
                />
                <label class="form-check-label" for="internationalCheck">
                    International buyers
                </label>
            </div>
        </div>
    </div>
</template>

<script>
import { onMounted, ref, watch } from "vue";
import { debounce } from "../../../helpers/all-kind";
import FGVirtualSelect from "../generic/FGVirtualSelect.vue";

const currentYear = new Date().getFullYear();

const initialState = {
    dealType: "",
    agent: "",
    teamLeaderAgents: "",
    year: `${currentYear}`,
    refNo: "",
    leadSource: "",
    dealEndMonth: "",
    dealEndYear: "",
    createdAtFrom: "",
    createdAtTo: "",
    nationalities: [],
    ageRange: "",
    interest: "",
    international: false,
};

// Helper function to generate unique class names for virtual selects
const virtualSelectClassName = () => {
    return "r" + (Math.random() + 1).toString(36).substring(7);
};

export default {
    name: "DealsFilters",
    components: {
        FGVirtualSelect,
    },
    setup() {
        const options = ref({
            dealType: [],
            agents: [],
            teamLeaderAgents: [],
            months: [],
            years: [],
            leadSources: [],
            nationalities: [],
        });

        // Generate unique class names for virtual selects
        const leadSourceSelectorClass = virtualSelectClassName();
        const nationalitySelectorClass = virtualSelectClassName();

        // Virtual select configurations
        const leadSourcesSelectorConfig = {
            multiple: false,
            search: true,
            maxHeight: '32px',
            dropboxWrapper: 'form-select-sm'
        };

        const nationalitySelectorConfig = {
            multiple: true,
            search: true,
            showValueAsTags: true,
            maxHeight: '32px',
            dropboxWrapper: 'form-select-sm'
        };

        // Change handlers for virtual selects
        const onLeadSourceSelectChange = (leadSource) => {
            state.value.leadSource = leadSource;
        };

        const onNationalitySelectChange = (nationalities) => {
            state.value.nationalities = nationalities;
        };

        const state = ref({ ...initialState });
        const doSearch = debounce((availableFilters) => {
            if (typeof onDealsFiltersParamsChange != "undefined") {
                onDealsFiltersParamsChange(availableFilters);
            }
        });
        const monthsArr = [
            {
                key: "01",
                value: "January",
            },
            {
                key: "02",
                value: "February",
            },
            {
                key: "03",
                value: "March",
            },
            {
                key: "04",
                value: "April",
            },
            {
                key: "05",
                value: "May",
            },
            {
                key: "06",
                value: "June",
            },
            {
                key: "07",
                value: "July",
            },
            {
                key: "08",
                value: "August",
            },
            {
                key: "09",
                value: "September",
            },
            {
                key: "10",
                value: "October",
            },
            {
                key: "11",
                value: "November",
            },
            {
                key: "12",
                value: "December",
            },
        ];

        const typeArr = [
            {
                key: "SALE",
                value: "Sale",
            },
            {
                key: "RENTAL",
                value: "Rent",
            },
        ];

        const years = [];
        for (let i = currentYear - 5; i <= currentYear; i++) {
            years.push(i);
        }

        watch(
            () => ({ ...state.value }),
            (currentState) => {
                // if operation type changed, reset the prices value
                localStorage.setItem(
                    "dealsTableFilters_",
                    JSON.stringify(currentState)
                );
                const availableFilters = {};
                if (!!state.value.dealType) {
                    availableFilters.dealType = state.value.dealType;
                }
                if (!!state.value.createdAtFrom) {
                    availableFilters.createdAtFrom = state.value.createdAtFrom;
                }
                if (!!state.value.createdAtTo) {
                    availableFilters.createdAtTo = state.value.createdAtTo;
                }
                if (!!state.value.agent) {
                    availableFilters.agentId = state.value.agent;
                }
                if (!!state.value.teamLeaderAgents) {
                    availableFilters.teamLeaderId = state.value.teamLeaderAgents;
                }
                if (!!state.value.dealEndMonth) {
                    availableFilters.dealEndMonth = state.value.dealEndMonth;
                }
                if (!!state.value.dealEndYear) {
                    availableFilters.dealEndYear = state.value.dealEndYear;
                }
                if (!!state.value.refNo) {
                    availableFilters.refNo = state.value.refNo;
                }
                if (!!state.value.leadSource) {
                    availableFilters.leadSource = state.value.leadSource;
                }
                if (!!state.value.nationalities && state.value.nationalities.length > 0) {
                    availableFilters.nationalities = state.value.nationalities;
                }
                if (!!state.value.ageRange) {
                    availableFilters.ageRange = state.value.ageRange;
                }
                if (!!state.value.interest) {
                    availableFilters.interest = state.value.interest;
                }
                if (state.value.international) {
                    availableFilters.international = state.value.international;
                }
                doSearch(availableFilters);
            }
        );

        const resetState = () => {
            state.value = { ...initialState };
            $("#createdAtRangeInput").val("");
        };

        const dateRangeMap = {
            createdAtRangeInput: {
                from: "createdAtFrom",
                to: "createdAtTo",
            },
        };

        const clearDaterange = (daterangeId) => {
            $(`#${daterangeId}`).val("");
            state.value[dateRangeMap[daterangeId].from] = null;
            state.value[dateRangeMap[daterangeId].to] = null;
        };

        onMounted(() => {
            const currentStoredFilters =
                localStorage.getItem("dealsTableFilters_");
            let createdAtFrom = null;
            let createdAtTo = null;
            if (!!currentStoredFilters) {
                const persistedFilters = JSON.parse(currentStoredFilters);
                state.value = { ...persistedFilters };
                createdAtFrom = persistedFilters.createdAtFrom ?? "";
                createdAtTo = persistedFilters.createdAtTo ?? "";
            } else {
                resetState();
            }

            setTimeout(() => {
                options.value.agents = agents.map((agent) => {
                    return {
                        key: agent.id,
                        value: agent.selectName,
                    };
                });
                options.value.teamLeaderAgents = teamLeaderAgents.map((agent) => {
                    return {
                        key: agent.id,
                        value: agent.name,
                    };
                });
                if (typeof leadSources != "undefined") {
                    options.value.leadSources = leadSources.map((item) => {
                        return {
                            value: item.value,
                            label: item.label,
                        };
                    });
                }

                // Load nationalities
                if (typeof request !== "undefined") {
                    request("/api/nationality").then((nationalityData) => {
                        options.value.nationalities = nationalityData.map((item) => ({
                            value: item.key,
                            label: item.value,
                        }));
                    }).catch((error) => {
                        console.error('Failed to load nationalities:', error);
                    });
                }
            });
            setTimeout(() => {
                const configObj = {
                    opens: "left",
                    autoApply: true,
                }
                if(createdAtFrom) {
                    configObj.startDate = new Date(Date.parse(createdAtFrom));
                }
                if(createdAtTo) {
                    configObj.endDate = new Date(Date.parse(createdAtTo));
                }

                $("#createdAtRangeInput").daterangepicker(
                    configObj,
                    function (start, end) {
                        state.value.createdAtFrom =
                            start.format("YYYY-MM-DD");
                        state.value.createdAtTo =
                            end.format("YYYY-MM-DD");
                    }
                );
                if(!createdAtFrom && !createdAtTo) {
                    $("#createdAtRangeInput").val("");
                }
            }, 500);
        });

        return {
            typeArr,
            options,
            state,
            monthsArr,
            years,
            resetState,
            clearDaterange,
            leadSourceSelectorClass,
            nationalitySelectorClass,
            leadSourcesSelectorConfig,
            nationalitySelectorConfig,
            onLeadSourceSelectChange,
            onNationalitySelectChange,
        };
    },
};
</script>

<style lang="scss"></style>
