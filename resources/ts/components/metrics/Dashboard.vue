<template>
  <div class="dashboard">
    <div class="d-flex justify-content-between">
      <h2 class="ps-3">Performance dashboard
      </h2>
      <div class="float-end d-flex justifty-content-end align-items-center gap-2">
        <button :disabled="!isCusomizableMode" class="btn btn-outline-secondary btn-sm" data-bs-toggle="modal"
          data-bs-target="#widgetModal"><i class="bi bi-plus"></i> Widget selection</button>
        <div class="form-check form-switch">
          <label class="form-check-label" for="customizationOn">Enable customization</label>
          <input v-model="isCusomizableMode" class="form-check-input" type="checkbox" role="switch"
            id="customizationOn">

        </div>
      </div>
    </div>
    <div class="mx-2">
      <div class="alert alert-warning">Please note that this feature is in continuing progress, so there might be
        errors. Please report any issues to the development team.</div>
    </div>

    <GridLayout v-model:layout="userWidgetsRef" :is-draggable="isCusomizableMode" :is-resizable="isCusomizableMode"
      :responsive="true" :col-num="12" :row-height="100" :vertical-compact="true">
      <GridItem v-for="userWidgetConfig in userWidgetsRef" :key="userWidgetConfig.i" class="l-item" :static="false"
        :x="userWidgetConfig.x" :y="userWidgetConfig.y" :w="userWidgetConfig.w" :h="userWidgetConfig.h"
        @moved="(i, newX, newY) => handleDragStop(i, newX, newY, userWidgetsRef)" :i="userWidgetConfig.i">
        <div class="l-item-slot card rounded shadow-xs p-2" style="height: 100%;">
          <component :is="ComponentsLib[userWidgetConfig.widget.data.type]" v-if="userWidgetConfig.widget?.data?.type"
            :userWidgetConfig="userWidgetConfig" source="userWidgets">
          </component>
        </div>
      </GridItem>
    </GridLayout>
  </div>
  <!-- Modal -->
  <div id="widgetModal" class="modal fade" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">

        <!-- Modal Header -->
        <div class="modal-header d-flex justify-content-between">
          <h5 class="modal-title">Widgets</h5>
          <input type="text" class="form-control w-50 float-end" placeholder="Search..." v-model="searchQuery">
          <button type="button" class="btn-close m-0" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <!-- Modal Body -->
        <div class="modal-body">
          <div class="container">
            <div class="row">
              <div class="col-md-4 mb-3" v-for="item in filteredWidgets">
                <div class="position-relative border rounded p-3 shadow-sm"
                  :class="{ 'border-3 border-success': item.user_widgets.length !== 0 }">
                  <button v-if="item.user_widgets.length === 0"
                    class="btn btn-success position-absolute top-0 end-0 m-2 p-0 d-flex align-items-center justify-content-center"
                    style="width: 24px; height: 24px; border-radius: 50%; font-size: 16px;"
                    @click="addWidget(item.id)">+</button>
                  <button v-if="item.user_widgets.length !== 0"
                    class="btn btn-danger position-absolute top-0 end-0 m-2 p-0 d-flex align-items-center justify-content-center"
                    style="width: 24px; height: 24px; border-radius: 50%; font-size: 16px;"
                    @click="removeWidget(item.id)">-</button>
                  <div>
                    {{ item.name }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>

      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
declare const request: Function;
import { ref, onMounted, markRaw, computed } from "vue";
import BarChart from "./widgets/BarWidget.vue"
import LineChart from "./widgets/LineWidget.vue"
import { GridLayout, GridItem } from "vue3-grid-layout";
import ContainerWidget from "./widgets/ContainerWidget.vue";
import { BEUserWidget, BEWidget, UserWidgetConfig, WidgetDTO } from "./models";
import NumberContainerWidget from "./widgets/NumberContainerWidget.vue";
import PieContainerWidget from "./widgets/PieContainerWidget.vue";
import BarContainerWidget from "./widgets/BarContainerWidget.vue";
import TreemapContainerWidget from "./widgets/TreemapContainerWidget.vue";
import DynamicContainer from "./widgets/DynamicContainerWidget.vue";

const userWidgetsRef = ref([]);
const unselectedWidgets = ref([]);
const searchQuery = ref('');
const isCusomizableMode = ref(false);

const ComponentsLib = markRaw({
  LineChart,
  BarChart,
  Container: ContainerWidget,
  NumberContainer: NumberContainerWidget,
  PieContainer: PieContainerWidget,
  BarContainer: BarContainerWidget,
  TreemapContainer: TreemapContainerWidget,
  DynamicContainer: DynamicContainer,
})

let currentX = 0;
let currentY = 0;
let maxRowHeight = 0;
const cols = 12;

onMounted(async () => {
  getUserWidgets();
  getUnselectedWidgets();
});

function getUserWidgets() {
  request("/api/metrics/dashboard/widgets").then((data: any) => {
    const userWidgets = formUserWidgetDTOs(data);
    userWidgets.forEach((widget: UserWidgetConfig, index: number) => {
      if (widget.w ?? 0 > maxRowHeight) {
        maxRowHeight = widget.w;
      }
      if (currentX + widget.w > cols) {
        currentX = 0;
        currentY = maxRowHeight;
      }

      if (widget.x == null) {
        widget.x = currentX;
        widget.y = currentY;
      }

      widget.w = widget.w ?? 0;
      widget.h = widget.h ?? 0;
      widget.i = index;

      currentX += widget.w;
    });
    userWidgetsRef.value = userWidgets;
    // console.log(userWidgetsRef.value)
  }).catch((error: any) => {
    console.error("Error on loading widgets:", error);
  })
}

const formUserWidgetDTOs = (userWidgets: Array<BEUserWidget>): Array<UserWidgetConfig> => {
  const mappedWidgets = userWidgets.map(beUserWidget => formUserWidgetDTO(beUserWidget));
  return mappedWidgets;
}

const formWidgetDTO = (beWidget: BEWidget): WidgetDTO => {
  const object: WidgetDTO = {
    data: {
      code: beWidget.code,
      config: beWidget.config,
      id: beWidget.id,
      min_height: beWidget.min_height,
      min_width: beWidget.min_width,
      name: beWidget.name,
      type: beWidget.type,
      widgets: (beWidget.widgets ?? []).map(formWidgetDTO)
    },
    state: {

    }
  }
  return object;
}

const formUserWidgetDTO = (beUserWidget: BEUserWidget): UserWidgetConfig => {

  const object: UserWidgetConfig = {
    id: beUserWidget.id,
    filters: beUserWidget.filters || {},
    w: beUserWidget.widget.min_width,
    h: beUserWidget.widget.min_height,
    i: beUserWidget.position,
    x: beUserWidget.x,
    y: beUserWidget.y,
    code: beUserWidget.widget.code,
    widget: formWidgetDTO(beUserWidget.widget),
  }

  return object;
}

function getUnselectedWidgets() {
  request("/api/metrics/dashboard/get-unselected-widgets").then((data: any) => {
    unselectedWidgets.value = data;
  }).catch((error: any) => {
    console.error("Error on loading unselected widgets:", error);
  })
}

const sortLayout = (layout) => {
  return layout
    .sort((a, b) => a.y - b.y || a.x - b.x)
    .map((item, index) => ({ position: index + 1, id: item.id, x: item.x, y: item.y }));
};

const handleDragStop = (i, newX, newY, layout) => {
  console.log("Drag stopped:", i, newX, newY, layout);
  request(`/api/metrics/dashboard/save-new-layout`, {
    method: "POST",
    body: { layout: sortLayout(layout) },
    headers: {
      "Content-Type": "application/json",
    },
  }).then(() => {
    console.log("Layout saved successfully!");
  }).catch((error: any) => {
    console.error("Error on saving layout:", error);
  });
};

const filteredWidgets = computed(() => {
  return unselectedWidgets.value.filter(widget =>
    widget.name.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

const addWidget = (widgetId: any): void => {
  request("/api/metrics/dashboard/add-user-widget/" + widgetId).then(() => {
    getUserWidgets();
    getUnselectedWidgets();
  }).catch((error: any) => {
    console.error("Error on adding unselected widget:", error);
  })
};
const removeWidget = (widgetId: any): void => {
  request("/api/metrics/dashboard/remove-user-widget/" + widgetId).then(() => {
    getUserWidgets();
    getUnselectedWidgets();
  }).catch((error: any) => {
    console.error("Error on removing unselected widget:", error);
  })
};
</script>

<style scoped>
.dashboard {
  padding: 20px;
  background: #f5f5f5;
}
</style>