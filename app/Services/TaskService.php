<?php

namespace App\Services;

use Carbon\Carbon;
use DB;
use Illuminate\Support\Facades\DB as FacadesDB;

class TaskService
{
    public function getQueriedTasks($viewType, $offset, $limit, $orderColumn, $orderDirection)
    {
        // Validate and sanitize order column and direction
        $allowedColumns = ['subject', 'due_date', 'status', 'lead.name']; // Add other valid columns as needed
        if (!in_array($orderColumn, $allowedColumns)) {
            $orderColumn = 't.created_at';
        } else {
            $orderColumn = "t." . $orderColumn;
        }

        if (!in_array(strtolower($orderDirection), ['asc', 'desc'])) {
            $orderDirection = 'desc';
        }

        $userId = auth()->user()->id;
        $tasksQueryBase = DB::table('tasks as t')
            ->leftJoin(
                DB::raw('(SELECT il.id AS lead_id, ilc.name AS contact_name 
                        FROM leads AS il 
                        JOIN lead_assignments ila ON il.id = ila.lead_id 
                        JOIN contacts ilc ON ilc.id = il.contact_id 
                        WHERE ila.user_id = ' . $userId . ') AS lt'),
                function ($join) {
                    $join->on('lt.lead_id', '=', 't.object_id')
                        ->where('t.object_type', '=', 'lead');
                }
            )
            ->leftJoin(
                DB::raw('(SELECT ic.id AS contact_id, ic.name AS contact_name 
                        FROM contacts AS ic) AS ct'),
                function ($join) {
                    $join->on('ct.contact_id', '=', 't.object_id')
                        ->where('t.object_type', '=', 'contact');
                }
            )
            ->leftJoin(
                'leads as l',
                't.object_id',
                '=',
                'l.id'
            )
            ->leftJoin(
                'lead_assignments as la',
                function ($join) {
                    $join->on('la.lead_id', '=', 'l.id')
                        ->whereNull('la.deleted_at');
                }
            )
            ->leftJoin(
                'contacts as c',
                'l.contact_id',
                '=',
                'c.id'
            )
            ->where(function ($query) use ($userId) {
                $query->where('t.object_type', '=', 'lead')
                    ->whereIn('t.object_id', function ($subquery) use ($userId) {
                        $subquery->select('l.id')
                            ->from('leads as l')
                            ->join('lead_assignments as la', 'la.lead_id', '=', 'l.id')
                            ->whereNull('la.deleted_at')
                            ->where('la.user_id', '=', $userId);
                    })
                    ->orWhere(function ($query) use ($userId) {
                        $query->where('t.object_type', '=', 'contact')
                            ->whereIn('t.object_id', function ($subquery) use ($userId) {
                                $subquery->select('c.id')
                                    ->from('contacts as c')
                                    ->whereNull('c.deleted_at')
                                    ->where('c.created_by', '=', $userId);
                            });
                    })
                    ->orWhere('t.assigned_to', '=', $userId);
            });
        $todayDate = (new \DateTime())->format('Y-m-d');

        $tasksQueryBase->where('t.status', '!=', 'completed');
        if ($viewType == 'today') {
            $tasksQueryBase->whereRaw('DATE(t.due_date) = ?', [$todayDate]);
        } elseif ($viewType == 'today_completed') {
            $tasksQueryBase->whereRaw('DATE(t.due_date) = ? AND t.status LIKE \'completed\'', [$todayDate]);
        } elseif ($viewType == 'upcoming') {
            $tasksQueryBase->whereRaw('DATE(t.due_date) > ?', [$todayDate]);
        } elseif ($viewType == 'overdue') {
            $tasksQueryBase->whereRaw('DATE(t.due_date) < ? AND t.status NOT LIKE ?', [$todayDate, 'completed']);
        } elseif ($viewType == 'autogenerated') {
            $tasksQueryBase->whereNotNull('t.automatization_id');
        }
        // ->groupBy('t.id');
        $total = $tasksQueryBase->count();
        $tasksQuery = $tasksQueryBase->select(
            't.id',
            't.subject',
            't.due_date',
            't.status',
            't.created_at',
            'l.id as lead_id',
            't.object_type as object_type',
            'lt.contact_name as contact_name',
            'ct.contact_name as ct_contact_name',
            'ct.contact_id as ct_contact_id',
            't.automatization_id as automatization'
        )
            ->orderBy($orderColumn, $orderDirection)
            ->offset($offset)
            ->limit($limit);

        // echo $tasksQuery->toSql();
        // die;

        $tasks = $tasksQuery->get();

        return [$tasks, $total];
    }

    public function getCountQueriedTasksWithUserIds($userIds, $viewType, $startDate, $endDate, $offset, $limit, $orderColumn, $orderDirection)
    {
        // Validate and sanitize order column and direction
        $allowedColumns = ['subject', 'due_date', 'status', 'lead.name']; // Add other valid columns as needed
        if (!in_array($orderColumn, $allowedColumns)) {
            $orderColumn = 't.created_at';
        } else {
            $orderColumn = "t." . $orderColumn;
        }

        if (!in_array(strtolower($orderDirection), ['asc', 'desc'])) {
            $orderDirection = 'desc';
        }

        // $userId = auth()->user()->id;
        $tasksQueryBase = DB::table('tasks as t')
            ->leftJoin(
                DB::raw('(SELECT il.id AS lead_id, ilc.name AS contact_name 
                        FROM leads AS il 
                        JOIN lead_assignments ila ON il.id = ila.lead_id 
                        JOIN contacts ilc ON ilc.id = il.contact_id 
                        WHERE ila.user_id IN ( ' . implode(',', $userIds) . ')) AS lt'),
                function ($join) {
                    $join->on('lt.lead_id', '=', 't.object_id')
                        ->where('t.object_type', '=', 'lead');
                }
            )
            ->leftJoin(
                DB::raw('(SELECT ic.id AS contact_id, ic.name AS contact_name 
                        FROM contacts AS ic) AS ct'),
                function ($join) {
                    $join->on('ct.contact_id', '=', 't.object_id')
                        ->where('t.object_type', '=', 'contact');
                }
            )
            ->leftJoin(
                'leads as l',
                't.object_id',
                '=',
                'l.id'
            )
            ->leftJoin(
                'lead_assignments as la',
                function ($join) {
                    $join->on('la.lead_id', '=', 'l.id')
                        ->whereNull('la.deleted_at');
                }
            )
            ->leftJoin(
                'contacts as c',
                'l.contact_id',
                '=',
                'c.id'
            )
            ->where(function ($query) use ($userIds) {
                $query->where('t.object_type', '=', 'lead')
                    ->whereIn('t.object_id', function ($subquery) use ($userIds) {
                        $subquery->select('l.id')
                            ->from('leads as l')
                            ->join('lead_assignments as la', 'la.lead_id', '=', 'l.id')
                            ->whereNull('la.deleted_at')
                            ->whereIn('la.user_id', $userIds);
                    })
                    ->orWhere(function ($query) use ($userIds) {
                        $query->where('t.object_type', '=', 'contact')
                            ->whereIn('t.object_id', function ($subquery) use ($userIds) {
                                $subquery->select('c.id')
                                    ->from('contacts as c')
                                    ->whereNull('c.deleted_at')
                                    ->whereIn('c.created_by', $userIds);
                            });
                    })
                    ->orWhereIn('t.assigned_to', $userIds);
            });
        $todayDate = (new \DateTime())->format('Y-m-d');
        // dd($startDate, $endDate);

        $tasksQueryBase->where('t.status', '!=', 'completed');
        if ($viewType == 'today') {
            $tasksQueryBase->whereRaw('DATE(t.due_date) = ?', [$todayDate]);
        } elseif ($viewType == 'today_completed') {
            $tasksQueryBase->whereRaw('DATE(t.due_date) = ? AND t.status LIKE \'completed\'', [$todayDate]);
        } elseif ($viewType == 'completed') {
            $tasksQueryBase->whereRaw('t.completion_date IS NOT NULL AND DATE(t.completion_date) >= ? AND DATE(t.completion_date) <= ? AND t.status LIKE ?', [$startDate, $endDate, 'completed']);
        } elseif ($viewType == 'upcoming') {
            $tasksQueryBase->whereRaw('DATE(t.due_date) > ?', [$todayDate]);
        } elseif ($viewType == 'overdue') {
            $tasksQueryBase->whereRaw('DATE(t.due_date) < ? AND t.status NOT LIKE ?', [$todayDate, 'completed']);
        } elseif ($viewType == 'autogenerated') {
            $tasksQueryBase->whereNotNull('t.automatization_id');
        }

        // echo $tasksQueryBase->toSql();
        // die;
        // ->groupBy('t.id');
        $total = $tasksQueryBase->count();
        $tasksQuery = $tasksQueryBase->select(
            't.id',
            't.subject',
            't.due_date',
            't.status',
            't.created_at',
            'l.id as lead_id',
            't.object_type as object_type',
            'lt.contact_name as contact_name',
            'ct.contact_name as ct_contact_name',
            'ct.contact_id as ct_contact_id',
            't.automatization_id as automatization'
        )
            ->orderBy($orderColumn, $orderDirection)
            ->offset($offset)
            ->limit($limit);

        // echo $tasksQuery->toSql();
        // die;

        $tasks = $tasksQuery->get();

        return [$tasks, $total];
    }

    public function getAllTasksSummaryForUsers($userIds, $startDate, $endDate)
    {
        $tasksQueryBase = DB::table('tasks as t')
            ->select(['t.id', 't.object_type', 't.object_id', 't.created_by', 't.assigned_to', 't.status', 't.completion_date', 't.due_date', 't.automatization_id'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where(function ($query) use ($userIds) {
                $query->whereIn('t.assigned_to', $userIds)
                    ->orWhereIn('t.created_by', $userIds);
            });

        $tasks = $tasksQueryBase->get();
        $tasksPerUser = ['total' => 0, 'autogenerated' => 0, 'autogenerated_completed' => 0, 'self' => 0, 'self_completed' => 0, 'overdue' => 0, 'due_today' => 0];

        foreach ($userIds as $userId) {
            foreach ($tasks as $task) {
                $taskIsAutogenerated = (!is_null($task->automatization_id) && $task->assigned_to == $userId);
                $taskIsSelfCreated = (is_null($task->automatization_id) && $task->created_by == $userId);
                if ($taskIsAutogenerated || $taskIsSelfCreated) {
                    $tasksPerUser['total']++;
                }
                if ($taskIsAutogenerated) {
                    $tasksPerUser['autogenerated']++;
                    if ($task->status == 'completed') {
                        $tasksPerUser['autogenerated_completed']++;
                    }
                }
                if ($taskIsSelfCreated) {
                    $tasksPerUser['self']++;
                    if ($task->status == 'completed') {
                        $tasksPerUser['self_completed']++;
                    }
                }
                if ($task->status != 'completed' && Carbon::parse($task->due_date)->isBefore(Carbon::today())) {
                    $tasksPerUser['overdue']++;
                }
                if ($task->status != 'completed' && Carbon::parse($task->due_date)->isSameDay(Carbon::today())) {
                    $tasksPerUser['due_today']++;
                }
            }
        }

        return $tasksPerUser;
    }
}
