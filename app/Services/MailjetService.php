<?php

namespace App\Services;

use Mailjet\LaravelMailjet\Facades\Mailjet;
use Illuminate\Support\Facades\Log;
use Exception;
use Mailjet\Resources;

class MailjetService
{
    /**
     * Get all contact lists from Mailjet
     *
     * @return array
     */
    public function getContactLists()
    {
        try {
            $response = Mailjet::get(Resources::$Contactslist);

            if ($response->success()) {
                return [
                    'success' => true,
                    'data' => $response->getData()
                ];
            }

            Log::error('Mailjet API error when fetching contact lists', [
                'status' => $response->getStatus(),
                'reason' => $response->getReasonPhrase()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to fetch contact lists from Mailjet'
            ];
        } catch (Exception $e) {
            Log::error('Exception when fetching Mailjet contact lists', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'An error occurred while connecting to Mailjet'
            ];
        }
    }

    /**
     * Create a new contact list in Mailjet
     *
     * @param string $name
     * @return array
     */
    public function createContactList($name)
    {
        try {
            $response = Mailjet::post(Resources::$Contactslist, [
                'body' => [
                    'Name' => $name
                ]
            ]);

            if ($response->success()) {
                return [
                    'success' => true,
                    'data' => $response->getData()
                ];
            }

            Log::error('Mailjet API error when creating contact list', [
                'status' => $response->getStatus(),
                'reason' => $response->getReasonPhrase(),
                'name' => $name
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create contact list in Mailjet'
            ];
        } catch (Exception $e) {
            Log::error('Exception when creating Mailjet contact list', [
                'message' => $e->getMessage(),
                'name' => $name,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'An error occurred while creating the contact list'
            ];
        }
    }

    /**
     * Add a contact to a Mailjet contact list
     *
     * @param int $listId
     * @param string $email
     * @param string $name
     * @return array
     */
    public function addContactToList($listId, $email, $name = '')
    {
        Log::info('Attempting to add contact to Mailjet list', [
            'email' => $email,
            'listId' => $listId,
            'name' => $name
        ]);

        try {
            // First, create or get the contact
            $contactResponse = Mailjet::post(Resources::$Contact, [
                'body' => [
                    'Email' => $email,
                    'Name' => $name
                ]
            ]);

            // 400 status usually means contact already exists, which is fine
            if (!$contactResponse->success() && $contactResponse->getStatus() !== 400) {
                Log::error('Mailjet API error when creating contact', [
                    'status' => $contactResponse->getStatus(),
                    'reason' => $contactResponse->getReasonPhrase(),
                    'email' => $email
                ]);

                return [
                    'success' => false,
                    'error' => 'Failed to create contact in Mailjet',
                    'status' => 'error'
                ];
            }

            // Then add the contact to the list
            $listResponse = Mailjet::post(Resources::$ContactslistManagecontact, [
                'id' => $listId,
                'body' => [
                    'Email' => $email,
                    'Action' => 'addnoforce'
                ]
            ]);

            if ($listResponse->success()) {
                $responseData = $listResponse->getData();

                Log::info('Mailjet managecontact API response', [
                    'email' => $email,
                    'listId' => $listId,
                    'responseData' => $responseData
                ]);

                // For 'addnoforce' action, Mailjet returns success (200) when:
                // 1. Contact is successfully added to the list
                // 2. Contact already exists in the list (no error, just no change)
                //
                // Since we're using 'addnoforce', a successful response means the contact
                // is now in the list. We'll default to 'added' and only mark as 'already_exists'
                // if we detect specific patterns in error responses (handled below).

                return [
                    'success' => true,
                    'status' => 'added',
                    'data' => $responseData
                ];
            }

            $status = $listResponse->getStatus();
            $reason = $listResponse->getReasonPhrase();

            // Check if this is a "contact already in list" error
            // Handle various status codes that might indicate existing contacts
            $existingContactStatuses = [400, 409]; // 400 = Bad Request (often duplicate), 409 = Conflict
            $existingContactReasonPatterns = ['already', 'duplicate', 'exists', 'conflict'];

            $isExistingContactStatus = in_array($status, $existingContactStatuses);
            $hasExistingContactReason = false;

            foreach ($existingContactReasonPatterns as $pattern) {
                if (stripos($reason, $pattern) !== false) {
                    $hasExistingContactReason = true;
                    break;
                }
            }

            if ($isExistingContactStatus && $hasExistingContactReason) {
                Log::info('Contact already exists in Mailjet list (API response)', [
                    'email' => $email,
                    'listId' => $listId,
                    'status' => $status,
                    'reason' => $reason
                ]);

                return [
                    'success' => true,
                    'status' => 'already_exists',
                    'data' => null
                ];
            }

            Log::error('Mailjet API error when adding contact to list', [
                'status' => $status,
                'reason' => $reason,
                'email' => $email,
                'listId' => $listId
            ]);

            return [
                'success' => false,
                'error' => 'Failed to add contact to list in Mailjet',
                'status' => 'error'
            ];
        } catch (Exception $e) {
            $errorMessage = $e->getMessage();

            // Check if the error is related to contact already existing
            // Common Mailjet error patterns for existing contacts
            $existingContactPatterns = [
                'already exists',
                'duplicate',
                'MJ18', // Mailjet's duplicate contact error code
                'MJ19', // Another Mailjet duplicate contact error code
                'already in list',
                'contact is already',
                'already subscribed',
                'already a member',
                'contact already in the list',
                'email already exists',
                'subscriber already exists',
                'contact exists',
                'duplicate email',
                'email is already',
                'contact already exists in this list'
            ];

            $isExistingContactError = false;
            foreach ($existingContactPatterns as $pattern) {
                if (stripos($errorMessage, $pattern) !== false) {
                    $isExistingContactError = true;
                    break;
                }
            }

            if ($isExistingContactError) {

                Log::info('Contact already exists in Mailjet list', [
                    'email' => $email,
                    'listId' => $listId,
                    'message' => $errorMessage
                ]);

                return [
                    'success' => true,
                    'status' => 'already_exists',
                    'data' => null
                ];
            }

            Log::error('Exception when adding contact to Mailjet list', [
                'message' => $errorMessage,
                'email' => $email,
                'listId' => $listId,
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => 'An error occurred while adding contact to list',
                'status' => 'error'
            ];
        }
    }

    /**
     * Sync multiple contacts to a Mailjet contact list
     *
     * @param int $listId
     * @param array $contacts Array of contacts with 'email' and 'name' keys
     * @return array
     */
    public function syncContactsToList($listId, $contacts)
    {
        $results = [
            'success' => true,
            'total' => count($contacts),
            'synced' => 0,
            'already_exists' => 0,
            'failed' => 0,
            'no_email' => 0,
            'errors' => []
        ];

        foreach ($contacts as $contact) {
            $email = $contact['email'] ?? '';
            $name = $contact['name'] ?? '';

            if (empty($email)) {
                $results['no_email']++;
                continue;
            }

            $result = $this->addContactToList($listId, $email, $name);

            if ($result['success']) {
                if ($result['status'] === 'added') {
                    $results['synced']++;
                } elseif ($result['status'] === 'already_exists') {
                    $results['already_exists']++;
                }
            } else {
                $results['failed']++;
                $results['errors'][] = "Failed to sync {$email}: " . ($result['error'] ?? 'Unknown error');
            }
        }

        // Only mark as failed if there were actual errors, not if contacts already existed or had no email
        // Success means: contacts were synced OR they already existed (no actual failures)
        // Contacts without email are tracked separately and don't count as failures
        if ($results['failed'] > 0) {
            $results['success'] = false;
        } else {
            $results['success'] = true;
        }

        Log::info('Mailjet contacts sync completed', [
            'listId' => $listId,
            'total' => $results['total'],
            'synced' => $results['synced'],
            'already_exists' => $results['already_exists'],
            'failed' => $results['failed'],
            'no_email' => $results['no_email'],
            'success_rate' => $results['total'] > 0 ? round((($results['synced'] + $results['already_exists']) / $results['total']) * 100, 2) . '%' : '0%'
        ]);

        return $results;
    }
}
