<?php

// app/Services/WhatsAppMessageService.php
namespace App\Services;

use App\Models\Property;
use App\Models\User;
use App\Models\WhatsappMessage;
use App\Services\WhatsAppApiService;
use App\Services\LeadsService;
use Illuminate\Support\Facades\Log;

class WhatsAppMessageService
{
    protected WhatsAppApiService $whatsAppApiService;
    protected LeadsService $leadsService;

    public function __construct(
        WhatsAppApiService $whatsAppApiService,
        LeadsService $leadsService
    ) {
        $this->whatsAppApiService = $whatsAppApiService;
        $this->leadsService = $leadsService;
    }
    /**
     * Process incoming WhatsApp message
     */
    public function processMessage(array $message, array $messageData, ?string $entryId = null): void
    {
        try {
            // Skip if message already exists
            if (WhatsappMessage::where('message_id', $message['id'])->exists()) {
                Log::info('Message already processed', ['message_id' => $message['id']]);
                return;
            }

            // Extract message content
            $content = $this->extractMessageContent($message);
            $messageType = $message['type'] ?? 'unknown';
            
            // Get sender info - improved extraction from Facebook WhatsApp payload
            $senderPhone = $message['from'] ?? null;
            $senderName = null;

            // Extract sender name from contacts array if available
            if (isset($messageData['contacts']) && is_array($messageData['contacts'])) {
                foreach ($messageData['contacts'] as $contact) {
                    if (isset($contact['wa_id']) && $contact['wa_id'] === $senderPhone) {
                        $senderName = $contact['profile']['name'] ?? null;
                        break;
                    }
                }
            }

            // Fallback to first contact if no matching wa_id found
            if (!$senderName && isset($messageData['contacts'][0]['profile']['name'])) {
                $senderName = $messageData['contacts'][0]['profile']['name'];
            }

            // Extract reference number
            $refNumber = $this->extractReferenceNumber($content);
            
            // Find agent if reference number exists
            // $agent = $refNumber ? $this->findAgentByReference($refNumber) : null;
            $agent = null;

            // Create message record with improved metadata
            $whatsappMessage = WhatsappMessage::create([
                'message_id' => $message['id'],
                'sender_phone' => $senderPhone,
                'sender_name' => $senderName,
                'message_content' => $content,
                'message_type' => $messageType,
                'metadata' => [
                    'original_message' => $message,
                    'contacts' => $messageData['contacts'] ?? [],
                    'business_account_id' => $messageData['metadata']['phone_number_id'] ?? null,
                    'display_phone_number' => $messageData['metadata']['display_phone_number'] ?? null,
                    'messaging_product' => $messageData['messaging_product'] ?? null,
                    'entry_id' => $entryId,
                    'timestamp' => $message['timestamp'] ?? null
                ],
                'ref_number' => $refNumber,
                'agent_id' => $agent ? $agent->id : null,
                'message_timestamp' => isset($message['timestamp']) ?
                    \Carbon\Carbon::createFromTimestamp($message['timestamp']) : now()
            ]);

            // Notify agent if found
            if ($agent) {
                $this->notifyAgent($agent, $whatsappMessage);
            }

            // Check for listing inquiry first to determine response type
            $isListingInquiry = $this->isListingInquiry($content);

            // Send auto-response to the sender
            $this->sendAutoResponse($senderPhone, $whatsappMessage, $isListingInquiry);

            // Handle listing inquiry and notify agent if it's an inquiry
            if ($isListingInquiry) {
                $this->handleListingInquiry($content, $senderPhone, $whatsappMessage);
            }

            Log::info('WhatsApp message processed successfully', [
                'message_id' => $message['id'],
                'sender' => $senderPhone,
                'ref_number' => $refNumber,
                'agent_id' => $agent ? $agent->id : null
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to process WhatsApp message', [
                'message_id' => $message['id'] ?? 'unknown',
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Extract message content based on message type
     */
    protected function extractMessageContent(array $message): string
    {
        switch ($message['type']) {
            case 'text':
                return $message['text']['body'] ?? '';

            case 'image':
                $caption = $message['image']['caption'] ?? '';
                $mediaId = $message['image']['id'] ?? '';
                return $caption ?: "[Image received - ID: $mediaId]";

            case 'document':
                $filename = $message['document']['filename'] ?? 'Unknown file';
                $caption = $message['document']['caption'] ?? '';
                $mediaId = $message['document']['id'] ?? '';
                return $caption ? "$caption [Document: $filename - ID: $mediaId]" : "[Document: $filename - ID: $mediaId]";

            case 'audio':
                $mediaId = $message['audio']['id'] ?? '';
                return "[Audio message - ID: $mediaId]";

            case 'video':
                $caption = $message['video']['caption'] ?? '';
                $mediaId = $message['video']['id'] ?? '';
                return $caption ?: "[Video received - ID: $mediaId]";

            case 'location':
                $lat = $message['location']['latitude'] ?? '';
                $lng = $message['location']['longitude'] ?? '';
                $name = $message['location']['name'] ?? '';
                $address = $message['location']['address'] ?? '';
                $locationInfo = $name ? "Name: $name" : '';
                $locationInfo .= $address ? ($locationInfo ? ", Address: $address" : "Address: $address") : '';
                return "[Location shared: $lat, $lng" . ($locationInfo ? " - $locationInfo" : '') . "]";

            case 'contacts':
                $contactsCount = count($message['contacts'] ?? []);
                return "[Contact(s) shared - Count: $contactsCount]";

            case 'sticker':
                $mediaId = $message['sticker']['id'] ?? '';
                return "[Sticker sent - ID: $mediaId]";

            case 'button':
                $buttonText = $message['button']['text'] ?? '';
                $buttonPayload = $message['button']['payload'] ?? '';
                return "[Button pressed: $buttonText" . ($buttonPayload ? " - Payload: $buttonPayload" : '') . "]";

            case 'interactive':
                $interactiveType = $message['interactive']['type'] ?? '';
                if ($interactiveType === 'button_reply') {
                    $buttonId = $message['interactive']['button_reply']['id'] ?? '';
                    $buttonTitle = $message['interactive']['button_reply']['title'] ?? '';
                    return "[Interactive button: $buttonTitle - ID: $buttonId]";
                } elseif ($interactiveType === 'list_reply') {
                    $listId = $message['interactive']['list_reply']['id'] ?? '';
                    $listTitle = $message['interactive']['list_reply']['title'] ?? '';
                    return "[Interactive list: $listTitle - ID: $listId]";
                }
                return "[Interactive message - Type: $interactiveType]";

            default:
                return '[Unsupported message type: ' . $message['type'] . ']';
        }
    }

    /**
     * Extract reference number from message content
     */
    protected function extractReferenceNumber(string $content): ?string
    {
        // Common patterns for reference numbers
        $patterns = [
            '/REF[\s:]*([A-Z0-9]{6,12})/i',           // REF: ABC123456
            '/REF[\s#]*([A-Z0-9]{6,12})/i',           // REF #ABC123456
            '/REFERENCE[\s:]*([A-Z0-9]{6,12})/i',     // REFERENCE: ABC123456
            '/\b([A-Z]{2,3}[0-9]{4,8})\b/',           // ABC12345678
            '/\b([0-9]{6,10})\b/',                    // 1234567890
            '/#([A-Z0-9]{6,12})/i',                   // #ABC123456
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                return strtoupper(trim($matches[1]));
            }
        }

        return null;
    }

    /**
     * Find agent by reference number
     */
    protected function findAgentByReference(string $refNumber): ?User
    {
        // Get all active agents
        $agents = User::where('active', true)->get();

        foreach ($agents as $agent) {
            // Check if agent has specific patterns
            if ($agent->ref_patterns) {
                foreach ($agent->ref_patterns as $pattern) {
                    if (preg_match($pattern, $refNumber)) {
                        return $agent;
                    }
                }
            }

            // Default pattern matching based on agent ID or prefix
            $agentPrefix = strtoupper(substr($agent->name, 0, 2));
            if (str_starts_with($refNumber, $agentPrefix)) {
                return $agent;
            }
        }

        return null;
    }

    /**
     * Check if a message is a listing inquiry
     */
    protected function isListingInquiry(string $messageContent): bool
    {
        // Pattern to match listing inquiry messages with square brackets
        $patterns = [
            '/\[([A-Z0-9\-_]+)\]/i',  // Primary pattern: anything inside square brackets
            '/listing[:\s]*\[([A-Z0-9\-_]+)\]/i',  // "listing: [REF]" or "listing [REF]"
            '/property[:\s]*\[([A-Z0-9\-_]+)\]/i'   // "property: [REF]" or "property [REF]"
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $messageContent)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Send auto-response to message sender
     */
    protected function sendAutoResponse(string $senderPhone, WhatsappMessage $message, bool $isListingInquiry = false): void
    {
        try {
            if ($isListingInquiry) {
                $autoResponseMessage = "Thank you for submitting your enquiry. A FGRealty representative will get in touch with you shortly to provide with more details";
            } else {
                $autoResponseMessage = "We got your message";
            }

            Log::info('[WhatsAppMessageService] Sending auto-response', [
                'to' => $senderPhone,
                'original_message_id' => $message->message_id,
                'response' => $autoResponseMessage,
                'is_listing_inquiry' => $isListingInquiry
            ]);

            $result = $this->whatsAppApiService->sendTextMessage($senderPhone, $autoResponseMessage);

            if ($result['success']) {
                Log::info('[WhatsAppMessageService] Auto-response sent successfully', [
                    'to' => $senderPhone,
                    'original_message_id' => $message->message_id,
                    'response_message_id' => $result['message_id'] ?? null
                ]);

                // Update the message record to indicate auto-response was sent
                $metadata = $message->metadata ?? [];
                $metadata['auto_response_sent'] = true;
                $metadata['auto_response_message_id'] = $result['message_id'] ?? null;
                $metadata['auto_response_sent_at'] = now()->toISOString();
                $metadata['auto_response_type'] = $isListingInquiry ? 'listing_inquiry' : 'general';
                $metadata['auto_response_message'] = $autoResponseMessage;
                $message->update(['metadata' => $metadata]);

            } else {
                Log::error('[WhatsAppMessageService] Failed to send auto-response', [
                    'to' => $senderPhone,
                    'original_message_id' => $message->message_id,
                    'error' => $result['error'] ?? 'Unknown error'
                ]);

                // Update the message record to indicate auto-response failed
                $metadata = $message->metadata ?? [];
                $metadata['auto_response_sent'] = false;
                $metadata['auto_response_error'] = $result['error'] ?? 'Unknown error';
                $metadata['auto_response_attempted_at'] = now()->toISOString();
                $metadata['auto_response_type'] = $isListingInquiry ? 'listing_inquiry' : 'general';
                $metadata['auto_response_message'] = $autoResponseMessage;
                $message->update(['metadata' => $metadata]);
            }

        } catch (\Exception $e) {
            Log::error('[WhatsAppMessageService] Exception while sending auto-response', [
                'to' => $senderPhone,
                'original_message_id' => $message->message_id,
                'error' => $e->getMessage()
            ]);

            // Update the message record to indicate auto-response failed with exception
            try {
                $metadata = $message->metadata ?? [];
                $metadata['auto_response_sent'] = false;
                $metadata['auto_response_error'] = $e->getMessage();
                $metadata['auto_response_attempted_at'] = now()->toISOString();
                $metadata['auto_response_type'] = $isListingInquiry ? 'listing_inquiry' : 'general';
                $metadata['auto_response_message'] = $autoResponseMessage;
                $message->update(['metadata' => $metadata]);
            } catch (\Exception $updateException) {
                Log::error('[WhatsAppMessageService] Failed to update message metadata after auto-response exception', [
                    'original_message_id' => $message->message_id,
                    'update_error' => $updateException->getMessage()
                ]);
            }
        }
    }

    /**
     * Handle listing inquiry messages and notify the appropriate agent
     */
    protected function handleListingInquiry(string $messageContent, string $senderPhone, WhatsappMessage $whatsappMessage): void
    {
        try {
            // Pattern to match listing inquiry messages with square brackets
            // Matches: "Hi, I want to know more details about this listing: [LISTING_REF_NO]"
            $patterns = [
                '/\[([A-Z0-9\-_]+)\]/i',  // Primary pattern: anything inside square brackets
                '/listing[:\s]*\[([A-Z0-9\-_]+)\]/i',  // "listing: [REF]" or "listing [REF]"
                '/property[:\s]*\[([A-Z0-9\-_]+)\]/i'   // "property: [REF]" or "property [REF]"
            ];

            $listingRefNo = null;

            // Try to extract listing reference number using patterns
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $messageContent, $matches)) {
                    $listingRefNo = strtoupper(trim($matches[1]));
                    break;
                }
            }

            if (!$listingRefNo) {
                Log::info('[WhatsAppMessageService] No listing reference found in message', [
                    'message_content' => $messageContent,
                    'sender' => $senderPhone
                ]);
                return;
            }

            Log::info('[WhatsAppMessageService] Listing inquiry detected with square brackets', [
                'listing_ref_no' => $listingRefNo,
                'sender' => $senderPhone,
                'message_content' => $messageContent
            ]);

            // Find the listing by ref_no
            $listing = Property::where('ref_no', $listingRefNo)->first();

            if (!$listing) {
                Log::warning('[WhatsAppMessageService] Listing not found', [
                    'listing_ref_no' => $listingRefNo,
                    'sender' => $senderPhone
                ]);
                return;
            }

            // Get the agent (created_by user)
            $agent = $listing->author; // Using the author relationship which maps to created_by

            if (!$agent) {
                Log::warning('[WhatsAppMessageService] No agent found for listing', [
                    'listing_ref_no' => $listingRefNo,
                    'listing_id' => $listing->id,
                    'created_by' => $listing->created_by
                ]);
                return;
            }

            // Get agent's phone number
            $agentPhone = env("FAKE_AGENT_NUMBER", $agent->getCompletePhoneNo());

            if (empty($agentPhone)) {
                Log::warning('[WhatsAppMessageService] Agent has no phone number', [
                    'agent_id' => $agent->id,
                    'agent_name' => $agent->name,
                    'listing_ref_no' => $listingRefNo
                ]);
                return;
            }

            // Send notification to agent
            $this->notifyAgentAboutListingInquiry($agent, $agentPhone, $listingRefNo, $senderPhone, $whatsappMessage);

            // Create lead for this WhatsApp enquiry
            $this->leadsService->createLeadFromWhatsAppInquiry($listing, $agent, $senderPhone, $whatsappMessage, $listingRefNo);

        } catch (\Exception $e) {
            Log::error('[WhatsAppMessageService] Exception while handling listing inquiry', [
                'message_content' => $messageContent,
                'sender' => $senderPhone,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send WhatsApp notification to agent about listing inquiry
     */
    protected function notifyAgentAboutListingInquiry(User $agent, string $agentPhone, string $listingRefNo, string $inquirerPhone, WhatsappMessage $originalMessage): void
    {
        try {
            $notificationMessage = "You got a new lead on WhatsApp for the listing: {$listingRefNo} from +{$inquirerPhone}";

            Log::info('[WhatsAppMessageService] Sending listing inquiry notification to agent', [
                'agent_id' => $agent->id,
                'agent_name' => $agent->name,
                'agent_phone' => $agentPhone,
                'listing_ref_no' => $listingRefNo,
                'inquirer_phone' => $inquirerPhone,
                'original_message_id' => $originalMessage->message_id
            ]);

            $result = $this->whatsAppApiService->sendTextMessage($agentPhone, $notificationMessage);

            if ($result['success']) {
                Log::info('[WhatsAppMessageService] Agent notification sent successfully', [
                    'agent_id' => $agent->id,
                    'agent_phone' => $agentPhone,
                    'listing_ref_no' => $listingRefNo,
                    'inquirer_phone' => $inquirerPhone,
                    'notification_message_id' => $result['message_id'] ?? null
                ]);

                // Update the original message metadata to track agent notification
                $metadata = $originalMessage->metadata ?? [];
                $metadata['listing_inquiry'] = [
                    'detected' => true,
                    'listing_ref_no' => $listingRefNo,
                    'agent_notified' => true,
                    'agent_id' => $agent->id,
                    'agent_phone' => $agentPhone,
                    'notification_message_id' => $result['message_id'] ?? null,
                    'notified_at' => now()->toISOString()
                ];
                $originalMessage->update(['metadata' => $metadata]);

            } else {
                Log::error('[WhatsAppMessageService] Failed to send agent notification', [
                    'agent_id' => $agent->id,
                    'agent_phone' => $agentPhone,
                    'listing_ref_no' => $listingRefNo,
                    'inquirer_phone' => $inquirerPhone,
                    'error' => $result['error'] ?? 'Unknown error'
                ]);

                // Update metadata to track failed notification
                $metadata = $originalMessage->metadata ?? [];
                $metadata['listing_inquiry'] = [
                    'detected' => true,
                    'listing_ref_no' => $listingRefNo,
                    'agent_notified' => false,
                    'agent_id' => $agent->id,
                    'agent_phone' => $agentPhone,
                    'notification_error' => $result['error'] ?? 'Unknown error',
                    'notification_attempted_at' => now()->toISOString()
                ];
                $originalMessage->update(['metadata' => $metadata]);
            }

        } catch (\Exception $e) {
            Log::error('[WhatsAppMessageService] Exception while sending agent notification', [
                'agent_id' => $agent->id,
                'listing_ref_no' => $listingRefNo,
                'inquirer_phone' => $inquirerPhone,
                'error' => $e->getMessage()
            ]);

            // Update metadata to track exception
            try {
                $metadata = $originalMessage->metadata ?? [];
                $metadata['listing_inquiry'] = [
                    'detected' => true,
                    'listing_ref_no' => $listingRefNo,
                    'agent_notified' => false,
                    'agent_id' => $agent->id,
                    'notification_error' => $e->getMessage(),
                    'notification_attempted_at' => now()->toISOString()
                ];
                $originalMessage->update(['metadata' => $metadata]);
            } catch (\Exception $updateException) {
                Log::error('[WhatsAppMessageService] Failed to update message metadata after agent notification exception', [
                    'original_message_id' => $originalMessage->message_id,
                    'update_error' => $updateException->getMessage()
                ]);
            }
        }
    }

    /**
     * Notify agent about new message
     */
    protected function notifyAgent(User $agent, WhatsappMessage $message): void
    {
        Log::info("[WhatsApp] - agent should be notified", ['agent_id' => $agent->id, 'message_id' => $message->message_id]);
        // try {
        //     Notification::send($agent, new NewWhatsAppMessage($message));

        //     $message->update(['agent_notified' => true]);

        //     Log::info('Agent notified successfully', [
        //         'agent_id' => $agent->id,
        //         'message_id' => $message->message_id
        //     ]);
        // } catch (\Exception $e) {
        //     Log::error('Failed to notify agent', [
        //         'agent_id' => $agent->id,
        //         'message_id' => $message->message_id,
        //         'error' => $e->getMessage()
        //     ]);
        // }
    }

    /**
     * Process WhatsApp message status updates (delivery receipts, read receipts, etc.)
     */
    public function processMessageStatus(array $status, array $messageData, ?string $entryId = null): void
    {
        try {
            $messageId = $status['id'] ?? null;
            $statusType = $status['status'] ?? null;
            $recipientId = $status['recipient_id'] ?? null;
            $timestamp = $status['timestamp'] ?? null;

            if (!$messageId || !$statusType) {
                Log::warning('[WhatsAppMessageService] Invalid status update', [
                    'status' => $status
                ]);
                return;
            }

            // Find the original message
            $whatsappMessage = WhatsappMessage::where('message_id', $messageId)->first();

            if (!$whatsappMessage) {
                Log::info('[WhatsAppMessageService] Status update for unknown message', [
                    'message_id' => $messageId,
                    'status' => $statusType
                ]);
                return;
            }

            // Update message metadata with status information
            $metadata = $whatsappMessage->metadata ?? [];
            $metadata['status_updates'] = $metadata['status_updates'] ?? [];
            $metadata['status_updates'][] = [
                'status' => $statusType,
                'timestamp' => $timestamp,
                'recipient_id' => $recipientId,
                'updated_at' => now()->toISOString()
            ];

            // Update the latest status
            $metadata['latest_status'] = $statusType;
            $metadata['latest_status_timestamp'] = $timestamp;

            $whatsappMessage->update(['metadata' => $metadata]);

            Log::info('[WhatsAppMessageService] Message status updated', [
                'message_id' => $messageId,
                'status' => $statusType,
                'recipient_id' => $recipientId
            ]);

        } catch (\Exception $e) {
            Log::error('[WhatsAppMessageService] Failed to process message status', [
                'status' => $status,
                'error' => $e->getMessage()
            ]);
        }
    }
}