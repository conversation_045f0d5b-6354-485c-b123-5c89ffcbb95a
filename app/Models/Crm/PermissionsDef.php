<?php

namespace App\Models\Crm;

class PermissionsDef
{

    const ADMIN_USERS = 'admin_users';

    const SITE_CONTENT_READ = 'site_content_read';
    const SITE_CONTENT_ADD = 'site_content_add';

    const SEO_CONTENT_EDIT = 'seo_content_edit';
    const SEO_DYNAMIC_MENU_EDIT = 'seo_dynamic_menu_edit';

    const DASHBOARD = 'dashboard';

    const INVENTORY_MASTER_READ = 'inventory_master_read';
    const INVENTORY_PERSONAL_READ = 'inventory_personal_read';
    const INVENTORY_PENDING_READ = 'inventory_pending_read';
    const INVENTORY_ARCHIVED_READ = 'inventory_archived_read';
    const INVENTORY_WAITING_FOR_PICTURES = 'inventory_waiting_for_pictures';
    const INVENTORY_UPDATE = 'inventory_update';
    const INVENTORY_DELETE = 'inventory_delete';

    const REGIONS = 'regions';
    const SEO_MODULE = 'seo_module';
    const DYNAMIC_MENU = 'dynamic_menu';
    const CONTACTS = 'contacts';
    const LEADS_READ = 'leads_read';
    const LEADS_CREATE = 'leads_create';
    const LEADS_UPDATE = 'leads_update';
    const LEADS_DELETE = 'leads_delete';
    const DEALS_READ = 'deals_read';
    const DEALS_READ_ALL = 'deals_read_all';
    const DEALS_UPDATE = 'deals_update';
    const DEALS_DELETE = 'deals_delete';
    const CRM_ADMIN = 'crm_admin';
    const TASKS = 'crm_tasks';

    const CONTACTS_READ = 'contacts_read';
    const CONTACTS_UPDATE = 'contacts_update';
    const CONTACTS_DELETE = 'contacts_delete';

    const TRAININGS = 'trainings';

    const MATRIX_AGENT_MANAGEMENT = 'matrix_agent_management';
    const TEAM_LEADER = 'team_leader';

    const SHORT_STAY_USER = 'short_stay_user';
    const SHORT_STAY_ADMIN = 'short_stay_admin';

    // areas
    const SECTION_OVERVIEW = 'section_overview';
    const SECTION_CONTACTS = 'section_contacts';
    const SECTION_POLICIES_AND_PROCEDURES = 'section_policies_and_procedures';
    const POLICIES_AND_PROCEDURES_STORE = 'policies_and_procedures_store';
    const SECTION_FORMS = 'section_forms';
    const SECTION_DASHBOARD_CALENDAR = 'section_dashboard_calendar';
    const SECTION_DASHBOARD_PERFORMANCE = 'section_dashboard_performance';
    const SECTION_DASHBOARD_KPI = 'section_dashboard_kpi';

    const CAN_EDIT_CORPORATE_LANDLORDS = 'can_edit_corporate_landlords';
    const CAN_UPDATE_LISTING_PUBLISHING_STATUS = 'can_update_listing_publishing_status';
    const CAN_EDIT_ALL_LISTINGS = 'can_edit_all_listings';
    const HAS_ACCESS_TO_DEAL_FILES = 'has_access_to_deal_files';
    const CAN_EDIT_LISTING_AVAILABILITY_STATUS = 'can_edit_listing_availability_status';

    const GLOBAL_REPORTS_ACCESS = 'global_reports_access';
    const CAN_MANAGE_DUBAI_INVESTORS = 'can_manage_dubai_investors';
    const CAN_MANAGE_PEARL_OWNERS = 'can_manage_pearl_owners';

    const CAN_PUBLISH_OWN_LISTINGS = 'can_publish_own_listings';
    const CAN_PREVIEW_FEATURES = 'can_preview_features';
    const CAN_VIEW_CONTEST = 'can_view_contest';
    const EXTRA_USER_VIEW_CONTEST = 'extra_user_view_contest';

    const CAN_VIEW_LEAD_DETAILS_V2 = 'can_view_lead_details_v2';
    const CHECK_IN_ADMIN = 'check_in_admin';
    const CAN_ACCESS_ACCOUNTANT_SECTION = 'can_access_accountant_section';
}
