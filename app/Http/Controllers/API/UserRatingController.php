<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\CrmBaseController;
use App\Models\Contact;
use App\Models\Lead;
use App\Models\LeadAssignment;
use App\Models\MarketingParams;
use App\Models\User;
use App\Services\ContactsService;
use App\Services\OperationHistoryService;
use App\Services\UserRatingService;
use Illuminate\Support\Facades\Cache;
use App\Models\CacheKeys;
use App\Services\LeadsService;

class UserRatingController extends CrmBaseController
{
    private $userRatingService;
    private $operationHistoryService;
    private $contactsService;
    private $leadsService;

    protected $routes = [
        'index' => 'agent-rating.index',
        'edit' => 'agent-rating.edit'
    ];

    protected $views = [
        'index' => 'admin.agent-rating.index',
        'create' => 'admin.agent-rating.create',
        'edit' => 'admin.agent-rating.edit',
    ];

    public function __construct(
        UserRatingService $userRatingService,
        OperationHistoryService $operationHistoryService,
        ContactsService $contactsService,
        LeadsService $leadsService
    ) {
        $this->userRatingService = $userRatingService;
        $this->operationHistoryService = $operationHistoryService;
        $this->contactsService = $contactsService;
        $this->leadsService = $leadsService;
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getUserRating($userId)
    {
        $request = request();
        if ($request->ajax()) {
            $items = $this->userRatingService->getTableItems($request, false, ['userId' => $userId])->map(function ($item) {
                return $this->tableItemsMapper($item);
            });
            $count = $this->userRatingService->getTableItemsCount($request, ['userId' => $userId]);
            return [
                'data' => $items,
                'recordsTotal' => $count,
                'recordsFiltered' => $count
            ];
        }

        return [];
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store()
    {
        $request = request();
        $validFields = $request->validate([
            'name' => 'required|string',
            'email' => 'required|email',
            'phone' => 'required|string',
            'prefix' => 'required|string',
            'rating' => 'required|int',
            'userId' => 'required|exists:users,uniq_id',
            'message' => ['required', 'min:20', 'max:1024'],
        ]);

        $user = User::where('uniq_id', $validFields['userId'])->firstOrFail();

        $data = [];
        $data = array_merge($validFields);
        $data['remote_addr'] = getenv('REMOTE_ADDR');
        $data['user_id'] = $user->id;
        $data['message'] = strip_tags($validFields['message']);
        unset($data['userId']);

        try {
            $this->userRatingService->store($user, $data);

            $existingContactByEmail = $this->contactsService->getContactsByEmail($validFields['email']);
            $existingContactByPhone = $this->contactsService->getContactsByPhoneNumber($validFields['phone']);

            $existingMasterContact = $existingContactByEmail->whereNull('master_contact_id')->first();
            if (is_null($existingMasterContact)) {
                $existingContact = $existingContactByEmail->first();
            } else {
                $existingContact = $existingMasterContact;
            }

            if (is_null($existingContact)) {
                $existingMasterContact = $existingContactByPhone->whereNull('master_contact_id')->first();
                if (is_null($existingMasterContact)) {
                    $existingContact = $existingContactByPhone->first();
                } else {
                    $existingContact = $existingMasterContact;
                }
            }

            $contact = Contact::create([
                'name' => $validFields['name'],
                'email_1' => $validFields['email'],
                'prefix_mobile_1' => $validFields['prefix'],
                'mobile_1' => $validFields['phone'],
                'master_contact_id' => !is_null($existingContact) ? $existingContact->id : null,
                'is_master_contact' => is_null($existingContact) ? true : null
            ]);

            $this->operationHistoryService->addOperationHistory($contact, "Contact added rating for [" . $user->name . "] ", null);

            return response([], 201);
        } catch (\Exception $Ex) {
            return response($Ex->getMessage(), 500);
        }
    }

    public function connectWithAgent()
    {
        $request = request();
        $baseRules = [
            'name' => 'required|string',
            'email' => 'required|email',
            'phone' => 'required|string',
            'prefix' => 'required|string',
            'bestTimeToContact' => '',
            'interestedIn' => '',
            'interest' => '',
            'location' => '',
            'locationId' => '',
            'lookingFor' => '',
            'name' => '',
            'nationality' => '',
            'nationalityId' => '',
            'phone' => '',
            'propertyTypeId' => '',
            'specialRequest' => '',
            'uniqId' => 'required|exists:users,uniq_id'
        ];

        $marketingParams = array_fill_keys(MarketingParams::WHITELISTED_URL_PARAMS, '');
        $validFields = $request->validate(array_merge($baseRules, $marketingParams));

        $user = User::where('uniq_id', $validFields['uniqId'])->firstOrFail();

        $data = $validFields;
        $data["user_id"] = $user->id;

        $leadsRequestObj = (object)[];

        if ($validFields['lookingFor']) {
            if ($validFields['lookingFor'] == 'buy') {
                $leadsRequestObj->ot = 'sale';
            } else {
                $leadsRequestObj->ot = 'rent';
            }
        }
        if ($validFields['propertyTypeId']) {
            $leadsRequestObj->t =  $validFields['propertyTypeId'];
        }
        if ($validFields['locationId']) {
            $leadsRequestObj->loc =  $validFields['locationId'];
        }

        $existingContactByEmail = $this->contactsService->getContactsByEmail($validFields['email']);
        $existingContactByPhone = $this->contactsService->getContactsByPhoneNumber($validFields['phone']);

        $existingMasterContact = $existingContactByEmail->whereNull('master_contact_id')->first();
        if (is_null($existingMasterContact)) {
            $existingContact = $existingContactByEmail->first();
        } else {
            $existingContact = $existingMasterContact;
        }

        if (is_null($existingContact)) {
            $existingMasterContact = $existingContactByPhone->whereNull('master_contact_id')->first();
            if (is_null($existingMasterContact)) {
                $existingContact = $existingContactByPhone->first();
            } else {
                $existingContact = $existingMasterContact;
            }
        }

        $leadMetadata = [];

        // Add all whitelisted marketing parameters
        foreach (MarketingParams::WHITELISTED_URL_PARAMS as $param) {
            if (isset($validFields[$param]) && !empty($validFields[$param])) {
                $leadMetadata[$param] = $validFields[$param];
            }
        }

        $contact = Contact::create([
            'name' => $validFields['name'],
            'email_1' => $validFields['email'],
            'prefix_mobile_1' => $validFields['prefix'],
            'mobile_1' => $validFields['phone'],
            'master_contact_id' => !is_null($existingContact) ? $existingContact->id : null,
            'is_master_contact' => is_null($existingContact) ? true : null
        ]);

        $leadSource = $this->leadsService->getCachedSources()->where('code', 'WEBSITE_CONNECT_WITH_AGENT_FORM')->first();

        $lead = Lead::create([
            'contact_id' => $contact->id,
            'location_id' => $validFields['locationId'],
            'filter_operation_type' => $validFields['lookingFor'],
            'filter_property_type' => $validFields['propertyTypeId'],
            'requirements' => $validFields['specialRequest'],
            'platform_from' => !is_null($leadSource) ? $leadSource->id : '1',
            'lead_status_id' => '11', // NEW
            'leads_request' => json_encode($leadsRequestObj),

            'lead_metadata' => $leadMetadata,
            'utm_source' => isset($validFields['utm_source']) ? $validFields['utm_source'] : null,
            'utm_medium' => isset($validFields['utm_medium']) ? $validFields['utm_medium'] : null,
            'utm_campaign' => isset($validFields['utm_campaign']) ? $validFields['utm_campaign'] : null,
            'utm_term' => isset($validFields['utm_term']) ? $validFields['utm_term'] : null,
            'utm_content' => isset($validFields['utm_content']) ? $validFields['utm_content'] : null,
        ]);

        Cache::forget(CacheKeys::UTM_SOURCES);

        LeadAssignment::create([
            'user_id' => $user->id,
            'lead_id' => $lead->id,
        ]);

        $this->operationHistoryService->addOperationHistory($lead, 'The lead came from the website', $user);
        $this->operationHistoryService->addOperationHistory($lead, 'The lead is assigned to ' .  $user->name, $user);

        try {
            $this->userRatingService->createAgentConnection($user, $validFields);
            return response([], 200);
        } catch (\Exception $Ex) {
            return response($Ex->getMessage(), 500);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update($id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function switchApprovalForRating($userId, $ratingId)
    {
        try {
            $this->userRatingService->switchApprovalForRating($userId, $ratingId);
            return response([], 200);
        } catch (\Exception $Ex) {
            $statusCode = 500;
            if ($Ex->getMessage() == "No rating found") {
                $statusCode = 412;
            }
            return response(['message' => $Ex->getMessage()], $statusCode);
        }
    }

    protected function tableItemsMapper($item)
    {
        return $item;
    }
}
