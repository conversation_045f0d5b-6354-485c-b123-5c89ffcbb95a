<!DOCTYPE html>
<html lang="{{ app()->currentLocale() }}" @if(app()->isLocale('ar')) dir="rtl" @endif>
@php

$image2URL = asset('images/projects/marina-heights/3.png');
$image2BigURL = asset('images/projects/marina-heights/7.png');
$image1URL = asset('images/projects/marina-heights/2.jpg');
$image3URL = asset('images/projects/marina-heights/4.jpg');
$image4URL = asset('images/projects/marina-heights/5.jpg');
$image5URL = asset('images/projects/marina-heights/1.jpg');
$image6URL = asset('images/projects/marina-heights/1.jpg');
$image6BigURL = asset('images/projects/marina-heights/8.jpg');
$image7URL = asset('images/projects/marina-heights/1.jpg');
$image8URL = asset('images/projects/marina-heights/1.jpg');
$image8BigURL = asset('images/projects/marina-heights/6.jpg');
$image9URL = asset('images/projects/marina-heights/1.jpg');
$image10URL = asset('images/svg/hamburger-black.svg');
$image11URL = asset('images/projects/marina-heights/1.jpg');
$nameTranslations = [];

$isProductionEnvironment = env('APP_ENV') == 'production';
$metaTitle = __("Lusail Marina Heights: Luxury Waterfront Living");
$metaDescription = __('Experience modern luxury at Lusail Marina Heights. Enjoy elegant 1, 2, & 3-bedroom residences with panoramic marina views and premium finishes.');
if($locale == 'ar') {
    $nameTranslations = [
        'Serban Gabriel Spirea' => 'سيربان غابرييل سبيريا',
        'Michelle Addo' => 'ميشيل أدو',
        'Mostafa Karout' => 'مصطفى قروط',
        'Faheem Miswer' => 'فاهيم ميسوير',
        'Zennyla Bhutia' => 'زينيلا بوتيا',
        'Abdulkarim Wazeh' => 'عبد الكريم وازع'
    ];
}
$resolveCountryName = function($c) use ($locale) {
    if($locale == 'ar') {
        $firstTranslation = $c->translations->first();
        if(!is_null($firstTranslation)) {
            return $firstTranslation->value;
        }
    }
    return $c->name;
}
@endphp

<head>
    <meta charset="UTF-8">
    <title>{{$metaTitle}}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name='robots' content='max-image-preview:large' />
    <style>
        img:is([sizes="auto" i], [sizes^="auto," i]) {
            contain-intrinsic-size: 3000px 1500px
        }
    </style>
    <script defer src="data:text/javascript;base64,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"></script>
    <meta name="description" content="{{$metaDescription}}">
    <meta property="og:url" content="https://www.fgrealty.qa/en/projects/lusail-marina-heights">
    <meta property="og:type" content="website">
    <meta property="og:title" content="{{$metaTitle}}">
    <meta property="og:description" content="{{$metaDescription}}">
    <meta property="og:image" content="{{mix('/images/projects/marina-heights/logo.png')}}">

    <meta name="twitter:card" content="summary_large_image">
    <meta property="twitter:domain" content="fgrealty.qa">
    <meta property="twitter:url" content="https://www.fgrealty.qa/en/projects/lusail-marina-heights">
    <meta name="twitter:title" content="{{$metaTitle}}">
    <meta name="twitter:description" content="{{$metaDescription}}">
    <meta name="twitter:image" content="{{mix('/images/projects/marina-heights/logo.png')}}">
    @if($isProductionEnvironment)
    <script>
        window.console.log = function() {};
        window.console.error = function() {};
        (function(w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-WPD3N3NC');
    </script>
    <!-- End Google Tag Manager -->
    @endif
    <style id='wp-emoji-styles-inline-css'>
        img.wp-smiley,
        img.emoji {
            display: inline !important;
            border: none !important;
            box-shadow: none !important;
            height: 1em !important;
            width: 1em !important;
            margin: 0 0.07em !important;
            vertical-align: -0.1em !important;
            background: none !important;
            padding: 0 !important;
        }
    </style>
    <style id='wp-block-library-inline-css'>
        :root {
            --wp-admin-theme-color: #007cba;
            --wp-admin-theme-color--rgb: 0, 124, 186;
            --wp-admin-theme-color-darker-10: #006ba1;
            --wp-admin-theme-color-darker-10--rgb: 0, 107, 161;
            --wp-admin-theme-color-darker-20: #005a87;
            --wp-admin-theme-color-darker-20--rgb: 0, 90, 135;
            --wp-admin-border-width-focus: 2px;
            --wp-block-synced-color: #7a00df;
            --wp-block-synced-color--rgb: 122, 0, 223;
            --wp-bound-block-color: var(--wp-block-synced-color)
        }

        @media (min-resolution:192dpi) {
            :root {
                --wp-admin-border-width-focus: 1.5px
            }
        }

        .wp-element-button {
            cursor: pointer
        }

        :root {
            --wp--preset--font-size--normal: 16px;
            --wp--preset--font-size--huge: 42px
        }

        :root .has-very-light-gray-background-color {
            background-color: #eee
        }

        :root .has-very-dark-gray-background-color {
            background-color: #313131
        }

        :root .has-very-light-gray-color {
            color: #eee
        }

        :root .has-very-dark-gray-color {
            color: #313131
        }

        :root .has-vivid-green-cyan-to-vivid-cyan-blue-gradient-background {
            background: linear-gradient(135deg, #00d084, #0693e3)
        }

        :root .has-purple-crush-gradient-background {
            background: linear-gradient(135deg, #34e2e4, #4721fb 50%, #ab1dfe)
        }

        :root .has-hazy-dawn-gradient-background {
            background: linear-gradient(135deg, #faaca8, #dad0ec)
        }

        :root .has-subdued-olive-gradient-background {
            background: linear-gradient(135deg, #fafae1, #67a671)
        }

        :root .has-atomic-cream-gradient-background {
            background: linear-gradient(135deg, #fdd79a, #004a59)
        }

        :root .has-nightshade-gradient-background {
            background: linear-gradient(135deg, #330968, #31cdcf)
        }

        :root .has-midnight-gradient-background {
            background: linear-gradient(135deg, #020381, #2874fc)
        }

        .has-regular-font-size {
            font-size: 1em
        }

        .has-larger-font-size {
            font-size: 2.625em
        }

        .has-normal-font-size {
            font-size: var(--wp--preset--font-size--normal)
        }

        .has-huge-font-size {
            font-size: var(--wp--preset--font-size--huge)
        }

        .has-text-align-center {
            text-align: center
        }

        .has-text-align-left {
            text-align: left
        }

        .has-text-align-right {
            text-align: right
        }

        #end-resizable-editor-section {
            display: none
        }

        .aligncenter {
            clear: both
        }

        .items-justified-left {
            justify-content: flex-start
        }

        .items-justified-center {
            justify-content: center
        }

        .items-justified-right {
            justify-content: flex-end
        }

        .items-justified-space-between {
            justify-content: space-between
        }

        .screen-reader-text {
            border: 0;
            clip: rect(1px, 1px, 1px, 1px);
            clip-path: inset(50%);
            height: 1px;
            margin: -1px;
            overflow: hidden;
            padding: 0;
            position: absolute;
            width: 1px;
            word-wrap: normal !important
        }

        .screen-reader-text:focus {
            background-color: #ddd;
            clip: auto !important;
            clip-path: none;
            color: #444;
            display: block;
            font-size: 1em;
            height: auto;
            left: 5px;
            line-height: normal;
            padding: 15px 23px 14px;
            text-decoration: none;
            top: 5px;
            width: auto;
            z-index: 100000
        }

        html :where(.has-border-color) {
            border-style: solid
        }

        html :where([style*=border-top-color]) {
            border-top-style: solid
        }

        html :where([style*=border-right-color]) {
            border-right-style: solid
        }

        html :where([style*=border-bottom-color]) {
            border-bottom-style: solid
        }

        html :where([style*=border-left-color]) {
            border-left-style: solid
        }

        html :where([style*=border-width]) {
            border-style: solid
        }

        html :where([style*=border-top-width]) {
            border-top-style: solid
        }

        html :where([style*=border-right-width]) {
            border-right-style: solid
        }

        html :where([style*=border-bottom-width]) {
            border-bottom-style: solid
        }

        html :where([style*=border-left-width]) {
            border-left-style: solid
        }

        html :where(img[class*=wp-image-]) {
            height: auto;
            max-width: 100%
        }

        :where(figure) {
            margin: 0 0 1em
        }

        html :where(.is-position-sticky) {
            --wp-admin--admin-bar--position-offset: var(--wp-admin--admin-bar--height, 0px)
        }

        @media screen and (max-width:600px) {
            html :where(.is-position-sticky) {
                --wp-admin--admin-bar--position-offset: 0px
            }
        }

        .lang-ar .first-content-container {
            flex-direction: row-reverse !important;
        }
    </style>
    <!-- <link rel='stylesheet' id='jet-engine-frontend-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_c3894a4f010f6ad827eeddbf4d87d24b.css?ver=3.6.3' media='all' /> -->
    <link rel='stylesheet' id='jet-engine-frontend-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_1b837697661e721cde35f7d1612c0383.css?ver=3.6.9' media='all' />
    <link rel='stylesheet' id='elementor-frontend-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/frontend.min.css?ver=3.29.2' media='all' />
    <link rel='stylesheet' id='elementor-post-11-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_7daa6e250cc52251dbe46e6eddc87ab5.css?ver=1749121952' media='all' />
    <link rel='stylesheet' id='widget-image-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-image.min.css?ver=3.29.2' media='all' />
    <link rel='stylesheet' id='widget-icon-list-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-icon-list.min.css?ver=3.29.2' media='all' />
    <link rel='stylesheet' id='widget-heading-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-heading.min.css?ver=3.29.2' media='all' />
    <link rel='stylesheet' id='e-motion-fx-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/css/modules/motion-fx.min.css?ver=3.29.2' media='all' />
    <link rel='stylesheet' id='swiper-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/lib/swiper/v8/css/swiper.min.css?ver=8.4.5' media='all' />
    <link rel='stylesheet' id='e-swiper-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/conditionals/e-swiper.min.css?ver=3.29.2' media='all' />
    <link rel='stylesheet' id='widget-nested-carousel-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/css/widget-nested-carousel.min.css?ver=3.29.2' media='all' />
    <link rel='stylesheet' id='widget-counter-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-counter.min.css?ver=3.29.2' media='all' />
    <link rel='stylesheet' id='widget-spacer-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-spacer.min.css?ver=3.29.2' media='all' />
    <link rel='stylesheet' id='widget-nested-accordion-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-nested-accordion.min.css?ver=3.29.2' media='all' />
    <link rel='stylesheet' id='widget-form-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/css/widget-form.min.css?ver=3.29.2' media='all' />
        <link rel='stylesheet' id='elementor-post-16-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_5638991faa30a31d66e41d4bb2fc5553.css?ver=1749125854' media='all' />
        <link rel='stylesheet' id='elementor-post-2407-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_613095ccf0c2ef71cd9801bafd40d187.css?ver=1749125822' media='all' /> 
 
    <style id='global-styles-inline-css'>
        :root {
            --wp--preset--aspect-ratio--square: 1;
            --wp--preset--aspect-ratio--4-3: 4/3;
            --wp--preset--aspect-ratio--3-4: 3/4;
            --wp--preset--aspect-ratio--3-2: 3/2;
            --wp--preset--aspect-ratio--2-3: 2/3;
            --wp--preset--aspect-ratio--16-9: 16/9;
            --wp--preset--aspect-ratio--9-16: 9/16;
            --wp--preset--color--black: #000000;
            --wp--preset--color--cyan-bluish-gray: #abb8c3;
            --wp--preset--color--white: #ffffff;
            --wp--preset--color--pale-pink: #f78da7;
            --wp--preset--color--vivid-red: #cf2e2e;
            --wp--preset--color--luminous-vivid-orange: #ff6900;
            --wp--preset--color--luminous-vivid-amber: #fcb900;
            --wp--preset--color--light-green-cyan: #7bdcb5;
            --wp--preset--color--vivid-green-cyan: #00d084;
            --wp--preset--color--pale-cyan-blue: #8ed1fc;
            --wp--preset--color--vivid-cyan-blue: #0693e3;
            --wp--preset--color--vivid-purple: #9b51e0;
            --wp--preset--color--base: #f9f9f9;
            --wp--preset--color--base-2: #ffffff;
            --wp--preset--color--contrast: #111111;
            --wp--preset--color--contrast-2: #636363;
            --wp--preset--color--contrast-3: #A4A4A4;
            --wp--preset--color--accent: #cfcabe;
            --wp--preset--color--accent-2: #c2a990;
            --wp--preset--color--accent-3: #d8613c;
            --wp--preset--color--accent-4: #b1c5a4;
            --wp--preset--color--accent-5: #b5bdbc;
            --wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
            --wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
            --wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
            --wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
            --wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
            --wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
            --wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
            --wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
            --wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
            --wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
            --wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
            --wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
            --wp--preset--gradient--gradient-1: linear-gradient(to bottom, #cfcabe 0%, #F9F9F9 100%);
            --wp--preset--gradient--gradient-2: linear-gradient(to bottom, #C2A990 0%, #F9F9F9 100%);
            --wp--preset--gradient--gradient-3: linear-gradient(to bottom, #D8613C 0%, #F9F9F9 100%);
            --wp--preset--gradient--gradient-4: linear-gradient(to bottom, #B1C5A4 0%, #F9F9F9 100%);
            --wp--preset--gradient--gradient-5: linear-gradient(to bottom, #B5BDBC 0%, #F9F9F9 100%);
            --wp--preset--gradient--gradient-6: linear-gradient(to bottom, #A4A4A4 0%, #F9F9F9 100%);
            --wp--preset--gradient--gradient-7: linear-gradient(to bottom, #cfcabe 50%, #F9F9F9 50%);
            --wp--preset--gradient--gradient-8: linear-gradient(to bottom, #C2A990 50%, #F9F9F9 50%);
            --wp--preset--gradient--gradient-9: linear-gradient(to bottom, #D8613C 50%, #F9F9F9 50%);
            --wp--preset--gradient--gradient-10: linear-gradient(to bottom, #B1C5A4 50%, #F9F9F9 50%);
            --wp--preset--gradient--gradient-11: linear-gradient(to bottom, #B5BDBC 50%, #F9F9F9 50%);
            --wp--preset--gradient--gradient-12: linear-gradient(to bottom, #A4A4A4 50%, #F9F9F9 50%);
            --wp--preset--font-size--small: 0.9rem;
            --wp--preset--font-size--medium: 1.05rem;
            --wp--preset--font-size--large: clamp(1.39rem, 1.39rem + ((1vw - 0.2rem) * 0.767), 1.85rem);
            --wp--preset--font-size--x-large: clamp(1.85rem, 1.85rem + ((1vw - 0.2rem) * 1.083), 2.5rem);
            --wp--preset--font-size--xx-large: clamp(2.5rem, 2.5rem + ((1vw - 0.2rem) * 1.283), 3.27rem);
            --wp--preset--font-family--body: "Inter", sans-serif;
            --wp--preset--font-family--heading: Cardo;
            --wp--preset--font-family--system-sans-serif: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
            --wp--preset--font-family--system-serif: Iowan Old Style, Apple Garamond, Baskerville, Times New Roman, Droid Serif, Times, Source Serif Pro, serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
            --wp--preset--spacing--20: min(1.5rem, 2vw);
            --wp--preset--spacing--30: min(2.5rem, 3vw);
            --wp--preset--spacing--40: min(4rem, 5vw);
            --wp--preset--spacing--50: min(6.5rem, 8vw);
            --wp--preset--spacing--60: min(10.5rem, 13vw);
            --wp--preset--spacing--70: 3.38rem;
            --wp--preset--spacing--80: 5.06rem;
            --wp--preset--spacing--10: 1rem;
            --wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
            --wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
            --wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
            --wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
        }

        :root {
            --wp--style--global--content-size: 620px;
            --wp--style--global--wide-size: 1280px;
        }

        :where(body) {
            margin: 0;
        }

        .wp-site-blocks {
            padding-top: var(--wp--style--root--padding-top);
            padding-bottom: var(--wp--style--root--padding-bottom);
        }

        .has-global-padding {
            padding-right: var(--wp--style--root--padding-right);
            padding-left: var(--wp--style--root--padding-left);
        }

        .has-global-padding>.alignfull {
            margin-right: calc(var(--wp--style--root--padding-right) * -1);
            margin-left: calc(var(--wp--style--root--padding-left) * -1);
        }

        .has-global-padding :where(:not(.alignfull.is-layout-flow) > .has-global-padding:not(.wp-block-block, .alignfull)) {
            padding-right: 0;
            padding-left: 0;
        }

        .has-global-padding :where(:not(.alignfull.is-layout-flow) > .has-global-padding:not(.wp-block-block, .alignfull))>.alignfull {
            margin-left: 0;
            margin-right: 0;
        }

        .wp-site-blocks>.alignleft {
            float: left;
            margin-right: 2em;
        }

        .wp-site-blocks>.alignright {
            float: right;
            margin-left: 2em;
        }

        .wp-site-blocks>.aligncenter {
            justify-content: center;
            margin-left: auto;
            margin-right: auto;
        }

        :where(.wp-site-blocks)>* {
            margin-block-start: 1.2rem;
            margin-block-end: 0;
        }

        :where(.wp-site-blocks)> :first-child {
            margin-block-start: 0;
        }

        :where(.wp-site-blocks)> :last-child {
            margin-block-end: 0;
        }

        :root {
            --wp--style--block-gap: 1.2rem;
        }

        :root :where(.is-layout-flow)> :first-child {
            margin-block-start: 0;
        }

        :root :where(.is-layout-flow)> :last-child {
            margin-block-end: 0;
        }

        :root :where(.is-layout-flow)>* {
            margin-block-start: 1.2rem;
            margin-block-end: 0;
        }

        :root :where(.is-layout-constrained)> :first-child {
            margin-block-start: 0;
        }

        :root :where(.is-layout-constrained)> :last-child {
            margin-block-end: 0;
        }

        :root :where(.is-layout-constrained)>* {
            margin-block-start: 1.2rem;
            margin-block-end: 0;
        }

        :root :where(.is-layout-flex) {
            gap: 1.2rem;
        }

        :root :where(.is-layout-grid) {
            gap: 1.2rem;
        }

        .is-layout-flow>.alignleft {
            float: left;
            margin-inline-start: 0;
            margin-inline-end: 2em;
        }

        .is-layout-flow>.alignright {
            float: right;
            margin-inline-start: 2em;
            margin-inline-end: 0;
        }

        .is-layout-flow>.aligncenter {
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .is-layout-constrained>.alignleft {
            float: left;
            margin-inline-start: 0;
            margin-inline-end: 2em;
        }

        .is-layout-constrained>.alignright {
            float: right;
            margin-inline-start: 2em;
            margin-inline-end: 0;
        }

        .is-layout-constrained>.aligncenter {
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .is-layout-constrained> :where(:not(.alignleft):not(.alignright):not(.alignfull)) {
            max-width: var(--wp--style--global--content-size);
            margin-left: auto !important;
            margin-right: auto !important;
        }

        .is-layout-constrained>.alignwide {
            max-width: var(--wp--style--global--wide-size);
        }

        body .is-layout-flex {
            display: flex;
        }

        .is-layout-flex {
            flex-wrap: wrap;
            align-items: center;
        }

        .is-layout-flex> :is(*, div) {
            margin: 0;
        }

        body .is-layout-grid {
            display: grid;
        }

        .is-layout-grid> :is(*, div) {
            margin: 0;
        }

        body {
            background-color: var(--wp--preset--color--base);
            color: var(--wp--preset--color--contrast);
            font-family: var(--wp--preset--font-family--body);
            font-size: var(--wp--preset--font-size--medium);
            font-style: normal;
            font-weight: 400;
            line-height: 1.55;
            --wp--style--root--padding-top: 0px;
            --wp--style--root--padding-right: var(--wp--preset--spacing--50);
            --wp--style--root--padding-bottom: 0px;
            --wp--style--root--padding-left: var(--wp--preset--spacing--50);
        }

        a:where(:not(.wp-element-button)) {
            color: var(--wp--preset--color--contrast);
            text-decoration: underline;
        }

        :root :where(a:where(:not(.wp-element-button)):hover) {
            text-decoration: none;
        }

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
            color: var(--wp--preset--color--contrast);
            font-family: var(--wp--preset--font-family--heading);
            font-weight: 400;
            line-height: 1.2;
        }

        h1 {
            font-size: var(--wp--preset--font-size--xx-large);
            line-height: 1.15;
        }

        h2 {
            font-size: var(--wp--preset--font-size--x-large);
        }

        h3 {
            font-size: var(--wp--preset--font-size--large);
        }

        h4 {
            font-size: clamp(1.1rem, 1.1rem + ((1vw - 0.2rem) * 0.767), 1.5rem);
        }

        h5 {
            font-size: var(--wp--preset--font-size--medium);
        }

        h6 {
            font-size: var(--wp--preset--font-size--small);
        }

        :root :where(.wp-element-button, .wp-block-button__link) {
            background-color: var(--wp--preset--color--contrast);
            border-radius: .33rem;
            border-color: var(--wp--preset--color--contrast);
            border-width: 0;
            color: var(--wp--preset--color--base);
            font-family: inherit;
            font-size: var(--wp--preset--font-size--small);
            font-style: normal;
            font-weight: 500;
            line-height: inherit;
            padding-top: 0.6rem;
            padding-right: 1rem;
            padding-bottom: 0.6rem;
            padding-left: 1rem;
            text-decoration: none;
        }

        :root :where(.wp-element-button:hover, .wp-block-button__link:hover) {
            background-color: var(--wp--preset--color--contrast-2);
            border-color: var(--wp--preset--color--contrast-2);
            color: var(--wp--preset--color--base);
        }

        :root :where(.wp-element-button:focus, .wp-block-button__link:focus) {
            background-color: var(--wp--preset--color--contrast-2);
            border-color: var(--wp--preset--color--contrast-2);
            color: var(--wp--preset--color--base);
            outline-color: var(--wp--preset--color--contrast);
            outline-offset: 2px;
        }

        :root :where(.wp-element-button:active, .wp-block-button__link:active) {
            background-color: var(--wp--preset--color--contrast);
            color: var(--wp--preset--color--base);
        }

        :root :where(.wp-element-caption, .wp-block-audio figcaption, .wp-block-embed figcaption, .wp-block-gallery figcaption, .wp-block-image figcaption, .wp-block-table figcaption, .wp-block-video figcaption) {
            color: var(--wp--preset--color--contrast-2);
            font-family: var(--wp--preset--font-family--body);
            font-size: 0.8rem;
        }

        .has-black-color {
            color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-color {
            color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-color {
            color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-color {
            color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-color {
            color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-color {
            color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-color {
            color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-color {
            color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-color {
            color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-color {
            color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-color {
            color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-color {
            color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-base-color {
            color: var(--wp--preset--color--base) !important;
        }

        .has-base-2-color {
            color: var(--wp--preset--color--base-2) !important;
        }

        .has-contrast-color {
            color: var(--wp--preset--color--contrast) !important;
        }

        .has-contrast-2-color {
            color: var(--wp--preset--color--contrast-2) !important;
        }

        .has-contrast-3-color {
            color: var(--wp--preset--color--contrast-3) !important;
        }

        .has-accent-color {
            color: var(--wp--preset--color--accent) !important;
        }

        .has-accent-2-color {
            color: var(--wp--preset--color--accent-2) !important;
        }

        .has-accent-3-color {
            color: var(--wp--preset--color--accent-3) !important;
        }

        .has-accent-4-color {
            color: var(--wp--preset--color--accent-4) !important;
        }

        .has-accent-5-color {
            color: var(--wp--preset--color--accent-5) !important;
        }

        .has-black-background-color {
            background-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-background-color {
            background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-background-color {
            background-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-background-color {
            background-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-background-color {
            background-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-background-color {
            background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-background-color {
            background-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-background-color {
            background-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-background-color {
            background-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-background-color {
            background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-background-color {
            background-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-base-background-color {
            background-color: var(--wp--preset--color--base) !important;
        }

        .has-base-2-background-color {
            background-color: var(--wp--preset--color--base-2) !important;
        }

        .has-contrast-background-color {
            background-color: var(--wp--preset--color--contrast) !important;
        }

        .has-contrast-2-background-color {
            background-color: var(--wp--preset--color--contrast-2) !important;
        }

        .has-contrast-3-background-color {
            background-color: var(--wp--preset--color--contrast-3) !important;
        }

        .has-accent-background-color {
            background-color: var(--wp--preset--color--accent) !important;
        }

        .has-accent-2-background-color {
            background-color: var(--wp--preset--color--accent-2) !important;
        }

        .has-accent-3-background-color {
            background-color: var(--wp--preset--color--accent-3) !important;
        }

        .has-accent-4-background-color {
            background-color: var(--wp--preset--color--accent-4) !important;
        }

        .has-accent-5-background-color {
            background-color: var(--wp--preset--color--accent-5) !important;
        }

        .has-black-border-color {
            border-color: var(--wp--preset--color--black) !important;
        }

        .has-cyan-bluish-gray-border-color {
            border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
        }

        .has-white-border-color {
            border-color: var(--wp--preset--color--white) !important;
        }

        .has-pale-pink-border-color {
            border-color: var(--wp--preset--color--pale-pink) !important;
        }

        .has-vivid-red-border-color {
            border-color: var(--wp--preset--color--vivid-red) !important;
        }

        .has-luminous-vivid-orange-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-amber-border-color {
            border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
        }

        .has-light-green-cyan-border-color {
            border-color: var(--wp--preset--color--light-green-cyan) !important;
        }

        .has-vivid-green-cyan-border-color {
            border-color: var(--wp--preset--color--vivid-green-cyan) !important;
        }

        .has-pale-cyan-blue-border-color {
            border-color: var(--wp--preset--color--pale-cyan-blue) !important;
        }

        .has-vivid-cyan-blue-border-color {
            border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
        }

        .has-vivid-purple-border-color {
            border-color: var(--wp--preset--color--vivid-purple) !important;
        }

        .has-base-border-color {
            border-color: var(--wp--preset--color--base) !important;
        }

        .has-base-2-border-color {
            border-color: var(--wp--preset--color--base-2) !important;
        }

        .has-contrast-border-color {
            border-color: var(--wp--preset--color--contrast) !important;
        }

        .has-contrast-2-border-color {
            border-color: var(--wp--preset--color--contrast-2) !important;
        }

        .has-contrast-3-border-color {
            border-color: var(--wp--preset--color--contrast-3) !important;
        }

        .has-accent-border-color {
            border-color: var(--wp--preset--color--accent) !important;
        }

        .has-accent-2-border-color {
            border-color: var(--wp--preset--color--accent-2) !important;
        }

        .has-accent-3-border-color {
            border-color: var(--wp--preset--color--accent-3) !important;
        }

        .has-accent-4-border-color {
            border-color: var(--wp--preset--color--accent-4) !important;
        }

        .has-accent-5-border-color {
            border-color: var(--wp--preset--color--accent-5) !important;
        }

        .has-vivid-cyan-blue-to-vivid-purple-gradient-background {
            background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
        }

        .has-light-green-cyan-to-vivid-green-cyan-gradient-background {
            background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
        }

        .has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
        }

        .has-luminous-vivid-orange-to-vivid-red-gradient-background {
            background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
        }

        .has-very-light-gray-to-cyan-bluish-gray-gradient-background {
            background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
        }

        .has-cool-to-warm-spectrum-gradient-background {
            background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
        }

        .has-blush-light-purple-gradient-background {
            background: var(--wp--preset--gradient--blush-light-purple) !important;
        }

        .has-blush-bordeaux-gradient-background {
            background: var(--wp--preset--gradient--blush-bordeaux) !important;
        }

        .has-luminous-dusk-gradient-background {
            background: var(--wp--preset--gradient--luminous-dusk) !important;
        }

        .has-pale-ocean-gradient-background {
            background: var(--wp--preset--gradient--pale-ocean) !important;
        }

        .has-electric-grass-gradient-background {
            background: var(--wp--preset--gradient--electric-grass) !important;
        }

        .has-midnight-gradient-background {
            background: var(--wp--preset--gradient--midnight) !important;
        }

        .has-gradient-1-gradient-background {
            background: var(--wp--preset--gradient--gradient-1) !important;
        }

        .has-gradient-2-gradient-background {
            background: var(--wp--preset--gradient--gradient-2) !important;
        }

        .has-gradient-3-gradient-background {
            background: var(--wp--preset--gradient--gradient-3) !important;
        }

        .has-gradient-4-gradient-background {
            background: var(--wp--preset--gradient--gradient-4) !important;
        }

        .has-gradient-5-gradient-background {
            background: var(--wp--preset--gradient--gradient-5) !important;
        }

        .has-gradient-6-gradient-background {
            background: var(--wp--preset--gradient--gradient-6) !important;
        }

        .has-gradient-7-gradient-background {
            background: var(--wp--preset--gradient--gradient-7) !important;
        }

        .has-gradient-8-gradient-background {
            background: var(--wp--preset--gradient--gradient-8) !important;
        }

        .has-gradient-9-gradient-background {
            background: var(--wp--preset--gradient--gradient-9) !important;
        }

        .has-gradient-10-gradient-background {
            background: var(--wp--preset--gradient--gradient-10) !important;
        }

        .has-gradient-11-gradient-background {
            background: var(--wp--preset--gradient--gradient-11) !important;
        }

        .has-gradient-12-gradient-background {
            background: var(--wp--preset--gradient--gradient-12) !important;
        }

        .has-small-font-size {
            font-size: var(--wp--preset--font-size--small) !important;
        }

        .has-medium-font-size {
            font-size: var(--wp--preset--font-size--medium) !important;
        }

        .has-large-font-size {
            font-size: var(--wp--preset--font-size--large) !important;
        }

        .has-x-large-font-size {
            font-size: var(--wp--preset--font-size--x-large) !important;
        }

        .has-xx-large-font-size {
            font-size: var(--wp--preset--font-size--xx-large) !important;
        }

        .has-body-font-family {
            font-family: var(--wp--preset--font-family--body) !important;
        }

        .has-heading-font-family {
            font-family: var(--wp--preset--font-family--heading) !important;
        }

        .has-system-sans-serif-font-family {
            font-family: var(--wp--preset--font-family--system-sans-serif) !important;
        }

        .has-system-serif-font-family {
            font-family: var(--wp--preset--font-family--system-serif) !important;
        }

        :where(.wp-site-blocks *:focus) {
            outline-width: 2px;
            outline-style: solid
        }
    </style>
    <style id='wp-block-template-skip-link-inline-css'>
        .skip-link.screen-reader-text {
            border: 0;
            clip: rect(1px, 1px, 1px, 1px);
            clip-path: inset(50%);
            height: 1px;
            margin: -1px;
            overflow: hidden;
            padding: 0;
            position: absolute !important;
            width: 1px;
            word-wrap: normal !important;
        }

        .skip-link.screen-reader-text:focus {
            background-color: #eee;
            clip: auto !important;
            clip-path: none;
            color: #444;
            display: block;
            font-size: 1em;
            height: auto;
            left: 5px;
            line-height: normal;
            padding: 15px 23px 14px;
            text-decoration: none;
            top: 5px;
            width: auto;
            z-index: 100000;
        }
    </style>
    <link href="{{ mix('css/cview-nav-landing.css') }}" rel="stylesheet">
    <link rel='stylesheet' id='elementor-frontend-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/frontend.min.css?ver=3.27.3' media='all' />
    <link rel='stylesheet' id='elementor-post-11-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_7daa6e250cc52251dbe46e6eddc87ab5.css?ver=1738745532' media='all' />
    <link rel='stylesheet' id='widget-image-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-image.min.css?ver=3.27.3' media='all' />
    <link rel='stylesheet' id='widget-icon-list-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-icon-list.min.css?ver=3.27.3' media='all' />
    <link rel='stylesheet' id='widget-heading-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-heading.min.css?ver=3.27.3' media='all' />
    <link rel='stylesheet' id='widget-text-editor-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-text-editor.min.css?ver=3.27.3' media='all' />
    <link rel='stylesheet' id='e-motion-fx-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/css/modules/motion-fx.min.css?ver=3.27.2' media='all' />
    <link rel='stylesheet' id='swiper-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/lib/swiper/v8/css/swiper.min.css?ver=8.4.5' media='all' />
    <link rel='stylesheet' id='e-swiper-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/conditionals/e-swiper.min.css?ver=3.27.3' media='all' />
    <link rel='stylesheet' id='widget-nested-carousel-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/css/widget-nested-carousel.min.css?ver=3.27.2' media='all' />
    <link rel='stylesheet' id='widget-counter-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-counter.min.css?ver=3.27.3' media='all' />
    <link rel='stylesheet' id='widget-spacer-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-spacer.min.css?ver=3.27.3' media='all' />
    <link rel='stylesheet' id='widget-nested-accordion-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-nested-accordion.min.css?ver=3.27.3' media='all' />
    <link rel='stylesheet' id='widget-form-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/css/widget-form.min.css?ver=3.27.2' media='all' />
    <link rel='stylesheet' id='elementor-post-16-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_a4c92cfad008eaecd8bc17a6719ec6a6.css?ver=1738745533' media='all' />
    <script src="https://fgrealty.qa/lp/wp-includes/js/jquery/jquery.min.js?ver=3.7.1" id="jquery-core-js"></script>
    <script defer src="https://fgrealty.qa/lp/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1" id="jquery-migrate-js"></script>
    <link rel="https://api.w.org/" href="https://fgrealty.qa/lp/wp-json/" />
    <link rel="alternate" title="JSON" type="application/json" href="https://fgrealty.qa/lp/wp-json/wp/v2/pages/16" />
    <link rel="canonical" href="https://www.fgrealty.qa/en/projects/lusail-marina-heights" />
    <script defer src="data:text/javascript;base64,KGZ1bmN0aW9uKHcsZCxzLGwsaSl7d1tsXT13W2xdfHxbXTt3W2xdLnB1c2goeydndG0uc3RhcnQnOg0KbmV3IERhdGUoKS5nZXRUaW1lKCksZXZlbnQ6J2d0bS5qcyd9KTt2YXIgZj1kLmdldEVsZW1lbnRzQnlUYWdOYW1lKHMpWzBdLA0Kaj1kLmNyZWF0ZUVsZW1lbnQocyksZGw9bCE9J2RhdGFMYXllcic/JyZsPScrbDonJztqLmFzeW5jPXRydWU7ai5zcmM9DQonaHR0cHM6Ly93d3cuZ29vZ2xldGFnbWFuYWdlci5jb20vZ3RtLmpzP2lkPScraStkbDtmLnBhcmVudE5vZGUuaW5zZXJ0QmVmb3JlKGosZik7DQp9KSh3aW5kb3csZG9jdW1lbnQsJ3NjcmlwdCcsJ2RhdGFMYXllcicsJ0dUTS1XUEQzTjNOQycpOw=="></script>

    <style>
        html[dir="rtl"] .elementor-element {
            text-align: right !important;
        }

        html[dir="rtl"] .quote {
            text-align: left;
        }

        @media (max-width: 768px) {
            html[dir="rtl"] .title {
                text-align: center;
            }

            html[dir="rtl"] .logo {
                text-align: center;
            }

            html[dir="rtl"] .invest {
                text-align: center;
            }
        }

        html[dir="rtl"] .unit {
            padding-right: 0;
        }

        @media (min-width: 768px) {
            html[dir="rtl"] .unit {
                padding-right: 50px;
            }
        }
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
        .e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
            background-image: none !important;
        }

        .elementor-16 .elementor-element.elementor-element-923bc52 img {
            width: 280px;
        }

        .elementor-16 .elementor-element.elementor-element-21d1bc2:not(.elementor-motion-effects-element-type-background),
        .elementor-16 .elementor-element.elementor-element-21d1bc2>.elementor-motion-effects-container>.elementor-motion-effects-layer {
            background-image: url({{$image8BigURL}});
        }

        .elementor-element-6893f11 {
            margin-top:100px;
        }

        .elementor-element-8db0a7f {
            margin-bottom:0;
        }

        .elementor-16 .elementor-element.elementor-element-afc8dc9:not(.elementor-motion-effects-element-type-background),
        .elementor-16 .elementor-element.elementor-element-afc8dc9>.elementor-motion-effects-container>.elementor-motion-effects-layer {
            background-image: url({{$image11URL}});
            height: 420px;
            width: 100%;
            background-position: center center;
            background-repeat: no-repeat;
            background-size: cover;
            border-radius: 6px;
            margin-left: -2px;
        }

        .logo {
            margin-bottom: 20px;
        }

        .gm-style-iw-chr {
            display: none;
            visibility: hidden;
        }

        .elementor-16 .elementor-element.elementor-element-b56a821 {
            margin-bottom: 30px;
        }

        .elementor-16 .elementor-element.elementor-element-9019f7e:not(.elementor-motion-effects-element-type-background),
        .elementor-16 .elementor-element.elementor-element-9019f7e>.elementor-motion-effects-container>.elementor-motion-effects-layer {
            background-image: url({{$image6BigURL}});
        }

        .elementor-16 .elementor-element.elementor-element-3546133:not(.elementor-motion-effects-element-type-background),
        .elementor-16 .elementor-element.elementor-element-3546133>.elementor-motion-effects-container>.elementor-motion-effects-layer {
            background-image: url({{$image2BigURL}});
        }

        @media screen and (max-height: 1024px) {

            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }

        @media screen and (max-height: 640px) {

            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
            .e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
                background-image: none !important;
            }
        }
    </style>
    <style class='wp-fonts-local'>
        @font-face {
            font-family: Inter;
            font-style: normal;
            font-weight: 300 900;
            font-display: fallback;
            src: url('https://fgrealty.qa/lp/wp-content/themes/twentytwentyfour/assets/fonts/inter/Inter-VariableFont_slnt,wght.woff2') format('woff2');
            font-stretch: normal;
        }

        @font-face {
            font-family: Cardo;
            font-style: normal;
            font-weight: 400;
            font-display: fallback;
            src: url('https://fgrealty.qa/lp/wp-content/themes/twentytwentyfour/assets/fonts/cardo/cardo_normal_400.woff2') format('woff2');
        }

        @font-face {
            font-family: Cardo;
            font-style: italic;
            font-weight: 400;
            font-display: fallback;
            src: url('https://fgrealty.qa/lp/wp-content/themes/twentytwentyfour/assets/fonts/cardo/cardo_italic_400.woff2') format('woff2');
        }

        @font-face {
            font-family: Cardo;
            font-style: normal;
            font-weight: 700;
            font-display: fallback;
            src: url('https://fgrealty.qa/lp/wp-content/themes/twentytwentyfour/assets/fonts/cardo/cardo_normal_700.woff2') format('woff2');
        }
    </style>
    <link rel="icon" href="https://fgrealty.qa/lp/wp-content/uploads/2024/05/favicon.svg" sizes="32x32" />
    <link rel="icon" href="https://fgrealty.qa/lp/wp-content/uploads/2024/05/favicon.svg" sizes="192x192" />
    <link rel="apple-touch-icon" href="https://fgrealty.qa/lp/wp-content/uploads/2024/05/favicon.svg" />
    <meta name="msapplication-TileImage" content="https://fgrealty.qa/lp/wp-content/uploads/2024/05/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />

</head>
@include('components.next.chat-widget-for-landing')

<body class="@if(app()->isLocale('ar')) lang-ar @endif page-template page-template-elementor_canvas page page-id-16 wp-embed-responsive elementor-default elementor-template-canvas elementor-kit-11 elementor-page elementor-page-16">
    <div data-elementor-type="wp-page" data-elementor-id="16" class="elementor elementor-16" data-elementor-post-type="page">
        <div class="elementor-element elementor-element-07c7ac2 e-flex e-con-boxed e-con e-parent" data-id="07c7ac2" data-element_type="container">
            <div class="e-con-inner">
                <nav class="navbar">
                    <div class="elementor-element elementor-element-a2860dd elementor-hidden-tablet elementor-hidden-mobile elementor-widget elementor-widget-image" data-id="a2860dd" data-element_type="widget" data-widget_type="image.default">
                        <div class="elementor-widget-container"> <a href="{{persistQueryParamsToURL('/')}}"> <img decoding="async" width="97" height="48" src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/FGREALTY_logo.svg" class="attachment-large size-large wp-image-29" alt="" /> </a></div>
                    </div>
                    <div class="elementor-element elementor-element-f9d68f2 elementor-hidden-desktop elementor-hidden-tablet elementor-hidden-mobile elementor-widget elementor-widget-image" data-id="f9d68f2" data-element_type="widget" data-widget_type="image.default">
                        <div class="elementor-widget-container"> <noscript><img decoding="async" width="20" height="14" src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/menu_sl2.svg" class="attachment-large size-large wp-image-37" alt="" /></noscript><img decoding="async" width="20" height="14" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%2020%2014%22%3E%3C/svg%3E' data-src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/menu_sl2.svg" class="lazyload attachment-large size-large wp-image-37" alt="" /></div>
                    </div>
                    <div class="elementor-element elementor-element-62dac38 elementor-hidden-desktop elementor-widget elementor-widget-image" data-id="62dac38" data-element_type="widget" data-widget_type="image.default">
                        <div class="elementor-widget-container"> <a href="{{persistQueryParamsToURL('/')}}"> <noscript><img decoding="async" width="106" height="15" src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/FGREALTY_m.svg" class="attachment-large size-large wp-image-30" alt="" /></noscript><img decoding="async" width="106" height="15" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20106%2015%22%3E%3C/svg%3E' data-src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/FGREALTY_m.svg" class="lazyload attachment-large size-large wp-image-30" alt="" /> </a></div>
                    </div>

                    <div class="hamburger" id="hamburger">
                        <img src="{{$image10URL}}">
                    </div>

                    <ul class="nav-links" id="nav-menu">
                        <li class="nav-links__closeCta"><a id="navLinksCloseCta" href="#">&times;</a></li>
                        <li class="elementor-icon-list-item elementor-inline-item"> <a href="#about" class="clickable"> <span class="elementor-icon-list-text">{{__('About Lusail Marina Heights')}}</span> </a></li>
                        <li class="elementor-icon-list-item elementor-inline-item"> <a href="#facilities" class="clickable"> <span class="elementor-icon-list-text">{{__('Amenities')}}</span> </a></li>
                        <!-- <li class="elementor-icon-list-item elementor-inline-item"> <a href="#agents"> <span class="elementor-icon-list-text">Agents</span> </a></li> -->

                        <li class="elementor-icon-list-item elementor-inline-item"> <a href="#pricing" class="clickable"> <span class="elementor-icon-list-text">{{__('Pricing')}}</span> </a></li>
                        <li class="request_offer"> <a href="#enquire" class="clickable"> <span class="elementor-icon-list-text">{{__('Request Offer')}}</span> </a></li>

                    </ul>
                </nav>
                {{-- <div class="nav-links elementor-element elementor-element-e9c08f6 elementor-icon-list--layout-inline  elementor-list-item-link-inline elementor-align-right elementor-widget elementor-widget-icon-list" id="nav-menu" data-id="e9c08f6" data-element_type="widget" data-widget_type="icon-list.default">
                    <div class="elementor-widget-container">
                        <ul class="elementor-icon-list-items elementor-inline-items">
                            <li class="nav-links__closeCta"><a id="navLinksCloseCta" href="#">&times;</a></li>
                            <!-- <li class="elementor-icon-list-item elementor-inline-item"> <a href="#listings"> <span class="elementor-icon-list-text">Available Properties</span> </a></li> -->
                            <li class="elementor-icon-list-item elementor-inline-item"> <a href="#about"> <span class="elementor-icon-list-text">About CView</span> </a></li>
                            <!-- <li class="elementor-icon-list-item elementor-inline-item"> <a href="#agents"> <span class="elementor-icon-list-text">Agents</span> </a></li> -->
                            <li class="elementor-icon-list-item elementor-inline-item"> <a href="#amenities"> <span class="elementor-icon-list-text">Amenities</span> </a></li>
                            <li class="elementor-icon-list-item elementor-inline-item"> <a href="#pricing"> <span class="elementor-icon-list-text">Pricing</span> </a></li>
                          <!-- <li class="elementor-icon-list-item elementor-inline-item"> <a href="#amenities"> <span class="elementor-icon-list-text">Amenities</span> </a></li> -->
                        </ul>
                    </div>
                </div> --}}
                {{-- <div class="elementor-element elementor-element-714d3c2 elementor-widget elementor-widget-button" data-id="714d3c2" data-element_type="widget" data-widget_type="button.default">
                    <div class="elementor-widget-container">
                        <div class="elementor-button-wrapper"> <a class="elementor-button elementor-button-link elementor-size-sm" href="#enquire"> <span class="elementor-button-content-wrapper"> <span class="elementor-button-text">Request Offer</span> </span> </a></div>
                    </div>
                </div> --}}
            </div>
        </div>
        <div class="elementor-element elementor-element-b56a821 e-flex e-con-boxed e-con e-parent" id="about" data-id="b56a821" data-element_type="container">
            <div class="first-content-container e-con-inner" style="align-items: flex-start">
                <div class="elementor-element elementor-element-afc8dc9 e-con-full e-flex e-con e-child" aria-label=“” data-id="21d1bc2" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}"></div>
                {{-- <div class="elementor-element elementor-element-afc8dc9 e-con-full e-flex e-con e-child" data-id="afc8dc9" data-element_type="container">
                    <div class="elementor-element elementor-element-923bc52 elementor-widget elementor-widget-image" data-id="923bc52" data-element_type="widget" data-widget_type="image.default">
                        <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="432" height="136" src="/images/svg/CView_LogoGray.svg" class="attachment-large size-large wp-image-402" alt="" /></noscript><img loading="lazy" decoding="async" width="432" height="136" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20432%20136%22%3E%3C/svg%3E' data-src="/images/svg/CView_LogoGray.svg" class="lazyload attachment-large size-large wp-image-402" alt="" /></div>
                    </div>
                </div> --}}
                <div class=" elementor-element elementor-element-07680bf e-con-full e-flex e-con e-child" data-id="07680bf" data-element_type="container">
                    <div class="elementor-element elementor-element-eef70ca elementor-widget elementor-widget-heading" data-id="eef70ca" data-element_type="widget" data-widget_type="heading.default">
                        <div class="elementor-widget-container logo">
                            <noscript><img loading="lazy" decoding="async" width="132" height="35" src="/images/projects/marina-heights/logo.png" class="attachment-large size-large wp-image-402" alt="" /></noscript><img loading="lazy" decoding="async" width="132" height="20" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20432%20136%22%3E%3C/svg%3E' data-src="/images/projects/marina-heights/logo.png" class="lazyload attachment-large size-large wp-image-402" alt="" />
                        </div>
                        <div class="elementor-widget-container">
                            <h2 class="elementor-heading-title elementor-size-default">{{__('Lusail Marina Heights – A New Era of Luxury Living')}}</h2>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-a81d398 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="a81d398" data-element_type="widget" data-widget_type="text-editor.default">
                        <div class="elementor-widget-container">
                            {{-- <p>{{__('Discover Lusail Marina Heights – Where Prestige Meets Comfort')}}</p> --}}
                            <p>{{__('Welcome to Lusail Marina Heights, a masterpiece of modern architecture set in Qatar’s most prestigious waterfront destination. This exclusive off-plan high-rise redefines luxury with its sleek design, panoramic skyline views, and world-class amenities.')}}</p>
                            <p>{{__('Located in the heart of Lusail Marina, this development offers the perfect blend of urban sophistication and coastal tranquility. Whether you’re looking for a stylish city retreat or a smart investment, Lusail Marina Heights is your gateway to the future of luxury living.')}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="elementor-element elementor-element-5661e2d e-flex e-con-boxed e-con e-parent" style="margin-top: 80px" data-id="5661e2d" data-element_type="container">
            <div class="e-con-inner">
                <div class="elementor-element elementor-element-393f7b4 e-con-full e-flex e-con e-child" data-id="393f7b4" data-element_type="container">
                    <div class="elementor-element elementor-element-77ab129 elementor-widget elementor-widget-image" data-id="77ab129" data-element_type="widget" data-settings="{&quot;motion_fx_translateY_effect&quot;:&quot;yes&quot;,&quot;motion_fx_translateY_speed&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:1,&quot;sizes&quot;:[]},&quot;motion_fx_motion_fx_scrolling&quot;:&quot;yes&quot;,&quot;motion_fx_translateY_direction&quot;:&quot;negative&quot;,&quot;motion_fx_devices&quot;:[&quot;desktop&quot;],&quot;motion_fx_translateY_affectedRange&quot;:{&quot;unit&quot;:&quot;%&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:{&quot;start&quot;:0,&quot;end&quot;:100}}}" data-widget_type="image.default">
                        <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="768" height="1087" src="{{$image1URL}}" class="attachment-full size-full wp-image-1634" alt="" srcset="{{$image1URL}} 768w, {{$image1URL}} 723w" sizes="(max-width: 768px) 100vw, 768px" /></noscript><img loading="lazy" decoding="async" width="768" height="1087" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20768%201087%22%3E%3C/svg%3E' data-src="{{$image1URL}}" class="lazyload attachment-full size-full wp-image-1634" alt="" data-srcset="{{$image1URL}} 768w, {{$image1URL}} 723w" data-sizes="(max-width: 768px) 100vw, 768px" /></div>
                    </div>
                </div>
                <div class="elementor-element elementor-element-078a62d e-con-full e-flex e-con e-child" data-id="078a62d" data-element_type="container">
                    <div class="elementor-element elementor-element-a6fbdba elementor-widget elementor-widget-image" data-id="a6fbdba" data-element_type="widget" data-settings="{&quot;motion_fx_motion_fx_scrolling&quot;:&quot;yes&quot;,&quot;motion_fx_translateY_effect&quot;:&quot;yes&quot;,&quot;motion_fx_translateY_speed&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:2,&quot;sizes&quot;:[]},&quot;motion_fx_devices&quot;:[&quot;desktop&quot;],&quot;motion_fx_translateY_affectedRange&quot;:{&quot;unit&quot;:&quot;%&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:{&quot;start&quot;:0,&quot;end&quot;:100}}}" data-widget_type="image.default">
                        <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="768" height="576" src="{{$image2URL}}" class="attachment-full size-full wp-image-1635" alt="" srcset="{{$image2URL}} 768w" sizes="(max-width: 768px) 100vw, 768px" /></noscript><img loading="lazy" decoding="async" width="768" height="576" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20768%20576%22%3E%3C/svg%3E' data-src="{{$image2URL}}" class="lazyload attachment-full size-full wp-image-1635" alt="" data-srcset="{{$image2URL}} 768w" data-sizes="(max-width: 768px) 100vw, 768px" /></div>
                    </div>
                </div>
                <div class="elementor-element elementor-element-655cacb e-con-full e-flex e-con e-child" data-id="655cacb" data-element_type="container">
                    <div class="elementor-element elementor-element-6a50e1c elementor-widget elementor-widget-image" data-id="6a50e1c" data-element_type="widget" data-widget_type="image.default">
                        <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="768" height="853" src="{{$image3URL}}" class="attachment-full size-full wp-image-1637" alt="" srcset="{{$image3URL}} 768w" sizes="(max-width: 768px) 100vw, 768px" /></noscript><img loading="lazy" decoding="async" width="768" height="853" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20768%20853%22%3E%3C/svg%3E' data-src="{{$image3URL}}" class="lazyload attachment-full size-full wp-image-1637" alt="" data-srcset="{{$image3URL}} 768w" data-sizes="(max-width: 768px) 100vw, 768px" /></div>
                    </div>
                </div>
                <div class="elementor-element elementor-element-e01e890 e-con-full elementor-hidden-mobile e-flex e-con e-child" data-id="e01e890" data-element_type="container">
                    <div class="elementor-element elementor-element-4dbfbef elementor-widget elementor-widget-image" data-id="4dbfbef" data-element_type="widget" data-settings="{&quot;motion_fx_motion_fx_scrolling&quot;:&quot;yes&quot;,&quot;motion_fx_translateY_effect&quot;:&quot;yes&quot;,&quot;motion_fx_devices&quot;:[&quot;desktop&quot;],&quot;motion_fx_translateY_speed&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:1,&quot;sizes&quot;:[]},&quot;motion_fx_translateY_affectedRange&quot;:{&quot;unit&quot;:&quot;%&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:{&quot;start&quot;:0,&quot;end&quot;:100}}}" data-widget_type="image.default">
                        <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="768" height="576" src="{{$image4URL}}" class="attachment-full size-full wp-image-1636" alt="" srcset="{{$image4URL}} 768w, {{$image4URL}} 300w" sizes="(max-width: 768px) 100vw, 768px" /></noscript><img loading="lazy" decoding="async" width="768" height="576" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20768%20576%22%3E%3C/svg%3E' data-src="{{$image4URL}}" class="lazyload attachment-full size-full wp-image-1636" alt="" data-srcset="{{$image4URL}} 768w, {{$image4URL}} 300w" data-sizes="(max-width: 768px) 100vw, 768px" /></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="elementor-element elementor-element-6893f11 e-flex e-con-boxed e-con e-parent" data-id="6893f11" data-element_type="container" id="listings">
            <div class="e-con-inner">
                <div class="elementor-element elementor-element-45a6361 elementor-widget elementor-widget-heading" data-id="45a6361" data-element_type="widget" data-widget_type="heading.default">
                    <div class="elementor-widget-container">
                        <h2 class="elementor-heading-title elementor-size-default">{{__('Exquisite Residences Designed for Elegance & Comfort')}}</h2>
                    </div>
                </div>
                <div class="elementor-element elementor-element-c1c34a0 e-flex e-con-boxed e-con e-child" data-id="c1c34a0" data-element_type="container">
                    <div class="e-con-inner" style="align-items: flex-end;">
                        <div class="elementor-element elementor-element-91b82e3 e-con-full e-flex e-con e-child" data-id="91b82e3" data-element_type="container">
                            <div class="elementor-element elementor-element-acbeb05 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="acbeb05" data-element_type="widget" data-widget_type="text-editor.default">
                                <div class="elementor-widget-container">
                                    <p>{{__('Choose from spacious 1, 2, and 3-bedroom apartments, each crafted to offer unparalleled comfort, elegance, and breathtaking views.')}}</p>
                                        <p>{{__('Every unit is meticulously designed with:')}}</p>
                                        <p>{{__('✔ Floor-to-ceiling windows offering stunning marina and sea views.')}}</p>
                                        <p>{{__('✔ Premium finishes and contemporary interiors for a refined lifestyle.')}}</p>
                                        <p>{{__('✔ Open-plan living spaces that maximize natural light and comfort.')}}</p>
                                        <p>{{__('Whether you seek serenity, exclusivity, or a smart investment opportunity, Lusail Marina Heights delivers an exceptional living experience in one of Qatar’s most sought-after locations.')}}</p>
                                </div>
                            </div>
                        </div>
                        <div class="elementor-element elementor-element-db5d6d1 e-con-full e-flex e-con e-child" data-id="db5d6d1" data-element_type="container">
                            <div class="elementor-element elementor-element-4729af4 elementor-align-right elementor-tablet-align-left elementor-widget elementor-widget-button" data-id="4729af4" data-element_type="widget" data-widget_type="button.default">
                                <div class="elementor-widget-container">
                                    <div class="elementor-button-wrapper"> <a class="elementor-button elementor-button-link elementor-size-sm" href="#enquire"> <span class="elementor-button-content-wrapper"> <span class="elementor-button-text">{{__('Get a Quote')}}</span> </span> </a></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {{--
        <div class="elementor-element elementor-element-2a06e43 e-flex e-con-boxed e-con e-parent e-lazyloaded" data-id="2a06e43" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}" style="margin-top: 40px">
            <div class="e-con-inner">
                <div class="elementor-element elementor-element-c83e2c4 elementor-widget elementor-widget-jet-listing-grid" data-id="c83e2c4" data-element_type="widget" data-settings="{&quot;columns&quot;:&quot;4&quot;,&quot;columns_tablet&quot;:&quot;2&quot;,&quot;columns_mobile&quot;:&quot;1&quot;}" data-widget_type="jet-listing-grid.default">
                    <div class="elementor-widget-container">
                        <div class="jet-listing-grid jet-listing">
                            <div class="jet-listing-grid__items grid-col-desk-4 grid-col-tablet-2 grid-col-mobile-1 jet-listing-grid--2407 jet-equal-columns__wrapper" data-queried-id="16|WP_Post" data-nav="{&quot;enabled&quot;:false,&quot;type&quot;:null,&quot;more_el&quot;:null,&quot;query&quot;:[],&quot;widget_settings&quot;:{&quot;lisitng_id&quot;:2407,&quot;posts_num&quot;:4,&quot;columns&quot;:4,&quot;columns_tablet&quot;:2,&quot;columns_mobile&quot;:1,&quot;column_min_width&quot;:240,&quot;column_min_width_tablet&quot;:240,&quot;column_min_width_mobile&quot;:240,&quot;inline_columns_css&quot;:false,&quot;is_archive_template&quot;:&quot;&quot;,&quot;post_status&quot;:[&quot;publish&quot;],&quot;use_random_posts_num&quot;:&quot;&quot;,&quot;max_posts_num&quot;:9,&quot;not_found_message&quot;:&quot;No data was found&quot;,&quot;is_masonry&quot;:false,&quot;equal_columns_height&quot;:&quot;yes&quot;,&quot;use_load_more&quot;:&quot;&quot;,&quot;load_more_id&quot;:&quot;&quot;,&quot;load_more_type&quot;:&quot;click&quot;,&quot;load_more_offset&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:0,&quot;sizes&quot;:[]},&quot;use_custom_post_types&quot;:&quot;&quot;,&quot;custom_post_types&quot;:[],&quot;hide_widget_if&quot;:&quot;&quot;,&quot;carousel_enabled&quot;:&quot;&quot;,&quot;slides_to_scroll&quot;:&quot;1&quot;,&quot;arrows&quot;:&quot;true&quot;,&quot;arrow_icon&quot;:&quot;fa fa-angle-left&quot;,&quot;dots&quot;:&quot;&quot;,&quot;autoplay&quot;:&quot;true&quot;,&quot;pause_on_hover&quot;:&quot;true&quot;,&quot;autoplay_speed&quot;:5000,&quot;infinite&quot;:&quot;true&quot;,&quot;center_mode&quot;:&quot;&quot;,&quot;effect&quot;:&quot;slide&quot;,&quot;speed&quot;:500,&quot;inject_alternative_items&quot;:&quot;&quot;,&quot;injection_items&quot;:[],&quot;scroll_slider_enabled&quot;:&quot;&quot;,&quot;scroll_slider_on&quot;:[&quot;desktop&quot;,&quot;tablet&quot;,&quot;mobile&quot;],&quot;custom_query&quot;:false,&quot;custom_query_id&quot;:&quot;3&quot;,&quot;_element_id&quot;:&quot;&quot;,&quot;collapse_first_last_gap&quot;:false,&quot;list_items_wrapper_tag&quot;:&quot;div&quot;,&quot;list_item_tag&quot;:&quot;div&quot;,&quot;empty_items_wrapper_tag&quot;:&quot;div&quot;}}" data-page="1" data-pages="1" data-listing-source="rest_api_endpoint" data-listing-id="2407" data-query-id="">
                                @foreach($listings as $listing)
                                    @include('projects.marina-heights.list-item')
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        --}}

        <div style="margin:40px 0;">
            <div id="map" style="height: 400px; width: 100%; border-radius: 6px;"></div>

            <script>
                function initMap() {
                    var location = {
                        lat: 25.398231,
                        lng: 51.519114
                    };

                    var map = new google.maps.Map(document.getElementById("map"), {
                        zoom: 15,
                        center: location,
                        mapTypeId: "roadmap",
                    });

                    var marker = new google.maps.Marker({
                        position: location,
                        map: map,
                        title: "{{__('Marina Heights')}}",
                        icon: {
                            url: "{{ asset('images/projects/cview/landing/pin.png') }}",
                            scaledSize: new google.maps.Size(50, 50)
                        }
                    });

                    var infoWindow = new google.maps.InfoWindow({
                        content: `
                                <div style="display: flex; align-items: center; flex-direction:column; padding: 30px 15px 15px 20px">
                                    <img src="{{ asset('/images/projects/marina-height/logo.png') }}" alt="Logo" style="width: 60px; height: 30px; object-fit: contain; margin-bottom:5px">
                                    <div style="font-size:16px; font-family:Noto Kufi Arabic ">{{__('Marina Heights')}}</div>
                                </div>`,
                    });
                    infoWindow.open(map, marker);
                }
            </script>

            <script async defer src="https://maps.googleapis.com/maps/api/js?key={{env('GOOGLE_MAPS_API_KEY')}}&callback=initMap"></script>

            {{-- <iframe
                src="https://www.google.com/maps/embed?pb=!1m17!1m12!1m3!1d3627.4197781486982!2d51.522130999999995!3d25.406266!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m2!1m1!2zMjXCsDI0JzIyLjYiTiA1McKwMzEnMTkuNyJF!5e1!3m2!1sro!2sro!4v1740695925880!5m2!1sro!2sro" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade">
            </iframe> --}}
        </div>
        {{-- <div class="elementor-element elementor-element-d11d3c4 e-flex e-con-boxed e-con e-parent" data-id="d11d3c4" data-element_type="container" id="amenities">
            <div class="e-con-inner">
                <div class="elementor-element elementor-element-cc23a3c e-con-full e-flex e-con e-child" data-id="cc23a3c" data-element_type="container">
                    <div class="elementor-element elementor-element-0dde2d5 elementor-widget elementor-widget-heading" data-id="0dde2d5" data-element_type="widget" data-widget_type="heading.default">
                        <div class="elementor-widget-container">
                            <h2 class="elementor-heading-title elementor-size-default">Amenities &amp; Facilities</h2>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-1b1732e elementor-widget__width-initial elementor-widget elementor-widget-heading" data-id="1b1732e" data-element_type="widget" data-widget_type="heading.default">
                        <div class="elementor-widget-container">
                            <h2 class="elementor-heading-title elementor-size-default">DISCOVER LUSAIL'S PREMIER RESIDENCES</h2>
                        </div>
                    </div>
                </div>
                <div class="elementor-element elementor-element-a50370d e-con-full e-flex e-con e-child" data-id="a50370d" data-element_type="container">
                    <div class="elementor-element elementor-element-e6eb3ab elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="e6eb3ab" data-element_type="widget" data-widget_type="text-editor.default">
                        <div class="elementor-widget-container">
                            <p>Indulge in luxury living at CView Residence. Immerse yourself in a world of premium amenities, breathtaking waterfront views, and unparalleled comfort.</p>
                            <p>Elevate your lifestyle today.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div> --}}
        <style>
            .ctaContainer {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-wrap: wrap;
                gap: 10px;
                width: 100%;
            }


            .ctaContainer__inner {
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                flex-wrap: wrap;
                gap: 20px;
            }

            .ctaButton {
                display: inline-flex;
                justify-content: center;
                align-items: center;
                border-radius: 6px;
                border: 1px solid black;
                padding: 16px 24px;
                transition: all 0.3s;
                background-color: black;
                color: white;
                fill: white;
                width: 100%;
                max-width: 330px;
            }

            .ctaButton::after {
                display: inline-flex;
                content: '\2192';
                padding-left: 8px;
            }

            .ctaButton:hover {
                color: black;
                fill: black;
                background-color: white;
            }

            @media screen and (min-width: 900px) {
                .ctaContainer__inner {
                    flex-direction: row;
                    justify-content: flex-start;
                    flex-wrap: nowrap;
                }

                .ctaButton {
                    max-width: fit-content;
                    flex: 1;
                }
            }

            .btn--loadingState {
                opacity: 0.65;
                cursor: not-allowed !important;
            }

            .btn--loadingState::after {
                content: ''
            }
        </style>
       {{-- <div class="ctaContainer">
            <div class="ctaContainer__inner">
                <a href="https://www.fgrealty.qa/docs/CViewBrochure.pdf" target="_blank" class="downloadBrochureBtn ctaButton" style="cursor:pointer;">
                    <span class="btn-text">{{__('Download Brochure')}}</span>
                </a> --}}
                {{-- <a class="ctaButton" href="https://www.fgrealty.qa/docs/CViewBrochure.pdf" target="_blank">Download Brochure</a> --}}
                {{-- <a class="ctaButton" href="{{persistQueryParamsToRoute('project.3dwalkthrough', ['projectSlug' => 'cview', 'locale' => $locale])}}" target="_blank">{{__('Virtual Availability')}}</a>
                <a class="ctaButton" href="{{persistQueryParamsToRoute('project.360', ['projectSlug' => 'cview', 'locale' => $locale])}}" target="_blank">{{__('Aerial Overview')}}</a>
            </div>
        </div> --}}
        {{--<div class="elementor-element elementor-element-a07a8b7 e-flex e-con-boxed e-con e-parent" data-id="a07a8b7" data-element_type="container">
            <div class="e-con-inner">
                <div class="elementor-element elementor-element-c914b5c elementor-pagination-type-bullets elementor-arrows-position-inside elementor-pagination-position-outside elementor-widget elementor-widget-n-carousel" data-id="c914b5c" data-element_type="widget" data-settings="{&quot;carousel_items&quot;:[{&quot;slide_title&quot;:&quot;Slide&quot;,&quot;_id&quot;:&quot;32611f9&quot;},{&quot;slide_title&quot;:&quot;Slide&quot;,&quot;_id&quot;:&quot;2a57b9a&quot;},{&quot;slide_title&quot;:&quot;Slide&quot;,&quot;_id&quot;:&quot;1cecf4e&quot;},{&quot;slide_title&quot;:&quot;Slide&quot;,&quot;_id&quot;:&quot;18211f8&quot;},{&quot;slide_title&quot;:&quot;Slide&quot;,&quot;_id&quot;:&quot;cc5085f&quot;},{&quot;slide_title&quot;:&quot;Slide&quot;,&quot;_id&quot;:&quot;9f5c99c&quot;}],&quot;slides_to_show_tablet&quot;:&quot;2&quot;,&quot;slides_to_show_mobile&quot;:&quot;1&quot;,&quot;speed&quot;:500,&quot;arrows&quot;:&quot;yes&quot;,&quot;pagination&quot;:&quot;bullets&quot;,&quot;image_spacing_custom&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:10,&quot;sizes&quot;:[]},&quot;image_spacing_custom_tablet&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:[]},&quot;image_spacing_custom_mobile&quot;:{&quot;unit&quot;:&quot;px&quot;,&quot;size&quot;:&quot;&quot;,&quot;sizes&quot;:[]}}" data-widget_type="nested-carousel.default">
        <div class="elementor-widget-container">
            <div class="e-n-carousel swiper" role="region" aria-roledescription="carousel" aria-label="Carousel" dir="ltr">
                <div class="swiper-wrapper" aria-live="polite">
                    <div class="swiper-slide" data-slide="1" role="group" aria-roledescription="slide" aria-label="1 of 6">
                        <div class="elementor-element elementor-element-9beebf5 e-flex e-con-boxed e-con e-child" data-id="9beebf5" data-element_type="container">
                            <div class="e-con-inner">
                                <div class="elementor-element elementor-element-18a06bf elementor-widget elementor-widget-image" data-id="18a06bf" data-element_type="widget" data-widget_type="image.default">
                                    <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="768" height="432" src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-4-edited-768x432-copy.webp" class="attachment-full size-full wp-image-1652" alt="" srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-4-edited-768x432-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-4-edited-768x432-copy-300x169.webp 300w" sizes="(max-width: 768px) 100vw, 768px" /></noscript><img loading="lazy" decoding="async" width="768" height="432" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20768%20432%22%3E%3C/svg%3E' data-src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-4-edited-768x432-copy.webp" class="lazyload attachment-full size-full wp-image-1652" alt="" data-srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-4-edited-768x432-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-4-edited-768x432-copy-300x169.webp 300w" data-sizes="(max-width: 768px) 100vw, 768px" /></div>
                                </div>
                                <div class="elementor-element elementor-element-177ed40 elementor-widget elementor-widget-heading" data-id="177ed40" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">Infinity Pool</h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-799ae47 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="799ae47" data-element_type="widget" data-widget_type="text-editor.default">
                                    <div class="elementor-widget-container"> 300 sqm</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-slide" data-slide="2" role="group" aria-roledescription="slide" aria-label="2 of 6">
                        <div class="elementor-element elementor-element-bfe3ffb e-flex e-con-boxed e-con e-child" data-id="bfe3ffb" data-element_type="container">
                            <div class="e-con-inner">
                                <div class="elementor-element elementor-element-b5eb128 elementor-widget elementor-widget-image" data-id="b5eb128" data-element_type="widget" data-widget_type="image.default">
                                    <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="768" height="640" src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/gym-cam-1-768x640-1-copy.webp" class="attachment-full size-full wp-image-1648" alt="" srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/gym-cam-1-768x640-1-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/gym-cam-1-768x640-1-copy-300x250.webp 300w" sizes="(max-width: 768px) 100vw, 768px" /></noscript><img loading="lazy" decoding="async" width="768" height="640" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20768%20640%22%3E%3C/svg%3E' data-src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/gym-cam-1-768x640-1-copy.webp" class="lazyload attachment-full size-full wp-image-1648" alt="" data-srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/gym-cam-1-768x640-1-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/gym-cam-1-768x640-1-copy-300x250.webp 300w" data-sizes="(max-width: 768px) 100vw, 768px" /></div>
                                </div>
                                <div class="elementor-element elementor-element-04d0ae1 elementor-widget elementor-widget-heading" data-id="04d0ae1" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">Fitness Center</h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-6a4b85b elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="6a4b85b" data-element_type="widget" data-widget_type="text-editor.default">
                                    <div class="elementor-widget-container"> Techno Gym</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-slide" data-slide="3" role="group" aria-roledescription="slide" aria-label="3 of 6">
                        <div class="elementor-element elementor-element-91dafd6 e-flex e-con-boxed e-con e-child" data-id="91dafd6" data-element_type="container">
                            <div class="e-con-inner">
                                <div class="elementor-element elementor-element-eda8e68 elementor-widget elementor-widget-image" data-id="eda8e68" data-element_type="widget" data-widget_type="image.default">
                                    <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="768" height="576" src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/MULTIFUNCTION-02-768x576-copy.webp" class="attachment-full size-full wp-image-1650" alt="" srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/MULTIFUNCTION-02-768x576-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/MULTIFUNCTION-02-768x576-copy-300x225.webp 300w" sizes="(max-width: 768px) 100vw, 768px" /></noscript><img loading="lazy" decoding="async" width="768" height="576" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20768%20576%22%3E%3C/svg%3E' data-src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/MULTIFUNCTION-02-768x576-copy.webp" class="lazyload attachment-full size-full wp-image-1650" alt="" data-srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/MULTIFUNCTION-02-768x576-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/MULTIFUNCTION-02-768x576-copy-300x225.webp 300w" data-sizes="(max-width: 768px) 100vw, 768px" /></div>
                                </div>
                                <div class="elementor-element elementor-element-92b615d elementor-widget elementor-widget-heading" data-id="92b615d" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">Entertainment Lounge</h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-4cbb926 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="4cbb926" data-element_type="widget" data-widget_type="text-editor.default">
                                    <div class="elementor-widget-container"> Biliard / Tennis table</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-slide" data-slide="4" role="group" aria-roledescription="slide" aria-label="4 of 6">
                        <div class="elementor-element elementor-element-d75cc0c e-flex e-con-boxed e-con e-child" data-id="d75cc0c" data-element_type="container">
                            <div class="e-con-inner">
                                <div class="elementor-element elementor-element-6331eaa elementor-widget elementor-widget-image" data-id="6331eaa" data-element_type="widget" data-widget_type="image.default">
                                    <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="768" height="432" src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-1-EDITED-768x432-copy.webp" class="attachment-full size-full wp-image-1651" alt="" srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-1-EDITED-768x432-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-1-EDITED-768x432-copy-300x169.webp 300w" sizes="(max-width: 768px) 100vw, 768px" /></noscript><img loading="lazy" decoding="async" width="768" height="432" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20768%20432%22%3E%3C/svg%3E' data-src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-1-EDITED-768x432-copy.webp" class="lazyload attachment-full size-full wp-image-1651" alt="" data-srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-1-EDITED-768x432-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/POOL-CAM-1-EDITED-768x432-copy-300x169.webp 300w" data-sizes="(max-width: 768px) 100vw, 768px" /></div>
                                </div>
                                <div class="elementor-element elementor-element-212fae4 elementor-widget elementor-widget-heading" data-id="212fae4" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">Resort Style Terraces</h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-1127b8a elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="1127b8a" data-element_type="widget" data-widget_type="text-editor.default">
                                    <div class="elementor-widget-container"> Outdoor Bar</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-slide" data-slide="5" role="group" aria-roledescription="slide" aria-label="5 of 6">
                        <div class="elementor-element elementor-element-02edec8 e-flex e-con-boxed e-con e-child" data-id="02edec8" data-element_type="container">
                            <div class="e-con-inner">
                                <div class="elementor-element elementor-element-475a319 elementor-widget elementor-widget-image" data-id="475a319" data-element_type="widget" data-widget_type="image.default">
                                    <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="768" height="576" src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/W-06-CINEMA02-EDITED-768x576-copy.webp" class="attachment-full size-full wp-image-1653" alt="" srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/W-06-CINEMA02-EDITED-768x576-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/W-06-CINEMA02-EDITED-768x576-copy-300x225.webp 300w" sizes="(max-width: 768px) 100vw, 768px" /></noscript><img loading="lazy" decoding="async" width="768" height="576" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20768%20576%22%3E%3C/svg%3E' data-src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/W-06-CINEMA02-EDITED-768x576-copy.webp" class="lazyload attachment-full size-full wp-image-1653" alt="" data-srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/W-06-CINEMA02-EDITED-768x576-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/W-06-CINEMA02-EDITED-768x576-copy-300x225.webp 300w" data-sizes="(max-width: 768px) 100vw, 768px" /></div>
                                </div>
                                <div class="elementor-element elementor-element-b83a9df elementor-widget elementor-widget-heading" data-id="b83a9df" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">Cinema</h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-6b54a52 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="6b54a52" data-element_type="widget" data-widget_type="text-editor.default">
                                    <div class="elementor-widget-container"> 12-15 pax</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="swiper-slide" data-slide="6" role="group" aria-roledescription="slide" aria-label="6 of 6">
                        <div class="elementor-element elementor-element-8b71405 e-flex e-con-boxed e-con e-child" data-id="8b71405" data-element_type="container">
                            <div class="e-con-inner">
                                <div class="elementor-element elementor-element-584a6e4 elementor-widget elementor-widget-image" data-id="584a6e4" data-element_type="widget" data-widget_type="image.default">
                                    <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="768" height="508" src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/KIDS-AREA-CAM-01-768x508-copy.webp" class="attachment-full size-full wp-image-1649" alt="" srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/KIDS-AREA-CAM-01-768x508-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/KIDS-AREA-CAM-01-768x508-copy-300x198.webp 300w" sizes="(max-width: 768px) 100vw, 768px" /></noscript><img loading="lazy" decoding="async" width="768" height="508" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20768%20508%22%3E%3C/svg%3E' data-src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/KIDS-AREA-CAM-01-768x508-copy.webp" class="lazyload attachment-full size-full wp-image-1649" alt="" data-srcset="https://fgrealty.qa/lp/wp-content/uploads/2024/05/KIDS-AREA-CAM-01-768x508-copy.webp 768w, https://fgrealty.qa/lp/wp-content/uploads/2024/05/KIDS-AREA-CAM-01-768x508-copy-300x198.webp 300w" data-sizes="(max-width: 768px) 100vw, 768px" /></div>
                                </div>
                                <div class="elementor-element elementor-element-fc86c25 elementor-widget elementor-widget-heading" data-id="fc86c25" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">Kids Playground</h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-76eec1a elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="76eec1a" data-element_type="widget" data-widget_type="text-editor.default">
                                    <div class="elementor-widget-container"> Outdoor &#038; Indoor</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elementor-swiper-button elementor-swiper-button-prev" role="button" tabindex="0" aria-label="Previous"> <svg aria-hidden="true" class="e-font-icon-svg e-fas-chevron-left" viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                    <path d="M34.52 239.03L228.87 44.69c9.37-9.37 24.57-9.37 33.94 0l22.67 22.67c9.36 9.36 9.37 24.52.04 33.9L131.49 256l154.02 154.75c9.34 9.38 9.32 24.54-.04 33.9l-22.67 22.67c-9.37 9.37-24.57 9.37-33.94 0L34.52 272.97c-9.37-9.37-9.37-24.57 0-33.94z"></path>
                </svg></div>
            <div class="elementor-swiper-button elementor-swiper-button-next" role="button" tabindex="0" aria-label="Next"> <svg aria-hidden="true" class="e-font-icon-svg e-fas-chevron-right" viewBox="0 0 320 512" xmlns="http://www.w3.org/2000/svg">
                    <path d="M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z"></path>
                </svg></div>
            <div class="swiper-pagination"></div>
        </div>
    </div>
    </div>
    </div> --}}
    <div class="elementor-element elementor-element-5760984 e-flex e-con-boxed e-con e-parent" data-id="5760984" data-element_type="container" id="agents" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
        <div class="e-con-inner">
            <div class="elementor-element elementor-element-05d38e6 elementor-widget elementor-widget-heading" data-id="05d38e6" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('Work with qatar’s greatest real estate agents')}}</h2>
                </div>
            </div>
            <div class="elementor-element elementor-element-2732b4c e-grid e-con-boxed e-con e-child" data-id="2732b4c" data-element_type="container">
                <div class="e-con-inner">
                    @foreach($agents as $agent)
                    <div class="elementor-element elementor-element-fc53a6b e-flex e-con-boxed e-con e-child" data-id="fc53a6b" data-element_type="container">
                        <div class="e-con-inner">
                            <div class="elementor-element elementor-element-af2d0e7 e-con-full e-flex e-con e-child" data-id="af2d0e7" data-element_type="container">
                                <div class="elementor-element elementor-element-a7d81b6 elementor-widget elementor-widget-image" data-id="a7d81b6" data-element_type="widget" data-widget_type="image.default">
                                    <div class="elementor-widget-container"> <noscript><img loading="lazy" decoding="async" width="243" height="300" src="{{imageRoute('agent-normal', $agent->profile_image)}}" class="attachment-medium size-medium wp-image-807" alt="" srcset="{{imageRoute('agent-normal', $agent->profile_image)}} 243w, {{imageRoute('agent-normal', $agent->profile_image)}} 450w" sizes="(max-width: 243px) 100vw, 243px" /></noscript><img loading="lazy" decoding="async" width="243" height="300" src='data:image/svg+xml,%3Csvg%20xmlns=%22http://www.w3.org/2000/svg%22%20viewBox=%220%200%20243%20300%22%3E%3C/svg%3E' data-src="{{imageRoute('agent-normal', $agent->profile_image)}}" class="lazyload attachment-medium size-medium wp-image-807" alt="" data-srcset="{{imageRoute('agent-normal', $agent->profile_image)}} 243w, {{imageRoute('agent-normal', $agent->profile_image)}} 450w" data-sizes="(max-width: 243px) 100vw, 243px" /></div>
                                </div>
                            </div>
                            <div class="elementor-element elementor-element-6c517c9 e-con-full e-flex e-con e-child" data-id="6c517c9" data-element_type="container">
                                <div class="elementor-element elementor-element-22b14ad elementor-widget elementor-widget-heading" data-id="22b14ad" data-element_type="widget" data-widget_type="heading.default">
                                    <div class="elementor-widget-container">
                                        <h2 class="elementor-heading-title elementor-size-default">{{isset($nameTranslations[$agent->name]) ? $nameTranslations[$agent->name] : $agent->name}}</h2>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-fcdbff1 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="fcdbff1" data-element_type="widget" data-widget_type="text-editor.default">
                                    <div class="elementor-widget-container">
                                        <div class="agent-profile__mainInfo-position">{{__($agent->position)}}</div>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-4ced628 elementor-widget__width-initial elementor-icon-list--layout-traditional elementor-list-item-link-full_width elementor-widget elementor-widget-icon-list" data-id="4ced628" data-element_type="widget" data-widget_type="icon-list.default">
                                    <div class="elementor-widget-container">
                                        <ul class="elementor-icon-list-items">
                                            <li class="elementor-icon-list-item"> <span class="elementor-icon-list-icon"> <svg xmlns="http://www.w3.org/2000/svg" height="512" viewBox="0 0 64 64" width="512">
                                                        <g id="Pin">
                                                            <path d="m32 0a24.0319 24.0319 0 0 0 -24 24c0 17.23 22.36 38.81 23.31 39.72a.99.99 0 0 0 1.38 0c.95-.91 23.31-22.49 23.31-39.72a24.0319 24.0319 0 0 0 -24-24zm0 35a11 11 0 1 1 11-11 11.0066 11.0066 0 0 1 -11 11z"></path>
                                                        </g>
                                                    </svg> </span> <span class="elementor-icon-list-text">{{__('Doha, Qatar')}}</span></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="elementor-element elementor-element-7f95639 e-flex e-con-boxed e-con e-child" data-id="7f95639" data-element_type="container">
                                    <div class="e-con-inner">
                                        <div class="elementor-element elementor-element-6c25426 elementor-align-left elementor-widget-mobile__width-auto elementor-widget elementor-widget-button" data-id="6c25426" data-element_type="widget" data-widget_type="button.default">
                                            <div class="elementor-widget-container">
                                                <div class="elementor-button-wrapper"> <a class="elementor-button elementor-button-link elementor-size-sm" href="tel:{{getCompletePhoneNo($agent->prefix_phone, $agent->phone)}}" target="_blank"> <span class="elementor-button-content-wrapper"> <span class="elementor-button-icon"> <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="Capa_1" x="0px" y="0px" viewBox="0 0 108.27 108.27" style="enable-background:new 0 0 108.27 108.27;" xml:space="preserve">
                                                                    <g>
                                                                        <path d="M51.954,66.588c-9.284-7.634-15.483-17.054-18.742-22.414l-2.431-4.583c0.85-0.912,7.332-7.853,10.141-11.619   c3.53-4.729-1.588-9-1.588-9S24.933,4.569,21.651,1.712c-3.282-2.861-7.06-1.272-7.06-1.272C7.693,4.897,0.542,8.772,0.113,27.408   C0.097,44.856,13.342,62.852,27.665,76.784c14.346,15.734,34.043,31.504,53.086,31.486c18.634-0.425,22.508-7.575,26.965-14.473   c0,0,1.59-3.775-1.268-7.06c-2.86-3.284-17.265-17.688-17.265-17.688s-4.268-5.119-8.998-1.586   c-3.525,2.635-9.855,8.496-11.38,9.917C68.808,77.385,58.219,71.74,51.954,66.588z"></path>
                                                                    </g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                    <g></g>
                                                                </svg> </span> </span> </a></div>
                                            </div>
                                        </div>
                                        <div class="elementor-element elementor-element-4d153c6 elementor-align-left elementor-widget-mobile__width-auto elementor-widget elementor-widget-button" data-id="4d153c6" data-element_type="widget" data-widget_type="button.default">
                                            <div class="elementor-widget-container">
                                                <div class="elementor-button-wrapper"> <a class="elementor-button elementor-button-link elementor-size-sm" href="https://wa.me/{{getCompletePhoneNo($agent->prefix_phone, $agent->phone)}}?text=Hi,%20I%20want%20to%20know%20more%20about%20Marina%20Heights" target="_blank"> <span class="elementor-button-content-wrapper"> <span class="elementor-button-icon"> <svg xmlns="http://www.w3.org/2000/svg" id="Bold" height="512" viewBox="0 0 24 24" width="512">
                                                                    <path d="m17.507 14.307-.009.075c-2.199-1.096-2.429-1.242-2.713-.816-.197.295-.771.964-.944 1.162-.175.195-.349.21-.646.075-.3-.15-1.263-.465-2.403-1.485-.888-.795-1.484-1.77-1.66-2.07-.293-.506.32-.578.878-1.634.1-.21.049-.375-.025-.524-.075-.15-.672-1.62-.922-2.206-.24-.584-.487-.51-.672-.51-.576-.05-.997-.042-1.368.344-1.614 1.774-1.207 3.604.174 5.55 2.714 3.552 4.16 4.206 6.804 5.114.714.227 1.365.195 1.88.121.574-.091 1.767-.721 2.016-1.426.255-.705.255-1.29.18-1.425-.074-.135-.27-.21-.57-.345z"></path>
                                                                    <path d="m20.52 3.449c-7.689-7.433-20.414-2.042-20.419 8.444 0 2.096.549 4.14 1.595 5.945l-1.696 6.162 6.335-1.652c7.905 4.27 17.661-1.4 17.665-10.449 0-3.176-1.24-6.165-3.495-8.411zm1.482 8.417c-.006 7.633-8.385 12.4-15.012 8.504l-.36-.214-3.75.975 1.005-3.645-.239-.375c-4.124-6.565.614-15.145 8.426-15.145 2.654 0 5.145 1.035 7.021 2.91 1.875 1.859 2.909 4.35 2.909 6.99z"></path>
                                                                </svg> </span> </span> </a></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
    <div class="elementor-element elementor-element-8db0a7f e-con-full e-flex e-con e-parent" data-id="8db0a7f" id="facilities" data-element_type="container">
        <div class="elementor-element elementor-element-21d1bc2 e-con-full e-flex e-con e-child" data-id="21d1bc2" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}"></div>
        <div class="elementor-element elementor-element-fd83e26 e-con-full e-flex e-con e-child" data-id="fd83e26" data-element_type="container">
            <div class="elementor-element elementor-element-18607e3 elementor-widget__width-initial elementor-widget elementor-widget-heading" data-id="18607e3" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('Amenities & Facilities')}}</h2>
                    <p class="">{{__('Experience an exceptional lifestyle with a host of world-class amenities, designed to offer comfort, relaxation, and entertainment at every turn:')}}</p>
                </div>
            </div>
            <div class="elementor-element elementor-element-e834f67 elementor-widget elementor-widget-heading" data-id="e834f67" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('Infinity Pool & Spa')}}</h2>
                </div>
            </div>
            <div class="elementor-element elementor-element-0fcd4de elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="0fcd4de" data-element_type="widget" data-widget_type="text-editor.default">
                <div class="elementor-widget-container">
                    <ul>
                        <li>{{__('A stunning rooftop infinity pool that merges with the skyline.')}}</li>
                        <li>{{__('Dedicated male & female spa for ultimate relaxation.')}}</li>
                    </ul>
                </div>
            </div>
            <div class="elementor-element elementor-element-12a9ed6 elementor-widget elementor-widget-heading" data-id="12a9ed6" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('State-of-the-Art Fitness & Recreation')}}</h2>
                </div>
            </div>
            <div class="elementor-element elementor-element-e602c09 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="e602c09" data-element_type="widget" data-widget_type="text-editor.default">
                <div class="elementor-widget-container">
                    <ul>
                        <li>{{__('Fully equipped gym with the latest workout equipment.')}}</li>
                        <li>{{__('Exclusive entertainment lounges & theater room for social gatherings.')}}</li>
                    </ul>
                </div>
            </div>
            <div class="elementor-element elementor-element-12a9ed6 elementor-widget elementor-widget-heading" data-id="12a9ed6" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('Family & Leisure Spaces')}}</h2>
                </div>
            </div>
            <div class="elementor-element elementor-element-e602c09 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="e602c09" data-element_type="widget" data-widget_type="text-editor.default">
                <div class="elementor-widget-container">
                    <ul>
                        <li>{{__('Indoor & outdoor kids’ play zones for family fun.')}}</li>
                        <li>{{__('Elegant BBQ lounge for unforgettable moments with loved ones.')}}</li>
                    </ul>
                </div>
            </div>
            <div class="elementor-element elementor-element-12a9ed6 elementor-widget elementor-widget-heading" data-id="12a9ed6" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('Modern Living Spaces')}}</h2>
                </div>
            </div>
            <div class="elementor-element elementor-element-e602c09 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="e602c09" data-element_type="widget" data-widget_type="text-editor.default">
                <div class="elementor-widget-container">
                    <ul>
                        <li>{{__('Spacious 1, 2, & 3-bedroom apartments with panoramic views.')}}</li>
                        <li>{{__('Premium-quality interiors designed for luxury and comfort.')}}</li>
                    </ul>
                </div>
            </div>
            <div class="elementor-element elementor-element-23bada6 elementor-widget elementor-widget-button" data-id="23bada6" data-element_type="widget" data-widget_type="button.default">
                <div class="elementor-widget-container">
                <button onclick="javascript: downloadBrochure()" class="downloadBrochureBtn elementor-button elementor-button-link elementor-size-sm" style="cursor:pointer;">
                        <span class="btn-text">{{__('Download Brochure')}}</span>
                    </button>
                    {{-- <div class="elementor-button-wrapper"> <a class="elementor-button elementor-button-link elementor-size-sm" href="https://www.paperturn-view.com/?pid=ODg8870066&p=13&v=4.1" target="_blank"> <span class="elementor-button-content-wrapper"> <span class="elementor-button-icon"> <svg aria-hidden="true" class="e-font-icon-svg e-fas-cloud-download-alt" viewBox="0 0 640 512" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M537.6 226.6c4.1-10.7 6.4-22.4 6.4-34.6 0-53-43-96-96-96-19.7 0-38.1 6-53.3 16.2C367 64.2 315.3 32 256 32c-88.4 0-160 71.6-160 160 0 2.7.1 5.4.2 8.1C40.2 219.8 0 273.2 0 336c0 79.5 64.5 144 144 144h368c70.7 0 128-57.3 128-128 0-61.9-44-113.6-102.4-125.4zm-132.9 88.7L299.3 420.7c-6.2 6.2-16.4 6.2-22.6 0L171.3 315.3c-10.1-10.1-2.9-27.3 11.3-27.3H248V176c0-8.8 7.2-16 16-16h48c8.8 0 16 7.2 16 16v112h65.4c14.2 0 21.4 17.2 11.3 27.3z"></path>
                                    </svg> </span> <span class="elementor-button-text">{{__('Download Brochure')}}</span> </span> </a></div> --}}
                </div>
            </div>
        </div>
    </div>
    {{-- <div class="elementor-element elementor-element-fd83e26 e-con-full e-flex e-con e-child" data-id="" data-element_type="container">
        <div class="elementor-element elementor-element-12a9ed6 elementor-widget elementor-widget-heading" data-id="12a9ed6" data-element_type="widget" data-widget_type="heading.default">
            <div class="elementor-widget-container">
                <h2 class="elementor-heading-title elementor-size-default">{{__('1 BEDROOM APARTMENTS FOR SALE IN LUSAIL MARINA HEIGHTS')}}</h2>
            </div>
        </div>
        <div class="elementor-element elementor-element-e602c09 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="e602c09" data-element_type="widget" data-widget_type="text-editor.default">
            <div class="elementor-widget-container">
                <ul>
                    <li>{{__('Unit Sizes: 93 - 272 sqm')}}</li>
                    <li>{{__('Views: Side Sea View | Place Vendome Mall | City View')}}</li>
                </ul>
            </div>
        </div>
        <div class="elementor-element elementor-element-12a9ed6 elementor-widget elementor-widget-heading" data-id="12a9ed6" data-element_type="widget" data-widget_type="heading.default">
            <div class="elementor-widget-container">
                <h2 class="elementor-heading-title elementor-size-default">{{__('2 BEDROOM APARTMENTS FOR SALE IN LUSAIL MARINA HEIGHTS')}}</h2>
            </div>
        </div>
        <div class="elementor-element elementor-element-e602c09 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="e602c09" data-element_type="widget" data-widget_type="text-editor.default">
            <div class="elementor-widget-container">
                <ul>
                    <li>{{__('Unit Sizes: 210 sqm')}}</li>
                    <li>{{__('Views: Direct Sea View | Sea View/Place Vendome Mall | City View')}}</li>
                </ul>
            </div>
        </div>
        <div class="elementor-element elementor-element-12a9ed6 elementor-widget elementor-widget-heading" data-id="12a9ed6" data-element_type="widget" data-widget_type="heading.default">
            <div class="elementor-widget-container">
                <h2 class="elementor-heading-title elementor-size-default">{{__('3 BEDROOM APARTMENTS FOR SALE IN LUSAIL MARINA HEIGHTS')}}</h2>
            </div>
        </div>
        <div class="elementor-element elementor-element-e602c09 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="e602c09" data-element_type="widget" data-widget_type="text-editor.default">
            <div class="elementor-widget-container">
                <ul>
                    <li>{{__('Unit Sizes: 138 - 517 sqm')}}</li>
                    <li>{{__('Views: Direct Sea View | City View / Sea View')}}</li>
                </ul>
            </div>
        </div>
        <div class="elementor-button-wrapper"> <a class="elementor-button elementor-button-link elementor-size-sm" href="https://www.fgrealty.qa/" target="_blank"> <span class="elementor-button-content-wrapper"> <span class="elementor-button-icon">
         </span> <span class="elementor-button-text">{{__('View Floor Plans')}}</span> </span> </a></div>
    </div> --}}
    <div class="elementor-element elementor-element-c46a977 e-flex e-con-boxed e-con e-parent" data-id="c46a977" data-element_type="container">
        <div class="units e-con-inner">
            <div class="unit elementor-element elementor-element-94158ee e-con-full e-flex e-con e-child" data-id="94158ee" data-element_type="container">
                <div class="elementor-element elementor-element-7d8de3b elementor-widget elementor-widget-counter" data-id="7d8de3b" data-element_type="widget" data-widget_type="counter.default">
                    <div class="elementor-widget-container">
                        <div class="elementor-counter">
                            <div class="elementor-counter-title">{{__('Direct Sea View')}}</div>
                            <div class="elementor-counter-number-wrapper"> <span class="elementor-counter-number-prefix"></span> <span class="elementor-counter-number" data-duration="1000" data-to-value="36" data-from-value="0">0</span> <span class="elementor-counter-number-suffix"></span></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="unit elementor-element elementor-element-f8431f1 e-con-full e-flex e-con e-child" data-id="f8431f1" data-element_type="container">
                <div class="elementor-element elementor-element-fbd84f5 elementor-widget elementor-widget-counter" data-id="fbd84f5" data-element_type="widget" data-widget_type="counter.default">
                    <div class="elementor-widget-container">
                        <div class="elementor-counter">
                            <div class="elementor-counter-title">{{__('City View | City / Sea View')}}</div>
                            <div class="elementor-counter-number-wrapper"> <span class="elementor-counter-number-prefix"></span> <span class="elementor-counter-number" data-duration="1000" data-to-value="72" data-from-value="0" data-delimiter=",">0</span> <span class="elementor-counter-number-suffix"></span></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="unit elementor-element elementor-element-f60728c e-con-full e-flex e-con e-child" data-id="f60728c" data-element_type="container">
                <div class="elementor-element elementor-element-49c5e7a elementor-widget elementor-widget-counter" data-id="49c5e7a" data-element_type="widget" data-widget_type="counter.default">
                    <div class="elementor-widget-container">
                        <div class="elementor-counter">
                            <div class="elementor-counter-title">{{__('Sea View  / Place Vendome Mall')}}</div>
                            <div class="elementor-counter-number-wrapper"> <span class="elementor-counter-number-prefix"></span> <span class="elementor-counter-number" data-duration="1000" data-to-value="18" data-from-value="0" data-delimiter=",">0</span> <span class="elementor-counter-number-suffix"></span></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="unit elementor-element elementor-element-f60728c e-con-full e-flex e-con e-child" data-id="f60728c" data-element_type="container">
                <div class="elementor-element elementor-element-49c5e7a elementor-widget elementor-widget-counter" data-id="49c5e7a" data-element_type="widget" data-widget_type="counter.default">
                    <div class="elementor-widget-container">
                        <div class="elementor-counter">
                            <div class="elementor-counter-title">{{__('Place Vendome Mall')}}</div>
                            <div class="elementor-counter-number-wrapper"> <span class="elementor-counter-number-prefix"></span> <span class="elementor-counter-number" data-duration="1000" data-to-value="36" data-from-value="0" data-delimiter=",">0</span> <span class="elementor-counter-number-suffix"></span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="elementor-element elementor-element-41358eb e-con-full e-flex e-con e-parent" data-id="41358eb" data-element_type="container" id="pricing" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
        <div class="elementor-element elementor-element-3546133 e-con-full e-flex e-con e-child" data-id="3546133" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}"></div>
        <div class="elementor-element elementor-element-73a2ba0 e-con-full e-flex e-con e-child" data-id="73a2ba0" data-element_type="container">
            {{-- <div class="elementor-element elementor-element-9e94b98 elementor-widget-mobile__width-initial elementor-widget elementor-widget-heading" data-id="9e94b98" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('Where Luxury Meets the Waterfront.')}}</h2>
                </div>
            </div> --}}
            <div class="elementor-element elementor-element-3fde36f elementor-widget elementor-widget-heading" data-id="3fde36f" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('Pricing & Payment Plan')}}</h2>
                </div>
            </div>
            <div class="elementor-element elementor-element-e834f67 elementor-widget elementor-widget-heading" data-id="e834f67" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('Owning your dream home at Lusail Marina Heights has never been easier!')}}</h2>
                </div>
            </div>
            <div class="elementor-element elementor-element-0fcd4de elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="0fcd4de" data-element_type="widget" data-widget_type="text-editor.default">
                <div class="elementor-widget-container">
                    <ul>
                        <li style="color:#fff">{{__('5% Down Payment')}}</li>
                        <li style="color:#fff">{{__('Flexible 7-Year Payment Plan.')}}</li>
                        <li style="color:#fff">{{__('0% Interest')}}</li>
                        <li style="color:#fff">{{__('Project Delivery 2027')}}</li>
                    </ul>
                </div>
            </div>
            <div class="elementor-element elementor-element-0fcd4de elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="0fcd4de" data-element_type="widget" data-widget_type="text-editor.default">
                <div class="elementor-widget-container">
                   <p style="color:#fff">{{__('Limited Units Available! Secure yours today and be part of a prestigious waterfront community.')}}</p>
                </div>
            </div>
            <div class="elementor-element elementor-element-2183f06 elementor-widget elementor-widget-spacer" data-id="2183f06" data-element_type="widget" data-widget_type="spacer.default">
                <div class="elementor-widget-container">
                    <div class="elementor-spacer">
                        <div class="elementor-spacer-inner"></div>
                    </div>
                </div>
            </div>
            {{-- <div class="elementor-element elementor-element-5c6e64e elementor-widget elementor-widget-heading" data-id="5c6e64e" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('Apartments')}} </h2>
                </div>
            </div>
            <div class="elementor-element elementor-element-676ee26 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="676ee26" data-element_type="widget" data-widget_type="text-editor.default">
                <div class="elementor-widget-container"> {{__("1, 2, and 3 Bedroom")}}</div>
            </div>
            <div class="elementor-element elementor-element-424c316 elementor-widget elementor-widget-spacer" data-id="424c316" data-element_type="widget" data-widget_type="spacer.default">
                <div class="elementor-widget-container">
                    <div class="elementor-spacer">
                        <div class="elementor-spacer-inner"></div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-d481554 elementor-widget elementor-widget-heading" data-id="d481554" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('Flexible 5-Year')}} </h2>
                </div>
            </div>
            <div class="elementor-element elementor-element-54233f7 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="54233f7" data-element_type="widget" data-widget_type="text-editor.default">
                <div class="elementor-widget-container"> {{__('Payment Plan')}}</div>
            </div>
            <div class="elementor-element elementor-element-0702aa0 elementor-widget elementor-widget-spacer" data-id="0702aa0" data-element_type="widget" data-widget_type="spacer.default">
                <div class="elementor-widget-container">
                    <div class="elementor-spacer">
                        <div class="elementor-spacer-inner"></div>
                    </div>
                </div>
            </div>
            <div class="elementor-element elementor-element-71e0adf elementor-widget elementor-widget-heading" data-id="71e0adf" data-element_type="widget" data-widget_type="heading.default">
                <div class="elementor-widget-container">
                    <h2 class="elementor-heading-title elementor-size-default">{{__('Available for sale now')}}</h2>
                </div>
            </div>
            <div class="elementor-element elementor-element-b878b9d elementor-widget elementor-widget-spacer" data-id="b878b9d" data-element_type="widget" data-widget_type="spacer.default">
                <div class="elementor-widget-container">
                    <div class="elementor-spacer">
                        <div class="elementor-spacer-inner"></div>
                    </div>
                </div>
            </div> --}}
            <div class="elementor-element elementor-element-caa1656 elementor-widget elementor-widget-button" data-id="caa1656" data-element_type="widget" data-widget_type="button.default">
                <div class="elementor-widget-container">
                    <div class="elementor-button-wrapper"> <a class="elementor-button elementor-button-link elementor-size-sm" href="#enquire"> <span class="elementor-button-content-wrapper"> <span class="elementor-button-text">{{__('Get a Quote')}}</span> </span> </a></div>
                </div>
            </div>
            <div class="elementor-element elementor-element-344f51d elementor-widget elementor-widget-spacer" data-id="344f51d" data-element_type="widget" data-widget_type="spacer.default">
                <div class="elementor-widget-container">
                    <div class="elementor-spacer">
                        <div class="elementor-spacer-inner"></div>
                    </div>
                </div>
            </div>
            {{--
            <div class="elementor-element elementor-element-c9fe042 elementor-widget__width-inherit elementor-widget elementor-widget-text-editor" data-id="c9fe042" data-element_type="widget" data-widget_type="text-editor.default">
                <div class="elementor-widget-container">
                    <p>* 1st installment: After 6 months, 10%<br />2nd installment (upon handover): After 12 months from the first installment, 15%</p>
                    <p>Remaining 65% spread over a convenient 6-year post-handover payment plan</p>
                </div>
            </div>
            --}}
        </div>
    </div>
    {{-- <div class="elementor-element elementor-element-f0d9803 e-flex e-con-boxed e-con e-parent" style="min-height: 180px" data-id="f0d9803" data-element_type="container">
        <div class="e-con-inner" style="padding-top:0px; padding-bottom: 0px">
            <div class="elementor-element elementor-element-909b16a e-con-full e-flex e-con e-child" data-id="909b16a" data-element_type="container">
                <div class="elementor-element elementor-element-3727d7e elementor-widget elementor-widget-heading" data-id="3727d7e" data-element_type="widget" data-widget_type="heading.default">
                    <div class="elementor-widget-container">
                        <h2 class="elementor-heading-title elementor-size-default">{{__('Start Investing Today')}}</h2>
                    </div>
                </div>
                <div class="elementor-element elementor-element-d10d22d elementor-widget elementor-widget-heading" data-id="d10d22d" data-element_type="widget" data-widget_type="heading.default">
                    <div class="elementor-widget-container">
                        <h2 class="elementor-heading-title elementor-size-default">{{__('Why Investing in Lusail, Qatar is a Smart Move?')}}</h2>
                    </div>
                </div>
                <div class="elementor-element elementor-element-16eb96f elementor-widget elementor-widget-text-editor" data-id="16eb96f" data-element_type="widget" data-widget_type="text-editor.default">
                    <div class="elementor-widget-container"> {{__('Buying property in Lusail, Qatar, offers a variety of benefits, making it an attractive option for investors and residents alike.')}}</div>
                </div>
            </div>
        </div>
    </div> --}}
    <div class="elementor-element elementor-element-9019f7e e-con-full e-flex e-con e-parent" data-id="9019f7e" data-element_type="container" id="enquire" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
        <div class="elementor-element elementor-element-cca4930 e-flex e-con-boxed e-con e-child" data-id="cca4930" data-element_type="container">
            <div class="e-con-inner">
                <div class="elementor-element elementor-element-fc53d76 e-con-full e-flex e-con e-child" data-id="fc53d76" data-element_type="container"></div>
                <div class="elementor-element elementor-element-b889911 e-con-full e-flex e-con e-child" data-id="b889911" data-element_type="container" data-settings="{&quot;background_background&quot;:&quot;classic&quot;}">
                    <div class="elementor-element elementor-element-fd36feb elementor-widget elementor-widget-heading" data-id="fd36feb" data-element_type="widget" data-widget_type="heading.default">
                        <div class="elementor-widget-container">
                            <h2 class="elementor-heading-title elementor-size-default">{{__('Enquire Today')}}</h2>
                        </div>
                    </div>
                    <div class="elementor-element elementor-element-3703331 elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="3703331" data-element_type="widget" data-widget_type="text-editor.default">
                        <div class="elementor-widget-container">{{__('Please provide your contact information below so we can get back to you.')}}</div>
                    </div>
                    <div class="elementor-element elementor-element-30064fe elementor-button-align-stretch elementor-widget elementor-widget-form">
                        <div class="elementor-widget-container">
                            <form class="elementor-form" id="MarinaHeights_enquiry_form" method="post" name="MarinaHeights_enquiry_form" action="javascript:;" onsubmit="javascript: return submitForm()">
                                <div class="elementor-form-fields-wrapper elementor-labels-">
                                    <div class="elementor-field-type-text elementor-field-group elementor-column elementor-field-group-name elementor-col-100 elementor-field-required"> <label for="form-field-name" class="elementor-field-label elementor-screen-only"> Name </label> <input size="1" type="text" name="Name" id="form-field-name" class="elementor-field elementor-size-md  elementor-field-textual" placeholder="{{__('Your Name')}}*" required="required"></div>
                                    <div class="elementor-field-type-email elementor-field-group elementor-column elementor-field-group-E_mail elementor-col-100 elementor-field-required"> <label for="form-field-E_mail" class="elementor-field-label elementor-screen-only"> E_mail </label> <input size="1" type="email" name="E_mail" id="form-field-E_mail" class="elementor-field elementor-size-md  elementor-field-textual" placeholder="{{__('Email Address')}}*" required="required"></div>
                                    <div class="elementor-field-type-tel elementor-field-group elementor-column elementor-field-group-email elementor-col-100 elementor-field-required"> <label for="form-field-email" class="elementor-field-label elementor-screen-only"> Phone </label> <input size="1" type="tel" name="Phone" id="form-field-email" class="elementor-field elementor-size-md  elementor-field-textual" placeholder="{{__('Phone Number')}}*" required="required" pattern="[0-9()#&amp;+*-=.]+" title="Only numbers and phone characters (#, -, *, etc) are accepted."></div>
                                    <div class="elementor-field-type-select elementor-field-group elementor-column elementor-field-group-country elementor-col-100 elementor-field-required"> <label for="form-field-country" class="elementor-field-label elementor-screen-only"> Country </label>
                                        <div class="elementor-field elementor-select-wrapper remove-before ">
                                            <div class="select-caret-down-wrapper"> <svg aria-hidden="true" class="e-font-icon-svg e-eicon-caret-down" viewBox="0 0 571.4 571.4" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M571 393Q571 407 561 418L311 668Q300 679 286 679T261 668L11 418Q0 407 0 393T11 368 36 357H536Q550 357 561 368T571 393Z"></path>
                                                </svg></div> <select name="Country" id="form-field-country" class="elementor-field-textual elementor-size-md" required="required">
                                                <option value="">{{__('Select Your Country')}}*</option>
                                                @foreach($countries as $c)
                                                <option value="{{$c->id}}">{{$resolveCountryName($c)}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="elementor-field-type-select elementor-field-group elementor-column elementor-field-group-UnitType elementor-col-100 elementor-field-required"> <label for="form-field-UnitType" class="elementor-field-label elementor-screen-only"> UnitType </label>
                                        <div class="elementor-field elementor-select-wrapper remove-before ">
                                            <div class="select-caret-down-wrapper"> <svg aria-hidden="true" class="e-font-icon-svg e-eicon-caret-down" viewBox="0 0 571.4 571.4" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M571 393Q571 407 561 418L311 668Q300 679 286 679T261 668L11 418Q0 407 0 393T11 368 36 357H536Q550 357 561 368T571 393Z"></path>
                                                </svg></div> <select name="UnitType" id="form-field-UnitType" class="elementor-field-textual elementor-size-md" required="required">
                                                <option value="">{{__('Interested in')}}*</option>
                                                <option value="1BR">{{__('1 Bedroom Apartment')}}</option>
                                                <option value="2BR">{{__('2 Bedroom Apartment')}}</option>
                                                <option value="2BR">{{__('3 Bedroom Apartment')}}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="elementor-field-type-select elementor-field-group elementor-column elementor-field-group-UnitType elementor-col-100 elementor-field-required"> <label for="form-field-UnitType" class="elementor-field-label elementor-screen-only"> Looking to live / invest in project </label>
                                        <div class="elementor-field elementor-select-wrapper remove-before ">
                                            <div class="select-caret-down-wrapper"> <svg aria-hidden="true" class="e-font-icon-svg e-eicon-caret-down" viewBox="0 0 571.4 571.4" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M571 393Q571 407 561 418L311 668Q300 679 286 679T261 668L11 418Q0 407 0 393T11 368 36 357H536Q550 357 561 368T571 393Z"></path>
                                                </svg></div> <select name="lookingTo" id="form-field-lookingTo" class="elementor-field-textual elementor-size-md">
                                                <option value="">{{__('Looking to')}}</option>
                                                <option value="live">{{__('Live')}}</option>
                                                <option value="invest">{{__('Invest')}}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="elementor-field-type-select elementor-field-group elementor-column elementor-field-group-UnitType elementor-col-100"> <label for="form-field-UnitType" class="elementor-field-label elementor-screen-only"> Are you a resident? </label>
                                        <div class="elementor-field elementor-select-wrapper remove-before ">
                                            <div class="select-caret-down-wrapper"> <svg aria-hidden="true" class="e-font-icon-svg e-eicon-caret-down" viewBox="0 0 571.4 571.4" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M571 393Q571 407 561 418L311 668Q300 679 286 679T261 668L11 418Q0 407 0 393T11 368 36 357H536Q550 357 561 368T571 393Z"></path>
                                                </svg></div> <select name="resident" id="form-field-resident" class="elementor-field-textual elementor-size-md">
                                                <option value="">{{__('Resident')}}</option>
                                                <option value="yes">{{__('Yes')}}</option>
                                                <option value="no">{{__('No')}}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="elementor-field-type-select elementor-field-group elementor-column elementor-field-group-UnitType elementor-col-100"> <label for="form-field-UnitType" class="elementor-field-label elementor-screen-only"> Age group </label>
                                        <div class="elementor-field elementor-select-wrapper remove-before ">
                                            <div class="select-caret-down-wrapper">
                                                <svg aria-hidden="true" class="e-font-icon-svg e-eicon-caret-down" viewBox="0 0 571.4 571.4" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M571 393Q571 407 561 418L311 668Q300 679 286 679T261 668L11 418Q0 407 0 393T11 368 36 357H536Q550 357 561 368T571 393Z"></path>
                                                </svg>
                                            </div> <select name="agentId" id="form-field-agentId" class="elementor-field-textual elementor-size-md">
                                                <option value="">{{__('Agent')}}</option>
                                                @foreach($agents as $agent)
                                                <option value="{{$agent->id}}">{{$agent->name}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="elementor-field-type-select elementor-field-group elementor-column elementor-field-group-agentId elementor-col-100"> <label for="form-field-UnitType" class="elementor-field-label elementor-screen-only"> Age group </label>
                                        <div class="elementor-field elementor-select-wrapper remove-before ">
                                            <div class="select-caret-down-wrapper"> <svg aria-hidden="true" class="e-font-icon-svg e-eicon-caret-down" viewBox="0 0 571.4 571.4" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M571 393Q571 407 561 418L311 668Q300 679 286 679T261 668L11 418Q0 407 0 393T11 368 36 357H536Q550 357 561 368T571 393Z"></path>
                                                </svg></div> <select name="ageGroup" id="form-field-ageGroup" class="elementor-field-textual elementor-size-md">
                                                <option value="">{{__('Age group')}}</option>
                                                <option value="25-35">25-35</option>
                                                <option value="35-45">35-45</option>
                                                <option value="45+">45+</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="elementor-field-type-textarea elementor-field-group elementor-column elementor-field-group-message elementor-col-100 elementor-field-required">
                                        <label for="form-field-message" class="elementor-field-label elementor-screen-only"> Message </label>
                                        <textarea class="elementor-field-textual elementor-field elementor-size-md" name="Message" id="form-field-message" rows="4" required="required">{{ __('Hi, I`m interested in Lusial Marina Heights. Please provide me with more details.') }}
{{ __('Thank you!') }}</textarea>
                                    </div>
                                    <div class="elementor-field-type-hidden elementor-field-group elementor-column elementor-field-group-utm_source elementor-col-100"> <input size="1" type="hidden" name="form_id" id="form-field-form_id" class="elementor-field elementor-size-md  elementor-field-textual" value="MarinaHeights_enquiry_form"></div>
                                    <div class="elementor-field-type-hidden elementor-field-group elementor-column elementor-field-group-utm_source elementor-col-100"> <input size="1" type="hidden" name="form_name" id="form-field-form_name" class="elementor-field elementor-size-md  elementor-field-textual" value="MarinaHeights_enquiry_form"></div>

                                    @foreach(\App\Models\MarketingParams::WHITELISTED_URL_PARAMS as $k)
                                    <input type="hidden" name="{{$k}}" id="form-field-{{$k}}" value="{{request()->get($k, '')}}">
                                    @endforeach
                                    <div class="elementor-field-group elementor-column elementor-field-type-submit elementor-col-100 e-form__buttons"> <button class="elementor-button elementor-size-sm" style="cursor: pointer" type="submit"> <span class="elementor-button-content-wrapper"> <span class="elementor-button-text">{{__('Submit Enquiry')}}</span> </span> </button></div>
                                    <div id="errorsPlaceholder"></div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <footer class="elementor-element elementor-element-511da42 e-flex e-con-boxed e-con e-parent" data-id="511da42" data-element_type="container">
        <div class="e-con-inner">
            <div class="elementor-element elementor-element-d2af775 e-con-full e-flex e-con e-child" data-id="d2af775" data-element_type="container">
                <div class="elementor-element elementor-element-23e222a elementor-icon-list--layout-inline elementor-list-item-link-inline elementor-align-right elementor-mobile-align-center elementor-widget elementor-widget-icon-list" data-id="23e222a" data-element_type="widget" data-widget_type="icon-list.default">
                    <div class="elementor-widget-container">
                        <ul class="elementor-icon-list-items elementor-inline-items">
                            <li class="elementor-icon-list-item elementor-inline-item"> <a href="#about"> <span class="elementor-icon-list-text">{{__('About Lusail Marina Heights')}}</span> </a></li>
                            <li class="elementor-icon-list-item elementor-inline-item"> <a href="#facilities"> <span class="elementor-icon-list-text">{{__('Amenities')}}</span> </a></li>
                            <li class="elementor-icon-list-item elementor-inline-item"> <a href="#pricing"> <span class="elementor-icon-list-text">{{__('Pricing')}}</span> </a></li>
                            <li class="elementor-icon-list-item elementor-inline-item"> <a href="#enquire"> <span class="elementor-icon-list-text">{{__('Request Offer')}}</span> </a></li>
                        </ul>
                    </div>
                </div>
                {{--
                <div class="elementor-element elementor-element-d7b714e elementor-icon-list--layout-inline elementor-list-item-link-inline elementor-align-right elementor-mobile-align-center elementor-widget elementor-widget-icon-list" data-id="d7b714e" data-element_type="widget" data-widget_type="icon-list.default">
                    <div class="elementor-widget-container">
                        <ul class="elementor-icon-list-items elementor-inline-items">
                            <li class="elementor-icon-list-item elementor-inline-item"> <a href="#elementor-action%3Aaction%3Dpopup%3Aopen%26settings%3DeyJpZCI6IjEyNzAiLCJ0b2dnbGUiOmZhbHNlfQ%3D%3D"> <span class="elementor-icon-list-text">Privacy Policy</span> </a></li>
                        </ul>
                    </div>
                </div>
                --}}
            </div>
            <div class="elementor-element elementor-element-66d4479 e-con-full e-flex e-con e-child" data-id="66d4479" data-element_type="container">
                <div class="elementor-element elementor-element-eb9db08 elementor-widget__width-auto elementor-widget elementor-widget-heading" data-id="eb9db08" data-element_type="widget" data-widget_type="heading.default">
                    <div class="elementor-widget-container">
                        <p class="elementor-heading-title elementor-size-default">Copyright © 2012-{{date('Y')}} FGREALTY® – Find Great Realty WLL. All Rights Reserved. FGREALTY® is a registered trademark of Find Great Realty WLL Qatar. Trademark License number 121939</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>
    </div>
    <script>
        const hamburger = document.querySelector("#hamburger");
        const navLinksCloseCta = document.querySelector("#navLinksCloseCta");
        const navMenu = document.querySelector("#nav-menu");

        hamburger.addEventListener("click", () => {
            navMenu.classList.toggle("show");
        });
        navLinksCloseCta.addEventListener("click", () => {
            navMenu.classList.toggle("show");
        });

        document.addEventListener("click", (e) => {
            if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
                navMenu.classList.remove("show");
            }
        });
        document.querySelectorAll("#nav-menu a.clickable").forEach(link => {
            link.addEventListener("click", () => {
                setTimeout(() => navMenu.classList.remove("show"), 100)

            });
        });
    </script>
    <script defer src="data:text/javascript;base64,CgkJCQljb25zdCBsYXp5bG9hZFJ1bk9ic2VydmVyID0gKCkgPT4gewoJCQkJCWNvbnN0IGxhenlsb2FkQmFja2dyb3VuZHMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCBgLmUtY29uLmUtcGFyZW50Om5vdCguZS1sYXp5bG9hZGVkKWAgKTsKCQkJCQljb25zdCBsYXp5bG9hZEJhY2tncm91bmRPYnNlcnZlciA9IG5ldyBJbnRlcnNlY3Rpb25PYnNlcnZlciggKCBlbnRyaWVzICkgPT4gewoJCQkJCQllbnRyaWVzLmZvckVhY2goICggZW50cnkgKSA9PiB7CgkJCQkJCQlpZiAoIGVudHJ5LmlzSW50ZXJzZWN0aW5nICkgewoJCQkJCQkJCWxldCBsYXp5bG9hZEJhY2tncm91bmQgPSBlbnRyeS50YXJnZXQ7CgkJCQkJCQkJaWYoIGxhenlsb2FkQmFja2dyb3VuZCApIHsKCQkJCQkJCQkJbGF6eWxvYWRCYWNrZ3JvdW5kLmNsYXNzTGlzdC5hZGQoICdlLWxhenlsb2FkZWQnICk7CgkJCQkJCQkJfQoJCQkJCQkJCWxhenlsb2FkQmFja2dyb3VuZE9ic2VydmVyLnVub2JzZXJ2ZSggZW50cnkudGFyZ2V0ICk7CgkJCQkJCQl9CgkJCQkJCX0pOwoJCQkJCX0sIHsgcm9vdE1hcmdpbjogJzIwMHB4IDBweCAyMDBweCAwcHgnIH0gKTsKCQkJCQlsYXp5bG9hZEJhY2tncm91bmRzLmZvckVhY2goICggbGF6eWxvYWRCYWNrZ3JvdW5kICkgPT4gewoJCQkJCQlsYXp5bG9hZEJhY2tncm91bmRPYnNlcnZlci5vYnNlcnZlKCBsYXp5bG9hZEJhY2tncm91bmQgKTsKCQkJCQl9ICk7CgkJCQl9OwoJCQkJY29uc3QgZXZlbnRzID0gWwoJCQkJCSdET01Db250ZW50TG9hZGVkJywKCQkJCQknZWxlbWVudG9yL2xhenlsb2FkL29ic2VydmUnLAoJCQkJXTsKCQkJCWV2ZW50cy5mb3JFYWNoKCAoIGV2ZW50ICkgPT4gewoJCQkJCWRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoIGV2ZW50LCBsYXp5bG9hZFJ1bk9ic2VydmVyICk7CgkJCQl9ICk7CgkJCQ=="></script> <noscript>
        <style>
            .lazyload {
                display: none;
            }
        </style>
    </noscript>
    <script data-noptimize="1">
        window.lazySizesConfig = window.lazySizesConfig || {};
        window.lazySizesConfig.loadMode = 1;
    </script>
    <script defer data-noptimize="1" src='https://fgrealty.qa/lp/wp-content/plugins/autoptimize/classes/external/js/lazysizes.min.js?ao_version=3.1.13'></script>
    <link rel='stylesheet' id='elementor-post-1289-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_a34c2070e9f9fd96a0421a1e0261e280.css?ver=1738745534' media='all' />
    <link rel='stylesheet' id='widget-media-carousel-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/css/widget-media-carousel.min.css?ver=3.27.2' media='all' />
    <link rel='stylesheet' id='widget-carousel-module-base-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/css/widget-carousel-module-base.min.css?ver=3.27.2' media='all' />
    <link rel='stylesheet' id='e-popup-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/css/conditionals/popup.min.css?ver=3.27.2' media='all' />
    <link rel='stylesheet' id='elementor-post-1315-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_d5c371432d8ecb2ca958f91afdfcaf50.css?ver=1738745534' media='all' />
    <link rel='stylesheet' id='elementor-post-1317-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_42cbd81bad70eb7bbf3996429ec7ee6d.css?ver=1738745534' media='all' />
    <link rel='stylesheet' id='elementor-post-1319-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_0279215c05de077896fa463f4398afb3.css?ver=1738745534' media='all' />
    <link rel='stylesheet' id='elementor-post-1192-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_840e8cf21aca42d72fa2eae03ab96e1b.css?ver=1738745535' media='all' />
    <link rel='stylesheet' id='elementor-post-1270-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_03a504b535230cd2118b2942243aad40.css?ver=1738745535' media='all' />
    <script defer id="wp-block-template-skip-link-js-after" src="data:text/javascript;base64,CgkoIGZ1bmN0aW9uKCkgewoJCXZhciBza2lwTGlua1RhcmdldCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoICdtYWluJyApLAoJCQlzaWJsaW5nLAoJCQlza2lwTGlua1RhcmdldElELAoJCQlza2lwTGluazsKCgkJLy8gRWFybHkgZXhpdCBpZiBhIHNraXAtbGluayB0YXJnZXQgY2FuJ3QgYmUgbG9jYXRlZC4KCQlpZiAoICEgc2tpcExpbmtUYXJnZXQgKSB7CgkJCXJldHVybjsKCQl9CgoJCS8qCgkJICogR2V0IHRoZSBzaXRlIHdyYXBwZXIuCgkJICogVGhlIHNraXAtbGluayB3aWxsIGJlIGluamVjdGVkIGluIHRoZSBiZWdpbm5pbmcgb2YgaXQuCgkJICovCgkJc2libGluZyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoICcud3Atc2l0ZS1ibG9ja3MnICk7CgoJCS8vIEVhcmx5IGV4aXQgaWYgdGhlIHJvb3QgZWxlbWVudCB3YXMgbm90IGZvdW5kLgoJCWlmICggISBzaWJsaW5nICkgewoJCQlyZXR1cm47CgkJfQoKCQkvLyBHZXQgdGhlIHNraXAtbGluayB0YXJnZXQncyBJRCwgYW5kIGdlbmVyYXRlIG9uZSBpZiBpdCBkb2Vzbid0IGV4aXN0LgoJCXNraXBMaW5rVGFyZ2V0SUQgPSBza2lwTGlua1RhcmdldC5pZDsKCQlpZiAoICEgc2tpcExpbmtUYXJnZXRJRCApIHsKCQkJc2tpcExpbmtUYXJnZXRJRCA9ICd3cC0tc2tpcC1saW5rLS10YXJnZXQnOwoJCQlza2lwTGlua1RhcmdldC5pZCA9IHNraXBMaW5rVGFyZ2V0SUQ7CgkJfQoKCQkvLyBDcmVhdGUgdGhlIHNraXAgbGluay4KCQlza2lwTGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoICdhJyApOwoJCXNraXBMaW5rLmNsYXNzTGlzdC5hZGQoICdza2lwLWxpbmsnLCAnc2NyZWVuLXJlYWRlci10ZXh0JyApOwoJCXNraXBMaW5rLmhyZWYgPSAnIycgKyBza2lwTGlua1RhcmdldElEOwoJCXNraXBMaW5rLmlubmVySFRNTCA9ICdTa2lwIHRvIGNvbnRlbnQnOwoKCQkvLyBJbmplY3QgdGhlIHNraXAgbGluay4KCQlzaWJsaW5nLnBhcmVudEVsZW1lbnQuaW5zZXJ0QmVmb3JlKCBza2lwTGluaywgc2libGluZyApOwoJfSgpICk7CgkK"></script>
    <script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/lib/swiper/v8/swiper.min.js?ver=8.4.5" id="swiper-js"></script>
    <script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/lib/jquery-numerator/jquery-numerator.min.js?ver=0.2.1" id="jquery-numerator-js"></script>
    <script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/js/webpack-pro.runtime.min.js?ver=3.27.2" id="elementor-pro-webpack-runtime-js"></script>
    <script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.27.3" id="elementor-webpack-runtime-js"></script>
    <script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.27.3" id="elementor-frontend-modules-js"></script>
    <script src="https://fgrealty.qa/lp/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6" id="wp-hooks-js"></script>
    <script src="https://fgrealty.qa/lp/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6" id="wp-i18n-js"></script>
    <script defer id="wp-i18n-js-after" src="data:text/javascript;base64,CndwLmkxOG4uc2V0TG9jYWxlRGF0YSggeyAndGV4dCBkaXJlY3Rpb25cdTAwMDRsdHInOiBbICdsdHInIF0gfSApOwo="></script>
    <script defer id="elementor-pro-frontend-js-before" src="data:text/javascript;base64,CnZhciBFbGVtZW50b3JQcm9Gcm9udGVuZENvbmZpZyA9IHsiYWpheHVybCI6Imh0dHBzOlwvXC9mZ3JlYWx0eS5xYVwvbHBcL3dwLWFkbWluXC9hZG1pbi1hamF4LnBocCIsIm5vbmNlIjoiY2U1ZTMzMWI5YSIsInVybHMiOnsiYXNzZXRzIjoiaHR0cHM6XC9cL2ZncmVhbHR5LnFhXC9scFwvd3AtY29udGVudFwvcGx1Z2luc1wvZWxlbWVudG9yLXByb1wvYXNzZXRzXC8iLCJyZXN0IjoiaHR0cHM6XC9cL2ZncmVhbHR5LnFhXC9scFwvd3AtanNvblwvIn0sInNldHRpbmdzIjp7ImxhenlfbG9hZF9iYWNrZ3JvdW5kX2ltYWdlcyI6dHJ1ZX0sInBvcHVwIjp7Imhhc1BvcFVwcyI6dHJ1ZX0sInNoYXJlQnV0dG9uc05ldHdvcmtzIjp7ImZhY2Vib29rIjp7InRpdGxlIjoiRmFjZWJvb2siLCJoYXNfY291bnRlciI6dHJ1ZX0sInR3aXR0ZXIiOnsidGl0bGUiOiJUd2l0dGVyIn0sImxpbmtlZGluIjp7InRpdGxlIjoiTGlua2VkSW4iLCJoYXNfY291bnRlciI6dHJ1ZX0sInBpbnRlcmVzdCI6eyJ0aXRsZSI6IlBpbnRlcmVzdCIsImhhc19jb3VudGVyIjp0cnVlfSwicmVkZGl0Ijp7InRpdGxlIjoiUmVkZGl0IiwiaGFzX2NvdW50ZXIiOnRydWV9LCJ2ayI6eyJ0aXRsZSI6IlZLIiwiaGFzX2NvdW50ZXIiOnRydWV9LCJvZG5va2xhc3NuaWtpIjp7InRpdGxlIjoiT0siLCJoYXNfY291bnRlciI6dHJ1ZX0sInR1bWJsciI6eyJ0aXRsZSI6IlR1bWJsciJ9LCJkaWdnIjp7InRpdGxlIjoiRGlnZyJ9LCJza3lwZSI6eyJ0aXRsZSI6IlNreXBlIn0sInN0dW1ibGV1cG9uIjp7InRpdGxlIjoiU3R1bWJsZVVwb24iLCJoYXNfY291bnRlciI6dHJ1ZX0sIm1peCI6eyJ0aXRsZSI6Ik1peCJ9LCJ0ZWxlZ3JhbSI6eyJ0aXRsZSI6IlRlbGVncmFtIn0sInBvY2tldCI6eyJ0aXRsZSI6IlBvY2tldCIsImhhc19jb3VudGVyIjp0cnVlfSwieGluZyI6eyJ0aXRsZSI6IlhJTkciLCJoYXNfY291bnRlciI6dHJ1ZX0sIndoYXRzYXBwIjp7InRpdGxlIjoiV2hhdHNBcHAifSwiZW1haWwiOnsidGl0bGUiOiJFbWFpbCJ9LCJwcmludCI6eyJ0aXRsZSI6IlByaW50In0sIngtdHdpdHRlciI6eyJ0aXRsZSI6IlgifSwidGhyZWFkcyI6eyJ0aXRsZSI6IlRocmVhZHMifX0sImZhY2Vib29rX3NkayI6eyJsYW5nIjoiZW5fVVMiLCJhcHBfaWQiOiIifSwibG90dGllIjp7ImRlZmF1bHRBbmltYXRpb25VcmwiOiJodHRwczpcL1wvZmdyZWFsdHkucWFcL2xwXC93cC1jb250ZW50XC9wbHVnaW5zXC9lbGVtZW50b3ItcHJvXC9tb2R1bGVzXC9sb3R0aWVcL2Fzc2V0c1wvYW5pbWF0aW9uc1wvZGVmYXVsdC5qc29uIn19Owo="></script>
    <script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/js/frontend.min.js?ver=3.27.2" id="elementor-pro-frontend-js"></script>
    <script defer src="https://fgrealty.qa/lp/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3" id="jquery-ui-core-js"></script>
    <script defer id="elementor-frontend-js-before" src="data:text/javascript;base64,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"></script>
    <script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.27.3" id="elementor-frontend-js"></script>
    <script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/js/elements-handlers.min.js?ver=3.27.2" id="pro-elements-handlers-js"></script>
    <script defer src="data:text/javascript;base64,DQpfbGlua2VkaW5fcGFydG5lcl9pZCA9ICI3MTUyNzA1IjsNCndpbmRvdy5fbGlua2VkaW5fZGF0YV9wYXJ0bmVyX2lkcyA9IHdpbmRvdy5fbGlua2VkaW5fZGF0YV9wYXJ0bmVyX2lkcyB8fCBbXTsNCndpbmRvdy5fbGlua2VkaW5fZGF0YV9wYXJ0bmVyX2lkcy5wdXNoKF9saW5rZWRpbl9wYXJ0bmVyX2lkKTsNCg=="></script>
    <script>
        const downloadBrochure = () => {
            console.log('lmh');
            console.log('event', event)
            const brochureButton = event.target.closest('.downloadBrochureBtn');
            if (!brochureButton) return;
            const btnText = brochureButton.querySelector('.btn-text');

            brochureButton.classList.add('btn--loadingState');
            brochureButton.disabled = true;
            btnText.textContent = "Loading...";

            fetch("{{route('project.marinaHeights.download', ['projectsWord' => 'projects'])}}")
                .then(res => res.ok ? res.blob() : Promise.reject("Download failed"))
                .then(blob => {
                    const url = window.URL.createObjectURL(blob);
                    const downloadLink = document.createElement('a');
                    downloadLink.href = url;
                    downloadLink.download = "LMH_Brochure.pdf";
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    document.body.removeChild(downloadLink);
                    window.URL.revokeObjectURL(url);
                })
                .finally(() => {
                    brochureButton.classList.remove('btn--loadingState');
                    brochureButton.disabled = false;
                    btnText.textContent = "{{__('Download Brochure')}}";
                });
        }

        async function submitForm() {
            const form = document.querySelector('#MarinaHeights_enquiry_form');
            const formData = new FormData(form); // Create FormData object
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalBtnText = submitBtn.innerHTML;

            if(submitBtn.disabled) {
                return;
            }
            submitBtn.disabled = true;
            submitBtn.innerHTML = "{{__('Loading...')}}";

            try {
                const response = await fetch("/webhooks/enquire", {
                    method: "POST",
                    body: formData,
                });

                if (response.ok) {
                    const result = await response.json();
                    window.location.href = "{{persistQueryParamsToRoute('project.cview.landing.thankyou', ['projectsWord' => 'projects'])}}";
                } else {
                    const errorText = await response.text();
                    document.getElementById("errorsPlaceholder").innerHTML =
                        `<p style="color: red;">Error: ${errorText}</p>`;
                }
            } catch (error) {
                document.getElementById("errorsPlaceholder").innerHTML =
                    `<p style="color: red;">Request failed: ${error.message}</p>`;
            } finally {
                // Reset button state
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalBtnText;
            }
            return false;
        }
    </script>
</body>

</html>