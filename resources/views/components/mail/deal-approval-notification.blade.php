@extends('email.layout-html5')
@section('content')
@include('email.company-logo-centered')
<p>Hi,</p>
<p><strong>A deal has been approved and requires your immediate attention!</strong></p>
<p>Please contact the client to ask for a review regarding this approved deal.</p>

<h3>Deal Details</h3>
@php
$closingAgent = $deal->getEmailClosingAgent();
@endphp
<ul>
    <li><strong>Deal Reference:</strong> {{ $deal->getRefNo() }}</li>
    <li><strong>Client:</strong> @if(!is_null($deal->client)) {{ $deal->client->name }} @else - @endif</li>
    <li><strong>Owner:</strong> @if(!is_null($deal->owner)) {{ $deal->owner->name }} @else - @endif</li>
    <li><strong>Property:</strong> @if(!is_null($deal->property)) {{ $deal->property->ref_no }} @else - @endif</li>
    <li><strong>Closing Agent:</strong> @if(!is_null($closingAgent)) {{ $closingAgent->name }} @else - @endif</li>
    <li><strong>Rent Value / Transaction Value:</strong> @if(!is_null($deal->price)) {{ number_format($deal->price) }} QAR @else - @endif</li>
    <li><strong>Commission Landlord:</strong> @if(!is_null($deal->commission_landlord)) {{ number_format($deal->commission_landlord) }} QAR @else - @endif</li>
    <li><strong>Commission Client:</strong> @if(!is_null($deal->commission_client)) {{ number_format($deal->commission_client) }} QAR @else - @endif</li>
    <li><strong>Start Date:</strong> @if(!is_null($deal->start_date)) {{ \Carbon\Carbon::parse($deal->start_date)->format('d/m/Y') }} @else - @endif</li>
    <li><strong>End Date:</strong> @if(!is_null($deal->end_date)) {{ \Carbon\Carbon::parse($deal->end_date)->format('d/m/Y') }} @else - @endif</li>
</ul>

<div style="background-color: #f8f9fa; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;">
    <h4 style="color: #28a745; margin-top: 0;">Action Required:</h4>
    <p style="margin-bottom: 0;"><strong>Please contact the client to request a review for this approved deal.</strong></p>
    <p style="margin-bottom: 0;">A reminder has been set for you to follow up on this matter.</p>
</div>

<p>
    <a href="{{ route('crm.deals.read', ['id' => $deal->id]) }}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View Deal in CRM</a>
</p>

<p>Best regards,<br>FG Realty Team</p>
@endsection
