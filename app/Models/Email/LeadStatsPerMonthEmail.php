<?php

namespace App\Models\Email;

class LeadStatsPerMonthEmail extends GenericEmail
{
    private $leadsStats;
    private $startDate;
    private $endDate;

    public function __construct($leadsStats, $startDate, $endDate)
    {
        $this->leadsStats = $leadsStats;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
    }

    public function getSubject()
    {
        return "Leads stats per month";
    }

    public function getTemplateName()
    {
        return 'components.mail.leads-stats-per-month';
    }

    public function getTo()
    {
        return [
            env('EMAIL_ADDRESS_SERBAN'), 
            env('EMAIL_ADDRESS_HAYA'), 
            env('EMAIL_ADDRESS_NOHA')
        ];
    }

    public function getTemplateVars()
    {
        return [
            'leadsStats' => $this->leadsStats,
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];
    }
}
