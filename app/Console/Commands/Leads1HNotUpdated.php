<?php

namespace App\Console\Commands;

use App\Services\AuthorizationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class Leads1HNotUpdated extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'leads:handle-not-updated-in-1-h';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Move all leads not updated for 1 hour to TL';

    private $authorizationService;
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(AuthorizationService $authorizationService)
    {
        parent::__construct();
        $this->authorizationService = $authorizationService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info("[Automation] Leads1HNotUpdated command started at " . now()->toDateTimeString());
        try {
            $this->authorizationService->moveNotUpdatedLeads();
        } catch (\Exception $e) {
            Log::error("[Automation] Leads1HNotUpdated command failed: " . $e->getMessage());
            return 1;
        }
    }
}
