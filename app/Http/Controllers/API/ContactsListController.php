<?php

namespace App\Http\Controllers\API;
// SELECT uo.*, uos.* FROM user_order uo LEFT JOIN user_order_status uos ON uo.id = uos.user_order_id WHERE uos.createdAt > '2022-12-20' AND uos.status = 2

use App\Http\Controllers\Controller;
use App\Http\Requests\RecordContactsListTaskRequest;
use App\Imports\ContactsListImport;
use App\Models\Contact;
use App\Models\ContactLandlordData;
use App\Models\Crm\ContactsListTag;
use App\Models\Crm\RolesDef;
use App\Models\Nationality;
use App\Models\Task;
use App\Services\ContactsListService;
use App\Services\ContactsService;
use App\Services\OperationHistoryService;
use App\Services\MailjetService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use DB;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ContactsListController extends Controller
{
    protected $contactsListService;
    protected $contactsService;
    private $operationHistoryService;
    private $mailjetService;
    private $contactsMapper;

    const EXCEL_HEADERS = [
        'FULL_NAME' => 'Full Name',
        'FIRST_NAME' => 'First Name',
        'LAST_NAME' => 'Last Name',
        'EMAIL' => 'Email',
        'PHONES' => 'Phone(s)',
        'GENDER' => 'Gender',
        'NATIONALITY' => 'Nationality',
        'COMPANY' => 'Company',
        'WORK_POSITION' => 'Work Position',
        'DATE_OF_BIRTH' => 'Date of Birth',
        'QID' => 'QID',
        'PASSPORT' => 'Passport',
    ];

    private $validFormats = [
        'FORMAT_FULL' => [
            self::EXCEL_HEADERS['FULL_NAME'] => 0,
            self::EXCEL_HEADERS['EMAIL'] => 1,
            self::EXCEL_HEADERS['PHONES'] => 2,
            self::EXCEL_HEADERS['GENDER'] => 3,
            self::EXCEL_HEADERS['NATIONALITY'] => 4,
            self::EXCEL_HEADERS['COMPANY'] => 5,
            self::EXCEL_HEADERS['WORK_POSITION'] => 6,
            self::EXCEL_HEADERS['DATE_OF_BIRTH'] => 7,
            self::EXCEL_HEADERS['QID'] => 8,
            self::EXCEL_HEADERS['PASSPORT'] => 9
        ],
        'FORMAT_SEPARATE_NAMES' => [
            self::EXCEL_HEADERS['FIRST_NAME'] => 0,
            self::EXCEL_HEADERS['LAST_NAME'] => 1,
            self::EXCEL_HEADERS['EMAIL'] => 2,
            self::EXCEL_HEADERS['PHONES'] => 3,
            self::EXCEL_HEADERS['GENDER'] => 4,
            self::EXCEL_HEADERS['NATIONALITY'] => 5,
            self::EXCEL_HEADERS['COMPANY'] => 6,
            self::EXCEL_HEADERS['WORK_POSITION'] => 7,
            self::EXCEL_HEADERS['DATE_OF_BIRTH'] => 8,
            self::EXCEL_HEADERS['QID'] => 9,
        ]
    ];

    public function __construct(
        ContactsListService $contactsListService,
        ContactsService $contactsService,
        OperationHistoryService $operationHistoryService,
        MailjetService $mailjetService
    ) {
        $this->contactsListService = $contactsListService;
        $this->contactsService = $contactsService;
        $this->mailjetService = $mailjetService;
        $this->contactsMapper = function ($item) {
            $item->tags = empty(trim($item->tags)) ? [] : explode(",", $item->tags);
            $item->tag_labels = empty(trim($item->tag_labels)) ? [] : explode("||", $item->tag_labels);
            $item->leads_ids = is_null($item->leads_ids) ? [] : array_map(fn($str) => intval($str), explode(",", $item->leads_ids));
            $item->deals_ids = is_null($item->deals_ids) ? [] : array_map(fn($str) => intval($str), explode(",", $item->deals_ids));
            $item->can_delete = auth()->user()->hasAnyRole([RolesDef::OFFICE_MANAGER]);
            return $item;
        };
        $this->operationHistoryService = $operationHistoryService;
    }

    public function import()
    {
        $validFields = request()->validate([
            'fileContent' => 'required|string',
            'fileName' => 'required|string',
        ]);

        $nationalitiesMap = [];
        $nationalities = Nationality::get();
        foreach ($nationalities as $n) {
            $nationalitiesMap[$n->name] = $n->id;
        }

        // decode the content
        $content = base64_decode($validFields['fileContent']);
        Storage::disk('local')->put($validFields['fileName'], $content);

        $excelSheetsData = Excel::toArray(new ContactsListImport, $validFields['fileName'], 'local', \Maatwebsite\Excel\Excel::XLSX);
        if (is_array($excelSheetsData[0])) {
            // validate the header
            $headerRow = $excelSheetsData[0][0];
            $foundFormat = null;
            foreach ($this->validFormats as $formatKey => $formatDefinition) {
                $foundFormat = $formatKey;
                foreach ($formatDefinition as $colTitle => $colIndex) {
                    if (!isset($headerRow[$colIndex]) || $headerRow[$colIndex] !== $colTitle) {
                        $foundFormat = null;
                        continue;
                    }
                }
                if (!is_null($foundFormat)) {
                    break;
                }
            }
            if (is_null($foundFormat)) {
                return response([
                    "message" => "Invalid format for imported file. Please use the Template file.",
                    "type" => "error"
                ], 200);
            } else {
                $existingContacts = [];
                $invalidContacts = [];
                $importedContactsNo = 0;
                // get all rows and try to import
                foreach ($excelSheetsData[0] as $index => $dataRow) {
                    if ($index > 0) {
                        if ($foundFormat == "FORMAT_FULL") {
                            $fullName = $dataRow[$this->validFormats[$foundFormat][self::EXCEL_HEADERS['FULL_NAME']]];
                        } elseif ($foundFormat == "FORMAT_SEPARATE_NAMES") {
                            $fullName = $dataRow[$this->validFormats[$foundFormat][self::EXCEL_HEADERS['FIRST_NAME']]];
                            if (!empty($dataRow[$this->validFormats[$foundFormat][self::EXCEL_HEADERS['LAST_NAME']]])) {
                                $fullName .= " " . $dataRow[$this->validFormats[$foundFormat][self::EXCEL_HEADERS['FIRST_NAME']]];
                            }
                        }

                        $email = $dataRow[$this->validFormats[$foundFormat][self::EXCEL_HEADERS['EMAIL']]];
                        $phone = $dataRow[$this->validFormats[$foundFormat][self::EXCEL_HEADERS['PHONES']]];
                        $phones = [];
                        if (!empty(trim($phone))) {
                            $phones = array_filter(array_map(fn($item) => trim($item), explode(",", $phone)), fn($item) => !empty($item));
                        }
                        $emails = [];
                        if (!empty(trim($email))) {
                            $emails = array_filter(array_map(fn($item) => trim($item), explode(",", $email)), fn($item) => !empty($item));
                        }
                        if (!empty(trim($phone)) || !empty(trim($email))) {
                            $existingContact = Contact::where(function ($qb) use ($phones, $emails) {
                                $qb->whereIn('phone_1', $phones)
                                    ->orWhereIn('phone_2', $phones)
                                    ->orWhereIn('mobile_1', $phones)
                                    ->orWhereIn('mobile_2', $phones);
                                $qb->orWhereIn('email_1', $emails)
                                    ->orWhereIn('email_2', $emails);
                            })->first();
                            if (!is_null($existingContact)) {
                                $existingContacts[] = $fullName;
                            } else {

                                $c = new Contact();
                                $c->contact_type = "client";
                                $c->created_by = auth()->user()->id;
                                $c->name = $fullName;

                                // emails
                                if (count($emails) == 1) {
                                    $c->email_1 = $emails[0];
                                } elseif (count($emails) > 1) {
                                    $email_1 = array_shift($emails);
                                    $email_2 = join(", ", $emails);
                                    $c->email_1 = $email_1;
                                    $c->email_2 = $email_2;
                                }

                                // phones
                                foreach ($phones as $index => $phone) {
                                    if ($index < 2) {
                                        $field = "mobile_" . ($index + 1);
                                        $c->$field = $phone;
                                    } elseif ($index < 4) {
                                        $field = "phone_" . ($index + 1);
                                        $c->$field = $phone;
                                    }
                                }

                                // gender
                                if (!empty($dataRow[$this->validFormats[$foundFormat][self::EXCEL_HEADERS['GENDER']]])) {
                                    $cleanGender = strtolower(trim($dataRow[3]));
                                    if (in_array($cleanGender, ['m', 'male'])) {
                                        $c->gender = "M";
                                    } elseif (in_array($cleanGender, ['f', 'female'])) {
                                        $c->gender = "F";
                                    }
                                }

                                // nationality
                                if (!empty($dataRow[$this->validFormats[$foundFormat][self::EXCEL_HEADERS['NATIONALITY']]])) {
                                    $transformedNationality = ucfirst(trim(strtolower($dataRow[$this->validFormats[$foundFormat][self::EXCEL_HEADERS['NATIONALITY']]])));
                                    if (isset($nationalitiesMap[$transformedNationality])) {
                                        $nationalityId = $nationalitiesMap[$transformedNationality];
                                        $c->nationality_id = $nationalityId;
                                    }
                                }

                                // date of birth
                                try {
                                    if (!empty($dataRow[$this->validFormats[$foundFormat][self::EXCEL_HEADERS['DATE_OF_BIRTH']]])) {
                                        $excelDate = $dataRow[$this->validFormats[$foundFormat][self::EXCEL_HEADERS['DATE_OF_BIRTH']]];
                                        $unixDate = ($excelDate - 25569) * 86400;
                                        $excelDate = 25569 + ($unixDate / 86400);
                                        $unixDate = ($excelDate - 25569) * 86400;
                                        $c->date_of_birth = gmdate("Y-m-d", $unixDate);
                                    }
                                } catch (\Exception $Ex) {
                                    Log::debug($Ex->getMessage());
                                }

                                // company, work position, QID, Passport
                                foreach (
                                    [
                                        "company_name" => $this->validFormats[$foundFormat][self::EXCEL_HEADERS['COMPANY']],
                                        "position" => $this->validFormats[$foundFormat][self::EXCEL_HEADERS['WORK_POSITION']],
                                        "qatar_id_no" => $this->validFormats[$foundFormat][self::EXCEL_HEADERS['QID']]
                                    ] as $column => $dataIndex
                                ) {
                                    if (!empty($dataRow[$dataIndex])) {
                                        $c->$column = trim($dataRow[$dataIndex]);
                                    }
                                }

                                $c->save();
                                $this->operationHistoryService->addOperationHistory($c, "Contact added to system", auth()->user());

                                // operation Log

                                $importedContactsNo++;
                            }
                        } else {
                            $invalidContacts[] = $fullName;
                        }
                    }
                }

                if ($importedContactsNo > 0) {
                    if (count($existingContacts) > 0 || count($invalidContacts) > 0) {
                        $message = "The file was partially imported. Imported contacts: " . $importedContactsNo . ". Existing contacts: " . count($existingContacts) . ". Invalid rows: " . count($invalidContacts);
                    } else {
                        $message = "The file was successfully imported. " . $importedContactsNo . " contact(s)";
                    }
                } else {
                    if (count($existingContacts) > 0 || count($invalidContacts) > 0) {
                        $message = "No contacts imported. Existing contacts: " . count($existingContacts) . ". Invalid rows: " . count($invalidContacts);
                    } else {
                        $message = "No contacts imported";
                    }
                }
                return response([
                    "message" => $message,
                    "type" => "success",
                    "existingContacts" => $existingContacts,
                    "invalidContacts" => $invalidContacts,
                ]);
            }
        }
        return response([
            "message" => "Cannot read the excel file",
            "type" => "error"
        ], 200);
    }

    private function exportItemsToCsv($items, $exportType)
    {
        if ($exportType == 'whatsapp' || $exportType == 'whatsapp_qatar') {
            $itemsArr = [["phone_number", "name"]];
            foreach ($items as $item) {
                $itemsArr[] = [getCompletePhoneNo($item->prefix_mobile_1, $item->mobile_1), $item->name];
            }
        } elseif ($exportType == 'marketing_platform') {
            $itemsArr = [["Name", "Email"]];
            foreach ($items as $item) {
                $itemsArr[] = [$item->name, $item->email_1];
            }
        }

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="items.csv"',
        ];

        $callback = function () use ($itemsArr) {
            $file = fopen('php://output', 'w');

            foreach ($itemsArr as $item) {
                fputcsv($file, $item);
            }

            fclose($file);
        };

        return new StreamedResponse($callback, 200, $headers);
    }

    public function index()
    {
        $items = $this->contactsListService->getTableItems(request());

        if (request()->has('export')) {
            Log::info('Export performed by ['.auth()->user()->email.']. Export type: '.request()->get('exportType'));
            return $this->exportItemsToCsv($items, request()->get('exportType'));
        }

        $items->map(
            $this->contactsMapper
        );

        $count = $this->contactsListService->getTableItemsCount(request());

        return [
            'data' => $items,
            'recordsTotal' => $count,
            'recordsFiltered' => $count
        ];
    }

    public function tags()
    {
        return ContactsListTag::all();
    }

    public function updateVerified()
    {
        $validFields = request()->validate([
            "contact_id" => "required",
            "verified" => "required"
        ]);

        return $this->contactsService->toggleVerified($validFields);
    }

    public function create()
    {
        $validFields = request()->validate([
            "name" => "string|required",
            "gender" => "required",
            "position" => "string|nullable",
            "prefix_mobile_1" => "string|required",
            "mobile_1" => "string|required",
            "prefx_mobile_2" => "string|nullable",
            "mobile_2" => "string|nullable",
            "email_1" => "string|nullable",
            "company_name" => "string|nullable",
            "location" => "string|nullable",
            "nationality_id" => "int|nullable",
            "date_of_birth" => "date|nullable",
            "verified" => "nullable",
            "qatar_id_no" => "string|nullable",
            "occupation" => "string|nullable",
            "position" => "string|nullable",
            "residency" => "string|nullable",
            "tags" => "array|nullable",
            "remarks" => "nullable"
        ]);
        $validFields['created_by'] = auth()->user()->id;
        return $this->contactsService->createContactEntry($validFields);
    }

    public function update($recordId)
    {
        $validFields = request()->validate([
            "name" => "string|required",
            "gender" => "required",
            "position" => "string|nullable",
            "prefix_mobile_1" => "string|required",
            "mobile_1" => "string|required",
            "prefix_mobile_2" => "string|nullable",
            "mobile_2" => "string|nullable",
            "email_1" => "string|nullable",
            "company_name" => "string|nullable",
            "location" => "string|nullable",
            "nationality_id" => "int|nullable",
            "date_of_birth" => "date|nullable",
            "verified" => "nullable",
            "qatar_id_no" => "string|nullable",
            "occupation" => "string|nullable",
            "position" => "string|nullable",
            "residency" => "string|nullable",
            "tags" => "array|nullable",
            "remarks" => "nullable"
        ]);

        $editedContact = $this->contactsService->updateContactEntry($recordId, $validFields);
        if (!is_null($editedContact)) {
            return $editedContact;
        }
        return $this->getSingle($recordId);
    }

    public function getOperationHistory($id)
    {
        $contact = Contact::with(['operationHistory' => function ($qb) {
            return $qb->orderBy('id', 'DESC');
        }])->findOrFail($id);
        return $contact->operationHistory->map(function ($row) {
            return [
                'created_by' => $row->author->name,
                'created_at' => $row->created_at->format('Y-m-d H:i:s'),
                'content' => $row->content
            ];
        });
    }

    public function checkContactExists()
    {
        $validData = request()->validate([
            'email' => 'nullable|email',
            'id' => ''
        ]);
        // return first user with this email
        $existingContacts = $this->contactsService->getContactsByEmail($validData['email']);
        if (!empty($validData['id'])) {
            $firstContact = $existingContacts->firstWhere('id', '!=', $validData['id']);
        } else {
            $firstContact = $existingContacts->first();
        }
        if (!is_null($firstContact)) {
            $contactHasLandlord = ContactLandlordData::where([['contact_id', $firstContact->id], ['deleted_at', null]])->exists();

            $data = [
                'existingContactId' => $firstContact->id,
                'contactHasLandlord' => $contactHasLandlord,
            ];

            return response($data);
        }
        return response([], 200);
    }

    public function checkContactExistsPhone()
    {
        $validData = request()->validate([
            'phone' => 'nullable',
            'id' => ''
        ]);
        // return first user with this email
        $firstContact = Contact::where('mobile_1', $validData['phone'])->orWhere('mobile_2', $validData['phone'])->first();
        if (!is_null($firstContact)) {
            $contactHasLandlord = ContactLandlordData::where([['contact_id', $firstContact->id], ['deleted_at', null]])->exists();

            if (empty($validData['id']) || $validData['id'] != $firstContact->id) {
                $data = [
                    'existingContactId' => $firstContact->id,
                    'contactHasLandlord' => $contactHasLandlord,
                ];
    
                return response($data);
            }
        }
        return response([], 200);
    }

    public function getSingle($id)
    {
        $leadId = request()->query->get('leadId', null);
        $contact = $this->contactsListService->getSingleContact($id, $leadId);
        if (is_null($contact)) {
            return response([], 404);
        } else {
            $contactObject = Contact::with(['masterContact', 'operationHistory' => fn($qb) => $qb->orderBy('created_at', 'DESC'), 'operationHistory.author'])->where('id', $id)->first();
            $contact->tags = empty(trim($contact->tags)) ? [] : explode(",", $contact->tags);
            $contact->is_master_contact = $contactObject->is_master_contact;
            $contact->master_contact = $contactObject->masterContact;
            $contact->master_contact_id = $contactObject->master_contact_id;
            $contact->tag_labels = empty(trim($contact->tag_labels)) ? [] : explode("||", $contact->tag_labels);
            $contact->leads_ids = array_unique(is_null($contact->leads_ids) ? [] : array_map(fn($str) => intval($str), explode(",", $contact->leads_ids)));
            $contact->deals_ids = array_unique(is_null($contact->deals_ids) ? [] : array_map(fn($str) => intval($str), explode(",", $contact->deals_ids)));
            $contact->metadata = !empty($contact->metadata) ? json_decode($contact->metadata) : null;
            $contact->operation_history = is_null($contactObject->operationHistory) ? [] : $contactObject->operationHistory->map(function ($item) {
                $o = (object) null;
                $o->content = $item->content;
                $o->created_at = $item->created_at->format('Y-m-d H:i:s');
                $o->created_by = !is_null($item->author) ? $item->author->name : '-';
                return $o;
            })->toArray();
            return $contact;
        }
    }

    public function allDuplicates()
    {
        $term = request()->get('q');
        $extraSearchClause = "";
        if (trim($term)) {
            $searchTerm = "%$term%";
            $extraSearchClause = "
                AND (c.name LIKE '$searchTerm' OR c.email_1 LIKE '$searchTerm' OR c.email_2 LIKE '$searchTerm' OR c.phone_1 LIKE '$searchTerm' OR c.phone_2 LIKE '$searchTerm' OR c.mobile_1 LIKE '$searchTerm' OR c.mobile_2 LIKE '$searchTerm')
            ";
        }

        $sql = "
            SELECT
                c.id,
                c.name,
                c.phone_1,
                c.phone_2,
                c.mobile_1,
                c.mobile_2,
                c.email_1,
                c.email_2,
                GROUP_CONCAT(
                    IF(c1.id IS NULL, '', CONCAT(
                        'Match criteria: <strong>Phone 1</strong><br /> ', 'ID: ', COALESCE(c1.id, '-'), ' / ',
                        'Name: ', COALESCE(c1.name, '-'), ' / ',
                        'Phone 1: ', COALESCE(c1.phone_1, '-'), ' / ',
                        'Phone 2: ', COALESCE(c1.phone_2, '-'), ' / ',
                        'Mobile 1: ', COALESCE(c1.mobile_1, '-'), ' / ',
                        'Mobile 2: ', COALESCE(c1.mobile_2, '-'), ' / ',
                        'Email 1: ', COALESCE(c1.email_1, '-'), ' / ',
                        'Email 2: ', COALESCE(c1.email_2, '-'),
                        '<br />', '<br />'
                    )),
                    IF(c2.id IS NULL, '', CONCAT(
                        'Match criteria: <strong>Phone 2</strong><br /> ', 'ID: ', COALESCE(c2.id, '-'), ' / ',
                        'Name: ', COALESCE(c2.name, '-'), ' / ',
                        'Phone 1: ', COALESCE(c2.phone_1, '-'), ' / ',
                        'Phone 2: ', COALESCE(c2.phone_2, '-'), ' / ',
                        'Mobile 1: ', COALESCE(c2.mobile_1, '-'), ' / ',
                        'Mobile 2: ', COALESCE(c2.mobile_2, '-'), ' / ',
                        'Email 1: ', COALESCE(c2.email_1, '-'), ' / ',
                        'Email 2: ', COALESCE(c2.email_2, '-'),
                        '<br />', '<br />'
                    )),
                    IF(c3.id IS NULL, '', CONCAT(
                        'Match criteria: <strong>Mobile 1</strong><br /> ', 'ID: ', COALESCE(c3.id, '-'), ' / ',
                        'Name: ', COALESCE(c3.name, '-'), ' / ',
                        'Phone 1: ', COALESCE(c3.phone_1, '-'), ' / ',
                        'Phone 2: ', COALESCE(c3.phone_2, '-'), ' / ',
                        'Mobile 1: ', COALESCE(c3.mobile_1, '-'), ' / ',
                        'Mobile 2: ', COALESCE(c3.mobile_2, '-'), ' / ',
                        'Email 1: ', COALESCE(c3.email_1, '-'), ' / ',
                        'Email 2: ', COALESCE(c3.email_2, '-'),
                        '<br />', '<br />'
                    )),
                    IF(c4.id IS NULL, '', CONCAT(
                        'Match criteria: <strong>Mobile 2</strong><br /> ', 'ID: ', COALESCE(c4.id, '-'), ' / ',
                        'Name: ', COALESCE(c4.name, '-'), ' / ',
                        'Phone 1: ', COALESCE(c4.phone_1, '-'), ' / ',
                        'Phone 2: ', COALESCE(c4.phone_2, '-'), ' / ',
                        'Mobile 1: ', COALESCE(c4.mobile_1, '-'), ' / ',
                        'Mobile 2: ', COALESCE(c4.mobile_2, '-'), ' / ',
                        'Email 1: ', COALESCE(c4.email_1, '-'), ' / ',
                        'Email 2: ', COALESCE(c4.email_2, '-'),
                        '<br />', '<br />'
                    )),
                    IF(c5.id IS NULL, '', CONCAT(
                        'Match criteria: <strong>Email 1</strong><br /> ', 'ID: ', COALESCE(c5.id, '-'), ' / ',
                        'Name: ', COALESCE(c5.name, '-'), ' / ',
                        'Phone 1: ', COALESCE(c5.phone_1, '-'), ' / ',
                        'Phone 2: ', COALESCE(c5.phone_2, '-'), ' / ',
                        'Mobile 1: ', COALESCE(c5.mobile_1, '-'), ' / ',
                        'Mobile 2: ', COALESCE(c5.mobile_2, '-'), ' / ',
                        'Email 1: ', COALESCE(c5.email_1, '-'), ' / ',
                        'Email 2: ', COALESCE(c5.email_2, '-'),
                        '<br />', '<br />'
                    )),
                    IF(c6.id IS NULL, '', CONCAT(
                        'Match criteria: <strong>Email 2</strong><br /> ', 'ID: ', COALESCE(c6.id, '-'), ' / ',
                        'Name: ', COALESCE(c6.name, '-'), ' / ',
                        'Phone 1: ', COALESCE(c6.phone_1, '-'), ' / ',
                        'Phone 2: ', COALESCE(c6.phone_2, '-'), ' / ',
                        'Mobile 1: ', COALESCE(c6.mobile_1, '-'), ' / ',
                        'Mobile 2: ', COALESCE(c6.mobile_2, '-'), ' / ',
                        'Email 1: ', COALESCE(c6.email_1, '-'), ' / ',
                        'Email 2: ', COALESCE(c6.email_2, '-'),
                        '<br />', '<br />'
                    ))
                    SEPARATOR '====================<br /><br />'
                ) as duplicates
            FROM
                contacts c
            LEFT JOIN
                (SELECT * FROM contacts WHERE phone_1 IS NOT NULL AND phone_1 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c1 ON c.phone_1 = c1.phone_1 AND c.id != c1.id
            LEFT JOIN
                (SELECT * FROM contacts WHERE phone_2 IS NOT NULL AND phone_2 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c2 ON c.phone_2 = c2.phone_2 AND c.id != c2.id
            LEFT JOIN
                (SELECT * FROM contacts WHERE mobile_1 IS NOT NULL AND mobile_1 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c3 ON c.mobile_1 = c3.mobile_1 AND c.id != c3.id
            LEFT JOIN
                (SELECT * FROM contacts WHERE mobile_2 IS NOT NULL AND mobile_2 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c4 ON c.mobile_2 = c4.mobile_2 AND c.id != c4.id
            LEFT JOIN
                (SELECT * FROM contacts WHERE email_1 IS NOT NULL AND email_1 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c5 ON c.email_1 = c5.email_1 AND c.id != c5.id
            LEFT JOIN
                (SELECT * FROM contacts WHERE email_2 IS NOT NULL AND email_2 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c6 ON c.email_2 = c6.email_2 AND c.id != c6.id
            WHERE (c1.id IS NOT NULL OR c2.id IS NOT NULL OR c3.id IS NOT NULL OR c4.id IS NOT NULL OR c5.id IS NOT NULL OR c6.id IS NOT NULL)
                $extraSearchClause
                AND c.is_master_contact IS NULL AND c.master_contact_id IS NULL
            GROUP BY c.id
        ";
        $results = DB::select(DB::raw($sql));
        return $results;
    }

    public function getDuplicatesFor($contactId)
    {
        $masterContactSentence = "AND is_master_contact IS NULL AND master_contact_id IS NULL";
        $masterContactSentence = "";
        $sql = "
        SELECT
        c.id,
        c1.id as c1_id,
        c2.id as c2_id,
        c3.id as c3_id,
        c4.id as c4_id,
        c5.id as c5_id,
        c6.id as c6_id
            FROM
            contacts c
        LEFT JOIN
            (SELECT * FROM contacts WHERE phone_1 IS NOT NULL AND phone_1 != '' $masterContactSentence) c1 ON c.phone_1 = c1.phone_1 AND c.id != c1.id
        LEFT JOIN
            (SELECT * FROM contacts WHERE phone_2 IS NOT NULL AND phone_2 != '' $masterContactSentence) c2 ON c.phone_2 = c2.phone_2 AND c.id != c2.id
        LEFT JOIN
            (SELECT * FROM contacts WHERE mobile_1 IS NOT NULL AND mobile_1 != '' $masterContactSentence) c3 ON c.mobile_1 = c3.mobile_1 AND c.id != c3.id
        LEFT JOIN
            (SELECT * FROM contacts WHERE mobile_2 IS NOT NULL AND mobile_2 != '' $masterContactSentence) c4 ON c.mobile_2 = c4.mobile_2 AND c.id != c4.id
        LEFT JOIN
            (SELECT * FROM contacts WHERE email_1 IS NOT NULL AND email_1 != '' $masterContactSentence) c5 ON c.email_1 = c5.email_1 AND c.id != c5.id
        LEFT JOIN
            (SELECT * FROM contacts WHERE email_2 IS NOT NULL AND email_2 != '' $masterContactSentence) c6 ON c.email_2 = c6.email_2 AND c.id != c6.id
        WHERE
            c.id = :contactId
            AND (c1.id IS NOT NULL OR c2.id IS NOT NULL OR c3.id IS NOT NULL OR c4.id IS NOT NULL OR c5.id IS NOT NULL OR c6.id IS NOT NULL)
            AND c.is_master_contact IS NULL AND c.master_contact_id IS NULL
        ";
        $results = DB::select(DB::raw($sql), ['contactId' => $contactId]);

        $allIds = [];
        foreach ($results as $res) {
            foreach (['id', 'c1_id', 'c2_id', 'c3_id', 'c4_id', 'c5_id', 'c6_id'] as $prop) {
                if (!empty($res->$prop) && !in_array($res->$prop, $allIds)) {
                    $allIds[] = $res->$prop;
                }
            }
        }

        return Contact::with(['properties'])->whereIn('id', $allIds)->get();
    }

    public function markContactAsMaster($contactId)
    {
        $sql = "
        SELECT
        c.id,
        c1.id as c1_id,
        c2.id as c2_id,
        c3.id as c3_id,
        c4.id as c4_id,
        c5.id as c5_id,
        c6.id as c6_id
            FROM
            contacts c
        LEFT JOIN
            (SELECT * FROM contacts WHERE phone_1 IS NOT NULL AND phone_1 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c1 ON c.phone_1 = c1.phone_1 AND c.id != c1.id
        LEFT JOIN
            (SELECT * FROM contacts WHERE phone_2 IS NOT NULL AND phone_2 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c2 ON c.phone_2 = c2.phone_2 AND c.id != c2.id
        LEFT JOIN
            (SELECT * FROM contacts WHERE mobile_1 IS NOT NULL AND mobile_1 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c3 ON c.mobile_1 = c3.mobile_1 AND c.id != c3.id
        LEFT JOIN
            (SELECT * FROM contacts WHERE mobile_2 IS NOT NULL AND mobile_2 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c4 ON c.mobile_2 = c4.mobile_2 AND c.id != c4.id
        LEFT JOIN
            (SELECT * FROM contacts WHERE email_1 IS NOT NULL AND email_1 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c5 ON c.email_1 = c5.email_1 AND c.id != c5.id
        LEFT JOIN
            (SELECT * FROM contacts WHERE email_2 IS NOT NULL AND email_2 != '' AND is_master_contact IS NULL AND master_contact_id IS NULL) c6 ON c.email_2 = c6.email_2 AND c.id != c6.id
        WHERE
            c.id = :contactId
            AND (c1.id IS NOT NULL OR c2.id IS NOT NULL OR c3.id IS NOT NULL OR c4.id IS NOT NULL OR c5.id IS NOT NULL OR c6.id IS NOT NULL)
            AND c.is_master_contact IS NULL AND c.master_contact_id IS NULL
        ";
        $results = DB::select(DB::raw($sql), ['contactId' => $contactId]);

        $duplicateIds = [];
        foreach ($results as $res) {
            foreach (['c1_id', 'c2_id', 'c3_id', 'c4_id', 'c5_id', 'c6_id'] as $prop) {
                if (!empty($res->$prop) && !in_array($res->$prop, $duplicateIds)) {
                    $duplicateIds[] = $res->$prop;
                }
            }
        }
        $user = auth()->user();
        $contactsToUpdate = Contact::whereIn('id', $duplicateIds)->get();
        if ($contactsToUpdate->count()) {
            $contactsToUpdate->each(function ($item) use ($user, $contactId) {
                $item->master_contact_id = $contactId;
                $item->save();
                $this->operationHistoryService->addOperationHistory($item, "Master Contact set to Contact ID: " . $contactId, $user);
            });
        }

        $masterContact = Contact::where('id', $contactId)->first();
        if (!is_null($masterContact)) {
            $masterContact->is_master_contact = true;
            $masterContact->save();
            $this->operationHistoryService->addOperationHistory($masterContact, "Contact was flagged as Master Contact ", $user);
        }

        return ['msg' => 'Ok'];
    }

    public function delete($id)
    {
        $user = auth()->user();
        if (!$user->hasAnyRole([RolesDef::OFFICE_MANAGER])) {
            return response(null, 401);
        }
        $contactToDelete = Contact::where('id', $id)->first();
        $leadIds = [];
        $listingRefNos = [];
        if ($contactToDelete->leads->count()) {
            foreach ($contactToDelete->leads as $lead) {
                $leadIds[] = $lead->id;
            }
        }
        if ($contactToDelete->properties->count()) {
            foreach ($contactToDelete->properties as $property) {
                $listingRefNos[] = $property->ref_no;
            }
        }
        if (request()->has('forceDelete')) {
            $contactToDelete->forceDelete();
            return response(['isSuccess' => true, 'msg' => 'The contact has been successfully deleted'], 200);
        } else {
            if (count($leadIds) > 0 || count($listingRefNos) > 0) {
                return response(['isSuccess' => false, 'msg' => "The contact could not be deleted because it has some items associated with it. " . (count($leadIds) > 0 ? 'Lead ids: ' . implode(",", $leadIds) : '') . (count($listingRefNos) > 0 ? ' Listings: ' . implode(",", $listingRefNos) : '')], 200);
            } else {
                $contactToDelete->forceDelete();
                return response(['isSuccess' => true, 'msg' => 'The contact has been successfully deleted'], 200);
            }
        }
    }

    public function contactState($contactId)
    {
        $contact = Contact::with(['leads', 'properties'])->where('id', $contactId)->firstOrFail();
        return ['contact' => $contact];
    }

    public function recordTask(RecordContactsListTaskRequest $request, $id)
    {
        $validData = $request->validated();

        try {
            DB::beginTransaction();
            $contact = Contact::where('id', $id)->firstOrFail();
            $task = Task::create([
                "object_id" => $id,
                "object_type" => 'contact',
                "type" => $validData['type'],
                "call_response" => $validData['call_response'],
                "call_notes" => $validData['call_notes'],
                'created_by' => auth()->user()->id,
                'property_id' => $validData['property_id'] ?? null,
                'location_id' => $validData['location_id'] ?? null,
                'subject' => $validData['subject'] ?? null,
                'status' => $validData['status'] ?? 'completed',
                'notes' => $validData['notes'] ?? null,
                'start_date' => $validData['start_date'] ?? null,
                'start_time' => $validData['start_time'] ?? null,
                'end_date' => $validData['end_date'] ?? null,
                'end_time' => $validData['end_time'] ?? null,
                'reminder_set' => $validData['reminder_set'] ?? null,
            ]);

            // if ($task->call_response == 'answered' || $task->call_response == 'not_answered') {
            // $lead = Lead::where('id', $leadId)->first();

            // $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('name', 'CONTINUING_DISCUSSION');
            // if ($task->call_response == 'not_answered') {
            //     $newStatus = $this->leadStatusService->getLeadStatuses()->firstWhere('name', 'NOT_ANSWERED');
            // }

            // $previousStatus = $lead->leadStatus;
            // $oldStatusLabel = 'NO STATUS';
            // if (!is_null($previousStatus)) {
            //     $oldStatusLabel = $previousStatus->name;
            // }

            // if (!is_null($newStatus)) {
            //     $newStatusLabel = $newStatus->name;
            // }

            // $this->leadsService->trackLeadStatusChange($lead, $newStatus);
            // $lead->lead_status_id = $newStatus->id;
            // $lead->save();
            $remarksBody = "Call log: " . $task->call_response;
            $this->operationHistoryService->addOperationHistory($contact, $remarksBody, auth()->user());
            // }
            DB::commit();
            return response($task, 201);
        } catch (\Exception $Ex) {
            DB::rollBack();
            throw $Ex;
        }
    }

    public function contactTasks($id)
    {
        $tasks = Task::with(['property:id,ref_no', 'contact:id,name', 'author:id,name'])->orderBy('updated_at', 'DESC')->where(['object_type' => 'contact', 'object_id' => $id])->get();
        return $tasks;
    }

    /**
     * Get Mailjet contact lists
     */
    public function getMailjetContactLists()
    {
        $result = $this->mailjetService->getContactLists();

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'lists' => $result['data']
            ]);
        }

        return response()->json([
            'success' => false,
            'error' => $result['error']
        ], 500);
    }

    /**
     * Create a new Mailjet contact list
     */
    public function createMailjetContactList()
    {
        $validData = request()->validate([
            'name' => 'required|string|max:255'
        ]);

        $result = $this->mailjetService->createContactList($validData['name']);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'list' => $result['data']
            ]);
        }

        return response()->json([
            'success' => false,
            'error' => $result['error']
        ], 500);
    }

    /**
     * Sync contacts to Mailjet
     */
    public function syncContactsToMailjet()
    {
        $validData = request()->validate([
            'listId' => 'required|integer',
            'contactIds' => 'array',
            'contactIds.*' => 'integer'
        ]);

        // Log the received parameters for debugging
        Log::info('Mailjet sync request received', [
            'user' => auth()->user()->email,
            'listId' => $validData['listId'],
            'hasContactIds' => !empty($validData['contactIds']),
            'contactIdsCount' => count($validData['contactIds'] ?? []),
            'requestParams' => request()->except(['listId', 'contactIds']),
            'allParams' => request()->all()
        ]);

        // If no specific contact IDs provided, get all contacts based on current filters
        if (empty($validData['contactIds'])) {
            $contacts = $this->contactsListService->getTableItems(request(), true);
            Log::info('Contacts retrieved using filters', [
                'totalContactsFound' => $contacts->count(),
                'sampleContactIds' => $contacts->take(5)->pluck('id')->toArray()
            ]);
        } else {
            $contacts = Contact::whereIn('id', $validData['contactIds'])->get();
            Log::info('Contacts retrieved using specific IDs', [
                'totalContactsFound' => $contacts->count()
            ]);
        }

        // Prepare contacts data for Mailjet
        $contactsData = [];
        foreach ($contacts as $contact) {
            if (!empty($contact->email_1) && filter_var($contact->email_1, FILTER_VALIDATE_EMAIL)) {
                $contactsData[] = [ 
                    'email' => $contact->email_1,
                    'name' => $contact->name ?: ''
                ];
            }
        }

        Log::info('Contacts prepared for Mailjet sync', [
            'totalContactsWithEmail' => count($contactsData),
            'totalContactsWithoutEmail' => $contacts->count() - count($contactsData)
        ]);

        if (empty($contactsData)) {
            return response()->json([
                'success' => false,
                'error' => 'No contacts with valid email addresses found'
            ], 400);
        }

        $result = $this->mailjetService->syncContactsToList($validData['listId'], $contactsData);

        Log::info('Mailjet sync performed by ['.auth()->user()->email.']', [
            'listId' => $validData['listId'],
            'totalContacts' => count($contactsData),
            'synced' => $result['synced'],
            'failed' => $result['failed']
        ]);

        return response()->json($result);
    }
}
