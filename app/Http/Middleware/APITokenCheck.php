<?php

namespace App\Http\Middleware;

use App\Models\User;
use Illuminate\Support\Facades\Auth;

class APITokenCheck
{
    protected $except = [
        'api/sms',
        'api/twilio/call-tracking',
        'api/sms-auth',
        'api/agent-rating',
        'api/autocomplete',
        'api/login',
        'api/logs',
        'api/logs/batch',
        'api/geography',
        'api/public-leads',
        'api/country',
        'api/user',
        'api/listing',
        'api/profile/recommended',
        'api/hooks/pf-property-new',
        'api/hooks/pf-property-status',
        'api/hooks/pf-property-state',
        'api/start-box',
        'api/start-box-lite',
        'api/room-availability',
        'api/countries',
        'api/agent-contact',
        'api/enquiry',
        'api/qatar-2022',
        'api/feedback',
        'api/newsletter',
        'api/options/currency-rates',
        'api/nationality',
        'api/listing-type',
        'api/options/kitchen',
        'api/options/bedroom',
        'api/options/bathroom',
        'api/options/bill',
        'api/options/payment-method',
        'api/options/amenities',
        'api/options/marketing-platforms',
        'api/options/leads-status',
        'api/options/utm-sources',
        'api/options/utm-campaigns',
        'api/i18n-messages',
        'api/route-data',
        'api/reset-password',
        'api/reset-password-check-code',
        'api/change-password',
        'api/track',
        'api/contacts-list/check-contact-exists-phone',
        'api/contacts-list/check-contact-exists',
        'api/short-stay-check',
        'api/short-stay-room-check',
        'api/whatsapp/webhook',
        'api/webhooks/whatsapp',
    ];

    protected $exceptChunks = [
        '/requests',
        '/towers',
        'api/listing',
        'api/agent-rating',
        'api/rooms',
    ];
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, $next)
    {
        // check the token belongs to a valid user
        $requestPath = $request->path();

        if(in_array($requestPath, $this->except)) {
            return $next($request);
        }

        foreach ($this->exceptChunks as $exceptChunk) {
            if(str_contains($requestPath, $exceptChunk)) {
                return $next($request);
            }
        }

        if($request->hasHeader('Authorization')) {
            $bearerToken = $request->header('Authorization');
            if(strpos($bearerToken, 'bearer ') > -1 || strpos($bearerToken, 'Bearer ') > -1) {
                $token = str_replace(["bearer ", "Bearer "], "", $bearerToken);

                // check if there is a valid user for this token
                $user = User::where('api_token', '=', $token)->first();
                if(!is_null($user)) {
                    Auth::login($user);
                    return $next($request);
                }
            }
        }

        return response(null, 401);
    }

}
