<?php

namespace App\Services;

use App\Models\RouteListingItem;
use App\Models\Admin\ExportPlatform;
use App\Models\Asset;
use App\Models\CacheKeys;
use App\Models\Crm\ListingPublishingStatus;
use Spatie\SchemaOrg\ContactPoint;
use Spatie\SchemaOrg\GeoCoordinates;

use App\Models\AttributeDefinition;
use App\Models\Geography;
use App\Models\Language;
use App\Models\ParamsDef;
use App\Models\Property;
use App\Models\PropertyType;
use App\Models\PropertySnapshot;
use App\Models\PropertySnapshotsStats;
use App\Models\QueryParamsDef as ModelsQueryParamsDef;
use App\Models\Timings;
use App\Models\Crm\RolesDef;
use Cache;
use DB;
use Illuminate\Support\Facades\Log;
use QueryParamsDef;
use Spatie\SchemaOrg\SingleFamilyResidence;

class PropertiesService
{
    public $propertyAttributes = [
        'description' => 'description',
        'Property-Highlights' => 'highlights',
        'Property-Details' => 'details',
        'property-features-furnishings' => 'furnishings',
        'property-features-construction-year' => 'construction-year',
        'property-features-build-up-area' => 'build-up-area',
        'property-features-bedrooms' => 'bedrooms',
        'property-features-bathrooms' => 'bathrooms',
        'property-features-balcony' => 'balcony',
        'property-features-kitchen' => 'kitchen',
        'property-features-pantry' => 'pantry',
        'property-features-parking-info' => 'parking-info',
        'property-features-furnishings-office' => 'furnishings-office',
        'property-features-service_charge' => 'service_charge',
        'property-features-transfer_fee' => 'transfer_fee',
    ];

    private $propertyTypesService;
    private $lastRates;
    private $amenitiesService;
    private $geographyService;

    private $REF_PREFIXES = [
        1 => 'A',
        2 => 'V',
        3 => 'O',
        7 => 'CV',
        8 => 'RS',
        9 => 'SR',
        11 => 'L',
        14 => 'P',
        15 => 'T',
        16 => 'C',
        17 => 'H',
        18 => 'VC',
        19 => 'R',
        20 => 'WB',
        21 => 'LC',
        22 => 'W',
        23 => 'F',
        24 => 'IL',
        25 => 'DU',
        26 => 'SRS',
        27 => 'RES',
        28 => 'SCH',
        29 => 'HC',
        30 => 'LP',
        31 => 'RET',
        32 => 'HO',
        33 => 'BO',
        34 => 'PA',
    ];

    public $areas = [
        'min' => [10, 20, 45, 90, 120, 150],
        'max' => [150, 200, 350, 450, 500, 1000]
    ];

    public $minRentPrices = [4000, 5000, 6000, 7000, 8000, 9000, 10000, 11000, 12000, 13000, 14000, 15000, 16000, 17000, 18000, 19000, 20000];
    public $maxRentPrices = [6000, 7000, 8000, 9000, 10000, 12000, 14000, 16000, 18000, 20000, 22000, 24000, 26000, 28000, 30000, 40000, 50000, 60000];
    public $minSalePrices = [800000, 900000, 1000000, 1500000, 2000000, 2500000, 3000000, 3500000, 4000000, 4500000, 5000000];
    public $maxSalePrices = [950000, 1000000, 1750000, 2000000, 2750000, 3000000, 3750000, 4000000, 4750000, 5000000, 6000000, 7000000, 8000000, 9000000, 10000000, 20000000, 50000000, 100000000, 150000000];

    private $attributeDefinitionIds = [
        AttributeDefinition::PROPERTY_FEATURES_BALCONY => 0,
        AttributeDefinition::PROPERTY_FEATURES_KITCHEN => 0,
        AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS => 0,
        AttributeDefinition::PROPERTY_FEATURES_BUILD_UP_AREA => 0,
        AttributeDefinition::PROPERTY_FEATURES_PARKING_INFO => 0,
        AttributeDefinition::PROPERTY_FEATURES_CONSTRUCTION_YEAR => 0,
        AttributeDefinition::PROPERTY_FEATURES_BATHROOMS => 0,
        AttributeDefinition::PROPERTY_FEATURES_BEDROOMS => 0,
        AttributeDefinition::PROPERTY_FEATURES_SERVICE_CHARGE => 0,
        AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS_OFFICE => 0,
        AttributeDefinition::DESCRIPTION => 0
    ];

    const ONE_MONTH = 60 * 60 * 24 * 30;

    public $defaultAmenities = [
        // Porto Arabia
        '23' => [
            'Dedicated Parking Slot',
            'Low Floor',
            'Mid Floor',
            'High Floor',
            'Children’s Play Area',
            'Beach Access',
            'Shared Swimming Pool',
            'Marina View',
            'Sea View',
            'Pet Friendly',
            'Open Kitchen',
            'Storage Area',
            'Gymnasium',
            'Expressway',
            'Visitor Parking',
            'Mall',
            'Grocery Store',
            'Basement',
            'Basement Parking',
            'Hospital',
            'Security Access',
            'Central Air Conditioning',
            'Near Mosque',
            'Public Transportation',
            'Public Park'
        ],
        // Viva Bahriya
        '24' => [
            'Dedicated Parking Slot',
            'Low Floor',
            'Mid Floor',
            'High Floor',
            'Children’s Play Area',
            'Beach Access',
            'Shared Swimming Pool',
            'Marina View',
            'Sea View',
            'Pet Friendly',
            'Open Kitchen',
            'Closed Kitchen',
            'Storage Area',
            'Gymnasium',
            'Expressway',
            'Visitor Parking',
            'Mall',
            'Grocery Store',
            'Basement',
            'Basement Parking',
            'Hospital',
            'Security Access',
            'Central Air Conditioning',
            'Public Transportation',
            'Public Park'
        ]
    ];

    const USER_ID_OFFICE = 41;
    const USER_ID_SERBAN = 3;
    const USER_ID_CSR = 54;
    const USER_ID_AMIRA = 114;
    const NON_RATEABLE_USERS = [self::USER_ID_OFFICE, self::USER_ID_SERBAN, self::USER_ID_CSR, self::USER_ID_AMIRA];

    public function __construct(
        SyncRateService $syncRateService,
        PropertyTypesService $propertyTypesService,
        AmenitiesService $amenitiesService,
        GeographyService $geographyService
    ) {
        $this->syncRateService = $syncRateService;
        $this->userCurrency = $syncRateService->getSelectedCurrency();
        $this->lastRates = $syncRateService->getRates();
        $this->propertyTypesService = $propertyTypesService;
        $this->amenitiesService = $amenitiesService;
        $this->geographyService = $geographyService;
    }

    public function search($params = [], $limit = 0, $order = [], $perPage = 12)
    {
        $attributeDefinitionIds = $this->getAttributeDefinitionIds();
        //        dd($attributeDefinitionIds);

        $isRef = false;
        if (!empty($params['term'])) {

            //if the term is a reference no
            $refRes = Property::with('type')
                ->where('properties.ref_no', '=', $params['term'])
                ->limit(1);

            if (!empty($refRes->first())) {
                // $isRef = true;
                return [
                    "isRef" => true,
                    "res" => $refRes->get()->first()
                ];
            }
        }

        $q = Property::with('type')->select(
            'property_snapshots.*',
            'property_types.id as property_type_id',
            'property_types.filter_value as property_type_filter_value'
        )
            ->addSelect("be_attr.value as bedrooms")
            ->addSelect("ba_attr.value as bathrooms")
            ->addSelect("bua_attr.value as built_up_area")
            ->addSelect("desc_attr.value_large as description")
            ->join("property_types", 'property_snapshots.property_type_id', '=', 'property_types.id')
            ->leftJoin("attributes AS be_attr", "be_attr.asset_id", "=", "property_snapshots.asset_id")
            ->join('attribute_definitions AS be_attr_def', function ($join) use ($attributeDefinitionIds) {
                $join->on('be_attr_def.id', '=', 'be_attr.attribute_definition_id')
                    ->where("be_attr_def.id", "=", $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BEDROOMS]);
            })
            ->leftJoin("attributes AS ba_attr", "ba_attr.asset_id", "=", "property_snapshots.asset_id")
            ->join('attribute_definitions AS ba_attr_def', function ($join) use ($attributeDefinitionIds) {
                $join->on('ba_attr_def.id', '=', 'ba_attr.attribute_definition_id')
                    ->where("ba_attr_def.id", "=", $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BATHROOMS]);
            })
            ->leftJoin("attributes AS bua_attr", "bua_attr.asset_id", "=", "property_snapshots.asset_id")
            ->join('attribute_definitions AS bua_attr_def', function ($join) use ($attributeDefinitionIds) {
                $join->on('bua_attr_def.id', '=', 'bua_attr.attribute_definition_id')
                    ->where("bua_attr_def.id", "=", $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BUILD_UP_AREA]);
            })
            ->leftJoin("attributes AS desc_attr", "desc_attr.asset_id", "=", "property_snapshots.asset_id")
            ->join('attribute_definitions AS desc_attr_def', function ($join) use ($attributeDefinitionIds) {
                $join->on('desc_attr_def.id', '=', 'desc_attr.attribute_definition_id')
                    ->where("desc_attr_def.id", "=", $attributeDefinitionIds[AttributeDefinition::DESCRIPTION]);
            })
            ->leftJoin("seo AS seo_prop", function ($join) {
                $join->on('seo_prop.related_id', '=', 'property_snapshot.related_id')
                    ->where("seo_prop.related_type", "=", "property");
            })
            ->leftJoin('geography as location_name', 'location_name.id', '=', 'property_snapshots.location_id');
        // ->leftJoin('geography as regions', 'regions.id', '=', 'properties.region_id')
        // ->leftJoin('geography as areas', 'areas.id', '=', 'properties.area_id');

        if ($isRef) {
            $q->where('properties.ref_no', 'LIKE', $params['term']);
            $q->limit(1);

            return [
                "isRef" => true,
                "res" => $q->get()->first()
            ];
        }

        $this->resolveAndAddWheres($q, $params);

        if (is_array($order) && count($order) == 2) {
            $q->orderBy('properties.is_sold_leased', 'ASC');
            $q->orderBy($order[0], $order[1]);
        }

        if ($limit) {
            $q->limit($limit);
            return $q->get();
        } else {
            return $q->paginate($perPage);
        }
    }

    public function snapshotSearch($params = [], $limit = 0, $order = [], $perPage = 12)
    {
        $attributeDefinitionIds = $this->getAttributeDefinitionIds();

        $isRef = false;
        if (!empty($params['term'])) {

            //if the term is a reference no
            $refRes = PropertySnapshot::with(['property', 'type', 'location'])
                ->where('property_snapshots.ref_no', '=', $params['term'])
                ->limit(1);

            if (!empty($refRes->first())) {
                // $isRef = true;
                return [
                    "isRef" => true,
                    "res" => $refRes->get()->first()
                ];
            }
        }

        $q = PropertySnapshot::with(['type', 'property', 'location'])->select(
            'property_snapshots.*',
            'property_types.id as property_type_id',
            'property_types.filter_value as property_type_filter_value'
        )
            ->addSelect("be_attr.value as bedrooms")
            ->addSelect("ba_attr.value as bathrooms")
            ->addSelect("bua_attr.value as built_up_area")
            ->addSelect("desc_attr.value_large as description")
            ->join("property_types", 'property_snapshots.property_type_id', '=', 'property_types.id')
            ->leftJoin("attributes AS be_attr", "be_attr.asset_id", "=", "property_snapshots.asset_id")
            ->join('attribute_definitions AS be_attr_def', function ($join) use ($attributeDefinitionIds) {
                $join->on('be_attr_def.id', '=', 'be_attr.attribute_definition_id')
                    ->where("be_attr_def.id", "=", $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BEDROOMS]);
            })
            ->leftJoin("attributes AS ba_attr", "ba_attr.asset_id", "=", "property_snapshots.asset_id")
            ->join('attribute_definitions AS ba_attr_def', function ($join) use ($attributeDefinitionIds) {
                $join->on('ba_attr_def.id', '=', 'ba_attr.attribute_definition_id')
                    ->where("ba_attr_def.id", "=", $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BATHROOMS]);
            })
            ->leftJoin("attributes AS bua_attr", "bua_attr.asset_id", "=", "property_snapshots.asset_id")
            ->join('attribute_definitions AS bua_attr_def', function ($join) use ($attributeDefinitionIds) {
                $join->on('bua_attr_def.id', '=', 'bua_attr.attribute_definition_id')
                    ->where("bua_attr_def.id", "=", $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BUILD_UP_AREA]);
            })
            ->leftJoin("attributes AS desc_attr", "desc_attr.asset_id", "=", "property_snapshots.asset_id")
            ->join('attribute_definitions AS desc_attr_def', function ($join) use ($attributeDefinitionIds) {
                $join->on('desc_attr_def.id', '=', 'desc_attr.attribute_definition_id')
                    ->where("desc_attr_def.id", "=", $attributeDefinitionIds[AttributeDefinition::DESCRIPTION]);
            })
            //            ->leftJoin("seo AS seo_prop", function ($join) {
            //                $join->on('seo_prop.related_id', '=', 'property_snapshots.related_id')
            //                    ->where("seo_prop.related_type", "=", "property");
            //            })
            ->leftJoin('geography as location_name', 'location_name.id', '=', 'property_snapshots.location_id');
        // ->leftJoin('geography as regions', 'regions.id', '=', 'properties.region_id')
        // ->leftJoin('geography as areas', 'areas.id', '=', 'properties.area_id');

        if ($isRef) {
            $q->where('property_snapshots.ref_no', 'LIKE', $params['term']);
            $q->limit(1);

            return [
                "isRef" => true,
                "res" => $q->get()->first()
            ];
        }

        $this->resolveAndAddWheres($q, $params);

        if (is_array($order) && count($order) == 2) {
            $q->orderBy('property_snapshots.is_sold_leased', 'ASC');
            $q->orderBy($order[0], $order[1]);
        }

        if ($limit) {
            $q->limit($limit);
            return $q->get();
        } else {
            return $q->paginate($perPage);
        }
    }

    public function getMetadata($items)
    {
        $metadata = [];
        if (is_array($items) && count($items) > 0) {
            $propertyIds = array_map(function ($item) {
                return is_array($item) ? $item['id'] : $item->id;
            }, $items);

            $res = DB::table('attachment_assignments as att_ass')
                ->select(DB::raw('att_ass.object_id as att_as_object_id, att_ass.attachment_id as att_as_attachment_id'))
                ->addSelect(DB::raw('atts.name as atts_name, atts.asset_url_tag as atts_asset_url_tag, atts.is_primary as atts_is_primary'))
                ->addSelect(DB::raw('seo_img.img_path as seo_img_path, seo_img.img_alt as seo_img_alt, seo_img.img_title as seo_img_title'))
                ->join(DB::raw("
                    (SELECT
                      id, name, asset_url_tag, is_primary
                    FROM
                      attachments
                    WHERE
                      deleted_at IS NULL
                    ORDER BY id ASC, is_primary DESC)
                    as atts"), 'atts.id', '=', 'att_ass.attachment_id')
                ->leftJoin('seo as seo_img', function ($join) {
                    $join->on('seo_img.related_id', '=', 'atts.id')
                        ->where('seo_img.related_type', 'image');
                })
                ->where('att_ass.object_type', 'properties')
                ->whereIn('att_ass.object_id', $propertyIds)
                ->whereNull('att_ass.deleted_at')
                ->groupBy('att_ass.object_id')
                ->get();

            $res->each(function ($item) use (&$metadata) {
                $resExt = pathinfo($item->atts_name)['extension'];
                $resPath = empty($item->seo_img_path) ? $item->atts_name : $item->seo_img_path . '.' . $resExt;
                $item->resPath = $resPath;
                $metadata[$item->att_as_object_id] = $item;
            });
        }
        return $metadata;
    }

    public function getSnapshotAttributes($snapshot)
    {
        $res = PropertySnapshot::with('type')
            ->select(
                'ag.id',
                'ag.name as group_name',
                'ag.label as group_label',
                'ag.header as group_header',
                'ad.label as definition_label',
                'ad.name as definition_name',
                'a.value as attr_value'
            )
            ->from('property_snapshots')
            ->join("attributes as a", 'a.asset_id', '=', 'property_snapshots.asset_id')
            ->join("attribute_definitions as ad", 'a.attribute_definition_id', '=', 'ad.id')
            ->join("attribute_groups as ag", 'ag.id', '=', 'ad.attribute_group_id')
            ->where('property_snapshots.id', '=', $snapshot->id)
            ->whereNotNull('a.value');

        return $res->get();
    }

    public function getWebsiteContent($property)
    {
        return DB::table('attributes as a')
            ->select('a.value_large', 'ad.label')
            ->join('attribute_definitions as ad', 'ad.id', '=', 'a.attribute_definition_id')
            ->join('attribute_groups as ag', 'ag.id', '=', 'ad.attribute_group_id')
            ->where('ag.name', '=', 'Website-content')
            ->where('a.asset_id', '=', $property->asset_id)
            ->get();
    }

    public function getSnapshotWebsiteContent($propertySnapshot)
    {
        return DB::table('snapshot_attributes as a')
            ->select('a.value_large', 'ad.label')
            ->join('attribute_definitions as ad', 'ad.id', '=', 'a.attribute_definition_id')
            ->whereIn('ad.name', ['Property-Highlights', 'Property-Details', 'Description'])
            ->where('a.asset_id', '=', $propertySnapshot->asset_id)
            ->get();
    }

    public static function solveFilterKey($filterKey, $value)
    {
        $retVal = $filterKey;
        if (empty($value)) {
            $retVal = null;
        } else {
            switch ($filterKey) {
                case "id":
                    $retVal = ["col" => "properties.id", "op" => "=", "val" => $value];
                    break;
                case QueryParamsDef::OPERATION_TYPE:
                    if ($value === "all") {
                        $retVal = null;
                    } else {
                        if ($value === "buy") {
                            $value = "sale";
                        }

                        $retVal = ["col" => "ad_type", "op" => "=", "val" => $value];
                    }
                    break;
                case QueryParamsDef::PROPERTY_TYPE:
                    if ($value === "all") {
                        $retVal = null;
                    } else {
                        $retVal = ["col" => "property_types.url_value", "op" => "=", "val" => $value];
                    }
                    break;
                case "city_id":
                case "region_id":
                case "area_id":
                    $retVal = ["col" => $filterKey, "op" => "=", "val" => $value];
                    break;
                case "city":
                    $retVal = ["col" => "city", "op" => "LIKE", "val" => $value . "%"];
                    break;
                case "region":
                    $retVal = ["col" => "region", "op" => "LIKE", "val" => $value . "%"];
                    break;
                case "area":
                    $retVal = ["col" => "area", "op" => "LIKE", "val" => $value . "%"];
                    break;
                case QueryParamsDef::PRICE_FROM:
                    $retVal = null;
                    if (doubleval($value)) {
                        $retVal = ["col" => "price", "op" => ">=", "val" => $value];
                    }
                    break;
                case QueryParamsDef::PRICE_TO:
                    $retVal = null;
                    if (doubleval($value)) {
                        // if (false !== strpos($value, '+')) {
                        if (in_array($value, ['60000', '10000000'])) {
                            $retVal = ["col" => "price", "op" => ">", "val" => 0]; //because price more than something+ does not makes sense
                        } else {
                            $retVal = ["col" => "price", "op" => "<=", "val" => $value];
                        }
                    }
                    break;
                case QueryParamsDef::BEDROOMS:
                    $retVal = null;
                    if (doubleval($value)) {
                        $retVal = ["col" => "be_attr.value", "op" => "=", "val" => $value];
                    }
                    break;
                case QueryParamsDef::BATHROOMS:
                    $retVal = null;
                    if (doubleval($value)) {
                        $retVal = ["col" => "ba_attr.value", "op" => "=", "val" => $value];
                    }
                    break;
                case "is_exclusive":
                    $retVal = ["col" => "properties.is_exclusive", "op" => "=", "val" => 1];
                    break;
                case "new_development":
                    $retVal = ["col" => "properties.new_development", "op" => "=", "val" => 1];
                    break;
                case "ad_type":
                    $retVal = ["col" => "properties.ad_type", "op" => "=", "val" => $value];
                    break;
                case "property_type_id":
                    $retVal = ["col" => "properties.property_type_id", "op" => "=", "val" => $value];
                    break;
                case QueryParamsDef::DATE_POSTED:
                    $retVal = null;
                    if (in_array($value, ["new_today", "new_1_week", "new_1_month", "old_12_months", "old_24_months"])) {
                        $solvedVal = self::solveDateVal($value);
                        $retVal = ["col" => "properties.updated_at", "op" => ">=", "val" => $solvedVal];
                    }
                    break;
                default:
                    $retVal = null;
                    break;
            }
        }


        return $retVal;
    }

    public static function solveDateVal($value)
    {
        $dt = new \DateTime();
        $dt->setTime(0, 0);

        switch ($value) {
            case "new_1_week":
                $dt->sub(new \DateInterval('P1W'));
                break;
            case "new_1_month":
                $dt->sub(new \DateInterval('P1M'));
                break;
            case "old_12_months":
                $dt->sub(new \DateInterval('P12M'));
                break;
            case "old_24_months":
                $dt->sub(new \DateInterval('P24M'));
                break;
        }

        return $dt->format("Y-m-d H:i");
    }

    private function resolveAndAddWheres($q, $params)
    {
        foreach ($params as $k => $v) {
            $this->addWhereParam($q, $k, $v);
        }
    }

    private function addWhereParam($q, $key, $value)
    {
        switch ($key) {
            case ParamsDef::ID:
                $q->where('properties.id', $value);
                break;
            case ModelsQueryParamsDef::PROPERTY_TYPE:
                $q->whereIn('properties.property_type_id', explode(",", $value));
                break;

            case ModelsQueryParamsDef::OPERATION_TYPE:
                $q->where('properties.ad_type', '=', $value == 'rent' ? 'rent' : 'sale');
                break;

            case ModelsQueryParamsDef::FURNISHINGS:
                if (!empty($value)) {
                    $q->join("attributes AS furn_attr", "furn_attr.asset_id", "=", "properties.asset_id")
                        ->join("attribute_definitions AS furn_attr_def", "furn_attr_def.id", "=", "furn_attr.attribute_definition_id")
                        ->where([
                            ["furn_attr_def.id", "=", $value],
                            ["furn_attr.value", "=", 'true']
                        ]);
                }
                break;

            case ModelsQueryParamsDef::BEDROOMS:
                if (doubleval($value)) {
                    $q->where("be_attr.value", "=", $value);
                }
                break;

            case ModelsQueryParamsDef::BATHROOMS:
                if (doubleval($value)) {
                    $q->where("ba_attr.value", "=", $value);
                }
                break;

            case ModelsQueryParamsDef::PRICE_FROM:
                if (doubleval($value)) {
                    $q->where('price', '>=', $value);
                }
                break;

            case ModelsQueryParamsDef::PRICE_TO:
                if (doubleval($value)) {
                    if (in_array($value, ['60000', '10000000'])) {
                        $q->where('price', '>', 0);
                    } else {
                        $q->where('price', '>=', 0);
                    }
                }
                break;

            case ModelsQueryParamsDef::SQRFT_FROM:
                if (doubleval($value)) {
                    $q->where("bua_attr.value", ">=", $value);
                }
                break;

            case ModelsQueryParamsDef::SQRFT_TO:
                if (doubleval($value)) {
                    $q->where("bua_attr.value", "<=", $value);
                }
                break;

            default:
        }
    }

    public function mapModelToListItem($item, $listingsMetadata, $attachments)
    {
        $userCurrency = session()->get('userCurrency', 'qar');
        $itemListingMetadata = isset($listingsMetadata[$item->id]) ? $listingsMetadata[$item->id] : null;
        $itemAttachments = isset($attachments[$item->id]) ? $attachments[$item->id] : null;

        $listItem = [];

        $titlePieces = explode(' ', $item->title, 6);
        unset($titlePieces[5]);
        $title = join(' ', $titlePieces);
        $listItem['id'] = $item->id;
        $listItem['refNo'] = $item->ref_no;
        $listItem['title'] = $title;
        $listItem['imgPath'] = $this->extractListItemFirstImagePath($item, $itemListingMetadata, $itemAttachments);
        $listItem['adType'] = $item->ad_type;
        $listItem['address'] = $item->address;
        $listItem['bedrooms'] = $item->bedrooms;
        $listItem['bathrooms'] = $item->bathrooms;
        $listItem['builtUpArea'] = $item->built_up_area;

        $itemPrice = $userCurrency === 'qar' ? $item->price : $this->lastRates->{$userCurrency} * $item->price;

        $listItem['formattedPrice'] = number_format($itemPrice, 2);
        $listItem['selectedCurrency'] = $userCurrency;

        return $listItem;
    }

    public function mapSnapshotToListItem($item, $listingsMetadata, $attachments)
    {
        $userCurrency = session()->get('userCurrency', 'qar');
        $itemListingMetadata = isset($listingsMetadata[$item->id]) ? $listingsMetadata[$item->id] : null;
        $itemAttachments = isset($attachments[$item->asset_id]) ? $attachments[$item->asset_id] : null;

        $listItem = [];

        $titlePieces = explode(' ', $item->title, 6);
        unset($titlePieces[5]);
        $title = join(' ', $titlePieces);
        $listItem['id'] = $item->listing_id;
        $listItem['asset_id'] = $item->asset_id;
        $listItem['ref_no'] = $item->ref_no;
        $listItem['title'] = $title;
        $listItem['images'] = [[
            'img_url' => $this->extractListItemFirstImagePath($item, $itemListingMetadata, $itemAttachments),
            'is_primary' => false
        ]];
        $listItem['ad_type'] = $item->ad_type;
        //        $listItem['address'] = $item->location->path();
        $listItem['location'] = $item->location;
        $listItem['location_parent'] = !empty($item->location_parent) ? $item->location_parent : '';
        $listItem['bedrooms'] = $item->bedrooms;
        $listItem['bathrooms'] = $item->bathrooms;
        $listItem['built_up_area'] = $item->built_up_area;

        $itemPrice = $userCurrency === 'qar' ? $item->price : $this->lastRates->{$userCurrency} * $item->price;

        $listItem['formattedPrice'] = number_format($itemPrice, 2);
        $listItem['selectedCurrency'] = $userCurrency;

        return $listItem;
    }

    private function extractListItemFirstImagePath($item, $itemListingMetadata, $itemAttachments)
    {
        if (is_null($itemAttachments) || !is_array($itemAttachments) || !isset($itemAttachments[0])) {
            return '/images/card.jpg';
        }

        $firstImageItem = $itemAttachments[0];
        $imagePath = env('IMAGES_HOST') . "/images_cache/snapshots/" . $item->asset_id . "/" . $this->appendBeforeExtension($firstImageItem->img_path, '-image(375x250-crop)');
        return $imagePath;
    }

    private function appendBeforeExtension($appendedStr, $appendedPath)
    {
        $pi = pathinfo($appendedStr);

        return $pi['filename'] . $appendedPath . "." . $pi["extension"];
    }

    public function searchLiteMobile($params = [], $limit = 0, $order = [], $perPage = 12, $locale = Language::EN)
    {
        if (isset($params['loc']) && $params['loc'] == -1) {
            unset($params['loc']);
        }
        $user = auth()->user();
        $attributeDefinitionIds = $this->getAttributeDefinitionIds();
        $allAmenitiesIds = $this->amenitiesService->getListingAmenitiesOptions()->map(function ($item) {
            return $item->id;
        })->toArray();

        $fieldsToSelect = [
            'p.listing_id as id',
            'p.title',
            'p.listing_id as listing_id',
            'p.property_type_id as property_type_id',
            'p.asset_id as asset_id',
            'p.is_exclusive',
            'p.ad_type',
            'p.ref_no',
            'p.price',
            'p.geo_lat',
            'p.geo_lon',
            DB::raw('COALESCE(p.price_on_request, 0) as price_on_request'),
            'p.images',
            'p.image_alts',
            'p.primary_image',
            'p.offplan',
            'p.minimum_contract',
            'p.updated_at',
            DB::raw('COALESCE(p.is_sold_leased, 0) as is_sold_leased'),
            'p.location_id',
            'loc.name as location',
            'locp.name as location_parent',
            'be_attr.value as bedrooms',
            'ba_attr.value as bathrooms',
            'bua_attr.value as built_up_area',
            // 'furn_attr.value as furnishings',
            'pt.label as property_type',
            // 'cou.name as country_name',
            DB::raw('IF(FIND_IN_SET(p.created_by, "' . join(",", self::NON_RATEABLE_USERS) . '"), 1, 0) as creator_is_non_rateable'),
            DB::raw('IF(bla.id IS NOT NULL, bla.prefix_phone, a.prefix_phone) as agent_prefix_phone'),
            DB::raw('IF(bla.id IS NOT NULL, bla.phone, a.phone) as agent_phone'),
            DB::raw('IF(bla.id IS NOT NULL, bla.email, a.email) as agent_email'),
            'bla.prefix_phone as bla_prefix_phone',
            'bla.phone as bla_phone',
            'bla.email as bla_email',
            // DB::raw('GROUP_CONCAT(CONCAT(sad.id, "||", sad.name) SEPARATOR "~~") as grouped_amenities'),
            DB::raw('COALESCE(
                CAST(
                    GROUP_CONCAT(
                        DISTINCT IF(r.name LIKE "' . RolesDef::OFFICE_MANAGER . '", 5, NULL)
                    )
                as DOUBLE), a.rating
            ) as agent_rating')
        ];

        $q = DB::table('property_snapshots as p')
            ->join('users as a', 'a.id', '=', 'p.created_by')
            ->leftJoin('users as bla', 'bla.id', '=', 'a.brokerage_license_account_id')
            ->leftJoin('model_has_roles as mhr', 'mhr.model_id', '=', 'p.created_by')
            ->leftJoin('roles as r', 'r.id', '=', 'mhr.role_id')
            ->leftJoin('property_types as pt', 'pt.id', '=', 'p.property_type_id')
            ->leftJoin('geography as loc', 'loc.id', '=', 'p.location_id')
            ->leftJoin('geography as locp', 'locp.id', '=', 'loc.parent_id')
            // ->leftJoin('countries as cou', 'cou.id', '=', 'p.country_id')
            ->leftJoin("snapshot_attributes AS be_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("be_attr.asset_id", "=", "p.asset_id")
                    ->where("be_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BEDROOMS]);
            })
            ->leftJoin("snapshot_attributes AS ba_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("ba_attr.asset_id", "=", "p.asset_id")
                    ->where("ba_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BATHROOMS]);
            })
            ->leftJoin("snapshot_attributes AS bua_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("bua_attr.asset_id", "=", "p.asset_id")
                    ->where("bua_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BUILD_UP_AREA]);
            })
            ->leftJoin("snapshot_attributes AS amenities", function ($join) use ($allAmenitiesIds) {
                $join->on("amenities.asset_id", "=", "p.asset_id")
                    ->whereIn("amenities.attribute_definition_id", $allAmenitiesIds);
            })
            ->leftJoin("snapshot_attributes AS furn_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("furn_attr.asset_id", "=", "p.asset_id")
                    ->where("furn_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS]);
            })
            ->leftJoin("snapshot_attributes AS furn_attr_office", function ($join) use ($attributeDefinitionIds) {
                $join->on("furn_attr_office.asset_id", "=", "p.asset_id")
                    ->where("furn_attr_office.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS_OFFICE]);
            });

        if (!is_null($user)) {
            $q->leftJoin('user_x_saved_properties AS uxsp', 'uxsp.property_id', '=', 'p.listing_id');
            $fieldsToSelect[] = DB::raw('IF(uxsp.id IS NULL, 0, 1) as saved_by_user');
        } else {
            $fieldsToSelect[] = DB::raw('0 as saved_by_user');
        }
        // ->leftJoin("snapshot_attributes AS furn_attr", function ($join) use ($attributeDefinitionIds) {
        //     $join->on("furn_attr.asset_id", "=", "p.asset_id")
        //         ->where("furn_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS]);
        // })
        // ->leftJoin("snapshot_attributes AS furn_attr_office", function ($join) use ($attributeDefinitionIds) {
        //     $join->on("furn_attr_office.asset_id", "=", "p.asset_id")
        //         ->where("furn_attr_office.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS_OFFICE]);
        // })
        // ->leftJoin("attribute_definitions AS sad", function ($join) use ($allAmenitiesIds) {
        //     $join->on("sad.id", "=", "amenities.attribute_definition_id");
        // });

        // $q->where('mhr.model_type', 'App\Models\User');

        // if ($locale !== Language::EN) {
        //     $q->leftJoin('properties_snapshots_translations as pst', function ($join) use ($locale) {
        //         $join->on('pst.snapshot_id', '=', 'p.id')
        //             ->where('pst.language', strtolower($locale));
        //     })
        //         ->leftJoin('geography_translations as loc_trans', function ($query) use ($locale) {
        //             $query->on('loc_trans.geography_id', '=', 'p.location_id')
        //                 ->where('loc_trans.language', $locale);
        //         })
        //         ->leftJoin('geography_translations as locp_trans', function ($query) use ($locale) {
        //             $query->on('locp_trans.geography_id', '=', 'loc.parent_id')
        //                 ->where('locp_trans.language', $locale);
        //         })
        //         ->leftJoin('geography_translations as cou_trans', function ($query) use ($locale) {
        //             $query->on('cou_trans.geography_id', '=', 'p.country_id')
        //                 ->where('cou_trans.language', $locale);
        //         });
        //     $fieldsToSelect[] = 'pst.title as translated_title';
        //     $fieldsToSelect[] = 'loc_trans.name as loc_translated_name';
        //     $fieldsToSelect[] = 'loc_trans.slug as loc_translated_slug';
        //     $fieldsToSelect[] = 'locp_trans.name as locp_translated_name';
        //     $fieldsToSelect[] = 'locp_trans.slug as locp_translated_slug';
        //     $fieldsToSelect[] = 'cou_trans.name as cou_translated_name';
        //     $fieldsToSelect[] = 'cou_trans.slug as cou_translated_slug';
        //     //            $fieldsToSelect[] = 'pst.description as translated_description';
        // }

        $q->select(
            $fieldsToSelect
        );

        $wheres = [];

        if (isset($params[ModelsQueryParamsDef::LOCATION_DATA]) && is_array($params[ModelsQueryParamsDef::LOCATION_DATA])) {
            $params[ModelsQueryParamsDef::LOCATION_DATA] = join(",", $params[ModelsQueryParamsDef::LOCATION_DATA]);
        }

        $q->where(function($query) {
            $query->whereNull('p.is_short_stay')->orWhere('p.is_short_stay', 0);
        });

        foreach ($params as $key => $value) {
            if (is_array($value) || empty(trim($value))) {
                if (!in_array($key, [ModelsQueryParamsDef::BEDROOMS, ModelsQueryParamsDef::PROPERTY_TYPE, ModelsQueryParamsDef::MINIMUM_CONTRACT, ModelsQueryParamsDef::OFFPLAN])) {
                    continue;
                }
            }

            if ($key === ModelsQueryParamsDef::OPERATION_TYPE) {
                $urlValues = array_filter(explode(",", $value), function ($adType) {
                    return in_array($adType, ['rent', 'sale', 'buy']);
                });
                $urlValues = array_map(function ($urlValue) {
                    return $urlValue === 'buy' ? 'sale' : $urlValue;
                }, $urlValues);
                $q->whereIn('ad_type', $urlValues);
                continue;
            }
            if ($key == ModelsQueryParamsDef::OFFPLAN) {
                if ($value == 1) {
                    $wheres[] = ['p.offplan', '=', $value];
                } elseif ($value == 0) {
                    $q->where(function ($qb) {
                        $qb->whereNull('p.offplan')->orWhere('p.offplan', 0);
                    });
                }
                continue;
            }

            if ($key === ModelsQueryParamsDef::CONTACT_ID) {
                $q->where('contact_id', $value);
                continue;
            }

            if ($key === ModelsQueryParamsDef::EXCEPT_ID) {
                $q->where('p.id', '!=', $value);
                continue;
            }

            if ($key === ModelsQueryParamsDef::PROPERTY_TYPE && !empty($value)) {
                if (is_array($value)) {
                    $urlValues = $value;
                } else {
                    $urlValues = explode(",", $value);
                }
                $propertyTypes = $this->cachedPropertyTypes()->filter(function ($pt) use ($urlValues) {
                    return in_array($pt->url_value, $urlValues);
                })->map(function ($pt) {
                    return $pt->id;
                })->toArray();

                // include hotel apart when looking for apartment
                if (in_array(1, $propertyTypes) && !in_array(17, $propertyTypes)) {
                    $propertyTypes[] = 17;
                }
                if (
                    isset($params[ModelsQueryParamsDef::OPERATION_TYPE]) &&
                    $params[ModelsQueryParamsDef::OPERATION_TYPE] == 'rent' &&
                    !isset($params[ModelsQueryParamsDef::MINIMUM_CONTRACT])
                ) {
                    $q->where(function ($qb) {
                        $qb->whereNull('p.minimum_contract')
                            ->orWhereNotIn('p.minimum_contract', ['0.01', '0.02', '0.03', '0.1']);
                    });
                }

                $q->whereIn('p.property_type_id', $propertyTypes);
                continue;
            }

            if ($key === ModelsQueryParamsDef::IS_EXCLUSIVE) {
                $wheres[] = ['p.is_exclusive', '=', 1];
                continue;
            }

            if ($key === ModelsQueryParamsDef::IS_HOMEPAGE_PROMOTED) {
                $wheres[] = ['p.is_homepage_promoted', '=', 1];
                continue;
            }

            if ($key === ModelsQueryParamsDef::IS_INVESTMENT_OPPORTUNITY) {
                $wheres[] = ['p.is_investment_opportunity', '=', 1];
                continue;
            }

            if ($key === ModelsQueryParamsDef::PRICE_FROM) {
                $wheres[] = ['p.price', '>=', $value];
                $wheres[] = ['p.is_sold_leased', '=', 0];
                continue;
            }

            if ($key === ModelsQueryParamsDef::PRICE_TO) {
                $wheres[] = ['p.price', '<=', $value];
                $wheres[] = ['p.is_sold_leased', '=', 0];
                continue;
            }

            if ($key === ModelsQueryParamsDef::SQRFT_FROM) {
                $wheres[] = [DB::raw('CAST(bua_attr.value as DECIMAL)'), '>=', doubleval($value)];
                continue;
            }

            if ($key === ModelsQueryParamsDef::SQRFT_TO) {
                $wheres[] = [DB::raw('CAST(bua_attr.value as DECIMAL)'), '<=', doubleval($value)];
                continue;
            }

            if ($key === ModelsQueryParamsDef::BEDROOMS) {
                $wheres[] = ['be_attr.value', '=', $value];
                continue;
            }

            if ($key === ModelsQueryParamsDef::BATHROOMS) {
                $wheres[] = ['ba_attr.value', '>=', $value];
                continue;
            }

            if ($key === ModelsQueryParamsDef::FURNISHINGS) {
                $furnishingArr = explode(",", $value);
                $q->whereIn('furn_attr.value', $furnishingArr);
                continue;
            }

            if ($key === ModelsQueryParamsDef::FURNISHINGS_OFFICE) {
                $furnishingArr = explode(",", $value);
                $q->whereIn('furn_attr_office.value', $furnishingArr);
                continue;
            }

            if ($key === ModelsQueryParamsDef::AMENITIES) {
                $amenitiesList = explode(",", $value);
                foreach ($amenitiesList as $amenity) {
                    $trimmedAmenityId = trim($amenity);
                    $q->join("snapshot_attributes AS amenities_attr_$trimmedAmenityId", function ($join) use ($trimmedAmenityId) {
                        $join->on("amenities_attr_$trimmedAmenityId.asset_id", "=", "p.asset_id")
                            ->where("amenities_attr_$trimmedAmenityId.attribute_definition_id", '=', $trimmedAmenityId);
                    });
                }
                continue;
            }

            if ($key === ModelsQueryParamsDef::MINIMUM_CONTRACT) {
                $processedValue = is_array($value) ? $value : (str_contains($value, ",") ? explode(",", $value) : $value);
                if (is_array($processedValue)) {
                    $q->whereIn('p.minimum_contract', $processedValue);
                } else {
                    $wheres[] = ['p.minimum_contract', '=', $value];
                    continue;
                }
            }

            if ($key === ModelsQueryParamsDef::MAP_BOUNDS && $this->isValidMapBound($value)) {
                $mapBounds = json_decode($value);

                $polygonStr = "POLYGON((
                   " . $mapBounds->northeast->lng . " " . $mapBounds->northeast->lat . ",
                   " . $mapBounds->southwest->lng . " " . $mapBounds->northeast->lat . ",
                   " . $mapBounds->southwest->lng . " " . $mapBounds->southwest->lat . ",
                   " . $mapBounds->northeast->lng . " " . $mapBounds->southwest->lat . ",
                   " . $mapBounds->northeast->lng . " " . $mapBounds->northeast->lat . "
                ))";

                $q->where(DB::raw("ST_CONTAINS(ST_GEOMFROMTEXT('$polygonStr'), p.geo_point)"), 1);
            }

            if ($key === ModelsQueryParamsDef::LOCATION_DATA && !isset($params[ModelsQueryParamsDef::MAP_BOUNDS])) {
                if (is_array($value)) {
                    $locationIds = $value;
                } else if (!empty(trim($value))) {
                    $locationIds = explode(",", $value);
                }

                if (!is_numeric($locationIds[0])) {
                    $locationIds = array_map(function ($slug) {
                        $g = Geography::where('slug', 'LIKE', $slug)->first();
                        return is_null($g) ? null : $g->id;
                    }, $locationIds);
                }

                foreach ($locationIds as $locationId) {
                    if (!is_numeric($locationId)) {
                        $gId = Geography::where('slug', $locationId)->get();
                    }
                }

                $dbLocationIds1 = DB::table('geography as g')
                    ->leftJoin('geography as c1', 'c1.parent_id', '=', 'g.id')
                    ->leftJoin('geography as c2', 'c2.parent_id', '=', 'c1.id')
                    ->whereIn('g.id', $locationIds)
                    ->select('g.id as id', 'c1.id as c1_id', 'c2.id as c2_id')
                    ->get();

                $dbLocationIds = [];
                $dbLocationIds1->each(function ($item) use (&$dbLocationIds) {
                    if (!in_array($item->id, $dbLocationIds)) {
                        $dbLocationIds[] = $item->id;
                    }

                    if (!is_null($item->c1_id) && !in_array($item->c1_id, $dbLocationIds)) {
                        $dbLocationIds[] = $item->c1_id;
                    }

                    if (!is_null($item->c2_id) && !in_array($item->c2_id, $dbLocationIds)) {
                        $dbLocationIds[] = $item->c2_id;
                    }
                });

                // dd($dbLocationIds);

                $q->where(function ($sql) use ($dbLocationIds) {
                    $sql->whereIn('p.location_id', array_unique($dbLocationIds));
                });
            }
        }

        $q->orderBy('creator_is_non_rateable', 'DESC');
        //As requested by @Serban in 27.01.2024
        // $q->orderBy('agent_rating', 'DESC');
        $q->orderBy(DB::raw('COALESCE(p.is_sold_leased, 0)', 'ASC'));
        $q->orderBy(DB::raw('COALESCE(p.price_on_request, 0)', 'ASC'));

        if (isset($params['sort'])) {
            $sortDir = 'asc';

            if (isset($params['dir']) && $params['dir'] == 'desc') {
                $sortDir = 'desc';
            } elseif (isset($params['sort-dir']) && $params['sort-dir'] == 'desc') {
                $sortDir = 'desc';
            }

            $q->orderBy($this->getTableSortColumn($params['sort']), $sortDir);
        }

        if (!empty($order) && is_array($order)) {
            if (count($order) == 2) {
                $q->orderBy($order[0], $order[1] ?? 'desc');
            } elseif (count($order) == 0) {
                $q->orderBy('p.updated_at', 'desc');
            }
        }

        if (isset($params['ids']) && is_array($params['ids'])) {
            $q->whereIn('p.listing_id', $params['ids']);
        }

        if (count($wheres) > 0) {
            $q->where($wheres);
        }

        // skip investment opportunities
        if (!isset($params[ModelsQueryParamsDef::IS_INVESTMENT_OPPORTUNITY])) {
            $q->where(function ($qb) {
                $qb->whereNull('p.is_investment_opportunity')
                    ->orWhere('p.is_investment_opportunity', '!=', 1);
            });
        }

        if (isset($params[ModelsQueryParamsDef::PROPERTY_CREATED_BY]) && !empty($params[ModelsQueryParamsDef::PROPERTY_CREATED_BY])) {
            $q->where('p.created_by', $params[ModelsQueryParamsDef::PROPERTY_CREATED_BY]);
        }

        if (isset($params[ModelsQueryParamsDef::PROPERTY_CREATED_BY]) && !empty($params[ModelsQueryParamsDef::PROPERTY_CREATED_BY])) {
            $q->where('p.created_by', $params[ModelsQueryParamsDef::PROPERTY_CREATED_BY]);
        }

        $q->where('p.publishing_status', ListingPublishingStatus::STATUS_PUBLISHED);
        // skip price on request
        $q->where(function ($qb) {
            $qb->whereNull('p.price_on_request')
                ->orWhere('p.price_on_request', '!=', 1);
        });
        $q->where(function ($qb) {
            $qb->whereNull('p.is_sold_leased')
                ->orWhere('p.is_sold_leased', '!=', 1);
        });
        $q->whereNotIn('p.status', ['rented', 'sold', 'to-be-available']);
        $q->whereNull('p.deleted_at');
        $q->whereNotIn('p.created_by', [125]); // ignore the <NAME_EMAIL>
        $q->groupBy('p.id');

        // echo $q->toSql();
        // die;

        return $q->paginate($perPage);
    }

    public function getSearchLiteQ($params = [], $limit = 0, $order = [], $perPage = 12, $locale = Language::EN)
    {
        if (isset($params['loc']) && $params['loc'] == -1) {
            unset($params['loc']);
        }
        $attributeDefinitionIds = $this->getAttributeDefinitionIds();
        $allAmenitiesIds = $this->amenitiesService->getListingAmenitiesOptions()->map(function ($item) {
            return $item->id;
        })->toArray();

        $fieldsToSelect = [
            'p.listing_id as id',
            'p.title',
            'p.unit_no',
            'p.listing_id as listing_id',
            'p.property_type_id as property_type_id',
            'p.asset_id as asset_id',
            'p.is_exclusive',
            'p.ad_type',
            'p.ref_no',
            'p.price',
            'p.geo_lat',
            'p.geo_lon',
            DB::raw('COALESCE(p.price_on_request, 0) as price_on_request'),
            'p.images',
            'p.image_alts',
            'p.primary_image',
            'p.offplan',
            'p.minimum_contract',
            'p.updated_at',
            DB::raw('COALESCE(p.is_sold_leased, 0) as is_sold_leased'),
            'p.location_id',
            'p.is_short_stay',
            'p.tour_360',
            'p.title_deed',
            'loc.name as location',
            'locp.name as location_parent',
            'be_attr.value as bedrooms',
            'ba_attr.value as bathrooms',
            'bua_attr.value as built_up_area',
            'furn_attr.value as furnishings',
            'pt.label as property_type',
            'cou.name as country_name',
            DB::raw('IF(bla.id IS NOT NULL, bla.prefix_phone, a.prefix_phone) as agent_prefix_phone'),
            DB::raw('IF(bla.id IS NOT NULL, bla.phone, a.phone) as agent_phone'),
            DB::raw('IF(bla.id IS NOT NULL, bla.email, a.email) as agent_email'),
            'bla.prefix_phone as bla_prefix_phone',
            'bla.phone as bla_phone',
            'bla.email as bla_email',
            'a.should_use_whatsapp_chatbot as should_use_whatsapp_chatbot',
            't.name as tower_name',
            DB::raw('IF(FIND_IN_SET(p.created_by, "' . join(",", self::NON_RATEABLE_USERS) . '"), 1, 0) as creator_is_non_rateable'),
            DB::raw('GROUP_CONCAT(CONCAT(sad.id, "||", sad.name) SEPARATOR "~~") as grouped_amenities'),
            DB::raw('COALESCE(
                CAST(
                    GROUP_CONCAT(
                        DISTINCT IF(r.name LIKE "' . RolesDef::OFFICE_MANAGER . '", 5, NULL)
                    )
                as DOUBLE), a.rating
            ) as agent_rating')
        ];

        $q = DB::table('property_snapshots as p')
            ->join('users as a', 'a.id', '=', 'p.created_by')
            ->leftJoin('users as bla', 'bla.id', '=', 'a.brokerage_license_account_id')
            ->leftJoin('model_has_roles as mhr', 'mhr.model_id', '=', 'p.created_by')
            ->leftJoin('roles as r', 'r.id', '=', 'mhr.role_id')
            ->leftJoin('property_types as pt', 'pt.id', '=', 'p.property_type_id')
            ->leftJoin('geography as loc', 'loc.id', '=', 'p.location_id')
            ->leftJoin('geography as locp', 'locp.id', '=', 'loc.parent_id')
            ->leftJoin('countries as cou', 'cou.id', '=', 'p.country_id')
            ->leftJoin('towers as t', 't.id', '=', 'p.tower_id')
            ->leftJoin("snapshot_attributes AS be_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("be_attr.asset_id", "=", "p.asset_id")
                    ->where("be_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BEDROOMS]);
            })
            ->leftJoin("snapshot_attributes AS ba_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("ba_attr.asset_id", "=", "p.asset_id")
                    ->where("ba_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BATHROOMS]);
            })
            ->leftJoin("snapshot_attributes AS bua_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("bua_attr.asset_id", "=", "p.asset_id")
                    ->where("bua_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BUILD_UP_AREA]);
            })
            ->leftJoin("snapshot_attributes AS furn_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("furn_attr.asset_id", "=", "p.asset_id")
                    ->where("furn_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS]);
            })
            ->leftJoin("snapshot_attributes AS furn_attr_office", function ($join) use ($attributeDefinitionIds) {
                $join->on("furn_attr_office.asset_id", "=", "p.asset_id")
                    ->where("furn_attr_office.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS_OFFICE]);
            })
            ->leftJoin("snapshot_attributes AS amenities", function ($join) use ($allAmenitiesIds) {
                $join->on("amenities.asset_id", "=", "p.asset_id")
                    ->whereIn("amenities.attribute_definition_id", $allAmenitiesIds);
            })
            ->leftJoin("attribute_definitions AS sad", function ($join) use ($allAmenitiesIds) {
                $join->on("sad.id", "=", "amenities.attribute_definition_id");
            });

        $q->where('mhr.model_type', 'App\Models\User');

        if ($locale !== Language::EN) {
            $q->leftJoin('properties_snapshots_translations as pst', function ($join) use ($locale) {
                $join->on('pst.snapshot_id', '=', 'p.id')
                    ->where('pst.language', strtolower($locale));
            })
                ->leftJoin('geography_translations as loc_trans', function ($query) use ($locale) {
                    $query->on('loc_trans.geography_id', '=', 'p.location_id')
                        ->where('loc_trans.language', $locale);
                })
                ->leftJoin('geography_translations as locp_trans', function ($query) use ($locale) {
                    $query->on('locp_trans.geography_id', '=', 'loc.parent_id')
                        ->where('locp_trans.language', $locale);
                })
                ->leftJoin('geography_translations as cou_trans', function ($query) use ($locale) {
                    $query->on('cou_trans.geography_id', '=', 'p.country_id')
                        ->where('cou_trans.language', $locale);
                });
            $fieldsToSelect[] = 'pst.title as translated_title';
            $fieldsToSelect[] = 'loc_trans.name as loc_translated_name';
            $fieldsToSelect[] = 'loc_trans.slug as loc_translated_slug';
            $fieldsToSelect[] = 'locp_trans.name as locp_translated_name';
            $fieldsToSelect[] = 'locp_trans.slug as locp_translated_slug';
            $fieldsToSelect[] = 'cou_trans.name as cou_translated_name';
            $fieldsToSelect[] = 'cou_trans.slug as cou_translated_slug';
            //            $fieldsToSelect[] = 'pst.description as translated_description';
        }

        $q->select(
            $fieldsToSelect
        );

        $wheres = [];

        if (isset($params[ModelsQueryParamsDef::LOCATION_DATA]) && is_array($params[ModelsQueryParamsDef::LOCATION_DATA])) {
            $params[ModelsQueryParamsDef::LOCATION_DATA] = join(",", $params[ModelsQueryParamsDef::LOCATION_DATA]);
        }

        foreach ($params as $key => $value) {
            if (is_array($value) || empty(trim($value))) {
                if (!in_array($key, [ModelsQueryParamsDef::BEDROOMS, ModelsQueryParamsDef::PROPERTY_TYPE, ModelsQueryParamsDef::MINIMUM_CONTRACT, ModelsQueryParamsDef::OFFPLAN])) {
                    continue;
                }
            }

            if ($key === ModelsQueryParamsDef::OPERATION_TYPE) {
                $urlValues = array_filter(explode(",", $value), function ($adType) {
                    return in_array($adType, ['rent', 'sale', 'buy']);
                });
                $urlValues = array_map(function ($urlValue) {
                    return $urlValue === 'buy' ? 'sale' : $urlValue;
                }, $urlValues);
                $q->whereIn('ad_type', $urlValues);
                continue;
            }

            if ($key === ModelsQueryParamsDef::CONTACT_ID) {
                $q->where('contact_id', $value);
                continue;
            }

            if ($key === ModelsQueryParamsDef::EXCEPT_ID) {
                $q->where('p.id', '!=', $value);
                continue;
            }

            if ($key === ModelsQueryParamsDef::PROPERTY_TYPE && !empty($value)) {
                if (is_array($value)) {
                    $urlValues = $value;
                } else {
                    $urlValues = explode(",", $value);
                }
                $propertyTypes = $this->cachedPropertyTypes()->filter(function ($pt) use ($urlValues) {
                    return in_array($pt->url_value, $urlValues);
                })->map(function ($pt) {
                    return $pt->id;
                })->toArray();

                // include hotel apart when looking for apartment
                if (in_array(1, $propertyTypes) && !in_array(17, $propertyTypes)) {
                    $propertyTypes[] = 17;
                }
                if (
                    isset($params[ModelsQueryParamsDef::OPERATION_TYPE]) &&
                    $params[ModelsQueryParamsDef::OPERATION_TYPE] == 'rent' &&
                    !isset($params[ModelsQueryParamsDef::MINIMUM_CONTRACT])
                ) {
                    $q->where(function ($qb) {
                        $qb->whereNull('p.minimum_contract')
                            ->orWhereNotIn('p.minimum_contract', ['0.01', '0.02', '0.03', '0.1']);
                    });
                }

                // dd($propertyTypes);

                $q->whereIn('p.property_type_id', $propertyTypes);
                continue;
            }

            if ($key === ModelsQueryParamsDef::COUNTRY_ID) {
                if ($value == 187) {
                    $q->where(function ($qb) use ($value) {
                        $qb->whereNull('p.country_id')->orWhere('p.country_id', $value);
                    });
                } else {
                    $wheres[] = ['p.country_id', '=', $value];
                }

                continue;
            }

            // @TODO - pay more attention to this
            if ($key === ModelsQueryParamsDef::PROJECT_ID) {
                $wheres[] = ['p.project_id', '=', $value];
                continue;
            }

            if ($key === ModelsQueryParamsDef::CITY_ID) {
                $wheres[] = ['p.location_id', '=', $value];
                continue;
            }

            if ($key === ModelsQueryParamsDef::IS_EXCLUSIVE) {
                $wheres[] = ['p.is_exclusive', '=', 1];
                continue;
            }

            if ($key === ModelsQueryParamsDef::IS_HOMEPAGE_PROMOTED) {
                $wheres[] = ['p.is_homepage_promoted', '=', 1];
                continue;
            }

            if ($key === ModelsQueryParamsDef::IS_INVESTMENT_OPPORTUNITY) {
                $wheres[] = ['p.is_investment_opportunity', '=', 1];
                continue;
            }

            if ($key === ModelsQueryParamsDef::PRICE_FROM) {
                $wheres[] = ['p.price', '>=', $value];
                $wheres[] = ['p.is_sold_leased', '=', 0];
                continue;
            }

            if ($key === ModelsQueryParamsDef::PRICE_TO) {
                $wheres[] = ['p.price', '<=', $value];
                $wheres[] = ['p.is_sold_leased', '=', 0];
                continue;
            }

            if ($key === ModelsQueryParamsDef::SQRFT_FROM) {
                $wheres[] = [DB::raw('CAST(bua_attr.value as DECIMAL)'), '>=', doubleval($value)];
                continue;
            }

            if ($key === ModelsQueryParamsDef::SQRFT_TO) {
                $wheres[] = [DB::raw('CAST(bua_attr.value as DECIMAL)'), '<=', doubleval($value)];
                continue;
            }

            if ($key === ModelsQueryParamsDef::BEDROOMS) {
                $q->where(function ($qb) use ($value) {
                    $qb->where('be_attr.value', $value)
                        ->orWhere('be_attr.value', intval($value) . "+");
                });
                // $wheres[] = ['be_attr.value', 'LIKE', $value];
                continue;
            }

            if ($key === ModelsQueryParamsDef::BATHROOMS) {
                $wheres[] = ['ba_attr.value', '>=', $value];
                continue;
            }

            if ($key === ModelsQueryParamsDef::FURNISHINGS) {
                $furnishingArr = explode(",", $value);
                $q->whereIn('furn_attr.value', $furnishingArr);
                continue;
            }

            if ($key === ModelsQueryParamsDef::FURNISHINGS_OFFICE) {
                $furnishingArr = explode(",", $value);
                $q->whereIn('furn_attr_office.value', $furnishingArr);
                continue;
            }

            if ($key === ModelsQueryParamsDef::AMENITIES) {
                $amenitiesList = explode(",", $value);
                foreach ($amenitiesList as $amenity) {
                    $trimmedAmenityId = trim($amenity);
                    $q->join("snapshot_attributes AS amenities_attr_$trimmedAmenityId", function ($join) use ($trimmedAmenityId) {
                        $join->on("amenities_attr_$trimmedAmenityId.asset_id", "=", "p.asset_id")
                            ->where("amenities_attr_$trimmedAmenityId.attribute_definition_id", '=', $trimmedAmenityId);
                    });
                }
                continue;
            }

            if ($key === ModelsQueryParamsDef::MINIMUM_CONTRACT) {
                $processedValue = is_array($value) ? $value : (str_contains($value, ",") ? explode(",", $value) : $value);
                if (is_array($processedValue)) {
                    $q->whereIn('p.minimum_contract', $processedValue);
                } else {
                    $wheres[] = ['p.minimum_contract', '=', $value];
                    continue;
                }
            }

            if ($key === ModelsQueryParamsDef::MAP_BOUNDS && $this->isValidMapBound($value)) {
                $mapBounds = json_decode($value);

                $polygonStr = "POLYGON((
                   " . $mapBounds->northeast->lng . " " . $mapBounds->northeast->lat . ",
                   " . $mapBounds->southwest->lng . " " . $mapBounds->northeast->lat . ",
                   " . $mapBounds->southwest->lng . " " . $mapBounds->southwest->lat . ",
                   " . $mapBounds->northeast->lng . " " . $mapBounds->southwest->lat . ",
                   " . $mapBounds->northeast->lng . " " . $mapBounds->northeast->lat . "
                ))";

                $q->where(DB::raw("ST_CONTAINS(ST_GEOMFROMTEXT('$polygonStr'), p.geo_point)"), 1);
            }

            if ($key === ModelsQueryParamsDef::LOCATION_DATA && !isset($params[ModelsQueryParamsDef::MAP_BOUNDS])) {
                if (is_array($value)) {
                    $locationIds = $value;
                } else if (!empty(trim($value))) {
                    $locationIds = explode(",", $value);
                }

                if (!is_numeric($locationIds[0])) {
                    $locationIds = array_map(function ($slug) {
                        $g = Geography::where('slug', 'LIKE', $slug)->first();
                        return is_null($g) ? null : $g->id;
                    }, $locationIds);
                }

                foreach ($locationIds as $locationId) {
                    if (!is_numeric($locationId)) {
                        $gId = Geography::where('slug', $locationId)->get();
                    }
                }

                $dbLocationIds1 = DB::table('geography as g')
                    ->leftJoin('geography as c1', 'c1.parent_id', '=', 'g.id')
                    ->leftJoin('geography as c2', 'c2.parent_id', '=', 'c1.id')
                    ->whereIn('g.id', $locationIds)
                    ->select('g.id as id', 'c1.id as c1_id', 'c2.id as c2_id')
                    ->get();

                $dbLocationIds = [];
                $dbLocationIds1->each(function ($item) use (&$dbLocationIds) {
                    if (!in_array($item->id, $dbLocationIds)) {
                        $dbLocationIds[] = $item->id;
                    }

                    if (!is_null($item->c1_id) && !in_array($item->c1_id, $dbLocationIds)) {
                        $dbLocationIds[] = $item->c1_id;
                    }

                    if (!is_null($item->c2_id) && !in_array($item->c2_id, $dbLocationIds)) {
                        $dbLocationIds[] = $item->c2_id;
                    }
                });

                // dd($dbLocationIds);

                $q->where(function ($sql) use ($dbLocationIds) {
                    $sql->whereIn('p.location_id', array_unique($dbLocationIds));
                });
            }

            if ($key == ModelsQueryParamsDef::OFFPLAN) {
                if ($value == 1) {
                    $wheres[] = ['p.offplan', '=', $value];
                } elseif ($value == 0) {
                    $q->where(function ($qb) {
                        $qb->whereNull('p.offplan')->orWhere('p.offplan', 0);
                    });
                }
                continue;
            }
        }

        if(request()->has(ModelsQueryParamsDef::PROJECT_ID)) {
            $q->where('p.project_id', request()->get(ModelsQueryParamsDef::PROJECT_ID));
        }

        if (!isset($params['is_short_stay'])) {
            $q->where(function ($qb) {
                $qb->whereNull('is_short_stay')->orWhere('is_short_stay', 0);
            });
        } else {
            $q->where(function ($qb) {
                $qb->where('is_short_stay', 1);
            });
        }

        // $q->orderBy('creator_is_non_rateable', 'DESC');
        $q->orderBy('p.is_exclusive', 'DESC');
        //As requested by @Serban in 27.01.2024
        // $q->orderBy('agent_rating', 'DESC');
        $q->orderBy(DB::raw('COALESCE(p.is_sold_leased, 0)', 'ASC'));
        $q->orderBy(DB::raw('COALESCE(p.price_on_request, 0)', 'ASC'));

        if (isset($params['sort'])) {
            $sortDir = 'asc';

            if (isset($params['dir']) && $params['dir'] == 'desc') {
                $sortDir = 'desc';
            } elseif (isset($params['sort-dir']) && $params['sort-dir'] == 'desc') {
                $sortDir = 'desc';
            }

            $q->orderBy($this->getTableSortColumn($params['sort']), $sortDir);
        }

        if (!empty($order) && is_array($order)) {
            if (count($order) == 2) {
                $q->orderBy($order[0], $order[1] ?? 'desc');
            } elseif (count($order) == 0) {
                $q->orderBy('p.updated_at', 'desc');
            }
        }

        if (isset($params['ids']) && is_array($params['ids'])) {
            $q->whereIn('p.listing_id', $params['ids']);
        }

        if (count($wheres) > 0) {
            $q->where($wheres);
        }

        // skip investment opportunities
        if (!isset($params[ModelsQueryParamsDef::IS_INVESTMENT_OPPORTUNITY])) {
            $q->where(function ($qb) {
                $qb->whereNull('p.is_investment_opportunity')
                    ->orWhere('p.is_investment_opportunity', '!=', 1);
            });
        }

        if (isset($params[ModelsQueryParamsDef::PROPERTY_CREATED_BY]) && !empty($params[ModelsQueryParamsDef::PROPERTY_CREATED_BY])) {
            $q->where('p.created_by', $params[ModelsQueryParamsDef::PROPERTY_CREATED_BY]);
        }

        if (isset($params[ModelsQueryParamsDef::PROPERTY_CREATED_BY]) && !empty($params[ModelsQueryParamsDef::PROPERTY_CREATED_BY])) {
            $q->where('p.created_by', $params[ModelsQueryParamsDef::PROPERTY_CREATED_BY]);
        }

        $q->where('p.publishing_status', ListingPublishingStatus::STATUS_PUBLISHED);
        // skip price on request
        $q->where(function ($qb) {
            $qb->whereNull('p.price_on_request')
                ->orWhere('p.price_on_request', '!=', 1);
        });
        $q->where(function ($qb) {
            $qb->whereNull('p.is_sold_leased')
                ->orWhere('p.is_sold_leased', '!=', 1);
        });
        $q->whereNotIn('p.status', ['rented', 'sold', 'to-be-available']);
        $q->whereNull('p.deleted_at');
        if (env('APP_ENV') == 'production') {
            $q->whereNotIn('p.created_by', [125]); // ignore the <NAME_EMAIL>
        }
        $q->groupBy('p.id');

        // echo $q->toSql();
        // die;

        return $q;
    }

    public function searchLite($params = [], $limit = 0, $order = [], $perPage = 12, $locale = Language::EN)
    {
        $q = $this->getSearchLiteQ($params, $limit, $order, $perPage, $locale);

        // echo $q->toSql();
        // die;

        return $q->paginate($perPage);
    }

    private function getTableSortColumn($sortParam)
    {
        if (in_array($sortParam, ['price', 'date'])) {
            if ($sortParam == 'date') {
                $sortParam = 'created_at';
            }

            return "p." . $sortParam;
        }

        if (in_array($sortParam, ['beds'])) {
            return "be_attr.value";
        }

        return $sortParam;
    }

    public function getAttributeDefinitionIds()
    {
        $definitionNames = array_keys($this->attributeDefinitionIds);
        return Cache::remember(CacheKeys::ATTRIBUTE_DEFINITION_IDS, self::ONE_MONTH, function () use ($definitionNames) {
            return AttributeDefinition::whereIn('name', $definitionNames)->get()->mapWithKeys(function ($item) {
                return [$item->name => $item->id];
            })->toArray();
        });
    }

    public function getSnapshot($propertyId, $refNo = null, $locale = Language::EN)
    {
        $attributeDefinitionIds = $this->getAttributeDefinitionIds();

        $selectableFields = [
            'ps.title',
            'ps.listing_id',
            'ps.is_exclusive',
            'ps.price_on_request',
            'ps.id',
            'ps.ref_no',
            'ps.unit_no',
            'ps.best_price',
            'ps.asset_id',
            'ps.ad_type',
            'ps.price',
            'ps.location_id',
            'ps.property_type_id',
            'pt.name as property_type',
            'pt.label as property_type_label',
            'pt.url_value as property_type_url_value',
            'tow.name as tower_name',
            'ps.images',
            'ps.image_alts',
            'ps.primary_image',
            'ps.handover_date',
            'ps.offplan',
            'ps.minimum_contract',
            DB::raw('Y(ps.geo_point) as geo_lat'),
            DB::raw('X(ps.geo_point) as geo_lon'),
            'ps.brochure_path',
            'ps.brochure_title',
            'ps.layout_path',
            'ps.layout_title',
            'ps.listing_id',
            'ps.created_at',
            DB::raw("DATE_FORMAT(ps.updated_at, '%d.%m.%Y %H:%i') as updated_at"),
            'ps.deleted_at',
            'ps.is_short_stay',
            'be_attr.value as bedrooms',
            'ba_attr.value as bathrooms',
            'bua_attr.value as built_up_area',
            'furn_attr.value as furnishings',
            'furno_attr.value as furnishings_office',
            'ps.is_sold_leased as is_sold_leased',
            'ps.created_by',
            'ps.tour_360',
            'ps.title_deed',
            'ps.status',
            'ps.embeed_youtube',
            'ps.remote_propertyfinder_id',
            'g.name as location_name',
            'c.id as country_id',
            'c.name as country_name',
            'g.parent_id as location_parent_id',
            'cl.marketing_agreement_path',
            DB::raw('IF(!ISNULL(contacts.contract_end_date), DATE(contacts.contract_end_date) < NOW(), false) as is_contract_expired'),
            'ps.project_id'
        ];

        $q = DB::table('property_snapshots as ps')
            ->join('properties as p', 'p.asset_id', '=', 'ps.asset_id')
            ->join('property_types as pt', 'p.property_type_id', '=', 'pt.id')
            ->leftJoin("snapshot_attributes AS be_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("be_attr.asset_id", "=", "p.asset_id")
                    ->where("be_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BEDROOMS]);
            })
            ->leftJoin("snapshot_attributes AS ba_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("ba_attr.asset_id", "=", "p.asset_id")
                    ->where("ba_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BATHROOMS]);
            })
            ->leftJoin("snapshot_attributes AS bua_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("bua_attr.asset_id", "=", "p.asset_id")
                    ->where("bua_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BUILD_UP_AREA]);
            })
            ->leftJoin("snapshot_attributes AS furn_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("furn_attr.asset_id", "=", "p.asset_id")
                    ->where("furn_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS]);
            })
            ->leftJoin("snapshot_attributes AS furno_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("furno_attr.asset_id", "=", "p.asset_id")
                    ->where("furno_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS_OFFICE]);
            })
            ->join('geography as g', 'g.id', '=', 'ps.location_id')
            ->leftJoin('countries as c', 'c.id', '=', 'ps.country_id')
            ->leftJoin('towers as tow', 'tow.id', '=', 'ps.tower_id')
            ->leftJoin('contacts', 'ps.contact_id', '=', 'contacts.id')
            ->leftJoin('contacts_landlords as cl', 'contacts.id', '=', 'cl.contact_id')
            ->where('ps.listing_id', '=', $propertyId);

        if ($locale !== Language::EN) {
            $locale = strtolower($locale);
            $q->leftJoin('properties_snapshots_translations as pst', function ($join) use ($locale) {
                $join->on('pst.snapshot_id', '=', 'ps.id')
                    ->where('pst.language', '=', $locale);
            });
            $selectableFields[] = 'pst.title as translated_title';
            $selectableFields[] = 'pst.description as translated_description';
        }

        if (!is_null($refNo)) {
            $q->where('ps.ref_no', $refNo);
        }

        $q->select($selectableFields);


        $q->where('ps.publishing_status', ListingPublishingStatus::STATUS_PUBLISHED);
        // skip price on request
        $q->where(function ($qb) {
            $qb->whereNull('ps.price_on_request')
                ->orWhere('ps.price_on_request', '!=', 1);
        });
        $q->where(function ($qb) {
            $qb->whereNull('ps.is_sold_leased')
                ->orWhere('ps.is_sold_leased', '!=', 1);
        });
        $q->whereNotIn('ps.status', ['rented', 'sold', 'to-be-available']);
        $q->whereNull('ps.deleted_at');
        // $q->whereNotIn('ps.created_by', [125]); // ignore the <NAME_EMAIL>

        // echo $q->toSql();
        // die;

        return $q->first();
    }

    public function cachedPropertyTypes()
    {
        return $this->propertyTypesService->getCachedTypes();
    }

    public function cachedPropertyTypesHashmap()
    {
        return Cache::remember('cachedPropertyTypesHashmap', 24 * 3600, function () {
            $map = [];
            $allTypes = PropertyType::where('used', '=', 1)->get();
            foreach ($allTypes as $t) {
                $map[$t->url_value] = [
                    'id' => $t->id,
                    'filter_value' => $t->filter_value,
                    'url_value' => $t->url_value,
                ];
            }

            return $map;
        });
    }

    public function cachedGeographiesMap($locale = Language::EN)
    {
        return Cache::remember('cachedGeographiesMap_' . $locale, Timings::MONTH, function () use ($locale) {
            return $this->geographyService->getCachedGeographiesMapWithTranslations($locale);
        });
    }

    public function mapSnapshotModelToListItem($item, $imageTemplateName = 'list-item', $locale = Language::EN)
    {
        $userCurrency = request()->cookie('userCurrency', 'qar');
        $listItem = [];
        $currencyPrice = $userCurrency === 'qar' ? $item->price : $this->lastRates->{$userCurrency} * $item->price;
        $itemPrice = $item->price;
        $item->currency = strtoupper($userCurrency);

        $images = $this->prepareSnapshotImages($item->images, $item->primary_image, $imageTemplateName);
        $imageAlts = prepareImageAlts($item->image_alts);
        foreach ($images as $index => $imagePayload) {
            $imageAlt = "";
            if (array_key_exists($index, $imageAlts) && !empty($imageAlts[$index])) {
                $imageAlt = $imageAlts[$index];
            } else {
                $imageAlt = resolveListingImageAlt($item, "", $index + 1);
            }
            $images[$index]['alt'] = $imageAlt;
        }

        $cachedPropertyTypesHashmap = $this->cachedPropertyTypesHashmap();

        $routeListingItem = new RouteListingItem();
        $routeListingItem->id = $item->id;
        $routeListingItem->ref_no = $item->ref_no;
        $routeListingItem->ad_type = $item->ad_type;
        $routeListingItem->geography_slug = '';
        $routeListingItem->bedrooms = intval($item->bedrooms) ?? 0;

        $cachedGeographies = $this->cachedGeographiesMap($locale);
        // dd($cachedGeographies);
        foreach ($cachedGeographies as $geoSlug => $geoData) {
            if ($geoData['id'] == $item->location_id) {
                $usedSlug = $geoSlug;
                if ($locale == Language::AR) {
                    $slugParts = [];
                    if (!is_null($geoData['parent'])) {
                        if (!is_null($geoData['parent']['parent'])) {
                            $slugParts[] = $geoData['parent']['parent']['translation'][$locale]['slug'];
                        }
                        $slugParts[] = $geoData['parent']['translation'][$locale]['slug'];
                    }
                    $slugParts[] = $geoData['translation'][$locale]['slug'];
                    $usedSlug = implode("--", $slugParts);
                }

                $routeListingItem->geography_slug = $usedSlug;
            }
        }

        foreach ($cachedPropertyTypesHashmap as $_ => $propertyTypeData) {
            if ($propertyTypeData['id'] == $item->property_type_id) {
                $routeListingItem->property_type_url = $propertyTypeData['filter_value'];
            }
        }

        $listItem['title'] = $item->title;
        $listItem['id'] = $item->listing_id;
        $listItem['asset_id'] = $item->asset_id;
        $listItem['ref_no'] = $item->ref_no;
        $listItem['is_exclusive'] = $item->is_exclusive;
        $listItem['property_type'] = $item->property_type;
        $listItem['property_type_id'] = $item->property_type_id;
        $listItem['geo_lat'] = (float)$item->geo_lat;
        $listItem['geo_lon'] = (float)$item->geo_lon;
        $listItem['images'] = $images;
        $listItem['primary_image'] = $item->primary_image;
        $listItem['ad_type'] = $item->ad_type;
        $listItem['location'] = $item->location;
        $listItem['location_parent'] = $item->location_parent;
        $listItem['country_name'] = $item->country_name;
        $listItem['bedrooms'] = $item->bedrooms;
        $listItem['bathrooms'] = $item->bathrooms;
        $listItem['built_up_area'] = (float)$item->built_up_area;
        $listItem['furnishings'] = $item->furnishings;
        $listItem['currency'] = strtoupper($item->currency);
        $listItem['currency_price'] = (float)$currencyPrice;
        $listItem['price'] = (float) $itemPrice;
        $listItem['price_on_request'] = $item->price_on_request;
        $listItem['url'] = MenuHelperService::createListingURL($routeListingItem, $locale);
        $listItem['JSONLDScript'] = $this->getJSONLDScriptForItem($item);
        $listItem['is_sold_leased'] = $item->is_sold_leased;
        $listItem['amenities'] = [];
        $listItem['offplan'] = $item->offplan;
        $listItem['tour_360'] = $item->tour_360;
        $listItem['title_deed'] = $item->title_deed;
        $listItem['minimum_contract'] = $item->minimum_contract;
        $listItem['agent_prefix_phone'] = $item->agent_prefix_phone;
        $listItem['agent_phone'] = $item->agent_phone;
        $listItem['agent_email'] = $item->agent_email;
        $listItem['should_use_whatsapp_chatbot'] = $item->should_use_whatsapp_chatbot;
        $listItem['agent_complete_phone'] = getCompletePhoneNo($item->agent_prefix_phone, $item->agent_phone);
        $listItem['agent_complete_whatsapp_phone'] = /*!!$item->should_use_whatsapp_chatbot ? env('WHATSAPP_CHATBOT_PHONE_NO') : */ $listItem['agent_complete_phone'];
        $listItem['is_short_stay'] = !!$item->is_short_stay;

        if (isset($item->grouped_amenities) && !is_null($item->grouped_amenities)) {
            $amenitiesPairs = explode("~~", $item->grouped_amenities);
            foreach ($amenitiesPairs as $amenityPair) {
                $parts = explode("||", $amenityPair);
                $listItem['amenities'][] = (object)["id" => (float)$parts[0], "name" => $parts[1]];
            }
        }

        if ($locale != 'en') {
            $translatableFields = [
                'translated_title' => 'title',
                'loc_translated_name' => 'location',
                'locp_translated_name' => 'location_parent'
            ];
            foreach ($translatableFields as $transField => $field) {
                if (!empty($item->{$transField})) {
                    $listItem[$field] = $item->{$transField};
                }
            }
        }

        return $listItem;
    }

    public function mapSnapshotModelToMobileListItem($item, $imageTemplateName = 'list-item', $locale = Language::EN)
    {
        $userCurrency = session()->get('userCurrency', 'qar');
        $listItem = [];

        $currencyPrice = $userCurrency === 'qar' ? $item->price : $this->lastRates->{$userCurrency} * $item->price;
        $itemPrice = $item->price;
        $item->currency = strtoupper($userCurrency);

        $images = $this->prepareSnapshotImages($item->images, $item->primary_image, $imageTemplateName);
        $imageAlts = prepareImageAlts($item->image_alts);
        foreach ($images as $index => $imagePayload) {
            $images[$index]['alt'] = array_key_exists($index, $imageAlts) ? $imageAlts[$index] : '';
        }

        $cachedPropertyTypesHashmap = $this->cachedPropertyTypesHashmap();

        $routeListingItem = new RouteListingItem();
        $routeListingItem->id = $item->id;
        $routeListingItem->ref_no = $item->ref_no;
        $routeListingItem->ad_type = $item->ad_type;
        $routeListingItem->geography_slug = '';
        $routeListingItem->bedrooms = intval($item->bedrooms) ?? 0;

        $cachedGeographies = $this->cachedGeographiesMap($locale);
        // dd($cachedGeographies);
        foreach ($cachedGeographies as $geoSlug => $geoData) {
            if ($geoData['id'] == $item->location_id) {
                $usedSlug = $geoSlug;
                if ($locale == Language::AR) {
                    $slugParts = [];
                    if (!is_null($geoData['parent'])) {
                        if (!is_null($geoData['parent']['parent'])) {
                            $slugParts[] = $geoData['parent']['parent']['translation'][$locale]['slug'];
                        }
                        $slugParts[] = $geoData['parent']['translation'][$locale]['slug'];
                    }
                    $slugParts[] = $geoData['translation'][$locale]['slug'];
                    $usedSlug = implode("--", $slugParts);
                }

                $routeListingItem->geography_slug = $usedSlug;
            }
        }

        foreach ($cachedPropertyTypesHashmap as $_ => $propertyTypeData) {
            if ($propertyTypeData['id'] == $item->property_type_id) {
                $routeListingItem->property_type_url = $propertyTypeData['filter_value'];
            }
        }

        $listItem['title'] = $item->title;
        $listItem['id'] = $item->listing_id;
        $listItem['asset_id'] = $item->asset_id;
        $listItem['ref_no'] = $item->ref_no;
        $listItem['is_exclusive'] = $item->is_exclusive;
        $listItem['property_type'] = $item->property_type;
        $listItem['property_type_id'] = $item->property_type_id;
        $listItem['geo_lat'] = (float)$item->geo_lat;
        $listItem['geo_lon'] = (float)$item->geo_lon;
        $listItem['images'] = $images;
        $listItem['primary_image'] = $item->primary_image;
        $listItem['ad_type'] = $item->ad_type;
        $listItem['location'] = $item->location;
        $listItem['location_parent'] = $item->location_parent;
        // $listItem['country_name'] = $item->country_name;
        $listItem['bedrooms'] = $item->bedrooms;
        $listItem['bathrooms'] = $item->bathrooms;
        $listItem['built_up_area'] = (float)$item->built_up_area;
        // $listItem['furnishings'] = $item->furnishings;
        $listItem['currency'] = strtoupper($item->currency);
        $listItem['currency_price'] = (float)$currencyPrice;
        $listItem['price'] = (float) $itemPrice;
        $listItem['price_on_request'] = $item->price_on_request == 1 ? 1 : 0;
        $listItem['url'] = MenuHelperService::createListingURL($routeListingItem, $locale);
        // $listItem['JSONLDScript'] = $this->getJSONLDScriptForItem($item);
        $listItem['is_sold_leased'] = $item->is_sold_leased;
        // $listItem['amenities'] = [];
        $listItem['offplan'] = $item->offplan;
        $listItem['minimum_contract'] = $item->minimum_contract;
        $listItem['saved_by_user'] = $item->saved_by_user;

        // if (isset($item->grouped_amenities) && !is_null($item->grouped_amenities)) {
        //     $amenitiesPairs = explode("~~", $item->grouped_amenities);
        //     foreach ($amenitiesPairs as $amenityPair) {
        //         $parts = explode("||", $amenityPair);
        //         $listItem['amenities'][] = (object)["id" => (float)$parts[0], "name" => $parts[1]];
        //     }
        // }

        if ($locale != 'en') {
            $translatableFields = [
                'translated_title' => 'title',
                'loc_translated_name' => 'location',
                'locp_translated_name' => 'location_parent'
            ];
            foreach ($translatableFields as $transField => $field) {
                if (!empty($item->{$transField})) {
                    $listItem[$field] = $item->{$transField};
                }
            }
        }

        return $listItem;
    }

    public function sortSnapshotImages($imagesArray, $primaryImage)
    {
        $imagesArray = is_array($imagesArray) ? $imagesArray : json_decode($imagesArray ?? "[]");
        if (empty($primaryImage)) {
            return $imagesArray;
        }

        if (!empty($primaryImage)) {
            $primaryImagePos = array_search($primaryImage, $imagesArray);
            if ($primaryImagePos > 0) {
                unset($imagesArray[$primaryImagePos]);
                array_unshift($imagesArray, $primaryImage);
            }
        }

        return $imagesArray;
    }

    public function sortSnapshotImageALTs($imageALTsArray, $primaryImage)
    {
        $imageALTsArray = is_array($imageALTsArray) ? $imageALTsArray : json_decode($imageALTsArray ?? "[]");
        if (empty($primaryImage)) {
            return $imageALTsArray;
        }

        if (!empty($primaryImage)) {
            $primaryImageALTPos = array_search($primaryImage, $imageALTsArray);
            if ($primaryImageALTPos > 0) {
                unset($imageALTsArray[$primaryImageALTPos]);
                array_unshift($imageALTsArray, $primaryImage);
            }
        }

        return $imageALTsArray;
    }

    public function prepareSnapshotImages($imagesArray, $primaryImage, $templateName = 'list-item')
    {
        $imagesArray = $this->sortSnapshotImages($imagesArray, $primaryImage);

        return array_map(function ($imgName) use ($primaryImage, $templateName) {
            $fileInfo = pathinfo($imgName);
            if ($fileInfo['extension'] === 'webp') {
                $imgName = str_replace('.webp', '.jpg', $imgName);
            }
            return [
                'img_url' => imageRoute($templateName, $imgName),
                'is_primary' => $imgName === $primaryImage
            ];
        }, $imagesArray);
    }

    private function getJSONLDScriptForItem($item)
    {
        if (in_array($item->property_type, ['Apartment', 'Villa', 'Penthouse', 'Townhouse', 'Challet', 'Hotel Apart', 'Villa compound'])) {
            $schemaItem = new SingleFamilyResidence();
        }
        //        elseif() {
        //
        //        }
        $localBusiness = new SingleFamilyResidence();
        $localBusiness->name($item->title);
        if (!empty($item->geo_lat) && !empty($item->geo_lon)) {
            $geo = new GeoCoordinates();
            $geo->latitude($item->geo_lat);
            $geo->longitude($item->geo_lon);
        }

        //        dd(get_class_methods($localBusiness));

        $localBusiness->contactPoint((new ContactPoint())->areaServed('Worldwide'));
    }

    public function getPropertyTypes($filterFn = null)
    {
        $propertyTypes = $this->cachedPropertyTypes();

        if (!is_null($filterFn)) {
            $propertyTypes = $propertyTypes->filter($filterFn);
        }

        return $propertyTypes->toArray();
    }

    private function isValidMapBound($supposedBoundObject): bool
    {
        $obj = json_decode($supposedBoundObject);
        return
            isset($obj->northeast)
            && isset($obj->southwest)
            && isset($obj->northeast->lat)
            && isset($obj->northeast->lng)
            && isset($obj->southwest->lat)
            && isset($obj->southwest->lng)
            && $obj->northeast->lat > -90
            && $obj->northeast->lat < 90
            && $obj->northeast->lng > -180
            && $obj->northeast->lng < 180
            && $obj->southwest->lat > -90
            && $obj->southwest->lat < 90
            && $obj->southwest->lng > -180
            && $obj->southwest->lng < 180;
    }

    public function getNextRefNo($propertyTypeId, $adType)
    {
        $recNos = DB::select("SELECT ref_no FROM properties WHERE ad_type = ? AND property_type_id = ? ", [$adType, $propertyTypeId]);
        $maxCurrNo = 0;
        for ($i = 0; $i < count($recNos); $i++) {
            $parts = explode("-", $recNos[$i]->ref_no);
            if (count($parts) === 3) {
                $no = $parts[1];
                $intPart = intval($no);
                if ($intPart > 0 && $intPart > $maxCurrNo) {
                    $maxCurrNo = $intPart;
                }
            }
        }

        $refPrefix = $this->REF_PREFIXES[$propertyTypeId] . \strtoupper(substr($adType, 0, 1)) . "-";
        $nextRefNoCount = $maxCurrNo + 1;

        return $refPrefix . sprintf('%06d', $nextRefNoCount);
    }

    public function getPrefixForPropertyTypeId($propertyTypeId)
    {
        return $this->REF_PREFIXES[$propertyTypeId] ?? null;
    }

    public function getProfileSavedProperties($profile)
    {
        $savedPropertyIds = DB::table('user_x_saved_properties as uxsp')
            ->where('uxsp.user_id', '=', $profile->id)
            ->get()
            ->map(function ($item) {
                return $item->property_id;
            })
            ->toArray();

        return $this->searchLiteMobile(['ids' => $savedPropertyIds]);
    }

    public function addProfileSavedProperties($profile, $propertyId)
    {
        $exists = true;

        $record = DB::table('user_x_saved_properties')
            ->where('user_id', '=', $profile->id)
            ->where('property_id', '=', $propertyId)
            ->first();


        if (is_null($record)) {
            $exists = false;
            DB::table('user_x_saved_properties')->insert([
                'user_id'  => $profile->id,
                'property_id' => $propertyId
            ]);
        }

        return $exists;
    }

    public function deleteProfileSavedProperties($profile, $propertyId)
    {
        $exists = false;

        $record = DB::table('user_x_saved_properties')
            ->where('user_id', '=', $profile->id)
            ->where('property_id', '=', $propertyId)
            ->first();

        if (!is_null($record)) {
            $exists = true;
            DB::table('user_x_saved_properties')
                ->where('user_id', '=', $profile->id)
                ->where('property_id', '=', $propertyId)
                ->delete();
        }

        return $exists;
    }

    public function getProperty($propertyId)
    {
        $property = Property::with(['location', 'country', 'refferedByAgent', 'contact', 'representative', 'representative.nationality', 'theTower', 'propertyViews', 'operationHistory', 'operationHistory.author'])->find($propertyId);
        return $property;
    }

    public function getNewItem()
    {
        $asset = new Asset();
        $asset->asset_definition_id = 1;
        $asset->save();
        $item = new Property();
        $item->created_by = auth()->user()->id;
        $item->publishing_status = ListingPublishingStatus::STATUS_DRAFT;
        $item->asset_id = $asset->id;

        return $item;
    }

    public function getDbItem($assetId)
    {
        $property = Property::with([
            'contact',
            'operationHistory' => fn($qb) =>
            $qb->orderBy('id', 'DESC'),
            'translations',
            'representative',
            'author'
        ])
            ->where('asset_id', '=', $assetId)
            ->withTrashed()
            ->first();

        if (!is_null($property) && !is_null($property->geo_point)) {
            // the coordinates are entered inverted
            $property->geo_lat = $property->geo_point->getLat();
            $property->geo_lon = $property->geo_point->getLng();
        }

        return $property;
    }

    public function getMinMaxPrices()
    {
        $prices = [
            'rent' => [
                'min' => [],
                'max' => []
            ],
            'buy' => [
                'min' => [],
                'max' => []
            ]
        ];

        foreach ($this->minRentPrices as $no) {
            $prices['rent']['min'][$no] = number_format($no, 0);
        }
        foreach ($this->maxRentPrices as $no) {
            $prices['rent']['max'][$no] = number_format($no, 0);
        }

        foreach ($this->minSalePrices as $no) {
            $prices['buy']['min'][$no] = number_format($no, 0);
        }
        foreach ($this->maxSalePrices as $no) {
            $prices['buy']['max'][$no] = number_format($no, 0);
        }

        return $prices;
    }

    public function getMinimumContractOptions()
    {
        return [
            (object)['value' => "", 'label' => 'Please select'],
            (object)['value' => 0.01, 'label' => '1 week'],
            (object)['value' => 0.02, 'label' => '2 weeks'],
            (object)['value' => 0.03, 'label' => '3 weeks'],
            (object)['value' => 0.1, 'label' => '1 month'],
            (object)['value' => 0.6, 'label' => '6 month'],
            (object)['value' => 1, 'label' => '1 year'],
            (object)['value' => 2, 'label' => '2 years'],
            (object)['value' => 3, 'label' => '3 years'],
            (object)['value' => 4, 'label' => '4 years']
        ];
    }

    private function getLiveConn()
    {
        return DB::connection('mysql_live');
    }

    public function getListingsWithOldAttributes()
    {
        $attributes = $this->getLiveConn()->select(
            DB::raw("
                SELECT
                    p.id,
                    p.ref_no,
                    p.title,
                    pt.name as property_type,
                    GROUP_CONCAT(CONCAT(ad.name, '---', COALESCE(a.value, '__')) SEPARATOR '||') as grp
                FROM
                    attributes a
                    JOIN properties p ON p.asset_id = a.asset_id
                    JOIN property_types pt ON p.property_type_id = pt.id
                    JOIN attribute_definitions ad ON ad.id = a.attribute_definition_id
                WHERE
                    p.ref_no IS NOT NULL AND
                    p.deleted_at IS NULL AND
                    a.attribute_definition_id IN (SELECT id from attribute_definitions WHERE name IN ('bedrooms', 'bathrooms', 'build-up-area', 'build-year'))
                GROUP BY p.id
                ORDER BY p.id DESC
                LIMIT 1000
            ")
        );

        return $attributes;
    }

    public function getListingsWithNewAttributes()
    {
        $attributes = DB::select(
            DB::raw("
                SELECT
                    p.id,
                    p.ref_no,
                    p.title,
                    pt.name as property_type,
                    GROUP_CONCAT(CONCAT(ad.name, '---', COALESCE(a.value, '__')) SEPARATOR '||') as grp
                FROM
                    attributes a
                    JOIN properties p ON p.asset_id = a.asset_id
                    JOIN property_types pt ON p.property_type_id = pt.id
                    JOIN attribute_definitions ad ON ad.id = a.attribute_definition_id
                WHERE
                    p.ref_no IS NOT NULL AND
                    p.deleted_at IS NULL AND
                    a.attribute_definition_id IN (SELECT id from attribute_definitions WHERE name IN ('property-features-bedrooms', 'property-features-bathrooms', 'property-features-build-up-area', 'property-features-construction-year'))
                GROUP BY p.id
                ORDER BY p.id DESC
                LIMIT 1000
            ")
        );

        return $attributes;
    }

    public function getNearbyListings($lat, $lng, $distanceKilometers = 1 /* kilometers */)
    {
        $nearbyListings = DB::select(
            DB::raw("
                SELECT title, ref_no, price, distance FROM (
                    SELECT *,
                        (
                            (
                                (
                                    acos(
                                        sin(( $lat * pi() / 180))
                                        *
                                        sin(( `geo_lat` * pi() / 180)) + cos(( $lat * pi() /180 ))
                                        *
                                        cos(( `geo_lat` * pi() / 180)) * cos((( $lng - `geo_lon`) * pi()/180)))
                                ) * 180/pi()
                            ) * 60 * 1.1515 * 1.609344
                        )
                    as distance FROM `property_snapshots`
                ) property_snapshots
                WHERE distance <= $distanceKilometers
                LIMIT 15;
            ")
        );

        return $nearbyListings;
    }

    public function getExportQueueItems(ExportPlatform $exportPlatform)
    {
        Cache::forget(CacheKeys::ATTRIBUTE_DEFINITION_IDS);
        $attributeDefinitionIds = $this->getAttributeDefinitionIds();
        $allAmenitiesIds = $this->amenitiesService->getListingAmenitiesOptions()->map(function ($item) {
            return $item->id;
        })->toArray();

        $qb = DB::table('property_snapshots as ps')
            ->leftJoin('property_types as pt', 'pt.id', '=', 'ps.property_type_id')
            ->leftJoin('snapshot_export_assignments as sea', 'sea.snapshot_id', '=', 'ps.id')
            ->leftJoin('geography as loc', 'loc.id', '=', 'ps.location_id')
            ->leftJoin('geography as locp', 'locp.id', '=', 'loc.parent_id')
            ->leftJoin('geography as locpp', 'locpp.id', '=', 'locp.parent_id')
            ->leftJoin('users as u', 'u.id', '=', 'ps.created_by')
            ->leftJoin('users as bla', 'bla.id', '=', 'u.brokerage_license_account_id')
            ->leftJoin("snapshot_attributes AS be_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("be_attr.asset_id", "=", "ps.asset_id")
                    ->where("be_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BEDROOMS]);
            })
            ->leftJoin("snapshot_attributes AS ba_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("ba_attr.asset_id", "=", "ps.asset_id")
                    ->where("ba_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BATHROOMS]);
            })
            ->leftJoin("snapshot_attributes AS bua_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("bua_attr.asset_id", "=", "ps.asset_id")
                    ->where("bua_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_BUILD_UP_AREA]);
            })
            ->leftJoin("snapshot_attributes AS furn_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("furn_attr.asset_id", "=", "ps.asset_id")
                    ->where("furn_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS]);
            })
            ->leftJoin("attribute_value_lists AS furn_attr_value_lists", function ($join) use ($attributeDefinitionIds) {
                $join->on("furn_attr_value_lists.name", "=", "furn_attr.value")
                    ->where("furn_attr_value_lists.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS]);
            })
            ->leftJoin("snapshot_attributes AS furn_attr_office", function ($join) use ($attributeDefinitionIds) {
                $join->on("furn_attr_office.asset_id", "=", "ps.asset_id")
                    ->where("furn_attr_office.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS_OFFICE]);
            })
            ->leftJoin("attribute_value_lists AS furn_attr_office_value_lists", function ($join) use ($attributeDefinitionIds) {
                $join->on("furn_attr_office_value_lists.name", "=", "furn_attr_office.value")
                    ->where("furn_attr_office_value_lists.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_FURNISHINGS_OFFICE]);
            })
            ->leftJoin("snapshot_attributes AS desc_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("desc_attr.asset_id", "=", "ps.asset_id")
                    ->where("desc_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::DESCRIPTION]);
            })
            ->leftJoin("snapshot_attributes AS constr_attr", function ($join) use ($attributeDefinitionIds) {
                $join->on("constr_attr.asset_id", "=", "ps.asset_id")
                    ->where("constr_attr.attribute_definition_id", '=', $attributeDefinitionIds[AttributeDefinition::PROPERTY_FEATURES_CONSTRUCTION_YEAR]);
            })
            ->leftJoin("snapshot_attributes AS amenities", function ($join) use ($allAmenitiesIds) {
                $join->on("amenities.asset_id", "=", "ps.asset_id")
                    ->whereIn("amenities.attribute_definition_id", $allAmenitiesIds);
            })
            ->leftJoin("attribute_definitions AS sad", function ($join) {
                $join->on("sad.id", "=", "amenities.attribute_definition_id");
            })
            ->leftJoin("countries AS cou", function ($join) {
                $join->on("cou.id", "=", "ps.country_id");
            })
            ->where('sea.export_platform_id', $exportPlatform->id)
            ->where('ps.price_on_request', '!=', '1')
            ->where('ps.is_sold_leased', '!=', '1')
            ->where('ps.is_investment_opportunity', '!=', 1)
            ->where('ps.publishing_status', '=', ListingPublishingStatus::STATUS_PUBLISHED)
            ->whereNotIn('ps.status', ['rented', 'sold', 'to-be-available'])
            ->whereNull('ps.deleted_at')
            // do not show short stay
            ->where(function ($qb) {
                $qb->whereNull('ps.minimum_contract')
                    ->orWhereNotIn('ps.minimum_contract', ['0.01', '0.02', '0.03', '0.1']);
            })
            ->groupBy('ps.id')
            ->select([
                'ps.id',
                'ps.title',
                'ps.ref_no',
                'ps.ad_type',
                'ps.listing_id',
                'pt.id as property_type_id',
                'pt.name as property_type',
                'ps.created_at',
                'ps.updated_at',
                'ps.price',
                'ps.payment_term',
                'ps.is_featured',
                'ps.images',
                'ps.primary_image',
                'ps.geo_lat',
                'ps.geo_lon',
                'ps.tour_360',
                'ps.embeed_youtube',
                'ps.available_when',
                'ps.images',
                'ps.price_on_request',
                'ps.is_sold_leased',
                'ps.offplan',
                'ps.handover_date',
                'ps.brochure_path',
                'ps.brochure_title',
                'ps.address',
                'bua_attr.value as build_up_area',
                'ba_attr.value as bathrooms',
                'be_attr.value as bedrooms',
                'furn_attr.value as furnishing',
                'furn_attr_value_lists.label as furnishing_label',
                'furn_attr_office.value as furnishing_office',
                'furn_attr_office_value_lists.label as furnishing_office_label',
                'constr_attr.value as construction_year',
                'desc_attr.value_large as description',
                'loc.name as location',
                'locp.name as location_parent',
                'locpp.name as location_gparent',
                'u.name as agent_name',
                'u.id as agent_id',
                DB::raw('IF(bla.id IS NOT NULL, bla.prefix_phone, u.prefix_phone) as agent_prefix_phone'),
                DB::raw('IF(bla.id IS NOT NULL, bla.phone, u.phone) as agent_phone'),
                DB::raw('IF(bla.id IS NOT NULL, bla.email, u.email) as agent_email'),
                'u.profile_image as agent_profile_image',
                'cou.name as country_name',
                DB::raw('GROUP_CONCAT(COALESCE(sad.label, "")) as amenities_group')
            ]);

        return $qb->get();
    }

    public function movePropertiesToUser($fromUserId, $toUserId)
    {
        DB::table('properties')
            ->where('created_by', $fromUserId)
            ->update(['created_by' => $toUserId]);
        DB::table('property_snapshots')
            ->where('created_by', $fromUserId)
            ->update(['created_by' => $toUserId]);
    }

    public function generateSnapshotsStats()
    {
        $hotelApartTypeId = 17;
        $apartmentTypeId  = 1;
        $qb = DB::table('property_snapshots as ps')
            ->select('ps.ad_type', 'ps.country_id', 'ps.property_type_id', 'ps.location_id', 'ps.minimum_contract', 'pg.id as parent_location_id', 'ppg.id as parent_parent_location_id')
            ->join('geography as g', 'ps.location_id', '=', 'g.id')
            ->leftJoin('geography as pg', 'g.parent_id', '=', 'pg.id')
            ->leftJoin('geography as ppg', 'pg.parent_id', '=', 'ppg.id')
            ->whereNull('ps.deleted_at')
            ->where('ps.publishing_status', 'published')
            ->where(function ($qb) {
                $qb->whereNull('ps.price_on_request')
                    ->orWhere('ps.price_on_request', '!=', 1);
            })
            ->where(function ($qb) {
                $qb->whereNull('ps.is_sold_leased')
                    ->orWhere('ps.is_sold_leased', '!=', 1);
            })
            ->whereNotIn('ps.status', ['rented', 'sold', 'to-be-available']);

        if (env('APP_ENV') == 'production') {
            $qb = $qb->whereNotIn('ps.created_by', [125]); // ignore the <NAME_EMAIL>
        }

        $dbSnapshots = $qb->get();

        $cachedGeographies = $this->cachedGeographiesMap();

        $stats = [];
        $countryNumbers = [];
        $cityNumbers = [];
        foreach ($dbSnapshots as $snapshot) {
            if (empty($snapshot->is_investment_opportunity) && (empty($snapshot->country_id) || $snapshot->country_id == 187)) {
                $adType = $snapshot->ad_type;
                $propertyTypeId = $snapshot->property_type_id;
                $locationId = $snapshot->location_id;
                $parentLocationId = $snapshot->parent_location_id;
                $parentParentLocationId = $snapshot->parent_parent_location_id;
                $isShortStay = $adType == 'rent' && in_array($snapshot->minimum_contract, ['0.01', '0.02', '0.03', '0.1']);

                if (!isset($stats[$adType])) {
                    $stats[$adType] = [
                        'property_types' => [
                            'short_stay' => 0,
                        ],
                        'locations' => []
                    ];
                }
                if (!isset($stats[$adType]['property_types'][$propertyTypeId])) {
                    $stats[$adType]['property_types'][$propertyTypeId] = 0;
                }
                if ($propertyTypeId == $hotelApartTypeId) {
                    if (!isset($stats[$adType]['property_types'][$apartmentTypeId])) {
                        $stats[$adType]['property_types'][$apartmentTypeId] = 0;
                    }
                }

                // location
                // $geo = null;
                foreach ($cachedGeographies as $cachedGeo) {
                    if ($cachedGeo['id'] == $locationId) {
                        // $geo = $cachedGeo;
                        break;
                    }
                }
                foreach ([$locationId, $parentLocationId, $parentParentLocationId] as $lId) {
                    if (!empty($lId)) {
                        if (!isset($stats[$adType]['locations'][$lId])) {
                            $stats[$adType]['locations'][$lId] = [
                                'total' => 0,
                                'short_stay' => 0,
                            ];
                        }
                        if (!isset($stats[$adType]['locations'][$lId][$propertyTypeId])) {
                            $stats[$adType]['locations'][$lId][$propertyTypeId] = 0;
                        }
                        if ($propertyTypeId == $hotelApartTypeId) {
                            if (!isset($stats[$adType]['locations'][$lId][$apartmentTypeId])) {
                                $stats[$adType]['locations'][$lId][$apartmentTypeId] = 0;
                            }
                        }
                        //     }
                        // }
                        // foreach([$locationId, $parentLocationId, $parentParentLocationId] as $lId) {
                        //     if(!empty($lId)) {
                        if ($isShortStay) {
                            if ($locationId === $lId) {
                                $stats[$adType]['property_types']['short_stay']++;
                            }
                            $stats[$adType]['locations'][$lId]['short_stay']++;
                            // $stats[$adType]['locations'][$lId]['total']++;
                        } else {
                            if ($locationId === $lId) {
                                $stats[$adType]['property_types'][$propertyTypeId]++;
                            }
                            $stats[$adType]['locations'][$lId][$propertyTypeId]++;
                            $stats[$adType]['locations'][$lId]['total']++;

                            if ($propertyTypeId == $hotelApartTypeId) {
                                if ($locationId === $lId) {
                                    $stats[$adType]['property_types'][$apartmentTypeId]++;
                                }
                                $stats[$adType]['locations'][$lId][$apartmentTypeId]++;
                                $stats[$adType]['locations'][$lId]['total']++;
                            }
                        }
                    }
                }
            } else {
                if (!isset($countryNumbers[$snapshot->country_id])) {
                    $countryNumbers[$snapshot->country_id] = 0;
                }
                if (!isset($cityNumbers[$snapshot->location_id])) {
                    $cityNumbers[$snapshot->location_id] = 0;
                }
                $cityNumbers[$snapshot->location_id] += 1;
                $countryNumbers[$snapshot->country_id] += 1;
            }
        }
        $stats['countryNumbers'] = $countryNumbers;
        $stats['cityNumbers'] = $cityNumbers;

        PropertySnapshotsStats::create([
            'stat' =>  json_encode($stats)
        ]);
        Log::info('Snapshot stats successfully generated');
        Cache::forget(CacheKeys::SNAPSHOT_STATS);
        Cache::forget(CacheKeys::SNAPSHOT_FULL_STATS);
    }

    function getFullLastStatsRecord()
    {
        $statsJsonString = Cache::remember(CacheKeys::SNAPSHOT_FULL_STATS, 12 * Timings::MONTH, function () {
            $dbData = DB::table('property_snapshots_stats')
                ->orderBy('id', 'DESC')
                ->first();

            if (is_null($dbData)) {
                $statsObject = [];
            } else {
                $statsObject = $dbData->stat;
                if (is_string($statsObject)) {
                    $statsObject = json_decode($statsObject);
                }
            }
            return $statsObject;
        });

        return $statsJsonString;
    }

    function getLastStatsRecord($opType, $locationId, $propertyTypeId = null, $isShortStay = false)
    {
        $statsJsonString = Cache::remember(CacheKeys::SNAPSHOT_STATS, Timings::MONTH, function () {
            return DB::table('property_snapshots_stats')
                ->orderBy('id', 'DESC')
                ->first();
        });
        if (is_null($statsJsonString)) {
            return [];
        } else {
            $statsObject = $statsJsonString->stat;
            if (is_string($statsObject)) {
                $statsObject = json_decode($statsObject);
            }
            // dd($statsObject);
            $cachedGeographies = $this->cachedGeographiesMap();
            $hotelApartTypeId = 17;
            $apartmentTypeId = 1;
            $propertyTypeVillaId = 2;
            $propertyTypeCommercialVillaId = 7;
            $propertyTypeCompoundVillaId = 18;

            if ($isShortStay) {
                return [];
            } else if (isset($statsObject->$opType)) {
                if (isset($statsObject->$opType->locations)) {
                    if (!empty($locationId)) {
                        if (!empty($propertyTypeId)) {
                            $locationChildrenIds = [];
                            foreach ($cachedGeographies as $geoData) {
                                if (!is_null($geoData['parent']) && $geoData['parent']['id'] == $locationId) {
                                    $locationChildrenIds[] = $geoData['id'];
                                }
                            }
                            $retObj = [];
                            if (isset($statsObject->$opType->locations->$locationId->$propertyTypeId)) {
                                if (count($locationChildrenIds) > 0) {
                                    foreach ($locationChildrenIds as $geoId) {
                                        if (isset($statsObject->$opType->locations->$geoId)) {
                                            $retObj[$geoId] = 0;
                                            if (isset($statsObject->$opType->locations->$geoId->$propertyTypeId)) {
                                                $retObj[$geoId] = $statsObject->$opType->locations->$geoId->$propertyTypeId;
                                            }
                                            if ($retObj[$geoId] == 0) {
                                                unset($retObj[$geoId]);
                                            }
                                        }
                                    }
                                } else {
                                    $retObj = [];
                                }
                            }
                            if ($propertyTypeId === $propertyTypeVillaId) {
                                foreach ([$propertyTypeCompoundVillaId, $propertyTypeCommercialVillaId] as $villaTempId) {
                                    if (isset($statsObject->$opType->locations->$locationId->$villaTempId)) {
                                        // get only direct children of that location
                                        if (count($locationChildrenIds) > 0) {
                                            // $retObj = [];
                                            foreach ($locationChildrenIds as $geoId) {
                                                // dd($geoId, $statsObject->$opType->locations->$geoId);
                                                if (isset($statsObject->$opType->locations->$geoId)) {
                                                    $retObj[$geoId] = $retObj[$geoId] ?? 0;
                                                    if (isset($statsObject->$opType->locations->$geoId->$villaTempId)) {
                                                        $retObj[$geoId] += $statsObject->$opType->locations->$geoId->$villaTempId;
                                                    }
                                                    if ($retObj[$geoId] == 0) {
                                                        unset($retObj[$geoId]);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            return $retObj;
                        }
                        if (isset($statsObject->$opType->locations->$locationId)) {
                            return $statsObject->$opType->locations->$locationId;
                        }
                    } else {
                        if (!empty($propertyTypeId)) { // partea asta e putin mai complicata - iteram peste obiectul de locations, iar daca locatia contine acel property type, o bagam intr-un array. Iteram doar peste parinti. Doha, Lusail etc
                            $locationsSelection = $statsObject->$opType->locations;
                            $parentGeographiesIds = [];
                            foreach ($cachedGeographies as $geoData) {
                                if (is_null($geoData['parent'])) {
                                    $parentGeographiesIds[$geoData['id']] = 1;
                                }
                            }
                            $results = [];
                            foreach ($locationsSelection as $locationId => $locationData) {
                                if (isset($parentGeographiesIds[$locationId])) {
                                    if (isset($locationData->$propertyTypeId)) {
                                        $results[$locationId] = $locationData->$propertyTypeId;
                                    }
                                    if ($opType == 'rent' && $propertyTypeId == $apartmentTypeId && isset($locationData->$hotelApartTypeId) && $locationData->short_stay > 0) {
                                        // if(!isset($results[$locationId])) {
                                        //     $results[$locationId] = $locationData->short_stay;
                                        // } else {
                                        //     $results[$locationId] += $locationData->short_stay;
                                        // }
                                    }
                                    if ($propertyTypeId === $propertyTypeVillaId) {
                                        foreach ([$propertyTypeCommercialVillaId, $propertyTypeCompoundVillaId] as $villaTypeId) {
                                            if (isset($locationData->$villaTypeId)) {
                                                if (!isset($results[$locationId])) {
                                                    $results[$locationId] = 0;
                                                }
                                                $results[$locationId] += $locationData->$villaTypeId;
                                            }
                                        }
                                    }
                                }
                            }
                            return (object) $results;
                        } else {
                            // skip hotel apart
                            $retObj = [];
                            foreach ($statsObject->$opType->property_types as $propertyTypeId => $count) {
                                if ($propertyTypeId != $hotelApartTypeId) {
                                    $retObj[$propertyTypeId] = $count;
                                }
                            }
                            return $retObj;
                        }
                    }
                }
            }
        }
        return [];
    }

    function getLastStatsForCountryNumbers()
    {
        $statsJsonString = Cache::remember(CacheKeys::SNAPSHOT_STATS, Timings::MONTH, function () {
            return DB::table('property_snapshots_stats')
                ->orderBy('id', 'DESC')
                ->first();
        });
        if (!is_null($statsJsonString) && isset($statsJsonString->stat)) {
            $statsObject = $statsJsonString->stat;
            if (is_string($statsObject)) {
                $statsObject = json_decode($statsObject);
            }
            return (array) $statsObject->countryNumbers;
        }
        return null;
    }

    function getLastStatsForCityNumbers()
    {
        $statsJsonString = Cache::remember(CacheKeys::SNAPSHOT_STATS, Timings::MONTH, function () {
            return DB::table('property_snapshots_stats')
                ->orderBy('id', 'DESC')
                ->first();
        });
        if (!is_null($statsJsonString) && isset($statsJsonString->stat)) {
            $statsObject = $statsJsonString->stat;
            if (is_string($statsObject)) {
                $statsObject = json_decode($statsObject);
            }
            return (array) $statsObject->cityNumbers;
        }
        return null;
    }

    public function getSimilarItems($searchParams, $locale = Language::EN)
    {
        $fullStatsObject = $this->getFullLastStatsRecord();
        $similarPropTypeId = null;
        $similarOpType = $searchParams[ModelsQueryParamsDef::OPERATION_TYPE];
        $similarPropType = $searchParams[ModelsQueryParamsDef::PROPERTY_TYPE] ?? null;

        if (!is_object($fullStatsObject)) {
            return [];
        }
        $opTypeStatsObject = $fullStatsObject->$similarOpType;

        $similarItems = [];
        if (!empty($similarPropType)) {
            $similarPropTypes = $this->propertyTypesService->getCachedTypes();
            $similarPropType = $similarPropTypes->where('url_value', $similarPropType)->first();
            if (!is_null($similarPropType)) {
                $similarPropTypeId = $similarPropType->id;
            }
        }

        if (array_key_exists(ModelsQueryParamsDef::BEDROOMS, $searchParams)) {
            if (isset($fullStatsObject->$similarOpType)) {
                $tempSearchParams = $searchParams;
                unset($tempSearchParams[ModelsQueryParamsDef::BEDROOMS]);
                if (isset($opTypeStatsObject->property_types->$similarPropTypeId) && $opTypeStatsObject->property_types->$similarPropTypeId > 0) {
                    $similarPaginator = $this->searchLite(
                        $tempSearchParams,
                        null,
                        ['p.updated_at', 'DESC'],
                        8,
                        $locale
                    );
                    $similarItems = $similarPaginator->items();
                }
            }
        }
        if (array_key_exists(ModelsQueryParamsDef::LOCATION_DATA, $searchParams)) {
            if (!is_array($searchParams[ModelsQueryParamsDef::LOCATION_DATA])) {
                // check parent
                $geographies = $this->geographyService->getCachedAllWithParent();
                $selectedGeo = $geographies->where('id', $searchParams[ModelsQueryParamsDef::LOCATION_DATA])->first();
                if (!is_null($selectedGeo)) {
                    $parentGeo = $selectedGeo->parent;
                    if (!is_null($parentGeo)) {
                        $parentGeoId = $parentGeo->id;
                        if (isset($opTypeStatsObject->locations->$parentGeoId) && isset($opTypeStatsObject->locations->$parentGeoId->$similarPropTypeId) && $opTypeStatsObject->locations->$parentGeoId->$similarPropTypeId > 0) {
                            //replace the location id with parent's
                            $tempSearchParams = $searchParams;
                            $tempSearchParams[ModelsQueryParamsDef::LOCATION_DATA] = $parentGeoId;
                            $similarPaginator = $this->searchLite(
                                $tempSearchParams,
                                null,
                                ['p.updated_at', 'DESC'],
                                8,
                                $locale
                            );
                            $similarItems = $similarPaginator->items();
                        }
                    }
                    if (count($similarItems) == 0) {
                        $tempSearchParams = $searchParams;
                        unset($tempSearchParams[ModelsQueryParamsDef::LOCATION_DATA]);
                        $similarPaginator = $this->searchLite(
                            $tempSearchParams,
                            null,
                            ['p.updated_at', 'DESC'],
                            8,
                            $locale
                        );
                        $similarItems = $similarPaginator->items();
                    }
                }
                // dd($searchParams);
            }
        }

        return $similarItems;
    }
}
