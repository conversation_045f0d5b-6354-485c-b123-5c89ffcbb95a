const mix = require("laravel-mix");
const tailwindcss = require("tailwindcss");
/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel applications. By default, we are compiling the CSS
 | file for the application as well as bundling up all the JS files.
 |
 */

const jsFiles = [
    "node_modules/glightbox/dist/js/glightbox.min.js",
    "resources/js/virtual-select.min.js",
    "resources/js/virtual-select.1033.min.js",
    "resources/js/collapsible-card.js",
    "resources/js/form-edit.js",
    "resources/js/crm/contact-lookup.js",
    "resources/js/jquery.ba-throttle-debounce.min.js",
    "resources/js/crm/leads/lead-create-form.js",
    "resources/js/lazysizes.min.js",
    "resources/js/jquery-2.2.0.min.js",
    "resources/js/datatables.min.js",
    "resources/js/dataTables.responsive.min.js",
    "resources/js/responsive.bootstrap.min.js",
    "resources/js/dataTables.select.min.js",
    "resources/js/bootstrap.datetimepicker.min.js",
    "resources/js/moment.min.js",
    "resources/js/fullcalendar.min.js",
    "resources/js/validator.min.js",
    "resources/js/jquery.form.min.js",
    "resources/js/project.js",
];

const brixJsFiles = ["resources/js/brix/form-validator.js"];

const crmJss = [
    "resources/js/crm/landlords/landlords-table.js",
    "resources/js/crm/landlords/landlords-corporate-table.js",
    "resources/js/crm/contacts-list/contacts-list-table.js",
    "resources/js/crm/inventory/inventory-table.js",
    "resources/js/crm/inventory/short-stay-table.js",
    "resources/js/crm/inventory/short-stay-bookings-table.js",
    "resources/js/crm/inventory/inventory-create.js",
    "resources/js/crm/magazine/magazine-table.js",
    "resources/js/crm/projects/marketing-projects-table.js",
    "resources/js/crm/developments/developments-table.js",
    "resources/js/crm/developments/developments-units-table.js",
    "resources/js/crm/geographies/geographies-table.js",
    "resources/js/crm/leads/leads-admin-table.js",
    "resources/js/crm/leads/leads-table.js",
    "resources/js/crm/leads-dashboard.js",
    "resources/js/crm/create-note-modal.js",
    "resources/js/crm/property-lookup.js",
    "resources/js/crm/leads-dashboard-notes.js",
    "resources/js/crm/leads-dashboard-close.js",
    "resources/js/crm/deal-create-form.js",
    "resources/js/crm/deals-table.js",
    "resources/js/crm/deals-invoices-table.js",
    "resources/js/crm/automatizations-table.js",
    "resources/js/crm/deals-dashboard.js",
    "resources/js/crm/contact-lookup-landlords.js",
    "resources/js/crm/global.js",
    "resources/js/helpers/datatables-action-menu.js",
];

const utilJss = [
    "resources/js/utils/transition-events.js",
    "resources/js/utils/time.js",
];

const adminJss = [
    "resources/js/admin/users/users-table.js",
    "resources/js/admin/users/users-rating-table.js",
    "resources/js/admin/marketing-platforms/marketing-platforms-table.js",
    "resources/js/admin/lead-source/lead-source-table.js",
    "resources/js/admin/companies/companies-table.js",
];

const cssFiles = [
    "node_modules/glightbox/dist/css/glightbox.min.css",
    "resources/css/virtual-select.min.css",
    "resources/css/virtual-select.1033.min.css",
    "resources/css/crm/crm-virtual-select.min.css",
    "resources/css/crm/listing.css",
    "resources/css/auth/signin.css",
    "resources/css/template-dashboard.css",
    "resources/css/bootstrap.datetimepicker.min.css",
    "resources/css/dataTables.bootstrap.min.css",
    "resources/css/responsive.bootstrap.min.css",
    "resources/css/fullcalendar.min.css",
    "resources/css/crm/leads-dashboard-notes.css",
    "resources/css/crm/calendar.css",
    "resources/css/crm/leads-dashboard-close.css",
    "resources/css/countries-flags.css",
];

mix.js("resources/js/vue/home.js", "public/js/home.js").minify("public/js/home.js");
mix.js("resources/js/vue/generic.js", "public/js/generic.js").minify("public/js/generic.js");
mix.ts("resources/ts/qnb-funding.ts", "public/js").minify("public/js/qnb-funding.js");

mix
    // .js("resources/js/app.js", "public/js")
    .js("resources/js/vue/search.js", "public/js/properties-search.js")
    .js("resources/js/vue/share-modal.js", "public/js/share-modal.js")
    .js("resources/js/vue/developments.js", "public/js/developments.js")
    .js("resources/js/vue/single.js", "public/js/single.js")
    
    .js(
        "resources/js/vue/crm/images-uploader-component.js",
        "public/js/images-uploader-component.js"
    )
    .js(
        "resources/js/vue/crm/inventory-landlord-controller.js",
        "public/js/inventory-landlord-controller.js"
    )
    .js(
        "resources/js/vue/crm/inventory-filters.js",
        "public/js/inventory-filters.js"
    )
    .js(
        "resources/js/vue/crm/lead-request-filters.js",
        "public/js/lead-request-filters.js"
    )
    .js("resources/js/vue/crm/leads-filters.js", "public/js/leads-filters.js")
    .js("resources/js/vue/crm/deals-filters.js", "public/js/deals-filters.js")
    .js("resources/js/vue/admin/users-filters.js", "public/js/users-filters.js")
    .js("resources/js/vue/crm/app-towers.js", "public/js/app-towers.js")
    .js("resources/js/vue/crm/seo-module.js", "public/js/seo-module.js")
    .js("resources/js/vue/crm/dynamic-menu.js", "public/js/dynamic-menu.js")
    .vue()
    .sass("resources/scss/index.scss", "public/css")
    .sass("resources/scss/search.scss", "public/css")
    .sass('resources/scss/cview-layout.scss', 'public/css')
    .sass('resources/scss/cview-nav-landing.scss', 'public/css')
    .sass('resources/scss/chat-widget-landing.scss', 'public/css')
    .options({
        postCss: [tailwindcss("./tailwind.config.js")],
    });

mix.copy('resources/js/plugins/speech-to-text.js', 'public/js/plugins');
mix.css('resources/css/plugins/speech-to-text.css', 'public/css/plugins');

mix.js("resources/js/map-with-all.js", "public/js");
mix.copy(
    "resources/js/crm/libs/fetch.js",
    "public/js/crm/libs/fetch.js"
).minify("public/js/crm/libs/fetch.js");
if (mix.inProduction()) {
    mix.version();
}

// // mix.copyDirectory("resources/images", "public/images");
mix
    .ts("resources/ts/contacts-list-with-router.ts", "public/js")
    .ts("resources/ts/contacts-list-form-lite.ts", "public/js")
    .ts("resources/ts/agent-rating.ts", "public/js")
    .ts("resources/ts/connect-with-agent.ts", "public/js")
    .ts("resources/ts/kpi-performance.ts", "public/js")
    .ts("resources/ts/agent-competition-container.ts", "public/js")
    .ts("resources/ts/dashboard-stats.ts", "public/js")
    .ts("resources/ts/listing-impressions-widget.ts", "public/js")
    .ts("resources/ts/trainings-area.ts", "public/js")
    .ts("resources/ts/landlord-details.ts", "public/js")
    .ts("resources/ts/leads-view-scheduled-container.ts", "public/js")
    .ts("resources/ts/inventory-rooms.ts", "public/js")
    .ts("resources/ts/contact-duplicates-modal.ts", "public/js")
    .ts("resources/ts/deal-change-listing.ts", "public/js")
    .ts("resources/ts/book-your-stay.ts", "public/js")
    .ts("resources/ts/listing-short-stay-app.ts", "public/js")
    .ts("resources/ts/listing-short-stay-checkout-panel.ts", "public/js")
    .ts("resources/ts/short-stay-booking-details.ts", "public/js")
    .ts("resources/ts/metrics-dashboard.ts", "public/js")
    .ts("resources/ts/leisure-hotels-app.ts", "public/js")
    .ts("resources/ts/leisure-user-hotels-app.ts", "public/js")
    .ts("resources/ts/short-stay-header-searcher.ts", "public/js")
    .js("resources/ts/leads-dashboard.ts", "public/js/leads-dashboard.js")
    .js("resources/ts/crm-global-search.ts", "public/js/crm-global-search.js")
    .js("resources/ts/dashboard-reports.ts", "public/js/dashboard-reports.js")
    .js("resources/ts/leads-view-scheduled-container-wrapper.ts", "public/js/leads-view-scheduled-container-wrapper.js")
    .js("resources/ts/lead-details-v2-panel.ts", "public/js/lead-details-v2-panel.js")
    .js("resources/ts/lead-details-lead-activity.ts", "public/js/lead-details-lead-activity.js")
    .js("resources/ts/tasks-screen.ts", "public/js/tasks-screen.js")
    .js("resources/ts/user-permissions.ts", "public/js/user-permissions.js")
    .vue({ version: 3 });

// copy tinymce dir
mix.copyDirectory("resources/js/tinymce", "public/js/crm/tinymce");
// mix.copyDirectory("resources/images", "public/images");
mix.copyDirectory("resources/images/crm", "public/images/crm");
mix.copyDirectory("resources/images/developments", "public/images/developments");
mix.copyDirectory("resources/images/project", "public/images/project");
// mix.copyDirectory("resources/images/projects", "public/images/projects");
mix.copyDirectory("resources/images/regions", "public/images/regions");
mix.copyDirectory("resources/images/svg", "public/images/svg");
mix.copyDirectory("resources/images/wef", "public/images/wef");
mix.copy("resources/images/*.png", "public/images");
mix.copy("resources/images/*.gif", "public/images");
mix.copy("resources/images/*.jpg", "public/images");
mix.copy("resources/images/*.jpeg", "public/images");
mix.copy("resources/images/*.svg", "public/images");
mix.copy("resources/images/*.webp", "public/images");
mix.copyDirectory("resources/fonts", "public/fonts");
mix.copyDirectory("resources/js/crm/libs", "public/js/crm/libs");
mix.copyDirectory("resources/css/crm", "public/css/crm");
mix.copyDirectory("resources/images/projects/marina-heights", "public/images/projects/marina-heights");

mix.copy("resources/docs/Contacts_Template_Fullname.xlsx", "public");
mix.copy("resources/docs/Contacts_Template_FirstNameLastName.xlsx", "public");
mix.copy("resources/images/favicon.png", "public");
mix.copy("resources/images/favicon-16x16.png", "public");
mix.copy("resources/images/favicon-32x32.png", "public");
// mix.copy('node_modules/feather-icons/dist/fonts', 'public/fonts');

jsFiles.forEach((filePath) => mix.copy(filePath, "public/js"));
cssFiles.forEach((filePath) => mix.copy(filePath, "public/css"));
crmJss.forEach((filePath) => mix.copy(filePath, "public/js/crm"));
adminJss.forEach((filePath) => mix.copy(filePath, "public/js/admin"));
utilJss.forEach((filePath) => mix.copy(filePath, "public/js/utils"));
brixJsFiles.forEach((filePath) => mix.copy(filePath, "public/js/brix"));
