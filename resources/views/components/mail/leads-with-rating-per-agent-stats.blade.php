@extends('email.layout-html5')
@section('content')

<style>
    table.content-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
    }

    table.content-table th,
    table.content-table td {
        border: 1px solid #ddd;
        padding: 8px;
        text-align: center;
        font-size: 14px;
    }

    table.content-table th {
        background-color: #f4f4f4;
        color: #333;
        font-weight: bold;
    }

    table.content-table tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    table.content-table tr:hover {
        background-color: #f1f1f1;
    }
</style>

@include('email.company-logo-centered')
<h4> Lead with rating per agent report - {{$date->format('d.m.Y')}}</h4>

<table class="content-table">
    <thead>
        <tr>
            <th>Agent Name</th>
            <th>A (Hot)</th>
            <th>B (Warm)</th>
            <th>C (Luke <PERSON>)</th>
            <th>D (Cool)</th>
            <th>E (Cold)</th>
            <th>F</th>
            <th>X</th>
            <th>Non Rated</th>
        </tr>
    </thead>
    <tbody>
        @forelse ($leadsStats as $data)
        <tr>
            <td>{{ $data->agent_name }}</td>
            <td>{{ $data->A ?: '-' }}</td>
            <td>{{ $data->B ?: '-' }}</td>
            <td>{{ $data->C ?: '-' }}</td>
            <td>{{ $data->D ?: '-' }}</td>
            <td>{{ $data->E ?: '-' }}</td>
            <td>{{ $data->F ?: '-' }}</td>
            <td>{{ $data->X ?: '-' }}</td>
            <td>{{ $data->NonRated ?: '-' }}</td>
        </tr>
        @empty
        <tr>
            <td colspan="9">No data available</td>
        </tr>
        @endforelse
    </tbody>
</table>

<p>Warmest regards</p>
<br>FGREALTY - Real Estate Brokers Qatar<br>
Phone: {{env('QA_COMPANY_PHONE')}}
<p><a href="https://maps.app.goo.gl/dpzgT7EP9nWhWRjcA?g_st=ic"> www.fgrealty.qa </a></p>

@endsection