<template>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h4 class="mb-0">KPI Performance</h4>
        <section class="d-flex gap-1">
            <article v-if="state.agents.length > 1">
                <select class="form-select" style="max-width: 200px;" name="" v-model="selectedAgentRef">
                    <optgroup v-for="teamGroup in groupedAgents" :label="teamGroup.teamLeaderName">
                        <option v-if="teamGroup.teamLeaderId"
                            :value="{ id: teamGroup.teamLeaderId, name: '--All team data--', isTeamData: true }">
                            --All team data--</option>
                        <option :value="agent" v-for="agent of teamGroup.agents">{{ agent.name }}</option>
                    </optgroup>
                </select>
            </article>
            <article class="rangeNavigator">
                <VueDatePicker v-model="monthRef" :min-date="new Date(2016, 0)" :typeable="false"
                    :bootstrap-styling="true" :calendar-class="['week-picker']" :auto-apply="true" month-picker>
                </VueDatePicker>
            </article>
        </section>
    </div>
    <div class="container">
        <div class="doughnut-card card shadow-sm" style="min-width: 300px;">
            <div class="doughnut-card__left">
                <div class="d-flex flex-column align-items-start">
                    <span>Total: <strong>{{ state.tasks?.total ?? '-' }}</strong></span>
                    <span>Autogenerated: <strong>{{ state.tasks?.autogenerated
                    }}/{{ state.tasks?.autogenerated_completed }}</strong></span>
                    <span>Self: <strong>{{ state.tasks?.self }}/{{ state.tasks?.self_completed }}</strong></span>
                    <span>Today tasks: <strong>{{ state.tasks?.due_today ?? '-' }}</strong></span>
                    <span>Overdue: <strong>{{ state.tasks?.overdue ?? '-' }}</strong></span>
                </div>

                <h5>Tasks</h5>
            </div>
            <div class="graphsContainers__graph p-0">
                <div class="graphsContainers__graph-content pb-2">
                    <div style="height: 130px;">
                        <!-- <Pie :data="state.tasksChart.data" :options="state.tasksChart.options" /> -->
                        <TasksStackedBarChart :chartData="state.tasksData" />
                    </div>
                </div>
            </div>
        </div>
        <div class="doughnut-card card shadow-sm">
            <div class="doughnut-card__left">
                <p>Total Listings: <strong>{{ state.totalListings ?? '-' }}</strong></p>
                <h5>Listings</h5>
            </div>
            <div class="graphsContainers__graph p-0">
                <div class="graphsContainers__graph-content pb-2">
                    <Doughnut :data="state.listingsChart.data" :options="state.listingsChart.options" />
                </div>
            </div>
        </div>
        <div class="doughnut-card card shadow-sm">
            <h5>Contacts</h5>
            <div class="graphsContainers__graph p-0">
                <div class="graphsContainers__graph-content pb-2">
                    <Doughnut :data="state.contactsChart.data" :options="state.contactsChart.options" />
                </div>
            </div>
        </div>
        <div class="doughnut-card card shadow-sm">
            <div class="doughnut-card__left">
                <div class="d-flex flex-column align-items-start">
                    <span>Total Leads: <strong>{{ state.totalLeads ?? '-' }}</strong></span>
                    <span>Current Leads: <strong>{{ state.activeLeads ?? '-' }}</strong></span>
                </div>
                <h5>Leads</h5>
            </div>
            <div class="graphsContainers__graph p-0">
                <div class="graphsContainers__graph-content py-2 pe-2">
                    <StackedBarChart :chartData="state.leadsData" />
                </div>
            </div>
        </div>
        <div class="doughnut-card card shadow-sm m-0">
            <div class="doughnut-card__left">
                <div class="d-flex flex-column align-items-start">
                    <span style="font-size: 2.5rem;">{{ state.deals?.cashedInThisMonth ?? '-' }}</span>
                    <span style="margin-top: -8px;">Cashed in</span>
                </div>
                <h5>Deals</h5>
            </div>
            <div class="graphsContainers__graph p-0">
                <div class="graphsContainers__graph-content pb-2">
                    <Doughnut :data="state.dealsChart.data" :options="state.dealsChart.options" />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, Ref, ref, watch, computed } from "vue";
import { Doughnut, Bar, Pie } from "vue-chartjs";
import VueDatePicker from "@vuepic/vue-datepicker";
import StackedBarChart from './StackedBarChart.vue';

// Monthly targets
const agentListingsTarget = 20;
const agentContactsTarget = 30;
const agentLeadsTarget = 30;
const agentDealsTarget = 7;

import "@vuepic/vue-datepicker/dist/main.css";
import {
    Chart as ChartJS,
    ArcElement,
    Tooltip,
    Legend,
    Title,
    BarElement,
    CategoryScale,
    LinearScale,
    Colors,
} from "chart.js";
import TasksStackedBarChart from "./TasksStackedBarChart.vue";
import { tasksChartConstants } from "./chart-data-config";

ChartJS.register(
    ArcElement,
    Tooltip,
    Legend,
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
    Colors
);


ChartJS.register({
    id: "textCenter",
    beforeDraw: function (chart: any) {
        if (chart.config.type !== "doughnut") {
            return;
        }
        const {
            ctx,
            chartArea: { width, height, top },
        } = chart;
        ctx.save();
        ctx.font = "bolder 12px Arial";
        ctx.fillStyle = "gray";
        ctx.textAlign = "center";
        const personalNo = new Intl.NumberFormat("en-GB", {
            maximumSignificantDigits: 3,
        }).format(chart.config._config.data.datasets[0].data[1]);
        const allNo = new Intl.NumberFormat("en-GB", {
            maximumSignificantDigits: 3,
        }).format(chart.config._config.data.datasets[0].data[0] + chart.config._config.data.datasets[0].data[1]);
        ctx.fillText(`${personalNo} / ${allNo}`, width / 2, top + height / 2);
    },
});

declare const request: Function;

interface StatsChart {
    data: {
        labels: Array<string>;
        datasets: Array<{
            data: Array<number>;
            backgroundColor: string | Array<string>;
            label?: string;
            stack?: string;
            barThickness?: number;
        }>;
    };
    options: {
        responsive: boolean;
        cutoutPercentage?: number;
        scales?: any;
        plugins?: any;
    };
    plugins: Array<any>;
    type?: string;
}

interface Agent {
    id: number;
    name: string;
    isCurrentUser: boolean;
    teamLeaderId: number | null;
    teamLeaderName: string | null;
}

interface TeamGroup {
    teamLeaderId: number | null;
    teamLeaderName: string;
    agents: Agent[];
}

export interface DashboardKpiContainerState {
    totalListings: number;
    tasks: {
        autogenerated: number;
        autogenerated_completed: number;
        overdue: number;
        due_today: number;
        self: number;
        self_completed: number;
        total: number;
    };
    deals: {
        cashedInThisMonth: string;
    }
    selfGeneratedLeads: number;
    totalLeads: number;
    activeLeads: number;
    topChartsLoaded: boolean;
    listingsChart: StatsChart;
    contactsChart: StatsChart;
    dealsChart: StatsChart;
    tasksChart: StatsChart;
    agents: Array<Agent>;
    leadsData: any;
    tasksData: any;
}

const hoverLabel = {
    id: "hoverLabel",
    afterDraw(chart: any) {
        const {
            ctx,
            chartArea: { width, height },
        } = chart;
        ctx.save();
        ctx.font = "bolder 40px Arial";
        ctx.fillStyle = "red";
        ctx.textAlign = "center";
        ctx.fillText("Test", width / 2, height);
    },
};
const createListingsChart = (data: any): StatsChart => {
    const targetNo = agentListingsTarget * (data.kpiData.listings?.agents_count ?? 1);
    const targetReached = data.kpiData.listings?.count >= targetNo;
    const remainingTarget = data.kpiData.listings?.count > targetNo ? 0 : targetNo - data.kpiData.listings?.count;

    return {
        data: {
            labels: null,
            datasets: [
                {
                    data: [remainingTarget, data.kpiData.listings?.count ?? 0],
                    backgroundColor: [
                        "rgb(255,99,71)",
                        "rgb(34,139,34)"
                    ],
                },
            ],
        },
        options: {
            responsive: true,
            cutoutPercentage: 50,
        },
        plugins: [hoverLabel],
    };
};
const createContactsChart = (data: any): StatsChart => {
    const targetNo = agentContactsTarget * (data.kpiData.contacts?.agents_count ?? 1);
    const targetReached = data.kpiData.contacts?.count >= targetNo;
    const remainingTarget = data.kpiData.contacts?.count > targetNo ? 0 : targetNo - data.kpiData.contacts?.count;
    return {
        data: {
            labels: null,
            datasets: [
                {
                    data: [remainingTarget, data.kpiData.contacts?.count ?? 0],
                    backgroundColor: [
                        "rgb(255,99,71)",
                        "rgb(34,139,34)"
                    ],
                },
            ],
        },
        options: {
            responsive: true,
            cutoutPercentage: 50,
        },
        plugins: [hoverLabel],
    };
};
const createLeadsChart = (leadsData: {
    officeAssigned: { agents_count: number, count: number };
    allActive: { agents_count: number, count: number };
    selfGenerated: { agents_count: number, count: number };
}): any => {

    let leadsChartData = {
        labels: ['Target', 'Self', 'Office'],
        datasets: [
            {
                label: 'Lead Metrics',
                backgroundColor: ['rgb(255,99,71)', '#36A2EB', '#FFCE56'],
                borderColor: ['rgb(255,99,71)', '#36A2EB', '#FFCE56'],
                borderWidth: 1,
                data: [agentLeadsTarget, 0, 0]
            }
        ]
    };

    if (leadsData) {
        const targetNo = agentLeadsTarget * (leadsData?.selfGenerated?.agents_count ?? 1);
        let officeAssignedLeads = 0;
        let selfGeneratedLeads = 0;

        if (!!leadsData?.officeAssigned) {
            officeAssignedLeads = leadsData.officeAssigned.count;
        }
        if (!!leadsData?.selfGenerated) {
            selfGeneratedLeads = leadsData.selfGenerated.count;
        }
        leadsChartData = {
            labels: ['Target', 'Self', 'Office'],
            datasets: [
                {
                    label: 'Leads',
                    backgroundColor: ['rgb(255,99,71)', '#36A2EB', '#FFCE56'],
                    borderColor: ['rgb(255,99,71)', '#36A2EB', '#FFCE56'],
                    borderWidth: 1,
                    data: [targetNo, selfGeneratedLeads, officeAssignedLeads]
                }
            ]
        };
    }

    return leadsChartData;
};
const createTasksChart = (tasksData: {
    autogenerated: number;
    autogenerated_completed: number;
    overdue: number;
    self: number;
    self_completed: number;
    total: number;
}): any => {

    let tasksChartData = {
        labels: tasksChartConstants.labels,
        datasets: [
            {
                label: 'Tasks',
                backgroundColor: tasksChartConstants.colors,
                borderColor: tasksChartConstants.colors,
                borderWidth: 1,
                data: [0, 0, 0, 0, 0, 0]
            }
        ]
    };

    if (tasksData) {
        tasksChartData = {
            labels: tasksChartConstants.labels,
            datasets: [
                {
                    label: 'Tasks',
                    backgroundColor: tasksChartConstants.colors,
                    borderColor: tasksChartConstants.colors,
                    borderWidth: 1,
                    data: [tasksData.total, tasksData.autogenerated, tasksData.autogenerated_completed, tasksData.self, tasksData.self_completed, tasksData.overdue]
                }
            ]
        };
    }

    return tasksChartData;
};
const createDealsChart = (data: any, state: any): StatsChart => {
    const targetNo = agentDealsTarget * (data.kpiData.deals?.agents_count ?? 1);
    // const targetReached = data.kpiData.deals?.count >= targetNo;
    const cashedInThisMonth = data.kpiData.deals?.cashedInThisMonth ?? '-';
    
    const remainingTarget = data.kpiData.deals?.count > targetNo ? 0 : targetNo - data.kpiData.deals?.count;
    if(!!state) {
        state.value.deals.cashedInThisMonth = cashedInThisMonth;
    }
    return {
        data: {
            labels: null,
            datasets: [
                {
                    data: [remainingTarget, data.kpiData.deals ? data.kpiData.deals.count : 0],
                    backgroundColor: [
                        "rgb(255,99,71)",
                        "rgb(34,139,34)"
                    ],
                },
            ],
        },
        options: {
            responsive: true,
            cutoutPercentage: 50,
        },
        plugins: [hoverLabel],
    };
};

const updateTasksData = (state: any, data: any) => {
    state.value.tasks = data;
};

export default defineComponent({
    name: "DashboardKpiContainer",
    setup() {
        const monthRef = ref({
            month: new Date().getMonth(),
            year: new Date().getFullYear()
        });
        const selectedAgentRef = ref(null);

        const state: Ref<DashboardKpiContainerState> = ref({
            topChartsLoaded: false,
            totalListings: 0,
            tasks: {
                total: 0,
                autogenerated: 0,
                self: 0,
                overdue: 0,
                due_today: 0,
                autogenerated_completed: 0,
                self_completed: 0
            },
            deals: {
                cashedInThisMonth: '-'
            },
            selfGeneratedLeads: 0,
            totalLeads: 0,
            activeLeads: 0,
            listingsChart: createListingsChart({ kpiData: { listings: { count: 0 } } }),
            contactsChart: createContactsChart({ kpiData: { contacts: { count: 0 } } }),
            leadsData: createLeadsChart(null),
            tasksData: createLeadsChart(null),
            dealsChart: createDealsChart({ kpiData: { deals: { count: 0 } } }, null),
            tasksChart: null,//createTasksChart({ tasks: { total: 0, autogenerated: 0, self: 0, overdue: 0, autogenerated_completed: 0, self_completed: 0 } }),
            agents: []
        });

        // Computed property to group agents by team leader
        const groupedAgents = computed(() => {
            // Create a map to store team leader groups
            const teamGroups = new Map<string, TeamGroup>();

            // Create a separate array for team leaders
            const teamLeaders: Agent[] = [];

            // Group agents by team leader
            state.value.agents.forEach(agent => {
                // Check if this agent is a team leader (has agents reporting to them)
                const isTeamLeader = state.value.agents.some(a => a.teamLeaderId === agent.id);

                if (isTeamLeader) {
                    // Add to team leaders array
                    teamLeaders.push(agent);
                }

                const teamLeaderId = agent.teamLeaderId;
                const teamLeaderName = agent.teamLeaderName;

                // Create a key for the team leader group
                const key = teamLeaderId ? teamLeaderId.toString() : 'null';

                if (!teamGroups.has(key)) {
                    teamGroups.set(key, {
                        teamLeaderId,
                        teamLeaderName: teamLeaderId ? (teamLeaderName || 'Unknown Team') : 'Other Agents',
                        agents: []
                    });
                }

                // Add agent to the team leader group (if not a team leader)
                if (!isTeamLeader || (isTeamLeader && !!agent.teamLeaderId)) {
                    teamGroups.get(key)!.agents.push(agent);
                }
            });

            // Sort agents alphabetically within each team
            teamGroups.forEach(group => {
                group.agents.sort((a: Agent, b: Agent) => a.name.localeCompare(b.name));
            });

            // Sort team leaders alphabetically
            teamLeaders.sort((a: Agent, b: Agent) => a.name.localeCompare(b.name));

            // Create the result array with team leaders group first
            const result: TeamGroup[] = [];

            // Add Team Leaders group if there are any team leaders
            if (teamLeaders.length > 0) {
                result.push({
                    teamLeaderId: null,
                    teamLeaderName: 'Team Leaders',
                    agents: teamLeaders
                });
            }

            // Add the rest of the teams
            result.push(...Array.from(teamGroups.values())
                .sort((a: TeamGroup, b: TeamGroup) => {
                    // Sort null team leaders (Other Agents) to the end
                    if (a.teamLeaderName === 'Other Agents') return 1;
                    if (b.teamLeaderName === 'Other Agents') return -1;
                    return a.teamLeaderName.localeCompare(b.teamLeaderName);
                }));

            return result;
        });

        const updateChartsData = (res: any) => {
            state.value.totalListings = res.kpiData?.totalListings?.count ?? "-";
            state.value.totalLeads = res.kpiData?.leads?.total?.count ?? 0;
            state.value.activeLeads = res.kpiData?.leads?.total?.activeTotal ?? 0;

            state.value.listingsChart = createListingsChart(res);
            state.value.contactsChart = createContactsChart(res);
            state.value.leadsData = createLeadsChart(res.kpiData.leads);
            state.value.tasksData = createTasksChart(res.kpiData.tasks);//createLeadsChart(res.kpiData.leads);
            state.value.dealsChart = createDealsChart(res, state);
            updateTasksData(state, res.kpiData.tasks);
        };

        const prepareAndPopulateData = (month: number, year: number, agentIds: Array<number>) => {
            request(`/crm/dashboard/kpi?year=${year}&month=${month + 1}&agentIds=${(agentIds ?? []).map(id => id.toString()).join(",")}`).then((res: any) => {
                updateChartsData(res);
                state.value.topChartsLoaded = true;
            });
        };

        const populateAgentsList = () => {
            return request(`/crm/user-team`).then((agents: Array<{
                id: number;
                name: string;
                isCurrentUser: boolean;
                teamLeaderId: number | null;
                teamLeaderName: string | null;
            }>) => {
                state.value.agents = agents;
                const currentAgent = agents.find(eachAgent => eachAgent.isCurrentUser === true);
                if (!!currentAgent) {
                    selectedAgentRef.value = currentAgent;
                }
            });
        }

        watch(
            () => [monthRef.value, selectedAgentRef.value],
            () => {
                // Handle both regular agent selection and "All team data" selection
                let agentIds = [];
                if (selectedAgentRef.value?.isTeamData) {
                    agentIds = state.value.agents.filter(agent => agent.teamLeaderId === selectedAgentRef.value.id).map(agent => agent.id);
                } else {
                    agentIds = [selectedAgentRef.value.id];
                }

                prepareAndPopulateData(monthRef.value.month, monthRef.value.year, agentIds);
            }
        );

        onMounted(() => {
            populateAgentsList().then(() => {
                prepareAndPopulateData(monthRef.value.month, monthRef.value.year, [selectedAgentRef.value?.id]);
            });
        });

        return {
            state,
            monthRef,
            selectedAgentRef,
            groupedAgents,
        };
    },

    components: { Doughnut, Bar, Pie, VueDatePicker, StackedBarChart, TasksStackedBarChart },
});
</script>
<style lang="scss">
.graphsContainers {
    $blockName: &;
    display: flex;
    justify-content: flex-start;
    gap: 20px;
    flex-wrap: wrap;
    margin-bottom: 20px;

    &__graph {
        max-width: 250px;
        padding: 10px;

        &-title {
            text-align: center;
        }

        &-content {
            height: 150px
        }
    }

    &--fullWidth {
        #{$blockName}__graph {
            flex: 1;
        }
    }
}

.graphsContainerLoader {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 16px;
    margin-left: 0;
    margin-right: 0;
    padding: 0px;
    max-width: 100%;
}

.doughnut-card {
    height: 150px;
    flex: 1 1;
    box-sizing: border-box;
    max-width: 100%;
    padding-left: 5px;
    padding-top: 5px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    flex-direction: row;
    flex-basis: auto;
    text-align: center;

    &__left {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        height: 100%;

        h3 {
            text-align: left;
        }
    }
}
</style>