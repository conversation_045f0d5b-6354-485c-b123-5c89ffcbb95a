@php
$footerLogoURL = "images/svg/FGREALTY_footer.svg";
if(app()->isLocale('ar')) {
$footerLogoURL = "images/svg/FGREALTY_ar_dark.svg";
}
@endphp
<footer class="mainFooter">
    <section class="mainFooter__firstRow">
        <div class="mainFooter__firstRow-shortcuts">
            <article class="colHalf">
                <div>
                    <h2>{{__('About')}}</h2>
                </div>
                <div>
                    <ul>
                        <li><a href="{{persistQueryParamsToURL(route($staticPageRouteName, ['slug' => __('about-us')]))}}">{{__('Company')}}</a></li>
                        <li><a href="{{persistQueryParamsToURL(route($staticPageRouteName, ['slug' => __('careers')]))}}">{{__('Careers')}}</a></li>
                        <li><a href="{{persistQueryParamsToURL(route($staticPageRouteName, ['slug' => __('international')]))}}">{{__('International')}}</a></li>
                        <li><a href="{{persistQueryParamsToURL(route($staticPageRouteName, ['slug' => __('magazine')]))}}">{{__('Magazine')}}</a></li>
                        <li><a href="/blog/">{{__('Blog')}}</a></li>
                        <li><a href="/learn/en/info-guide-for-our-clients/">{{__('Info Guide')}}</a></li>
                        <li><a href="{{persistQueryParamsToURL(route($staticPageRouteName, ['slug' => __('newsletter')]))}}">{{__('Newsletter')}}</a></li>
                        <li><a href="{{persistQueryParamsToURL(route($staticPageRouteName, ['slug' => __('contact-us')]))}}">{{__('Contact Us')}}</a></li>
                    </ul>
                </div>
            </article>
            <article class="colHalf">
                <div>
                    <h2>{{__('Discover')}}</h2>
                </div>
                <div>
                    <ul>
                        <li><a href="{{persistQueryParamsToURL(route('developments', ['locale' => $locale, 'developmentsWord' => __('developments')]))}}">{{__('New Developments')}}</a></li>
                        <li><a href="{{persistQueryParamsToURL(route('investment-opportunities', ['locale' => $locale, 'investmentOpportunitiesWord' => __('investment-opportunities')]))}}">{{__('Investment Opportunities')}}</a></li>
                        <li><a href="{{persistQueryParamsToURL(route('properties.search.exclusive', ['searchWord' => __('search'), 'exclusiveWord' => __('exclusive'), 'locale' => $locale]))}}">{{__('Exclusive Properties')}}</a></li>
                        <li><a href="{{persistQueryParamsToURL(route('agents.index', ['locale' => 'en']))}}">{{__('Find your agent')}}</a></li>
                        <li><a href="javascript:openListWithUsModal()">{{__('List your property with us')}}</a></li>
                        <li><a href="/learn/en/franchise/">{{__('Franchise')}}</a></li>
                        {{--<li><a href="{{route($staticPageRouteName, ['slug' => __('become-an-agent')])}}">{{__('Become an Agent')}}</a></li>--}}
                        <li><a href="/learn/en/concierge-service/">{{__('Concierge Service')}}</a></li>
                        <li><a href="/learn/en/investors-guide/">{{__('Investors Guide')}}</a></li>
                    </ul>
                </div>
            </article>
        </div>
        <div class="mainFooter__firstRow-content">
            <div class="mainFooter__firstRow-content-apps">
                <h2>
                    {{__('Download Our App to Get Exclusive Offers & More')}}
                </h2>
                <div class="mainFooter__firstRow-content-apps-ctas">
                    <a href="https://apps.apple.com/us/app/fgrealty/id1537303031" target="_blank" title="Download IOS Application">
                        <img loading="lazy" width="138" height="45" src="https://www.fgrealty.qa/learn/wp-content/uploads/2024/03/AppStore.svg" class="attachment-full size-full wp-image-161" alt="">
                    </a>
                    <a href="https://play.google.com/store/apps/details?id=qa.fgrealty.app.mobile" target="_blank" title="Download Android Application">
                        <img loading="lazy" width="138" height="45" src="https://www.fgrealty.qa/learn/wp-content/uploads/2024/03/PlayStore.svg" class="attachment-full size-full wp-image-162" alt="">
                    </a>
                </div>
            </div>
            <div class="flex flex-row items-center">
                <img src="{{ asset('images/svg/whatsapp-inverted.svg') }}" alt="arrow-icon" class="w-2 h-2">
                <a class="text-white {{ app()->isLocale('ar') ? 'mr-1' : 'ml-1' }}" href="{{persistQueryParamsToURL(route($staticPageRouteName, ['slug' => __('fgrealty-whatsapp-channel')]))}}">{{__('Join WhatsApp Channel')}}</a></li>
            </div>
            <div class="mainFooter__firstRow-content-newsletter">
                <h2>
                    {{__('Get the Latest News')}}
                </h2>
                <form autocomplete="off" method="post" onsubmit="javascript: return subscribeToNewsletter()">
                    <div class="mainFooter__firstRow-content-newsletter-form">
                        <div class="footerFormGroup">
                            <input autocomplete="false" name="hidden" type="text" style="display:none;">
                            <input class="mainFooter__firstRow-content-newsletter-form-input" type="email" required placeholder="{{__('Type your Email Address...')}}">
                        </div>
                        <div>
                            <button type="submit">
                                <span class="elementor-button-text">Subscribe</span>
                            </button>
                        </div>
                    </div>
                </form>
                <div class="mainFooter__firstRow-content-newsletter-message">The email address was successfully saved</div>
            </div>
        </div>
    </section>
    <section class="mainFooter__brandsRow">
        <div class="mainFooter__brandsRow-brand">
            <div>
                <img loading="lazy" src="https://www.fgrealty.qa/learn/wp-content/uploads/2024/03/FGREALTY_w.svg" alt="">
            </div>
            <p>
                {{__('Founded in 2012 to provide impended brokerages with a powerful marketing and referral program for luxury listings, the FGREALTY’s network was designed to connect the finest independent real estate developers and landlords with its most prestigious international clients.')}}
            </p>
        </div>
        <div class="mainFooter__brandsRow-awards">
            <div>
                <img loading="lazy" src="https://www.fgrealty.qa/learn/wp-content/uploads/2024/03/APAW.svg" alt="">
            </div>
            <p>{{__('Copyright © 2012-:year FGREALTY® – Find Great Realty WLL. All Rights Reserved. FGREALTY® is a registered trademark of Find Great Realty WLL Qatar.', ['year' => date('Y')])}}. {{__('Trademark License number :number', ['number' => '121939'])}}. {{__('Real Estate Brokerage License number :number', ['number' => '303'])}}. <a class="hover:underline" href="{{route($staticPageRouteName, ['slug' => __('contact-us')])}}#map-section">{{env('COMPANY_ADDRESS')}}</a></p>
        </div>
    </section>
    <section class="mainFooter__bottomLine">
        <div class="mainFooter__bottomLine-links">
            <a href="{{persistQueryParamsToURL(route($staticPageRouteName, ['slug' => __('privacy-policy')]))}}" title="{{__('Privacy Policy')}}">{{__('Privacy Policy')}}</a> |
            <a href="{{persistQueryParamsToURL(route($staticPageRouteName, ['slug' => __('terms-and-conditions')]))}}">{{__('Terms and Conditions')}}</a> |
            <a href="{{persistQueryParamsToURL(route($staticPageRouteName, ['slug' => __('cookie-policy')]))}}">{{__('Use of Cookies')}}</a> 
            {{-- <a href="{{route($staticPageRouteName, ['slug' => __('covid19-response')])}}">{{__('COVID-19 Response')}}</a> --}}
        </div>
        <div class="mainFooter__bottomLine-socialMedia">
            <a title="Facebook" href="{{env('SOCIAL_MEDIA_FACEBOOK')}}" target="_blank">
                <svg xmlns="http://www.w3.org/2000/svg" width="8.631" height="16" viewBox="0 0 8.631 16">
                    <defs>
                        <style>
                            .a {
                                fill: #ffffff;
                            }
                        </style>
                    </defs>
                    <g transform="translate(-28.4 -10.1)">
                        <path class="a" d="M30.8,19.131H28.821c-.32,0-.421-.12-.421-.421V16.288c0-.32.12-.421.421-.421H30.8V14.105a4.448,4.448,0,0,1,.541-2.263,3.323,3.323,0,0,1,1.782-1.482,4.455,4.455,0,0,1,1.542-.26H36.63c.28,0,.4.12.4.4v2.283c0,.28-.12.4-.4.4-.541,0-1.081,0-1.622.02a.723.723,0,0,0-.821.821c-.02.6,0,1.181,0,1.8H36.51c.32,0,.441.12.441.441v2.423c0,.32-.1.421-.441.421H34.187v6.528c0,.34-.1.461-.461.461h-2.5c-.3,0-.421-.12-.421-.421V19.131Z" />
                    </g>
                </svg>
            </a>
            <a title="X" href="{{env('SOCIAL_MEDIA_TWITTER')}}" target="_blank">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="13.919" viewBox="0 0 16 13.919">
                    <defs>
                        <style>
                            .a {
                                fill: #ffffff;
                            }
                        </style>
                    </defs>
                    <g transform="translate(-0.001 -33.299)">
                        <g transform="translate(0.001 33.299)">
                            <path class="a" d="M15.931,35.682a.477.477,0,0,0-.448-.225l-.8.069.763-1.537a.477.477,0,0,0-.637-.641l-2.018.988a3.41,3.41,0,0,0-3.877.514,4.128,4.128,0,0,0-1.277,3.318,6.947,6.947,0,0,1-5.488-3.736.477.477,0,0,0-.8-.057,3.273,3.273,0,0,0-.208,3.432c-.157-.04-.324-.093-.509-.155a.477.477,0,0,0-.622.53,4.013,4.013,0,0,0,2.279,3.036,4.007,4.007,0,0,1-.5.139.477.477,0,0,0-.251.793,6.462,6.462,0,0,0,3.143,1.673,5.4,5.4,0,0,1-3.854.909.477.477,0,0,0-.37.8c.691.762,3.208,1.62,5.854,1.685q.157,0,.32,0A8.481,8.481,0,0,0,12.7,45.057a8.6,8.6,0,0,0,2.48-4.564A8.875,8.875,0,0,0,15.2,37.5c-.006-.039-.012-.08-.017-.116l.746-1.2A.477.477,0,0,0,15.931,35.682Z" transform="translate(-0.001 -33.299)" />
                        </g>
                    </g>
                </svg>
            </a>
            <a title="Instagram" href="{{env('SOCIAL_MEDIA_INSTAGRAM')}}" target="_blank">
                <svg xmlns="http://www.w3.org/2000/svg" width="15.997" height="16" viewBox="0 0 15.997 16">
                    <defs>
                        <style>
                            .a {
                                fill: #ffffff;
                            }
                        </style>
                    </defs>
                    <g transform="translate(-0.449)">
                        <path class="a" d="M16.406,4.7a5.846,5.846,0,0,0-.372-1.941A4.1,4.1,0,0,0,13.692.422,5.861,5.861,0,0,0,11.752.05C10.9.009,10.623,0,8.451,0s-2.444.009-3.3.047A5.848,5.848,0,0,0,3.212.419a3.9,3.9,0,0,0-1.419.925A3.939,3.939,0,0,0,.871,2.76,5.862,5.862,0,0,0,.5,4.7C.459,5.557.449,5.829.449,8S.459,10.446.5,11.3A5.846,5.846,0,0,0,.868,13.24a4.1,4.1,0,0,0,2.341,2.341,5.862,5.862,0,0,0,1.941.372C6,15.991,6.275,16,8.448,16s2.444-.009,3.3-.047a5.845,5.845,0,0,0,1.941-.372,4.093,4.093,0,0,0,2.341-2.341A5.866,5.866,0,0,0,16.4,11.3c.037-.853.047-1.125.047-3.3S16.443,5.557,16.406,4.7Zm-1.441,6.533a4.386,4.386,0,0,1-.275,1.485A2.654,2.654,0,0,1,13.17,14.24a4.4,4.4,0,0,1-1.485.275c-.844.038-1.1.047-3.232.047s-2.391-.009-3.232-.047a4.383,4.383,0,0,1-1.485-.275,2.462,2.462,0,0,1-.919-.6,2.488,2.488,0,0,1-.6-.919,4.4,4.4,0,0,1-.275-1.485c-.038-.844-.047-1.1-.047-3.232s.009-2.391.047-3.232a4.383,4.383,0,0,1,.275-1.485,2.432,2.432,0,0,1,.6-.919,2.484,2.484,0,0,1,.919-.6A4.4,4.4,0,0,1,5.225,1.5c.844-.037,1.1-.047,3.232-.047s2.391.009,3.232.047a4.385,4.385,0,0,1,1.485.275,2.46,2.46,0,0,1,.919.6,2.487,2.487,0,0,1,.6.919,4.4,4.4,0,0,1,.275,1.485c.037.844.047,1.1.047,3.232S15,10.393,14.965,11.237Zm0,0" />
                        <path class="a" d="M129.059,124.5a4.11,4.11,0,1,0,4.11,4.11A4.111,4.111,0,0,0,129.059,124.5Zm0,6.776a2.666,2.666,0,1,1,2.666-2.666A2.667,2.667,0,0,1,129.059,131.276Zm0,0" transform="translate(-120.609 -120.609)" />
                        <path class="a" d="M364.368,89.561a.96.96,0,1,1-.96-.96A.96.96,0,0,1,364.368,89.561Zm0,0" transform="translate(-350.685 -85.832)" />
                    </g>
                </svg>
            </a>
            <a title="Linkedin" href="{{env('SOCIAL_MEDIA_LINKEDIN')}}" target="_blank">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
                    <defs>
                        <style>
                            .a {
                                fill: #ffffff;
                            }
                        </style>
                    </defs>
                    <path class="a" d="M18.826,18.525h0V12.656c0-2.871-.618-5.082-3.974-5.082A3.484,3.484,0,0,0,11.718,9.3h-.047V7.842H8.489V18.524H11.8V13.235c0-1.393.264-2.739,1.989-2.739,1.7,0,1.725,1.589,1.725,2.829v5.2Z" transform="translate(-2.83 -2.525)" />
                    <path class="a" d="M.4,7.977H3.713V18.659H.4Z" transform="translate(-0.132 -2.659)" />
                    <path class="a" d="M1.921,0A1.93,1.93,0,1,0,3.843,1.921,1.922,1.922,0,0,0,1.921,0Z" />
                </svg>
            </a>
            <a title="Tiktok" href="{{env('SOCIAL_MEDIA_TIKTOK')}}" target="_blank">
                <svg xmlns="http://www.w3.org/2000/svg" width="13.992" height="16" viewBox="0 0 13.992 16">
                    <defs>
                        <style>
                            .a {
                                fill: #ffffff;
                            }
                        </style>
                    </defs>
                    <path class="a" d="M13.523,3.515A3.049,3.049,0,0,1,10.477.469.469.469,0,0,0,10.008,0H7.5a.469.469,0,0,0-.469.469V10.761A1.791,1.791,0,1,1,5.239,8.971.469.469,0,0,0,5.707,8.5V5.992a.469.469,0,0,0-.469-.469,5.239,5.239,0,1,0,5.239,5.239V6.205a6.448,6.448,0,0,0,3.046.757.469.469,0,0,0,.469-.469V3.983A.469.469,0,0,0,13.523,3.515Zm0,0" />
                </svg>
            </a>
            <a title="Youtube" href="{{env('SOCIAL_MEDIA_YOUTUBE')}}" target="_blank">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="12" viewBox="0 0 16 12">
                    <defs>
                        <style>
                            .a {
                                fill: #ffffff;
                            }
                        </style>
                    </defs>
                    <path class="a" d="M15.67-4.2a2.081,2.081,0,0,0-1.41-1.511A43.753,43.753,0,0,0,8-6.082a45.455,45.455,0,0,0-6.26.353A2.125,2.125,0,0,0,.329-4.2,24.2,24.2,0,0,0,0-.082,24.111,24.111,0,0,0,.329,4.04,2.081,2.081,0,0,0,1.74,5.551,43.832,43.832,0,0,0,8,5.918a45.455,45.455,0,0,0,6.26-.353,2.081,2.081,0,0,0,1.41-1.511A24.208,24.208,0,0,0,16-.068,22.972,22.972,0,0,0,15.67-4.2ZM6.405,2.487V-2.651L10.57-.082Zm0,0" transform="translate(0 6.082)" />
                </svg>
            </a>
        </div>
    </section>
</footer>
<span class="app-list-with-us hidden"></span>

<script>
    const whitelistedMarketingParams = JSON.parse('{!! json_encode(\App\Models\MarketingParams::WHITELISTED_URL_PARAMS) !!}');

    function subscribeToNewsletter() {
        const emailCtrl = document.querySelector('.mainFooter__firstRow-content-newsletter-form input[type=email]');
        const subscriptionMessage = document.querySelector('.mainFooter__firstRow-content-newsletter-message');
        httpRequest('POST', '/api/newsletter', {
                email: emailCtrl.value
            })
            .then(data => {
                subscriptionMessage.style.display = 'inline-block';
                setTimeout(() => {
                    subscriptionMessage.style.display = 'none';
                    emailCtrl.value = '';
                }, 4000)
            });
        return false;
    }

    async function fallbackToShareModal(listingURL, listingTitle, listingId) {
        const shareObject = {
            'url': listingURL,
            'title': listingTitle,
            'text': "{{__('Hi, I found this property on https://www.fgrealty.qa. Please check it and let me know what you think!')}}"
        };
        if (!!navigator.canShare && navigator.canShare(shareObject)) {
            try {
                await navigator.share(shareObject);
            } catch (Ex) {
                console.log('Ex', Ex)
            }
        } else {
            request(`/api/listing/${listingId}/share-payload`, {
                method: 'POST',
                body: {
                    listingURL
                }
            }).then((data) => {
                openShareModal({
                    ...data,
                    listingURL
                })
            })
        }
        trackCTAClick('share', listingId)
    }

    const trackCTAClick = (trackType, listingId) => {
        const body = {
            type: `listing`,
            trackType,
            listingId,
            url: window.location.href,
        };
        const currentSearchParams = new URLSearchParams(window.location.search);
        whitelistedMarketingParams.forEach(param => {
            if (Array.from(currentSearchParams.keys()).includes(param)) {
                body[param] = currentSearchParams.get(param);
            }
        });
        return request(`/api/track`, {
            method: "POST",
            body,
        });
    };
</script>