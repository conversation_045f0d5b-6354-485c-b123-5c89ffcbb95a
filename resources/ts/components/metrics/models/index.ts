export interface BEUserWidget {
    id: number;
    filters: string;
    width: number;
    height: number;
    position: number;
    x: number;
    y: number;
    widget: BEWidget;
}

export interface BEWidget {
    code: string;
    config: WidgetConfig;
    id: number;
    min_height: number;
    min_width: number;
    name: string;
    type: string;
    widgets: Array<BEWidget>;
}

export interface WidgetDTO {
    data: {
        code: string;
        config: WidgetConfig;
        id: number;
        min_height: number;
        min_width: number;
        name: string;
        type: string;
        widgets: Array<WidgetDTO>;
    };
    state: {

    }
}

export interface WidgetConfig {
    endpoint?: string;
    filters: Array<{
        value: string;
        tag: string;
    }>;
    variants?: {
        [variant: string]: {
            endpoint?: string;
        }
    }
}

export interface UserWidgetConfig {
    id: number;
    filters: any;
    w: number;
    h: number;
    i: number;
    x: number;
    y: number;
    widget: WidgetDTO;
    code: string;
}