@extends('crm.layout')

@section('head-css')
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.10.25/css/dataTables.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/fixedheader/3.1.9/css/fixedHeader.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css" />
<link rel="stylesheet" type="text/css" href="/css/virtual-select.min.css" />
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
<style>
    .vscomp-ele {
        max-width: 100%;
        display: block;
        width: 100%;
    }

    .client-status-RENTED {
        background-color: #B1AE8F;
        color: #fff;
    }

    .client-status-FOLLOW-UP {
        background-color: #FAE24B;
    }

    .client-status-FOCUS-ON {
        background-color: #4BFA8B !important;
    }

    .client-status-FAR-MOVING-DATE {
        background-color: #4B63FA !important;
        color: #fff;
    }

    .client-status-STUCK {
        background-color: #969094 !important;
        color: #fff;
    }

    .dataTable td {
        margin-bottom: 0px;
        margin-top: 0px;
        padding-bottom: 0px;
        padding-top: 0px;
        height: 12px;
    }

    .td .details-control {
        margin-bottom: 100px;
        width: 1em;
        height: 1em;
    }

    td.details-control {
        padding: 12px;
    }

    tr.details td.details-control:before {
        content: "-";
        background-color: #d33333;
    }

    td.details-control:before {
        left: 5px;
        height: 1em;
        width: 1em;
        margin-top: -9px;
        display: block;
        position: absolute;
        color: white;
        border: .15em solid white;
        border-radius: 1em;
        box-shadow: 0 0 0.2em #444;
        box-sizing: content-box;
        text-align: center;
        text-indent: 0 !important;
        font-family: "Courier New", Courier, monospace;
        line-height: 1em;
        content: "+";
        background-color: #0d6efd;
        cursor: pointer;
    }

    .status-warm {
        background-color: #FD7E14
    }
</style>
@endsection

@section('navbar')
@include('navbars.crm', ['active' => 'leads'])
@endsection

@section('content')
@include('crm.components.lead-update-status-modal')
<script>
    const statusOptions = JSON.parse('{!! addslashes(json_encode($leadStatuses)) !!}');
    const leadSourcesMap = JSON.parse('{!! $sources->mapWithKeys(function($s) { return [$s->id => $s->name];  })->toJSON() !!}');
    const leadSources = Object.keys(leadSourcesMap).map((key) => ({
        label: leadSourcesMap[key],
        value: key
    }))
    const leadStatuses = JSON.parse('{!! $leadStatuses->toJSON() !!}');
</script>
<div class="px-md-2">
    @if(request()->session()->has('success') || request()->session()->has('info'))
    <div class="messages-container pt-4">
        @include('crm.components.session-alerts')
    </div>
    @endif

    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Leads</h1>
        <div class="btn-toolbar mb-2 mb-md-0 gap-2">
            <a role="button" class="btn btn-sm btn-outline-warning" href="{{route('crm.lead-dashboard.index')}}">
                <span data-feather="flag"></span>
                Leads Dashboard
            </a>
            <div class="btn-group btn-group-sm me-2" role="group">
                @if(!auth()->user()->hasAnyRole(\App\Models\Crm\RolesDef::MATRIX_AGENT))
                <a href="{{route('crm.leads.index', ['vt' => 'master'])}}" type="button" class="btn btn-sm btn-outline-secondary @if($vt == 'master') active @endif">Master
                    List</a>
                @endif
                @if(auth()->user()->hasAnyRole(\App\Models\Crm\RolesDef::TEAM_LEADER))
                <a href="{{route('crm.leads.index', ['vt' => 'team_list'])}}" type="button" class="btn btn-sm btn-outline-secondary @if($vt == 'team_list') active @endif">Team
                    List</a>
                @endif
                <a href="{{route('crm.leads.index')}}" type="button" class="btn btn-sm btn-outline-secondary @if($vt == 'personal') active @endif">Personal
                    List</a>
            </div>
            @if($isSerban || auth()->user()->email == '<EMAIL>')
            <div class="dropdown">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-download"></i>
                    Export
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                    <li><a id="exportFilteredLeadsBtn" class="dropdown-item" href="javascript:exportFilteredLeads()">
                            <span>Filtered Leads</span>
                        </a></li>
                    <li><a id="exportBtn" class="dropdown-item" href="javascript:exportLeads()">
                            <span>Leads</span>
                        </a></li>
                </ul>
            </div>
            @endif
            <a href="{{route('crm.leads.create')}}" role="button" class="btn btn-sm btn-outline-secondary">
                <span data-feather="plus"></span>
                Add new item
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12 pb-2">
            <a role="button" class="btn btn-sm btn-outline-secondary" href="javascript:toggleExtraFilters()">
                <span data-feather="chevrons-right"></span>
                More filters
            </a>
        </div>
        <div class="card card-body p-2 m-2 bg-light d-none" id="extraFiltersPanel">
            @include('crm.leads.filters')
        </div>

        {{-- <div class="col mt-2">
            @include("crm.inventory.table")
        </div> --}}
    </div>

    @php
    $headerColumnsDefinition = [
    "index" => "",
    "ref_no" => "Ref No",
    "reminder" => "",
    "duplicate_contact" => "",
    "last_call" => "Last call",
    "status" => "Status",
    "agent_name" => "Agent",
    "rating" => "Rating",
    "created_at" => "Created",
    "last_contact_date" => "Last Contact Date",
    "full_name" => "Full Name",
    "phone" => "Mobile Phone",
    "filter_operation_type" => "Ad type",
    "filter_property_type" => "Property type",
    "assigned_to" => "Assigned to",
    "location_id" => "Location",
    "platform_from" => "Platform",
    "budget" => "Budget",
    "actions" => "Actions",
    ];

    $masterViewColumns = ['index', 'reminder', 'duplicate_contact', 'last_call', 'status', 'rating', 'created_at', 'last_contact_date', 'full_name', 'phone', 'filter_operation_type', 'filter_property_type', 'assigned_to', 'location_id', 'platform_from', 'budget','actions'];
    $personalViewColumns = ['index', 'reminder', 'duplicate_contact', 'last_call', 'status', 'rating', 'created_at', 'last_contact_date', 'full_name', 'phone', 'filter_operation_type', 'filter_property_type', 'location_id', 'platform_from', 'budget', 'actions'];
    $agentsViewColumns = ['index', 'reminder', 'duplicate_contact', 'last_call', 'status', 'rating', 'created_at', 'last_contact_date', 'full_name', 'phone', 'filter_operation_type', 'filter_property_type', 'location_id', 'platform_from', 'budget', 'actions'];
    $teamListViewColumns = ['index', 'reminder', 'duplicate_contact', 'last_call', 'status', 'agent_name', 'rating', 'created_at', 'last_contact_date', 'full_name', 'phone', 'filter_operation_type', 'filter_property_type', 'location_id', 'platform_from', 'budget', 'actions'];
    
    $admin = \App\Models\Crm\RolesDef::OFFICE_MANAGER;
    $columnsToUse = $masterViewColumns;
    if($vt == 'master' && auth()->user()->hasAnyRole([\App\Models\Crm\RolesDef::OFFICE_MANAGER])) {
    $columnsToUse = $masterViewColumns;
    } elseif($vt == 'personal' && auth()->user()->hasAnyRole([\App\Models\Crm\RolesDef::OFFICE_MANAGER])){
    $columnsToUse = $personalViewColumns;
    } elseif($vt == 'team_list') {
    $columnsToUse = $teamListViewColumns;
    } else {
    $columnsToUse = $agentsViewColumns;
    }

    $columnsHeadersToUse = array_map(function($columnIndex) use ($headerColumnsDefinition) {
    return $headerColumnsDefinition[$columnIndex];
    }, $columnsToUse);
    @endphp

    <div class="col mt-2">
        <table class="table table-striped dataTable fw-light align-middle" id="leads-list" width="100%">
            <thead>
                <tr>
                    @foreach($columnsHeadersToUse as $columnHeader)
                    <th>{{$columnHeader}}</th>
                    @endforeach
                </tr>
            </thead>
        </table>

        @component('crm.components.modal', [
        'modalId' => 'modal-notice',
        'modalLabel' => 'Request sent',
        'modalTitle' => 'Success'
        ])
        An export request has been sent to the Administrator. When this is approved, you will receive an email with the link to the report.
        @endcomponent

        @component('crm.components.modal', [
        'modalId' => 'modal-error',
        'modalLabel' => 'Request failed',
        'modalTitle' => 'Error'
        ])
        The export request cannot be sent. Please try again later.
        @endcomponent

    </div>

    <div class="modal fade" id="leadsDeleteModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="leadsDeleteConfirmLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="leadsDeleteConfirmLabel">Leads delete confirm</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="leadsDeleteMessage" class="d-none alert alert-success">The lead has been deleted.</div>
                    Are you sure you want to delete this lead?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button id="deleteLeadCta" onclick="javascript: deleteLead()" type="button" class="btn btn-primary">Delete the lead</button>
                </div>
            </div>
        </div>
    </div>

</div>
@include('crm.components.lead-operation-history-modal')
@include('crm.components.contacts-duplicate')
@include('crm.components.notes-modal')
@include('crm.components.lead-reassign-modal')
<leads-view-scheduled-container-wrapper></leads-view-scheduled-container-wrapper>
@endsection

@section('body-js')
<script>
    const vt = '{{$vt}}';
    const columnsToUse = JSON.parse('{!! json_encode($columnsToUse) !!}');
</script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/jquery/latest/jquery.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<script type="text/javascript" src="{{asset('/js/crm/libs/jquery.ba-throttle-debounce.min.js')}}"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/1.10.25/js/dataTables.bootstrap5.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/fixedheader/3.1.9/js/dataTables.fixedHeader.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.js"></script>
<script type="text/javascript" src="/js/virtual-select.min.js"></script>
<script type="text/javascript" src="{{mix('/js/crm/libs/datatables-util.js')}}"></script>
<script type="text/javascript" src="{{ mix('/js/crm/leads-admin-table.js') }}"></script>
<script type="text/javascript" src="{{mix('/js/leads-filters.js')}}"></script>
<script type="text/javascript" src="{{mix('/js/leads-view-scheduled-container.js')}}"></script>

<script>
    let isDownloading = false;
    let isDownloadingFilteredLeads = false;
    let leadToDelete = null;

    const leadDeleteModal = new bootstrap.Modal(document.getElementById('leadsDeleteModal'), {
        keyboard: false
    })

    const toggleExtraFilters = () => {
        const classList = document.querySelector('#extraFiltersPanel').classList;
        if (classList.contains('d-none')) {
            classList.remove('d-none');
        } else {
            classList.add('d-none');
        }
    }

    function exportLeads() {
        if (isDownloading) {
            return;
        }
        isDownloading = true;
        document.querySelector('#exportBtn span').innerText = 'Downloading...';
        return fetch("{{route('crm.leads.export')}}").then(response => {
            response.blob().then(blob => {
                const currDate = new Date();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `leads_${(currDate.getDate()) + '_' + (currDate.getMonth() + 1) + '_' + currDate.getFullYear()}.xlsx`;
                document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
                a.click();
                a.remove(); //afterwards we remove the element again
                document.querySelector('#exportBtn span').innerText = 'Export';
                isDownloading = false;
            });
        });
    }

    function exportFilteredLeads() {
        if (isDownloadingFilteredLeads) {
            return;
        }
        isDownloadingFilteredLeads = true;
        document.querySelector('#exportFilteredLeadsBtn span').innerText = 'Downloading...';
        const csrfTag = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const requestBody = {
            ...window.leadsSearchParams,
            vt: '{{$vt}}'
        }
        return fetch("{{route('crm.leads.export-filtered')}}", {
            method: 'POST',
            body: JSON.stringify(requestBody),
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                "X-CSRF-TOKEN": csrfTag
            }
        }).then(response => {
            response.blob().then(blob => {
                const currDate = new Date();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `Filtered Leads ${(currDate.getDate()) + '-' + (currDate.getMonth() + 1) + '-' + currDate.getFullYear()}.xlsx`;
                document.body.appendChild(a); // we need to append the element to the dom -> otherwise it will not work in firefox
                a.click();
                a.remove(); //afterwards we remove the element again
                document.querySelector('#exportFilteredLeadsBtn span').innerText = 'Export Filtered Leads';
                isDownloadingFilteredLeads = false;
            });
        });
    }

    const deleteLead = function() {
        if (!leadToDelete) {
            return;
        }
        document.querySelector('#deleteLeadCta').disabled = true;
        request(`/api/leads/${leadToDelete}/soft`, {
            method: 'DELETE'
        }).then(() => {
            document.querySelector('#leadsDeleteMessage').classList.remove('d-none');
            setTimeout(() => {
                window.location.reload()
            }, 3000);
        }).catch(err => {
            document.querySelector('#deleteLeadCta').disabled = false;
            console.log('err', err)
        });
    }

    const confirmDelete = function(leadId) {
        leadToDelete = leadId;
        leadDeleteModal.show();
    }
    const minRentPrices = JSON.parse('{!! json_encode($prmin) !!}');
    const maxRentPrices = JSON.parse('{!! json_encode($prmax) !!}');
    const minSalePrices = JSON.parse('{!! json_encode($psmin) !!}');
    const maxSalePrices = JSON.parse('{!! json_encode($psmax) !!}');
</script>
<script type="text/javascript" src="{{mix('/js/leads-view-scheduled-container-wrapper.js')}}"></script>
@endsection