<?php

namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;

use App\Models\Language;
use App\Models\QueryParamsDef;
use App\Models\SEOContent;
use App\Services\GeographyService;
use App\Services\MenusService;
use App\Services\PropertyTypesService;
use App\Services\SEOContentMetaBuilderService;
use App\Services\SEOContentService;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use App\Models\SEO;
use App\Models\SEOContentTranslation;
use Illuminate\Support\Facades\Log;
use SEOMeta;
use OpenGraph;
use Spatie\SchemaOrg\Schema;

class FrontController extends Controller
{
    protected $SEOContentService;
    protected $geographyService;
    protected $propertiesSearchParserService;
    protected $currentSearchParams;
    protected $SEOContentMetaBuilderService;
    protected $propertyTypesService;
    protected $propertiesService;

    private $metaDefaults = [
        Language::EN => [
            'rent' => 'Find [PageName]. [No] Luxury properties, Furnished & Unfurnished, Short Term & Long Term Rentals.',
            'buy' => 'Find [PageName]. [No] Luxury properties, Instalment Payment Plans available.',
            '' => 'Find [PageName]. [No] Luxury properties, Instalment Payment Plans available.'
        ],
        Language::AR => [
            // 'rent' => 'Search [PageName] with maps & photos on www.fgrealty.qa✓ [No] Luxury properties✓ Furnished & Unfurnished✓ Short Term & Long Term Rentals.',
            // 'rent' => 'ابحث عن [PageName] بالخرائط والصور على www.fgrealty.qa✓ [No] عقارات فاخرة مفروشة وغير مفروشة إيجارات قصيرة وطويلة الأجل.',
            'rent' => 'ابحث عن [PageName] بالخرائط والصور على www.fgrealty.qa✓ [No] عقارات فاخرة مفروشة وغير مفروشة إيجارات قصيرة وطويلة الأجل.',
            'buy' => 'ابحث في [PageName] بالخرائط والصور على www.fgrealty.qa✓ [No] عقارات فاخرة تتوفر خطط الدفع بالتقسيط.',
            '' => 'ابحث في [PageName] بالخرائط والصور على www.fgrealty.qa✓ [No] عقارات فاخرة تتوفر خطط الدفع بالتقسيط.'
        ]
    ];

    private $alternateHreflangsMap = [
        'en-AE' => 'home',
        'ar-AE' => 'home.localized',
        'en-BH' => 'home',
        "ar-BH" => 'home.localized',
        'en-EG' => 'home',
        'ar-EG' => 'home.localized',
        'en-QA' => 'home',
        'ar-QA' => 'home.localized',
        'ar-SA' => 'home.localized',
        'en-SA' => 'home',
        'en-er' => 'home',
        'ar-er' => 'home.localized',
        'en-ir' => 'home',
        'ar-ir' => 'home.localized',
        'en-ml' => 'home',
        'ar-ml' => 'home.localized',
        'en-sn' => 'home',
        'ar-sn' => 'home.localized',
        'en-tr' => 'home',
        'ar-tr' => 'home.localized',
        'en-il' => 'home',
        'ar-il' => 'home.localized',
        'ar' => 'home.localized',
        'x-default' => 'home'
    ];

    public function __construct(
        SEOContentService $SEOContentService,
        GeographyService $geographyService,
        SEOContentMetaBuilderService $SEOContentMetaBuilderService
    ) {
        $this->SEOContentService = $SEOContentService;
        $this->geographyService = $geographyService;
        $this->SEOContentMetaBuilderService = $SEOContentMetaBuilderService;
    }

    public function handleMetas($options = [], $locale = Language::EN)
    {
        Session::forget('jsonLdString');
        $route = request()->route();
        $routeName = $route->getName();

        $metaTitle = "";
        $metaDescription = "";
        $imageURL = "";
        $seoContent = null;

        if (in_array($routeName, ['home', 'home.localized'])) {
            $lang = app()->getLocale();
            $seoContent = $this->SEOContentService->findOne('page', 'home');
            if(!is_null($seoContent) && $lang == Language::AR) {
                $trans = SEOContentTranslation::where('language', Language::AR)->where('seo_content_id', $seoContent->id)->first();
                if(!is_null($trans)) {
                    $seoContent->meta_title = $trans->meta_title;
                    $seoContent->meta_description = $trans->meta_description;
                }
            }

            $metaTitle = $seoContent->meta_title;
            $metaDescription = $seoContent->meta_description;

            // canonical urls
            SEOMeta::reset();
            SEOMeta::setCanonical(env('APP_URL'));

            if (app()->isLocale('en')) {
                // SEOMeta::addAlternateLanguage('en-qa', route('home'));
                // SEOMeta::addAlternateLanguage('ar', route('home.localized', ['locale' => 'ar']));
                OpenGraph::setURL(env('APP_URL'));
            } elseif (app()->isLocale('ar')) {
                SEOMeta::setCanonical(route('home.localized', ['locale' => 'ar']));
                // SEOMeta::addAlternateLanguage('ar-qa', route('home'));
                OpenGraph::setURL(route('home.localized', ['locale' => 'ar']));
            }

            foreach ($this->alternateHreflangsMap as $locale => $routeName) {
                $url = $routeName == 'home' ? route('home') : route('home.localized', ["locale" => "ar"]);
                SEOMeta::addAlternateLanguage($locale, $url);
            }
        } elseif (in_array($routeName, ['pages.static', 'ar.pages.static'])) {
            $slug = $route->parameters()['slug'];
            if ($locale == Language::AR) {
                $slug = $this->SEOContentService->slugMap[Language::AR][$slug];
            }
            $seoContent = $this->SEOContentService->findOne('page', $slug);
            if (!is_null($seoContent)) {
                $metaTitle = $seoContent->meta_title;
                $metaDescription = $seoContent->meta_description;

                if ($locale == Language::AR) {
                    $translation = $this->SEOContentService->getTranslationFor($seoContent, $locale);
                    if (!is_null($translation)) {
                        $metaTitle = $translation->meta_title;
                        $metaDescription = $translation->meta_description;
                    }
                }
            }
            $this->addAlternatelanguagesForCurrentURL($locale);
        } elseif ($routeName === 'investment-opportunities') {
            $seoContent = $this->SEOContentService->findOne('page', 'investment-opportunities');
            if (!is_null($seoContent)) {
                $metaTitle = $seoContent->meta_title;
                $metaDescription = $seoContent->meta_description;

                if ($locale == Language::AR) {
                    $translation = $this->SEOContentService->getTranslationFor($seoContent, $locale);
                    if (!is_null($translation)) {
                        $metaTitle = $translation->meta_title;
                        $metaDescription = $translation->meta_description;
                    }
                }
            }
            $this->addAlternatelanguagesForCurrentURL($locale);
        } elseif ($routeName === 'international') {
            SEOMeta::setCanonical(url()->current());
            $lowercaseLocation = strtolower($options['searchLocation']);
            if(trans()->has('international_seo_meta_title_'.$lowercaseLocation)) {
                $metaTitle = __('international_seo_meta_title_'.$lowercaseLocation);
            } else {
                $metaTitle = "Buy and Rent Properties in ".$options['searchLocation']." | Real Estate Portal";
            }

            if(trans()->has('international_seo_meta_title_'.$lowercaseLocation)) {
                $metaDescription = __('international_seo_meta_description_'.$lowercaseLocation);
            } else {
                $metaDescription = __("international_seo_meta_description_default", ['__LOCATIONS__' => $options['searchLocation']]);
            }
            
            
            $this->addAlternatelanguagesForCurrentURL($locale);
        } elseif (in_array($routeName, ['search.dynamic', 'search.dynamic.region'])) {
            $urlPath = request()->path();
            $extraMenuMetas = null;
            $menusService = app()->make(MenusService::class);
            $extraMenuEntries = $menusService->getExtraMenuEntries();
            foreach ($extraMenuEntries as $menuEntryKey => $menuEntryConfig) {
                if ($menuEntryConfig['url'] === "/" . rawurldecode($urlPath)) {
                    if (!empty($menuEntryConfig['searchParams']) && is_array($menuEntryConfig['searchParams'])) {
                        $extraMenuMetas = [
                            'title' => $menuEntryConfig['title'],
                            'metaTitle' => $menuEntryConfig['metaTitle'],
                            'metaDescription' => $menuEntryConfig['metaDescription'],
                            'selectionTitle' => $menuEntryConfig['selectionTitle'] ?? $menuEntryConfig['title'],
                            'selectionContent' => $menuEntryConfig['selectionContent'],
                        ];
                    }
                }
            }

            if (!is_null($extraMenuMetas)) {
                $metaTitle = $extraMenuMetas['metaTitle'];
                $metaDescription = $extraMenuMetas['metaDescription'];
                $seoContent = new SEOContent();
                $seoContent->meta_title = $metaTitle;
                $seoContent->meta_description = $metaDescription;
                $seoContent->link_label = $extraMenuMetas['title'];
                $seoContent->selection_title = $extraMenuMetas['selectionTitle'];
                $seoContent->content = $extraMenuMetas['selectionContent'];
            } else {
                $urlSearchParams = $this->getURLSearchParams();
                $opType = 'all';
                if (isset($urlSearchParams[QueryParamsDef::OPERATION_TYPE]) && !empty($urlSearchParams[QueryParamsDef::OPERATION_TYPE])) {
                    if ($urlSearchParams[QueryParamsDef::OPERATION_TYPE] === 'sale') {
                        $opType = 'buy';
                    } else {
                        $opType = 'rent';
                    }
                }
                $location = '';
                if (!empty($urlSearchParams[QueryParamsDef::LOCATION_DATA])) {
                    if (is_array($urlSearchParams[QueryParamsDef::LOCATION_DATA])) {
                        $location = $urlSearchParams[QueryParamsDef::LOCATION_DATA][0];
                    } else {
                        $location = $urlSearchParams[QueryParamsDef::LOCATION_DATA];
                    }
                }
                $filterPropertyType = 'all';
                if (isset($urlSearchParams[QueryParamsDef::PROPERTY_TYPE]) && !empty($urlSearchParams[QueryParamsDef::PROPERTY_TYPE])) {
                    $filterPropertyType = $urlSearchParams[QueryParamsDef::PROPERTY_TYPE];
                }
                
                $bedroomsNo = $urlSearchParams["be"] ?? null;
                $seoContent = $this->SEOContentService->findOne('url', $opType, $filterPropertyType, $location, null, $bedroomsNo);

                if (!is_null($seoContent) && $locale !== Language::EN) {
                    $seoContentTranslation = $this->SEOContentService->getTranslationFor($seoContent, $locale);
                    if (!is_null($seoContentTranslation)) {
                        $seoContent = $seoContentTranslation;
                    }
                }
                $resultsStrings = $this->getListingSearchResultsString($urlSearchParams, $options['resultsNo'], $options['currentPage'], $locale);
                $resultsStr = $resultsStrings[0];
                $fullResultsStr = $resultsStrings[1];
                if (is_null($seoContent) || empty($seoContent->meta_title)) {
                    $metaTitle = $this->getDefaultMetaTitle($urlSearchParams, $fullResultsStr, $options['resultsNo'], $locale);
                } else {
                    $metaTitle = $seoContent->meta_title;
                }

                if (is_null($seoContent) || empty($seoContent->meta_description)) {
                    $metaDescription = $this->getDefaultMetaDescription($urlSearchParams, $resultsStr, $options['resultsNo'], $locale);
                } else {
                    $metaDescription = $seoContent->meta_description;
                }

                $metaTitle = str_replace(['[PageName]', '[No]'], [$fullResultsStr, $options['resultsNo']], $metaTitle);
                $metaDescription = str_replace(['[PageName]', '[No]'], [$resultsStr, $options['resultsNo']], $metaDescription);
            }

            if (isset($options['listItems']) && count($options['listItems'])) {
                $firstItem = $options['listItems'][0];
                $imageURL = $firstItem->primary_image;
                if (empty($primaryImage)) {
                    $images = json_decode(empty($firstItem->images) ? '[]' : $firstItem->images);
                    $imageURL = $images[0];
                }
            }
            SEOMeta::setCanonical(url()->current());
            $this->addAlternatelanguagesForCurrentURL($locale);
        } elseif (in_array($routeName, ['properties.single', 'single.listing.share-personal'])) {
            $snapshot = $options['snapshot'];
            $defaultMetas = $this->getDefaultMetasForSnapshot($snapshot, $locale);

            $metaTitle = $defaultMetas['title'];
            $metaDescription = $defaultMetas['description'];

            $imageURL = null;
            if (!empty($snapshot->primary_image)) {
                $imageURL = $snapshot->primary_image;
            } else {
                $imagesArr = json_decode($snapshot->images ?? '[]');
                if (count($imagesArr) > 0) {
                    $imageURL = $imagesArr[0];
                }
            }

            // this is just a temp
            $seo = SEO::where('related_type', 'property')
                ->where('related_id', $snapshot->listing_id)
                ->where(function ($qb) use ($locale) {
                    if ($locale == Language::EN) {
                        $qb->whereNull('language')
                            ->orWhere('language', '');
                    } else {
                        $qb->where('language', $locale);
                    }
                })
                ->first();

            if (!is_null($seo)) {
                if (!empty($seo->title)) {
                    $metaTitle = $seo->title;
                }
                if (!empty($seo->description)) {
                    $metaDescription = $seo->description;
                }
            }

            if (in_array($snapshot->property_type_id, [1, 14, 17, 16, 15, 18, 2, 7, 20])) { // more info in https://www.fgrealty.qa/api/listing-type
                $ldJsonObject = Schema::singleFamilyResidence()
                    ->additionalProperty($snapshot->property_type)
                    ->numberOfBedrooms($snapshot->bedrooms)
                    ->numberOfBathroomsTotal($snapshot->bathrooms);

                if (!empty($snapshot->built_up_area)) {
                    $ldJsonObject->floorSize($snapshot->built_up_area . ' MTK');
                }
            } else {
                $ldJsonObject = Schema::residence()
                    ->additionalProperty($snapshot->property_type);
            }

            $price = $snapshot->price_on_request || $snapshot->is_sold_leased ? 'Price on request' : $snapshot->price . ' QAR';
            $imagesArr = json_decode(empty($snapshot->images) ? '[]' : $snapshot->images);
            $primaryImage = empty($snapshot->primary_image) ? $imagesArr[0] : $snapshot->primary_image;
            $primaryImageURL = empty($primaryImage) ? '' : imageRoute('listing-big', $primaryImage);

            $ldJsonObject->name($snapshot->title)
                ->mainEntityOfPage(Schema::webPage()->id(url()->current()))
                ->address(Schema::postalAddress()->addressLocality($snapshot->location_name))
                ->priceSpecification(Schema::unitPriceSpecification()->price($price))
                ->image($primaryImageURL)
                ->description(strip_tags($snapshot->description))
                ->code($snapshot->ref_no);

            if (!empty($snapshot->geo_lat) && !empty($snapshot->geo_lon)) {
                $geo = Schema::geoCoordinates()
                    ->addressCountry('Qatar')
                    ->latitude($snapshot->geo_lat)
                    ->longitude($snapshot->geo_lon);

                $ldJsonObject->geo($geo);
            }

            $contactPoint = Schema::contactPoint()
                ->name($snapshot->owner->name)
                ->email($snapshot->owner->email)
                ->telephone($snapshot->owner->phone);
            if (!empty($snapshot->owner->profile_image)) {
                $contactPoint->image(imageRoute('square-150', $snapshot->owner->profile_image));
            }

            $agent = Schema::realEstateAgent()
                ->address(env('COMPANY_ADDRESS'))
                ->email($snapshot->owner->email)
                ->telephone($snapshot->owner->phone)
                ->name($snapshot->owner->name)
                ->contactPoint(
                    $contactPoint
                );
            if (!empty($snapshot->owner->profile_image)) {
                $agent->image(imageRoute('square-150', $snapshot->owner->profile_image));
            }

            if ($snapshot->ad_type === 'rent') {
                $offer = Schema::offerForLease()
                    ->offeredBy($agent);
            } else {
                $offer = Schema::offerForPurchase()
                    ->offeredBy($agent);
            }
            $offer->price($snapshot->price)
                ->priceCurrency('QAR');

            $realEstateListing = Schema::realEstateListing()
                ->mainEntity($ldJsonObject)
                ->offers($offer);

            Session::put('jsonLdString', $realEstateListing->__toString());
            SEOMeta::setCanonical(url()->current());
            $this->addAlternatelanguagesForCurrentURL($locale);
        } elseif (in_array($routeName, ['developments', 'magazine'])) {
            $seoContent = $this->SEOContentService->findOne('page', $routeName);
            if (!is_null($seoContent)) {
                $metasSet = false;
                if($locale != Language::EN) {
                    $translation = $this->SEOContentService->findTranslationFor($seoContent, $locale);
                    if(!is_null($translation)) {
                        $metaTitle = $translation->meta_title;
                        $metaDescription = $translation->meta_description;
                        $metasSet = true;
                    }
                }
                if(!$metasSet) {
                    $metaTitle = $seoContent->meta_title;
                    $metaDescription = $seoContent->meta_description;
                }
            }
            SEOMeta::setCanonical(url()->current());
            $this->addAlternatelanguagesForCurrentURL($locale);
        } elseif(in_array($routeName, ['agents.index'])) {
            $seoContent = $this->SEOContentService->findOne('page', 'find-agent');
            if (!is_null($seoContent)) {
                $metaTitle = $seoContent->meta_title;
                $metaDescription = $seoContent->meta_description;
            } else {
                $metaTitle = 'Find Real Estate Agents in Qatar | Top Real Estate Brokers of FGrealty';
                $metaDescription = "Find the best properties in Qatar with FGrealty's verified agents. Book a Viewing Now!";
            }
            
            // ugly but it does the job
            $jsonLdContent = '
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "RealEstateAgent",
    "name": "FGRealty",
    "url": "https://www.fgrealty.qa/",
    "logo": "https://www.fgrealty.qa/images/svg/FGREALTY.svg",
    "telephone": "+97430451451",
    "address": {
    "@type": "PostalAddress",
    "streetAddress": "Al Jazeera Tower",
    "addressLocality": "Doha",
    "addressRegion": "QA",
    "postalCode": "4011",
    "addressCountry": "QA"
    },
    "geo": {
    "@type": "GeoCoordinates",
    "latitude": "25.3243557",
    "longitude": "51.5357731"
    },
    "openingHours": "Mo-Sa 09:00-18:30",
    "priceRange": "QAR",
    "description": "FGrealty is a leading property finding portal in Qatar. Find properties for sale and rent with our verified property agents",
    "sameAs": [
    "https://www.facebook.com/fgrealtyqatar/",
    "https://twitter.com/fgrealty_qatar",
    "https://www.instagram.com/fgrealty/",
    "https://www.linkedin.com/company/fg-realty/"
    ]
}
</script>';
            Session::put('jsonLdString', $jsonLdContent);
            $this->addAlternatelanguagesForCurrentURL($locale);
        } elseif($routeName === 'fgrealty-whatsapp-channel') {
            $seoContent = $this->SEOContentService->findOne('page', 'fgrealty-whatsapp-channel');
            if (!is_null($seoContent)) {
                $metaTitle = $seoContent->meta_title;
                $metaDescription = $seoContent->meta_description;
            }
            $this->addAlternatelanguagesForCurrentURL($locale);
            if (!is_null($seoContent)) {
                $metaTitle = $seoContent->meta_title;
                $metaDescription = $seoContent->meta_description;
            } else {
                $metaTitle = 'FGRealty’s WhatsApp channel | FGREALTY';
                $metaDescription = "Join FGRealty’s WhatsApp channel for curated luxury listings, price trends, and VIP access to Qatar’s top properties. Tap to stay ahead today!";
            }
        } elseif(in_array($routeName, ['agents.single'])) {
            $agent = $options['agent'];
            $metaTitle = "$agent->name - Property Agent Qatar";
            $metaDescription = "$agent->name, $agent->position, FGRealty Qatar. {$agent->getFirstNamePart()} is a top-rated property agent. Book a viewing now!";
            $this->addAlternatelanguagesForCurrentURL($locale);
        } else {
            switch ($routeName) {
                case 'properties.search':
                    $metaTitle = "Search properties for rent and sale. " . $options['resultsNo'] . " properties";
                    $metaDescription = str_replace(['[PageName]', '[No]'], ["properties", $options['resultsNo']], $this->metaDefaults[$locale]['']);
                    break;
                case 'properties.search.exclusive':
                    $metaTitle = "Exclusive properties for rent and sale. " . $options['resultsNo'] . " properties";
                    $metaDescription = str_replace(['[PageName]', '[No]'], ["exclusive properties", $options['resultsNo']], $this->metaDefaults[$locale]['']);
                    break;
            }

            if (isset($options['listItems']) && count($options['listItems'])) {
                $firstItem = $options['listItems'][0];
                $imageURL = $firstItem->primary_image;
                if (empty($primaryImage)) {
                    $images = json_decode(empty($firstItem->images) ? '[]' : $firstItem->images);
                    $imageURL = $images[0];
                }
            }
            $this->addAlternatelanguagesForCurrentURL($locale);
        }
        $this->addFreeMetas($metaTitle, $metaDescription ?? "", $imageURL);
        SEOMeta::addMeta('robots', 'index,follow');
        SEOMeta::setCanonical(url()->current());
        return $seoContent;
    }

    private function addAlternatelanguagesForCurrentURL($locale) {
        if($locale == Language::EN) {
            SEOMeta::addAlternateLanguage("en-AE", url()->current());
        } else {
            SEOMeta::addAlternateLanguage("ar-AE", url()->current());
        }
    }

    private function getDefaultMetaTitle($params, $resultsStr, $recordsNo, $locale = Language::AR)
    {
        $str = "";
        $str = $this->SEOContentMetaBuilderService->appendPropertyType($params, $str, $locale);
        $str = $this->SEOContentMetaBuilderService->appendOperationType($params, $str, $locale);
        $str = $resultsStr . " - " . (isset($params[QueryParamsDef::STUDIO]) || isset($params[QueryParamsDef::BEDROOMS]) || !$recordsNo ? "" : $recordsNo . " ") . $str;
        return $str;
    }

    private function getDefaultMetaDescription($params, $resultsStr, $recordsNo, $locale = Language::EN)
    {
        $opType = '';
        if (!empty($params[QueryParamsDef::OPERATION_TYPE])) {
            $opType = $params[QueryParamsDef::OPERATION_TYPE] === 'sale' ? 'buy' : 'rent';
        }

        return $this->metaDefaults[$locale][$opType];
    }

    protected function getURLSearchParams()
    {
        $request = request();
        $urlPath = $request->decodedPath();

        // if this is a custom url, add the custom search params
        $extraConfigParams = [];
        $menusService = app()->make(MenusService::class);
        $extraMenuEntries = $menusService->getExtraMenuEntries();

        foreach ($extraMenuEntries as $menuEntryKey => $menuEntryConfig) {
            if ($menuEntryConfig['url'] === "/" . $urlPath) {
                if (!empty($menuEntryConfig['searchParams']) && is_array($menuEntryConfig['searchParams'])) {
                    $extraConfigParams = $menuEntryConfig['searchParams'];
                }
            }
        }
        if(\Str::endsWith($urlPath, "short-stay-property")) {
            $extraConfigParams['isShortStay'] = true;
        }

        $routeParameters = $request->route()->parameters();
        $routeName = $request->route()->getName();
        $geographiesMap = $this->geographyService->getCachedGeographiesMap();
        $searchParams = [];

        $pathParamsParser = function ($path) use ($geographiesMap, $routeParameters) {
            $parts = explode('-for-', $path);
            $urlParams = [];
            $geographyIds = [];

            if (count($parts) > 1) {
                $remainingParts = explode('-', $parts[1]);
                $areaSlug = join("-", array_splice($remainingParts, 1, count($remainingParts) - 1));

                // dd($geographiesMap);
                if (!empty($areaSlug)) {
                    if (isset($geographiesMap[$areaSlug])) {
                        $urlParams[QueryParamsDef::LOCATION_DATA] = $geographiesMap[$areaSlug];
                        $geographyIds[] = $geographiesMap[$areaSlug];
                    } elseif (isset($routeParameters['regionSlug']) && isset($geographiesMap[$routeParameters['regionSlug'] . '-' . $areaSlug])) {
                        $geographyIds[] = $geographiesMap[$routeParameters['regionSlug'] . '-' . $areaSlug];
                    }
                }

                if (!count($geographyIds)) {
                    if (
                        isset($routeParameters['regionSlug']) &&
                        isset($geographiesMap[$routeParameters['regionSlug']])
                    ) {
                        $geographyIds[] = $geographiesMap[$routeParameters['regionSlug']];
                    }
                }
            }

            $propertyTypesHashmap = $this->propertiesService->cachedPropertyTypesHashmap();

            if (strpos($parts[0], '-bedroom-') > -1) {
                $propertyTypePart = last(explode("-", $parts[0]));
                $urlParams[QueryParamsDef::PROPERTY_TYPE] = $propertyTypePart;
                $bedrooms = intval($parts[0]);
                $urlParams[QueryParamsDef::BEDROOMS] = $bedrooms;
            } else if (in_array($parts[0], ['studio-apartments'/*, '1-bedroom', '2-bedroom', '3-bedroom', '4-bedroom', '5-bedroom'*/])) {
                $urlParams[QueryParamsDef::PROPERTY_TYPE] = 'apartments';
                $bedrooms = 0;
                if (!Str::startsWith($parts[0], 'studio')) {
                    $bedrooms = intval($parts[0]);
                }
                $urlParams[QueryParamsDef::BEDROOMS] = $bedrooms;
            } else if (isset($propertyTypesHashmap[$parts[0]]) && !empty($propertyTypesHashmap[$parts[0]])) {
                if ($parts[0] === 'villas') {
                    $urlParams[QueryParamsDef::PROPERTY_TYPE] = [
                        'villas',
                        'commercial-villas',
                        'villa-compound'
                    ];
                } else {
                    $urlParams[QueryParamsDef::PROPERTY_TYPE] = $parts[0];
                }
            } else {
                if (count($parts) === 1) {
                    $parts = array_merge(['properties'], $parts);
                }

                if (!in_array($parts[1], ['rent', 'sale'])) {
                    $subparts = array_filter(explode("-", $parts[1]), function ($subpart) {
                        return !in_array($subpart, ['sale', 'rent']);
                    });

                    $cleanSubparts = join("-", $subparts);

                    if (isset($geographiesMap[$cleanSubparts])) {
                        $geographyIds[] = $geographiesMap[$cleanSubparts];
                    }
                }
            }
            $urlParams[QueryParamsDef::LOCATION_DATA] = count($geographyIds) > 0 ? $geographyIds[count($geographyIds) - 1] : [];

            return $urlParams;
        };

        $pathParamsParserAr = function ($path) use ($geographiesMap, $routeParameters) {
            $locale = Language::AR;
            $urlParams = [];
            $propertyURL = $routeParameters['propertyURL'];
            // extract property type
            $propertyTypesService = app()->make(PropertyTypesService::class);
            $allTypes = $propertyTypesService->getCachedTypesWithTranslations(Language::AR);

            // parsed translations
            $allTranslatedSlugs = [];
            $allTranslatedSlugsMap = [];
            foreach ($allTypes as $t) {
                if (!is_null($t->translations->first())) {
                    $allTranslatedSlugs[] = $t->translations->first()->url_value;
                    $allTranslatedSlugsMap[$t->translations->first()->url_value] = $t->url_value;
                }
            }

            usort($allTranslatedSlugs, function ($firstItem, $secondItem) {
                $len1 = strlen($firstItem);
                $len2 = strlen($secondItem);
                if ($len1 == $len2) {
                    return 0;
                }
                return $len1 < $len2 ? 1 : -1;
            });

            $transFound = false;
            foreach ($allTranslatedSlugs as $transURLValue) {
                if (strpos($routeParameters['propertyURL'], $transURLValue) > -1 && !$transFound) {
                    $urlParams[QueryParamsDef::PROPERTY_TYPE] = $allTranslatedSlugsMap[$transURLValue];
                    $propertyURL = str_replace($transURLValue, '', $propertyURL);
                    $transFound = true;
                }
            }

            if (isset($urlParams[QueryParamsDef::PROPERTY_TYPE]) && $urlParams[QueryParamsDef::PROPERTY_TYPE] === 'villas') {
                $urlParams[QueryParamsDef::PROPERTY_TYPE] = [
                    'villas',
                    'commercial-villas',
                    'villa-compound'
                ];
            }
            //اللؤلؤة-بورتو-أرابيا
            // مشيرب-قلب-الدوحة-مشيرب-قلب-الدوحة-للايجار-شقة-فندقية

            //remove op type
            $propertyURL = str_replace(['للايجار', 'للبيع', 'للايجار', 'تجارية-للايجار'], '', $propertyURL);
            $propertyURL = str_replace('--', '', $propertyURL);

            // check for location
            $locationsTranslationsMap = $this->geographyService->getCachedGeographiesMapWithTranslations($locale);
            $isRentOp = strpos($routeParameters['propertyURL'], __('for-rent', [], 'ar')) > -1;
            $isSaleOp = strpos($routeParameters['propertyURL'], __('for-sale', [], 'ar')) > -1;
            if ($isSaleOp) {
                $propertyURLParts = explode(__('for-sale', [], 'ar'), $routeParameters['propertyURL']);
                if (isset($propertyURLParts[1]) && !empty(trim($propertyURLParts[1], "-"))) {
                    $propertyURLParts = explode('-', trim($propertyURLParts[1], "-"));
                } else {
                    $propertyURLParts = [];
                }
            } elseif ($isRentOp) {
                $propertyURLParts = explode(__('for-rent', [], 'ar'), $routeParameters['propertyURL']);
                if (isset($propertyURLParts[1]) && !empty(trim($propertyURLParts[1], "-"))) {
                    $propertyURLParts = explode('-', trim($propertyURLParts[1], "-"));
                } else {
                    $propertyURLParts = [];
                }
            } else {
                $propertyURLParts = explode('-', $routeParameters['propertyURL']);
                Log::info("No operation type found for url " . $routeParameters['propertyURL']);
            }

            if (count($propertyURLParts) == 0) {
                if (isset($routeParameters['regionSlug']) && !empty($routeParameters['regionSlug'])) {
                    array_unshift($propertyURLParts, $routeParameters['regionSlug']);
                }
            }

            $pickedGeographyItem = null;
            if (is_array($propertyURLParts) && count($propertyURLParts) > 0) {
                $propertyURLPartsLength = count($propertyURLParts) - 1;
                $pickedGeographyItem = null;

                for ($i = 0; $i <= $propertyURLPartsLength; $i++) {
                    $firstLocationPartArr = [];
                    $secondLocationPartArr = [];
                    for ($j = 0; $j <= $propertyURLPartsLength; $j++) {
                        if ($j <= $i) {
                            $firstLocationPartArr[] = $propertyURLParts[$j];
                        } else {
                            $secondLocationPartArr[] = $propertyURLParts[$j];
                        }
                    }
                    $firstLocationPart = join("-", $firstLocationPartArr);
                    $secondLocationPart = join("-", $secondLocationPartArr);

                    $locationForFirstPart = null;
                    $locationForSecondPart = null;

                    foreach ($locationsTranslationsMap as $enSlug => $geographyTranslationArr) {
                        if (!empty($firstLocationPart) && $firstLocationPart == $geographyTranslationArr['translation']['ar']['slug']) {
                            $locationForFirstPart = $geographyTranslationArr;
                        }
                        if (!empty($secondLocationPart) && $secondLocationPart == $geographyTranslationArr['translation']['ar']['slug']) {
                            $locationForSecondPart = $geographyTranslationArr;
                        }
                        if (!is_null($locationForFirstPart) && !is_null($locationForSecondPart)) {
                            break;
                        }
                    }

                    if (empty($firstLocationPart) && !is_null($locationForSecondPart)) {
                        $pickedGeographyItem = $locationForSecondPart;
                    } else if (empty($secondLocationPart) && !is_null($locationForFirstPart)) {
                        $pickedGeographyItem = $locationForFirstPart;
                    } elseif (!is_null($locationForFirstPart) && !is_null($locationForSecondPart)) {
                        $pickedGeographyItem = $locationForSecondPart;
                    }

                    if (!is_null($pickedGeographyItem)) {
                        break;
                    }
                }
                // $iterationIndex = 0;
                // while (is_null($pickedGeographyItem) && !$geographiesIterated) {
                //     if ($iterationIndex == 0) {
                //         if(count($propertyURLParts) > 3) {
                //             $tempSlug = $propertyURLParts[$propertyURLPartsLength - 1] . '-' . $propertyURLParts[$propertyURLPartsLength];
                //         } else {
                //             $tempSlug = $propertyURLParts[$propertyURLPartsLength];
                //         }
                //     } else if ($iterationIndex == 1) {
                //         $tempSlug = $propertyURLParts[$propertyURLPartsLength];
                //     } else if ($iterationIndex == 2) {
                //         $tempSlug = $propertyURLParts[$propertyURLPartsLength - 1] . '-' . $propertyURLParts[$propertyURLPartsLength];
                //     } else if ($iterationIndex == 3) {
                //         $tempSlug = $propertyURLParts[$propertyURLPartsLength - 2] . '-' . $propertyURLParts[$propertyURLPartsLength - 1] . '-' . $propertyURLParts[$propertyURLPartsLength];
                //     } else if ($iterationIndex == 4) {
                //         $tempSlug = $propertyURLParts[$propertyURLPartsLength - 3] . '-' . $propertyURLParts[$propertyURLPartsLength - 2] . '-' . $propertyURLParts[$propertyURLPartsLength-1]. '-' . $propertyURLParts[$propertyURLPartsLength];
                //     }

                //     foreach ($locationsTranslationsMap as $enSlug => $geographyTranslationArr) {
                //         if ($tempSlug == $geographyTranslationArr['translation']['ar']['slug']) {
                //             $pickedGeographyItem = $geographyTranslationArr;
                //             break;
                //         }
                //     }

                //     if (empty($pickedSlug)) {
                //         $iterationIndex++;
                //     }
                //     if ($iterationIndex > 4 || $iterationIndex >= count($propertyURLParts)) {
                //         $geographiesIterated = true;
                //     }
                // }
            }

            if (!empty($pickedGeographyItem)) {
                $urlParams[QueryParamsDef::LOCATION_DATA] = $pickedGeographyItem['id'];
            }

            return $urlParams;
        };

        switch ($routeName) {
            case 'search.dynamic':
            case 'search.dynamic.region':
                $localOpType = 'sale';
                if (in_array($routeParameters['operationType'], ['rent', 'commercial-rent', 'للايجار'])) {
                    $localOpType = 'rent';
                }
                $searchParams[QueryParamsDef::OPERATION_TYPE] = $localOpType;
                $pathParserFn = $routeParameters['locale'] == Language::AR ? $pathParamsParserAr : $pathParamsParser;
                $searchParams = array_merge($searchParams, $pathParserFn($routeParameters['propertyURL']));
                break;
            case 'properties.search':
                $searchParams = [];
                break;
            case 'properties.search.allPropertyTypes':
                $searchParams[QueryParamsDef::PROPERTY_TYPE] = $routeParameters['propertyType'];
                break;
            case 'properties.search.allPropertyTypesWithLocation':
                // @TODO Continue the work here
                $searchParams[QueryParamsDef::PROPERTY_TYPE] = $routeParameters['propertyType'];
                break;
            case 'properties.search.exclusive':
                $searchParams[QueryParamsDef::IS_EXCLUSIVE] = true;
                break;
            default:
                //                die('no config found for '.$routeName);
                break;
        }

        $searchParams[QueryParamsDef::IS_EXCLUSIVE] = $routeName === 'properties.search.exclusive';

        return array_merge($extraConfigParams, $searchParams);
    }

    public function getBreadcrumbsLDJSON($breadcrumbs)
    {
        $locale = app()->getLocale();
        $jsonObject = null;
        if (is_array($breadcrumbs['breadcrumbsConfig'])) {
            $jsonObject = Schema::breadcrumbList()->name('Breadcrumbs');
            $listItems = [];
            foreach ($breadcrumbs['breadcrumbsConfig'] as $breadcrumbItem) {
                $listItems[] = Schema::ListItem()
                    ->position($breadcrumbItem['pos'])
                    ->name($breadcrumbItem['labels'][$locale])
                    ->item(
                        $breadcrumbItem['urls'][$locale]
                    );
            }
            // dd($listItems);
            $jsonObject->itemListElement($listItems);
        }
        return $jsonObject;
    }

    protected function getListingSearchResultsString($params, $resultsNo, $currentPage, $locale)
    {
        return $this->SEOContentMetaBuilderService->getListingSearchResultsString(request(), $params, $currentPage, $locale);
    }

    private function addFreeMetas(string $title, string $description, $imageName = ""): void
    {
        SEOMeta::setTitle($title);
        OpenGraph::setTitle($title);
        SEOMeta::setDescription($description);
        OpenGraph::setDescription($description);

        if (!empty($imageName)) {
            OpenGraph::addImage(imageRoute('og', $imageName));
        }
    }

    private function getDefaultMetasForSnapshot($snapshot, $locale = Language::EN) {
        if($locale == Language::EN) {
            $defaultMetaTitle = $snapshot->ad_type == 'rent' ? "For Rent -" : "For Sale -";
            $propertyTypeLabel = $snapshot->property_type_label;
            if(in_array($snapshot->property_type_id, [1, 2, 7, 14, 15, 16, 17, 18])) {
                if($snapshot->furnishings == "fully-furnished") {
                    $defaultMetaTitle .= "Furnished";
                }
                if($snapshot->bedrooms != "") {
                    if($snapshot->bedrooms == "0") {
                        $defaultMetaTitle .= " Studio";
                        $propertyTypeLabel = "Studio";
                    } else {
                        $defaultMetaTitle .= " ".intval($snapshot->bedrooms)." Bedroom";
                        if(strpos($snapshot->bedrooms, "+") > -1) {
                            $defaultMetaTitle .= " + Maid";
                        }
                    }
                }
            } else if(in_array($snapshot->property_type_id, [3])) {
                if($snapshot->furnishings_office == "fully-furnished") {
                    $defaultMetaTitle .= " Furnished";
                }
            }

            if($snapshot->bedrooms != "0") {
                $defaultMetaTitle .= " ".$propertyTypeLabel;
            }

            // location part
            $cachedGeographies = $this->propertiesService->cachedGeographiesMap($locale);
            $usedGeo = null;
            foreach($cachedGeographies as $gSlug => $geoData) {
                if($geoData['id'] == $snapshot->location_id) {
                    $usedGeo = $geoData;
                    break;
                }
            }
            // if(!empty($snapshot->tower_name)) {
            //     $defaultMetaTitle .= " in ".$snapshot->tower_name;
            // }
            if(!is_null($usedGeo)) {
                $defaultMetaTitle .= (empty($snapshot->tower_name) ? " in " : ", ").$usedGeo['name'];
                if(!is_null($usedGeo['parent'])) {
                    $defaultMetaTitle .= ", ".$usedGeo['parent']['name'];
                }
            }
            $defaultMetaTitle .= " [".$snapshot->ref_no."]";
            $defaultMetaDescription = $propertyTypeLabel." ".$defaultMetaTitle.". Book a viewing now.";
        } elseif($locale == Language::AR) {
            // dd($snapshot);
            $defaultMetaTitle = $snapshot->ad_type == 'rent' ? __("For Rent", [], $locale) : __("For Sale", [], $locale);
            $defaultMetaTitle .= " - ";
            $propertyTypeLabel = $snapshot->property_type_label;
            if(in_array($snapshot->property_type_id, [1, 2, 7, 14, 15, 16, 17, 18])) {
                if($snapshot->furnishings == "fully-furnished") {
                    $defaultMetaTitle .= " ".__("Furnished", [], $locale);
                }
                if($snapshot->bedrooms != "") {
                    if($snapshot->bedrooms == "0") {
                        $defaultMetaTitle .= " ".__("Studio", [], $locale);
                        $propertyTypeLabel = "Studio";
                    } else {
                        // $defaultMetaTitle .= " ".intval($snapshot->bedrooms)." Bedroom";
                        $defaultMetaTitle .= " ".__("bedroomsNo Bedroom", ['bedroomsNo' => intval($snapshot->bedrooms)], $locale);
                        if(strpos($snapshot->bedrooms, "+") > -1) {
                            $defaultMetaTitle .= " ".__("+ Maid", [], $locale);
                        }
                    }
                }
            } else if(in_array($snapshot->property_type_id, [3])) {
                if($snapshot->furnishings_office == "fully-furnished") {
                    $defaultMetaTitle .= " ".__("Furnished", [], $locale);
                }
            }

            $translatedPropertyLabel = __($propertyTypeLabel, [], $locale);
            if($snapshot->bedrooms != "0") {
                $defaultMetaTitle .= " ".$translatedPropertyLabel;
            }

            // location part
            $cachedGeographies = $this->propertiesService->cachedGeographiesMap($locale);
            $usedGeo = null;
            foreach($cachedGeographies as $gSlug => $geoData) {
                if($geoData['id'] == $snapshot->location_id) {
                    $usedGeo = $geoData;
                    break;
                }
            }
            // if(!empty($snapshot->tower_name)) {
            //     $defaultMetaTitle .= " ".__("in :location", ["location" => $snapshot->tower_name], $locale);
            // }
            if(!is_null($usedGeo)) {
                // if(!empty($snapshot->tower_name)) {
                //     $defaultMetaTitle .= " ,". $usedGeo['translation'][$locale]['name'];
                // } else {
                $defaultMetaTitle .= " ".__("in :location", ["location" => $usedGeo['translation'][$locale]['name']], $locale);
                // }
                if(!is_null($usedGeo['parent'])) {
                    $defaultMetaTitle .= ", ".$usedGeo['parent']['translation'][$locale]['name'];
                }
            }
            $defaultMetaTitle .= " [".$snapshot->ref_no."]";
            $defaultMetaDescription = $translatedPropertyLabel." ".$defaultMetaTitle.". ".__("Book a viewing now!", [], $locale);
        }

        return [
            "title" => $defaultMetaTitle,
            "description" => $defaultMetaDescription
        ];
    }
}
