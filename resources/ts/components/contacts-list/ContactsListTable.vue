<template>
    <div
        class="px-md-2 d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 v-if="componentState.viewType == viewType.Table" class="h2">
            <div class="d-flex align-items-center gap-2">
                <span>
                    Contacts List
                </span>
                <div v-if="storeState.isLoading" class="d-flex justify-content-center fs-6">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            </div>
        </h1>
        <h1 v-if="componentState.viewType == viewType.Edit" class="h2">
            Edit contact
        </h1>
        <h1 v-if="componentState.viewType == viewType.Add" class="h2">
            Add contact
        </h1>
        <div v-if="componentState.viewType === viewType.Table" class="btn-toolbar mb-2 mb-md-0 gap-2">
            <div v-if="componentState.userIsManager" class="btn-group btn-group-sm me-2">
                <button :disabled="storeState.isLoading" v-if="hasMasterAccess" class="btn btn-sm btn-outline-secondary"
                    :class="{
                        active:
                            storeState.vt ===
                            tableViewType.Masterlist,
                    }" @click="showList(tableViewType.Masterlist)">
                    Master
                </button>
                <button :disabled="storeState.isLoading" class="btn btn-sm btn-outline-secondary" :class="{
                    active:
                        storeState.vt ===
                        tableViewType.Personal,
                }" @click="showList(tableViewType.Personal)">
                    Personal
                </button>
            </div>
            <button :disabled="storeState.isLoading" @click="goTo('/check')" class="btn btn-sm btn-outline-secondary"
                :class="{ 'btn-disabled': storeState.isLoading }">
                <i class="bi bi-search"></i>&nbsp; Check
            </button>
            <button @click="goTo('/import')" :disabled="storeState.isLoading" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-upload"></i>&nbsp; Import
            </button>
            <button v-if="componentState.userIsManager" @click="componentState.modal.isOpen = true"
                :disabled="storeState.allRecords < 1 || storeState.isLoading"
                :class="{ 'disabled': storeState.allRecords < 1 }"
                class="btn btn-sm btn-outline-secondary position-relative">
                <i class="bi bi-download"></i>&nbsp; Export
            </button>

            <button v-if="componentState.userIsManager" :disabled="storeState.isLoading" @click="goTo('/duplicates')"
                class="btn btn-sm btn-outline-warning">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-copy"
                    viewBox="0 0 16 16">
                    <path fill-rule="evenodd"
                        d="M4 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V2Zm2-1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1H6ZM2 5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1h1v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1v1H2Z" />
                </svg>&nbsp; Duplicates
            </button>
            <button :disabled="storeState.isLoading" @click="goTo('/new')" class="btn btn-sm btn-outline-secondary">
                <span data-feather="plus"></span>
                Add new item
            </button>
        </div>
    </div>
    <div v-if="componentState.saving.isSuccess || componentState.saving.isFailure
    " class="row">
        <div class="col p-2">
            <div v-if="componentState.saving.isSuccess" class="alert alert-success">
                The contact was succesfully saved
            </div>
            <div v-if="componentState.saving.isFailure" class="alert alert-danger">
                An error occured while saving the contact
            </div>
        </div>
    </div>
    <div v-if="deleteState.msg">
        <div class="alert alert-dismissible"
            :class="{ 'alert-success': deleteState.isSuccess, 'alert-danger': !deleteState.isSuccess }">{{
                deleteState.msg }}
            <button @click="clearDeleteAlert" type="button" class="btn-close" data-bs-dismiss="alert"
                aria-label="Close"></button>
        </div>
    </div>
    <div v-if="componentState.viewType === viewType.Table" class="col mt-2">
        <!-- Search and More Filters Section -->
        <div class="d-flex justify-content-between align-items-center gap-2 mb-3">
            <div class="d-flex flex-1 justify-content-start">
                <button @click="toggleMoreFilters()" :disabled="storeState.isLoading"
                    class="btn btn-sm btn-outline-secondary" href="javascript:void(0)">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                        class="feather feather-chevrons-right">
                        <polyline points="13 17 18 12 13 7"></polyline>
                        <polyline points="6 17 11 12 6 7"></polyline>
                    </svg>
                    More filters
                </button>
            </div>
            <div>
                <input type="search" class="form-control form-control-sm" :value="storeState.term" placeholder="Search contacts..."
                    @input="(event: any) => onSearchTermChange(event?.target?.value)" />
            </div>
        </div>

        <!-- Contacts Filters Component -->
        <ContactsFilters />

        <!-- Bulk Actions Section -->
        <div v-if="componentState.showBulkActions" class="bulk-actions">
            <div class="input-group input-group-sm">
                <span class="input-group-text">With selection (<span>{{ componentState.selectedContactIds.length }}</span>):</span>
                <select class="form-select form-select-sm" v-model="componentState.selectedBulkAction" :disabled="componentState.selectedContactIds.length === 0">
                    <option value="">Select an action</option>
                    <option value="export_mailjet">Export to Mailjet</option>
                </select>
            </div>
            <button
                @click="applyBulkAction"
                :disabled="componentState.selectedContactIds.length === 0 || !componentState.selectedBulkAction"
                class="btn btn-primary btn-sm"
            >
                Apply
            </button>
        </div>

        <section>
            <article class="contactsListWrapper">
                <table class="table table-sm mb-0 dataTable fw-light align-middle contactsListWrapper__tableHead">
                    <thead>
                        <tr>
                            <th class="select">
                                <input
                                    type="checkbox"
                                    :checked="componentState.isAllSelected"
                                    @change="toggleAllSelection"
                                    class="form-check-input"
                                />
                            </th>
                            <th class="fav">&nbsp;</th>
                            <th class="rem"></th>
                            <th class="lastContact">Last contact</th>
                            <th class="id">Contact NO</th>
                            <th class="name">Name</th>
                            <th class="gender">Gender</th>
                            <th class="mobile">Mobile(s)</th>
                            <th class="email">Email(s)</th>
                            <th class="position">Occupation</th>
                            <th class="tags">Tags</th>
                            <th class="organization">Company</th>
                            <th v-if="componentState.tableViewType !==
                                tableViewType.Personal
                            " class="created_by">
                                Created By
                            </th>
                            <th class="actions">&nbsp;</th>
                        </tr>
                    </thead>
                </table>
                <div style="
                        display: inline-block;
                        width: 100%;
                        height: 100%;
                        min-height: calc(100vh - 285px);
                        max-height: calc(100vh - 285px);
                        overflow-y: scroll;
                        padding-right: 10px;
                    ">
                    <table id="contacts-list-table"
                        class="table table-sm dataTable fw-light align-middle contactsListWrapper__table">
                        <tbody>
                            <tr v-for="data in storeState.records" :class="{ 'is-favorite': !!data.verified }">
                                <td class="select">
                                    <input
                                        type="checkbox"
                                        :checked="isContactSelected(data.id)"
                                        @change="toggleContactSelection(data.id)"
                                        class="form-check-input"
                                    />
                                </td>
                                <td class="fav">
                                    <i @click="toggleContactStar(data)" class="bi bi-star-fill"></i>
                                </td>
                                <td class="rem">
                                    <i v-if="!!data.reminders" @click="triggerAddReminderModal(data, 'contacts_list')"
                                        class="bi bi-calendar-check"></i>
                                </td>
                                <td class="lastContact">
                                    <div v-if="data.last_call_response" data-bs-toggle="tooltip"
                                        :data-bs-title="data.last_call_response + ' at: ' + data.last_call_timing"
                                        style="white-space: nowrap">
                                        <i class="bi bi-telephone-fill" :class="{
                                            'text-success': data.last_call_response == 'answered',
                                            'text-danger': data.last_call_response == 'not_answered',
                                            'text-secondary': data.last_call_response == 'not_interested' || data.last_call_response == 'inactive',
                                        }"></i>
                                    </div>
                                </td>
                                <td class="id">{{ data.id || "-" }}</td>
                                <td class="name">{{ data.name || "-" }}</td>
                                <td class="gender">{{ data.gender || "-" }}</td>
                                <td class="mobile">
                                    {{ data.mobile_1 || "-" }}
                                </td>
                                <td class="email">{{ data.email_1 || "-" }}</td>
                                <td class="position">
                                    {{ data.position || "-" }}
                                </td>
                                <td class="tags">
                                    <div class="contacts-list">
                                        <span v-if="!!data.landlord_id"
                                            class="badge text-primary border border-primary">Landlord</span>
                                        <span v-if="!!data?.leads_ids?.length"
                                            class="badge text-info border border-info">Leads</span>
                                        <div v-for="tag of data.tag_labels.filter((_: any, index: any) => index < 3)"
                                            class="contacts-list__tag">
                                            {{ tag }}
                                        </div>
                                        <div v-if="data.tag_labels.some((_: any, index: any) => index >= 3)"
                                            class="contacts-list__tag contacts-list__tag--more">{{
                                                data.tag_labels.length - 3 }} more</div>
                                        &nbsp;
                                    </div>
                                </td>
                                <td class="organization">
                                    {{ data.company_name || "-" }}
                                </td>
                                <td v-if="componentState.tableViewType !==
                                    tableViewType.Personal
                                " class="created_by">
                                    <div>
                                        {{ data.agent_name }}
                                    </div>
                                    <div>
                                        {{ data.created_at }}
                                    </div>
                                </td>
                                <td class="actions">
                                    <div>
                                        <button class="btn btn-sm btn-light dropdown-toggle" type="button"
                                            :aria-labelledby="'dropdownMenuButton' + data.id" data-bs-toggle="dropdown"
                                            aria-expanded="false">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu" :id="'dropdownMenuButton' + data.id">
                                            <router-link :to="`/${data.id}/edit`" class="dropdown-item"><i
                                                    class="bi bi-pencil"></i> Edit</router-link>
                                            <li>
                                                <button @click="triggerAddReminderModal(data, 'contacts_list')"
                                                    type="button" class="dropdown-item"><i
                                                        class="bi bi-calendar-check"></i>
                                                    Reminder</button>
                                            </li>
                                            <li>
                                                <button v-if="data.can_delete" @click="confirmDelete(data)"
                                                    type="button" class="dropdown-item"><i class="bi bi-eraser"></i>
                                                    Delete</button>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            <tr v-if="!storeState.records.length">
                                <td colspan="12">No entries</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="contactsListWrapper__tableFooter">
                    <div>
                        Showing records
                        {{
                            (storeState.currentPage - 1) *
                            storeState.perPage +
                            1
                        }}
                        -
                        {{
                            (storeState.currentPage - 1) *
                            storeState.perPage +
                            storeState.records?.length ?? 0
                        }}
                        of {{ storeState.recordsTotal }}
                    </div>
                    <div>
                        <Paginator :count="storeState.recordsTotal" :currentPage="storeState.currentPage"
                            :perPage="storeState.perPage" @page-change="onPageChange($event)" />
                    </div>
                </div>
            </article>
        </section>
    </div>
    <div class="modal fade" id="actionModal" tabindex="-1" aria-labelledby="actionModalLabel" aria-hidden="true">
        <div :class="{ 'modal-dialog': true, 'modal-dialog-centered': true, 'modal-dialog-scrollable': true }">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="actionModalLabel">Export</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h3>Export contacts for:</h3>
                    <fieldset class="d-inline-block ps-4">
                        <div v-for="item of exportPossibilities" :key="item.key" class="form-check"
                            :class="{ 'disabled': item.isDisabled }">
                            <input :disabled="item.isDisabled" v-model="componentState.exportType" name="export_type"
                                class="form-check-input" type="radio" :id="`${item.key}Radio`" :value="item.key">
                            <label class="form-check-label" :for="`${item.key}Radio`">
                                {{ item.label }}
                            </label>
                        </div>
                    </fieldset>
                    <div class="input-group mb-3 mt-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" :value="true"
                                v-model="componentState.hasExportLimit" id="exportLimitDisabled">
                            <label class="form-check-label" for="exportLimitDisabled">
                                Limit export to:
                            </label>
                            <div class="d-flex align-items-center gap-2">
                                <input :max="10000" class="form-control mt-2" :disabled="!componentState.hasExportLimit"
                                    type="number" v-model="componentState.exportLimit">
                                <span>items</span>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Cancel
                    </button>
                    <button :disabled="!componentState.exportType" @click="executeExport($event)" type="button"
                        class="btn btn-primary">
                        Ok
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mailjet Modal -->
    <div class="modal fade" id="mailjetModal" tabindex="-1" aria-labelledby="mailjetModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="mailjetModalLabel">Sync Contacts to Mailjet</h5>
                    <button type="button" class="btn-close" @click="closeMailjetModal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Step 1: Select List -->
                    <div v-if="componentState.mailjetModal.step === 'select'">
                        <div v-if="componentState.mailjetModal.isLoading" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading contact lists...</p>
                        </div>

                        <div v-else>
                            <p v-if="componentState.selectedContactIds.length > 0">
                                <strong>{{ componentState.selectedContactIds.length }}</strong> selected contacts will be synced to Mailjet.
                                Only contacts with valid email addresses will be included.
                            </p>
                            <p v-else>
                                <strong>{{ storeState.recordsTotal }}</strong> contacts will be synced to Mailjet.
                                Only contacts with valid email addresses will be included.
                            </p>

                            <div v-if="componentState.mailjetModal.error" class="alert alert-danger">
                                {{ componentState.mailjetModal.error }}
                            </div>

                            <div class="mb-3">
                                <label for="contactListSelect" class="form-label">Select Contact List:</label>
                                <select
                                    id="contactListSelect"
                                    class="form-select"
                                    v-model="componentState.mailjetModal.selectedListId"
                                >
                                    <option value="">-- Select a list --</option>
                                    <option
                                        v-for="list in componentState.mailjetModal.contactLists"
                                        :key="list.ID"
                                        :value="list.ID"
                                    >
                                        {{ list.Name }}
                                    </option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Or create a new list:</label>
                                <div class="input-group">
                                    <input
                                        type="text"
                                        class="form-control"
                                        placeholder="Enter new list name"
                                        v-model="componentState.mailjetModal.newListName"
                                        @keyup.enter="createNewList"
                                    >
                                    <button
                                        class="btn btn-outline-secondary"
                                        type="button"
                                        @click="createNewList"
                                        :disabled="!componentState.mailjetModal.newListName.trim() || componentState.mailjetModal.isLoading"
                                    >
                                        Create
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Syncing -->
                    <div v-if="componentState.mailjetModal.step === 'syncing'" class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Syncing...</span>
                        </div>
                        <p class="mt-2">Syncing contacts to Mailjet...</p>
                        <p class="text-muted">Please wait while we sync your contacts.</p>
                    </div>

                    <!-- Step 3: Complete -->
                    <div v-if="componentState.mailjetModal.step === 'complete'">
                        <!-- Sync Summary Alert -->
                        <div v-if="componentState.mailjetModal.summaryMessage" class="alert" :class="{
                            'alert-success': componentState.mailjetModal.syncProgress.failed === 0 && (componentState.mailjetModal.syncProgress.synced > 0 || componentState.mailjetModal.syncProgress.already_exists > 0),
                            'alert-warning': componentState.mailjetModal.syncProgress.failed > 0 || (componentState.mailjetModal.syncProgress.no_email > 0 && componentState.mailjetModal.syncProgress.synced === 0 && componentState.mailjetModal.syncProgress.already_exists === 0),
                            'alert-info': componentState.mailjetModal.syncProgress.synced === 0 && componentState.mailjetModal.syncProgress.failed === 0 && componentState.mailjetModal.syncProgress.already_exists > 0
                        }">
                            <div class="mb-2">
                                <h6 class="mb-2">Sync results:</h6>
                                <div v-if="componentState.mailjetModal.syncProgress.synced > 0" class="mb-1">
                                    <strong>{{ componentState.mailjetModal.syncProgress.synced }}</strong> users have been synced
                                </div>
                                <div v-if="componentState.mailjetModal.syncProgress.already_exists > 0" class="mb-1">
                                    <strong>{{ componentState.mailjetModal.syncProgress.already_exists }}</strong> users already existed
                                </div>
                                <div v-if="componentState.mailjetModal.syncProgress.no_email > 0" class="mb-1">
                                    <strong>{{ componentState.mailjetModal.syncProgress.no_email }}</strong> users have no email
                                </div>
                                <div v-if="componentState.mailjetModal.syncProgress.failed > 0" class="mb-1">
                                    <strong>{{ componentState.mailjetModal.syncProgress.failed }}</strong> user synchronization failed
                                </div>
                                <div class="fw-bold mt-2 pt-2 border-top">
                                    Total users: <strong>{{ componentState.mailjetModal.syncProgress.total }}</strong>
                                </div>
                                <div v-if="componentState.mailjetModal.syncProgress.synced === 0 && componentState.mailjetModal.syncProgress.already_exists === 0 && componentState.mailjetModal.syncProgress.failed === 0 && componentState.mailjetModal.syncProgress.no_email === 0">
                                    No contacts were processed
                                </div>
                            </div>

                            <!-- Error Details -->
                            <div v-if="componentState.mailjetModal.syncProgress.errors.length > 0" class="mt-3">
                                <h6 class="text-danger mb-2">Error Details:</h6>
                                <div class="bg-light p-2 rounded" style="max-height: 200px; overflow-y: auto;">
                                    <div v-for="(error, index) in componentState.mailjetModal.syncProgress.errors" :key="index" class="text-danger small mb-1">
                                        • {{ error }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button
                        v-if="componentState.mailjetModal.step === 'select'"
                        type="button"
                        class="btn btn-secondary"
                        @click="closeMailjetModal"
                    >
                        Cancel
                    </button>
                    <button
                        v-if="componentState.mailjetModal.step === 'select'"
                        type="button"
                        class="btn btn-primary"
                        @click="syncToMailjet"
                        :disabled="!componentState.mailjetModal.selectedListId || componentState.mailjetModal.isLoading"
                    >
                        Sync Contacts
                    </button>
                    <button
                        v-if="componentState.mailjetModal.step === 'complete'"
                        type="button"
                        class="btn btn-primary"
                        @click="closeMailjetModal"
                    >
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref, watch } from "vue";
import type { Ref } from "vue";
import { BEContactsResponse } from "./../../model/be-models.interface";
import ContactsListService from "./contacts-list.service";
import StarIcon from "./../icons/StarIcon.vue";
import FGVirtualSelect from "./../FGVirtualSelect.vue";
import Paginator from "./../paginator/Index.vue";
import ContactsFilters from "./ContactsFilters.vue";
import {
    Contact,
    ContactListSearchState,
    ContactsListComponentState,
    TableViewType,
    ViewType,
} from "../../model/fe-models.interface";
import { useRouter } from "vue-router";
import { useStore } from "vuex";


export declare const window: any;
export declare const bootstrap: any;

export default defineComponent({
    name: "ContactsList",

    setup() {
        const store = useStore();
        const router = useRouter();
        const viewType = ViewType;
        const tableViewType = TableViewType;
        const modalRef = ref<any>(null);



        const componentState: Ref<ContactsListComponentState> = ref({
            viewType: ViewType.Table,
            tableViewType: TableViewType.Personal,
            editEntry: null,
            saving: {
                isSuccess: false,
                isFailure: false,
            },
            moreFiltersPanelVisible: false,
            userIsManager: false,
            modal: {
                isOpen: false,
                title: "Export contacts"
            },
            exportType: null,
            hasExportLimit: true,
            exportLimit: 1000,
            // Row selection state
            selectedContactIds: [],
            isAllSelected: false,
            showBulkActions: false,
            selectedBulkAction: '',
            mailjetModal: {
                isOpen: false,
                isLoading: false,
                step: 'select',
                selectedListId: null,
                newListName: '',
                contactLists: [],
                syncProgress: {
                    total: 0,
                    synced: 0,
                    already_exists: 0,
                    failed: 0,
                    no_email: 0,
                    errors: []
                },
                error: null,
                summaryMessage: null
            }
        });
        const exportPossibilities = [{
            key: 'whatsapp',
            label: 'Whatsapp',
            isDisabled: false
        },
        {
            key: 'whatsapp_qatar',
            label: 'Whatsapp Qatar',
            isDisabled: false
        },
        {
            key: 'marketing_platform',
            label: 'Marketing Platform',
            isDisabled: false
        },
        {
            key: 'newsletter',
            label: 'Newsletter',
            isDisabled: true
        }
        ]
        const deleteState = ref({
            isSuccess: false,
            msg: null
        })
        const userRef: Ref<any> = ref(null);
        const hasMasterAccess: Ref<boolean> = ref(false);

        const storeState = computed(() => ({
            ...store.state
        }));
        const updateVT = (viewType: TableViewType) => store.dispatch('updateVT', viewType);
        const onSearchTermChange = (term: string) => store.dispatch('updateSearchTerm', term);

        const toggleMoreFilters = () => {
            const filtersPanel = document.getElementById('contactsFiltersPanel');
            if (filtersPanel) {
                if (filtersPanel.classList.contains('d-none')) {
                    filtersPanel.classList.remove('d-none');
                } else {
                    filtersPanel.classList.add('d-none');
                }
            }
        };

        const onPageChange = (pageNo: number) => {
            store.dispatch('updatePage', pageNo)
        };



        const toggleContactStar = (contact: Contact) => {
            store.dispatch('updateStarredState', contact);
        };

        const showList = (vt: TableViewType) => {
            updateVT(vt);
        };

        watch(() => componentState.value.modal.isOpen, () => {
            componentState.value.modal.isOpen ? modalRef.value.show() : modalRef.value.hide();
            if (!componentState.value.modal.isOpen) {
                componentState.value.exportType = null;
            }
        })

        onMounted(async () => {
            if (!store.state.isStoreLoaded) {
                const changedFilters =
                    localStorage.getItem("contactsTableFilters") || "{}";
                const persistedFilters = JSON.parse(changedFilters);
                store.dispatch('loadStore', persistedFilters);
            }

            const userManagerRole = (
                (!!window.userRoles ? window.userRoles : []) as Array<{
                    name: string;
                }>
            ).find(
                (eachRole) =>
                    eachRole.name === "Office Manager" ||
                    eachRole.name === "Call Center Agent"
            );
            if (!!userManagerRole) {
                componentState.value = {
                    ...componentState.value,
                    userIsManager: true,
                };
            }
            userRef.value = window.user;
            const userIsCallCenterAgent = (
                (!!window.userRoles ? window.userRoles : []) as Array<{
                    name: string;
                }>
            ).find((eachRole) => eachRole.name === "Call Center Agent");

            const usersWithAccessToMasterlist = ["<EMAIL>", "<EMAIL>", "<EMAIL>"];

            if (
                (!!userRef.value &&
                    !!userRef.value.email &&
                    userRef.value.email === "<EMAIL>" ||
                    userRef.value.email === "<EMAIL>" ||
                    userRef.value.email === "<EMAIL>" ||
                    userRef.value.email === "<EMAIL>" ||
                    userRef.value.email === "<EMAIL>" ||
                    usersWithAccessToMasterlist.includes(userRef.value.email)) ||
                userIsCallCenterAgent
            ) {
                hasMasterAccess.value = true;
            }

            const modalEl = document.getElementById('addReminderModal');
            modal = new bootstrap.Modal(modalEl, {
                keyboard: false
            })

            const modalRefEl = document.getElementById("actionModal");
            const exportModal = new bootstrap.Modal(modalRefEl, {
                keyboard: false,
            });
            modalRef.value = exportModal;
            modalRefEl.addEventListener('hidden.bs.modal', function () {
                componentState.value.modal.isOpen = false;
            });

            // Initialize Mailjet modal
            const mailjetModalEl = document.getElementById("mailjetModal");
            if (mailjetModalEl) {
                const mailjetModal = new bootstrap.Modal(mailjetModalEl, {
                    keyboard: false,
                });

                // Watch for modal state changes
                watch(() => componentState.value.mailjetModal.isOpen, (isOpen) => {
                    if (isOpen) {
                        mailjetModal.show();
                    } else {
                        mailjetModal.hide();
                    }
                });

                mailjetModalEl.addEventListener('hidden.bs.modal', function () {
                    if (componentState.value.mailjetModal.isOpen) {
                        closeMailjetModal();
                    }
                });
            }
        });

        const executeExport = ($event: any) => {
            const exportLimit = componentState.value.hasExportLimit && componentState.value.exportLimit ? Number(componentState.value.exportLimit) : null
            const nextSearchState: ContactListSearchState = { ...store.state }
            ContactsListService.fetch(nextSearchState, true, componentState.value.exportType, exportLimit).then(
                (response: BEContactsResponse) => {
                    console.log('this is the response', response)
                }
            );
        }

        const onEntrySave = (data: Contact) => {
            componentState.value.saving.isSuccess = false;
            componentState.value.saving.isFailure = false;
            ContactsListService.saveEntry(data)
                .then((updatedItem: Contact) => {
                    componentState.value.editEntry = {
                        ...updatedItem,
                        remarks: "",
                    };
                    componentState.value.saving.isSuccess = true;
                    componentState.value.saving.isFailure = false;

                    window.location.hash = `#/${updatedItem.id}`;
                })
                .catch(() => {
                    componentState.value.saving.isSuccess = false;
                    componentState.value.saving.isFailure = true;
                });
        };


        let modal: any = null;

        const triggerAddReminderModal = (item: any, area: string) => {
            router.push({
                query: {
                    id: item.id,
                    param: 'contacts_list'
                }
            })
            window.triggerAddReminderModal(item.id, 'contacts_list');

        }

        const confirmDelete = (contactToDelete: Contact) => {
            if (confirm("Are you sure you want to delete contact: " + contactToDelete.name + " ?")) {
                ContactsListService.deleteContact(contactToDelete.id)
                    .then(async (data: {
                        isSuccess: boolean,
                        msg: string
                    }) => {
                        deleteState.value.isSuccess = data.isSuccess;
                        deleteState.value.msg = data.msg;

                        if (data.isSuccess) {
                            const data = await ContactsListService.fetch({
                                vt: componentState.value.tableViewType,
                                records: [],
                                recordsTotal: 0
                            });
                        }
                    })
                    .catch((err: any) => {
                        alert(err)
                    })
            }
        }

        const clearDeleteAlert = () => {
            deleteState.value.isSuccess = false;
            deleteState.value.msg = null;
        }

        const goTo = (url: string) => {
            return router.push(url)
        }

        const openMailjetModal = async () => {
            componentState.value.mailjetModal.isOpen = true;
            componentState.value.mailjetModal.isLoading = true;
            componentState.value.mailjetModal.error = null;

            try {
                const response = await ContactsListService.fetchMailjetContactLists();
                if (response.success) {
                    componentState.value.mailjetModal.contactLists = response.lists;
                } else {
                    componentState.value.mailjetModal.error = response.error || 'Failed to fetch contact lists';
                }
            } catch (error) {
                componentState.value.mailjetModal.error = 'Failed to connect to Mailjet';
            } finally {
                componentState.value.mailjetModal.isLoading = false;
            }
        };

        const closeMailjetModal = () => {
            componentState.value.mailjetModal.isOpen = false;
            componentState.value.mailjetModal.step = 'select';
            componentState.value.mailjetModal.selectedListId = null;
            componentState.value.mailjetModal.newListName = '';
            componentState.value.mailjetModal.error = null;
            componentState.value.mailjetModal.summaryMessage = null;
            componentState.value.mailjetModal.syncProgress = {
                total: 0,
                synced: 0,
                already_exists: 0,
                failed: 0,
                no_email: 0,
                errors: []
            };
        };

        const createNewList = async () => {
            if (!componentState.value.mailjetModal.newListName.trim()) {
                componentState.value.mailjetModal.error = 'Please enter a list name';
                return;
            }

            componentState.value.mailjetModal.isLoading = true;
            componentState.value.mailjetModal.error = null;

            try {
                const response = await ContactsListService.createMailjetContactList(
                    componentState.value.mailjetModal.newListName.trim()
                );

                if (response.success) {
                    // Ensure the list has the correct structure for the dropdown
                    const newList = response.list as Array<{
                        Address: string;
                        CreatedAt: string;
                        ID: number;
                        IsDeleted: boolean;
                        Name: string;
                        SubscriberCount: number;
                    }>;
                    console.log('new list', newList);

                    // Add the new list to the lists array
                    componentState.value.mailjetModal.contactLists.push(newList?.[0]);

                    // Set the newly created list as selected
                    componentState.value.mailjetModal.selectedListId = newList?.[0].ID;

                    // Clear the input field
                    componentState.value.mailjetModal.newListName = '';

                    // Force reactivity update to ensure dropdown shows the new list
                    componentState.value.mailjetModal.contactLists = [...componentState.value.mailjetModal.contactLists];
                } else {
                    componentState.value.mailjetModal.error = response.error || 'Failed to create list';
                }
            } catch (error) {
                componentState.value.mailjetModal.error = 'Failed to create list';
            } finally {
                componentState.value.mailjetModal.isLoading = false;
            }
        };

        const syncToMailjet = async () => {
            if (!componentState.value.mailjetModal.selectedListId) {
                componentState.value.mailjetModal.error = 'Please select a contact list';
                return;
            }

            componentState.value.mailjetModal.step = 'syncing';
            componentState.value.mailjetModal.error = null;

            try {
                // Use selected contact IDs if any are selected, otherwise use filters
                const contactIds = componentState.value.selectedContactIds.length > 0
                    ? componentState.value.selectedContactIds
                    : undefined;

                const response = await ContactsListService.syncContactsToMailjet(
                    componentState.value.mailjetModal.selectedListId,
                    contactIds, // Pass selected contact IDs
                    contactIds ? undefined : storeState.value // Only use filters if no specific contacts selected
                );

                componentState.value.mailjetModal.syncProgress = {
                    total: response.total,
                    synced: response.synced,
                    already_exists: response.already_exists || 0,
                    failed: response.failed,
                    no_email: response.no_email || 0,
                    errors: response.errors || []
                };

                // Always show the complete step to display detailed results
                componentState.value.mailjetModal.step = 'complete';

                // Create a comprehensive summary message
                let message = '';
                const parts = [];

                if (response.synced > 0) {
                    parts.push(`${response.synced} users synced`);
                }
                if (response.already_exists > 0) {
                    parts.push(`${response.already_exists} already existed`);
                }
                if (response.no_email > 0) {
                    parts.push(`${response.no_email} no email`);
                }
                if (response.failed > 0) {
                    parts.push(`${response.failed} failed`);
                }

                if (parts.length > 0) {
                    message = `Sync complete: ${parts.join(', ')} (Total: ${response.total})`;
                } else {
                    message = 'No contacts were processed';
                }

                // Store the summary message for display in modal
                componentState.value.mailjetModal.summaryMessage = message;
            } catch (error) {
                console.error('Mailjet sync error:', error);
                componentState.value.mailjetModal.step = 'complete';
                componentState.value.mailjetModal.syncProgress = {
                    total: componentState.value.selectedContactIds.length || 0,
                    synced: 0,
                    already_exists: 0,
                    failed: componentState.value.selectedContactIds.length || 0,
                    no_email: 0,
                    errors: ['Network error: Failed to connect to Mailjet service']
                };
                componentState.value.mailjetModal.summaryMessage = 'Failed to sync contacts - Network error';
            }
        };

        // Row selection functions
        const toggleContactSelection = (contactId: number) => {
            const index = componentState.value.selectedContactIds.indexOf(contactId);
            if (index > -1) {
                componentState.value.selectedContactIds.splice(index, 1);
            } else {
                componentState.value.selectedContactIds.push(contactId);
            }
            updateSelectionState();
        };

        const toggleAllSelection = () => {
            if (componentState.value.isAllSelected) {
                componentState.value.selectedContactIds = [];
            } else {
                componentState.value.selectedContactIds = storeState.value.records.map((contact: Contact) => contact.id);
            }
            updateSelectionState();
        };

        const updateSelectionState = () => {
            const totalRecords = storeState.value.records.length;
            const selectedCount = componentState.value.selectedContactIds.length;

            componentState.value.isAllSelected = totalRecords > 0 && selectedCount === totalRecords;
            componentState.value.showBulkActions = selectedCount > 0;

            // Reset bulk action selection when no items are selected
            if (selectedCount === 0) {
                componentState.value.selectedBulkAction = '';
            }
        };

        const isContactSelected = (contactId: number) => {
            return componentState.value.selectedContactIds.includes(contactId);
        };

        const openMailjetModalForSelected = () => {
            if (componentState.value.selectedContactIds.length === 0) {
                return;
            }
            openMailjetModal();
        };

        const applyBulkAction = () => {
            const action = componentState.value.selectedBulkAction;
            const selectedIds = componentState.value.selectedContactIds;

            if (!action || selectedIds.length === 0) {
                return;
            }

            switch (action) {
                case 'export_mailjet':
                    openMailjetModalForSelected();
                    break;
                default:
                    console.warn('Unknown bulk action:', action);
            }
        };

        // Watch for changes in records to reset selection
        watch(() => storeState.value.records, () => {
            componentState.value.selectedContactIds = [];
            updateSelectionState();
        });

        return {
            componentState,
            viewType,
            tableViewType,
            hasMasterAccess,
            deleteState,
            exportPossibilities,
            storeState,
            toggleContactStar,
            onEntrySave,
            triggerAddReminderModal,
            onPageChange,
            showList,
            confirmDelete,
            clearDeleteAlert,
            executeExport,
            goTo,
            onSearchTermChange,
            toggleMoreFilters,
            openMailjetModal,
            closeMailjetModal,
            createNewList,
            syncToMailjet,
            // Row selection functions
            toggleContactSelection,
            toggleAllSelection,
            isContactSelected,
            openMailjetModalForSelected,
            applyBulkAction,
        };
    },
    components: {
        StarIcon,
        FGVirtualSelect,
        Paginator,
        ContactsFilters,
    },
});
</script>

<style scoped lang="scss">
.vscomp-ele {
    width: 100%;
    max-width: 300px;
}

.drop-area {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    position: relative;
}

.drop-area input {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    width: 100%;
    height: 100%;
}

.fav {
    cursor: pointer;
    font-size: 20px;
    color: gray;
}

.rem {
    width: 10px;
    padding-top: 10px;
}

.is-favorite {
    .fav {
        color: #feca4e;
    }
}

.contacts-list {
    display: flex;
    flex-wrap: wrap;
    gap: 3px;

    &__tag {
        border: 1px solid #999;
        color: #999;

        display: inline-block;
        padding: 0.35em 0.65em;
        font-size: .75em;
        font-weight: 700;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 0.25rem;

        &--database {
            color: orange;
            border-color: orange;
        }

        &--more {
            color: blue;
            border: none;
        }
    }
}



.contactsListWrapper {
    $blockName: &;
    // width: 1400px;
    // max-width: calc(100vw - 250px);
    width: 100%;
    overflow-x: scroll;

    &__table {
        td {
            vertical-align: top;
        }
    }

    &__tableHead {
        width: 100%;
    }

    &__tableFooter {
        display: flex;
        flex-direction: column;
    }

    .select {
        width: 30px;
        min-width: 30px;
        max-width: 30px;
        overflow: hidden;
        text-align: center;
    }

    .fav {
        width: 10px;
        min-width: 10px;
        max-width: 50px;
        overflow: hidden;
    }

    .id {
        width: 60px;
        min-width: 60px;
        max-width: 60px;
        overflow: hidden;
    }

    .lastContact {
        width: 60px;
        min-width: 60px;
        max-width: 60px;
        overflow: hidden;
        text-align: center;

        >div {
            display: flex;
            justify-content: center;
        }
    }

    .name {
        width: 120px;
        min-width: 120px;
        max-width: 120px;
        overflow: hidden;
    }

    .gender {
        width: 50px;
        min-width: 50px;
        max-width: 50px;
        overflow: hidden;
    }

    .mobile,
    .email,
    .position,
    .tags,
    .organization,
    .created_by {
        width: 120px;
        min-width: 120px;
        max-width: 120px;
        overflow: hidden;
    }

    .actions {
        width: 80px;
        min-width: 80px;
        max-width: 80px;
        overflow: visible;
    }

    @media screen and (min-width: 768px) {
        #{$blockName} {
            width: 1400px;
            max-width: calc(100vw - 250px);

            &__tableFooter {
                flex-direction: row;
                justify-content: space-between;
                padding-right: 20px;
            }
        }
    }
}

.bulk-actions {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 15px;
    width: 100%;
    max-width: 440px;
}
</style>
