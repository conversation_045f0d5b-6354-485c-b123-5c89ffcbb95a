<?php

namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use App\Models\ContactWebsiteEntry;
use App\Models\LeadStatus;
use App\Models\Lead;
use App\Models\LeadAssignment;
use App\Models\User;
use App\Models\MarketingParams;
use App\Services\ContactsService;
use App\Services\EmailService;
use App\Services\LeadsService;
use App\Services\OperationHistoryService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use App\Models\CacheKeys;
use Log;

class ContactController extends Controller
{
    private $emailService;
    private $operationHistoryService;
    private $contactsService;
    private $leadsService;

    public function __construct(
        EmailService $emailService,
        OperationHistoryService $operationHistoryService,
        ContactsService $contactsService,
        LeadsService $leadsService
    ) {
        $this->emailService = $emailService;
        $this->operationHistoryService = $operationHistoryService;
        $this->contactsService = $contactsService;
        $this->leadsService = $leadsService;
    }

    public function index()
    {
        $validationMessages = [
            'name22458.required' => __('The name field is required'),
            'email31332.required' => __('Please specify the email address'),
            'email31332.email' => __('Please specify a valid email address'),
            'phone191y6.required' => __('Please specify your phone'),
            'phone191y6.invalid' => __('Phone format is invalid.'),
            'message5152a.required' => __('Please add your message'),
        ];

        // Add marketing parameters to validation rules
        $validationRules = [
            'name' => '',
            'email' => '',
            'phone' => '',
            'prefix' => '',
            'name22458' => 'required',
            'email31332' => 'required|email',
            'phone191y6' => 'required|min:4|max:40|regex:/^[0-9]*$/',
            'message5152a' => 'required'
        ];

        // Add all whitelisted marketing parameters to validation rules
        $marketingParams = array_fill_keys(MarketingParams::WHITELISTED_URL_PARAMS, '');
        $validationRules = array_merge($validationRules, $marketingParams);
        
        Log::info("Validation rules", $validationRules);
        Log::info("Request params", request()->all());
        

        $requestValidData = Validator::make(request()->all(), $validationRules, $validationMessages)->validate();
        if (empty($requestValidData['phone']) && empty($requestValidData['email']) && empty($requestValidData['name'])) {
            $validData = [
                'name' => $requestValidData['name22458'],
                'email' => $requestValidData['email31332'],
                'phone' => $requestValidData['phone191y6'],
                'prefix' => $requestValidData['prefix'],
                'message' => $requestValidData['message5152a']
            ];

            $contact = ContactWebsiteEntry::create($validData);
            $destinationEmail = env('EMAIL_CSR');
            $destinationEmailForUser = $validData['email'];
            $this->emailService->sendEmailContact($validData, $destinationEmail);
            $this->emailService->sendEmailToSender($destinationEmailForUser);

            $existingContactByEmail = $this->contactsService->getContactsByEmail($validData['email']);
            $existingContactByPhone = $this->contactsService->getContactsByPhoneNumber($validData['phone']);

            $existingMasterContact = $existingContactByEmail->whereNull('master_contact_id')->first();
            if (is_null($existingMasterContact)) {
                $existingContact = $existingContactByEmail->first();
            } else {
                $existingContact = $existingMasterContact;
            }

            if (is_null($existingContact)) {
                $existingMasterContact = $existingContactByPhone->whereNull('master_contact_id')->first();
                if (is_null($existingMasterContact)) {
                    $existingContact = $existingContactByPhone->first();
                } else {
                    $existingContact = $existingMasterContact;
                }
            }
            $systemUser = User::where(['email' => env('EMAIL_ADDRESS_SYSTEM')])->first();
            if ($existingContactByEmail->isEmpty() && $existingContactByPhone->isEmpty()) {
                $contactEntry = Contact::create([
                    'name' => $validData['name'],
                    'email_1' => $validData['email'],
                    'prefix_mobile_1' => $validData['prefix'],
                    'mobile_1' => $validData['phone'],
                    'created_by' => $systemUser->id,
                ]);
            }

            $newStatus = LeadStatus::where(['name' => 'NEW'])->first();
            $leadSource = $this->leadsService->getCachedSources()->where('code', 'WEBSITE_CONTACT_FORM')->first();

            // Collect marketing parameters
            $marketingParams = [];
            foreach (MarketingParams::WHITELISTED_URL_PARAMS as $param) {
                if (isset($requestValidData[$param])) {
                    $marketingParams[$param] = $requestValidData[$param];
                }
            }

            // Create lead with marketing parameters
            $lead = Lead::create(array_merge([
                'contact_id' => !is_null($existingMasterContact) ? $existingMasterContact->id : $contactEntry->id,
                'platform_from' => !is_null($leadSource) ? $leadSource->id : 1,
                'lead_status_id' => $newStatus->id,
                'lead_metadata' => $marketingParams,
                'utm_source' => isset($requestValidData['utm_source']) ? $requestValidData['utm_source'] : '',
                'utm_medium' => isset($requestValidData['utm_medium']) ? $requestValidData['utm_medium'] : '',
                'utm_campaign' => isset($requestValidData['utm_campaign']) ? $requestValidData['utm_campaign'] : '',
                'utm_term' => isset($requestValidData['utm_term']) ? $requestValidData['utm_term'] : '',
                'utm_content' => isset($requestValidData['utm_content']) ? $requestValidData['utm_content'] : '',
                'created_by' => $systemUser->id
            ], $marketingParams));

            Cache::forget(CacheKeys::UTM_SOURCES);
            
            $this->operationHistoryService->addOperationHistory($lead, 'The lead came from the website - contact us page', $systemUser);

            // assign the lead to csr
            $csrUser = User::where('email', env('EMAIL_CSR'))->first();
            if (!empty($csrUser)) {
                $assignment = new LeadAssignment();
                $assignment->user_id = $csrUser->id;
                $assignment->lead_id = $lead->id;
                $assignment->save();
                $this->operationHistoryService->addOperationHistory($lead, 'The lead was assigned to [' . $csrUser->name . ']', $systemUser);
            }

            return redirect()
                ->route('pages.thank-you', ['locale' => 'en', 'ft' => 'contact'])
                ->with(['message' => 'Your message has been successfully sent to us.']);
        } else {
            return redirect()
                ->route('home.localized', ['locale' => 'en']);
        }
    }
}
