<?php

namespace App\Providers;

use App\Listeners\LogSuccessfulLogin;
use App\Models\Crm\Deal;
use App\Models\GoogleLead;
use App\Models\Lead;
use App\Models\LeadAssignment;
use App\Models\Task;
use App\Observers\DealObserver;
use App\Observers\GoogleLeadObserver;
use App\Observers\LeadObserver;
use App\Observers\TaskObserver;
use App\Services\DealNotificationService;
use App\Services\LeadsService;
use App\Services\LeadStatusService;
use App\Services\NotesService;
use App\Services\OperationHistoryService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        Login::class => [
            LogSuccessfulLogin::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        $leadsService = $this->app->make(LeadsService::class);
        $leadStatusService = $this->app->make(LeadStatusService::class);
        $notesService = $this->app->make(NotesService::class);
        $operationHistoryService = $this->app->make(OperationHistoryService::class);
        $dealNotificationService = $this->app->make(DealNotificationService::class);

        LeadAssignment::created(function ($model) use ($leadsService) {
            $leadsService->onAssignment($model);
        });

        Lead::observe(new LeadObserver($leadsService));
        GoogleLead::observe(new GoogleLeadObserver($leadsService));
        Task::observe(new TaskObserver($leadsService, $leadStatusService, $notesService, $operationHistoryService));
        Deal::observe(new DealObserver($dealNotificationService));
    }
}
