<template>
    <div
        class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Tasks dashboard</h1>
    </div>

    <div class="col mt-2">
        <div class="dropdown mb-3">
            <button class="btn btn-secondary dropdown-toggle" type="button" id="taskFilterDropdown"
                data-bs-toggle="dropdown" aria-expanded="false">
                {{ filtersMap[state.currentFilter] == 'All' ? 'Filter Tasks' : filtersMap[state.currentFilter] }}
            </button>
            <ul class="dropdown-menu" aria-labelledby="taskFilterDropdown">
                <li v-for="(label, key) in filtersMap"><a class="dropdown-item"
                        :class="{ 'active': state.currentFilter == key }" @click="applyFilter(key as string)">{{ label
                        }}</a>
                </li>
            </ul>
        </div>
        <DataTable ref="table" :config="{ dom: 'frti' }" :columns="tableConfig.columns" :options="tableConfig.options"
            @draw="onDraw($event)" class="table table-striped" />
    </div>
    <div class="modal fade" id="actionModal" tabindex="-1" aria-labelledby="actionModalLabel" aria-hidden="true">
        <div :class="{ 'modal-dialog': true, 'modal-dialog-centered': true, 'modal-dialog-scrollable': true }">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="actionModalLabel">{{ state.modal.title }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ModalContentCallLog v-if="state.actionType == taskType.CallLog"
                        @dataChange="onModalContentCallLogDataChange($event)" :call-log="state.selectedActivity" />
                    <ModalContentFollowup v-if="state.actionType == taskType.FollowUp"
                        @dataChange="onModalContentFollowupDataChange($event)" :task="state.selectedActivity" />
                    <ModalContentMeeting v-if="state.actionType == taskType.Meeting"
                        @dataChange="onModalContentMeetingDataChange($event)" :meeting="state.selectedActivity" />
                    <ModalContentMeeting v-if="state.actionType == taskType.Viewing"
                        @dataChange="onModalContentMeetingDataChange($event)" :is-viewing="true"
                        :meeting="state.selectedActivity" />
                </div>
                <div class="modal-footer">
                    <button @click="closeModal()" class="btn btn-outline-secondary">Cancel</button>
                    <button @click="confirmAction()"
                        :disabled="!canSaveModal || !state.currentAction?.isDataValid || state.currentAction?.isLoading"
                        class="btn btn-outline-primary">Ok</button>
                </div>
            </div>
        </div>
    </div>
</template>
<style>
.automatization-highlight {
    background-color: lightblue !important;
}
</style>

<script lang="ts">
import { ActivityFollowupFormData, ActivityMeetingFormData, BETask, FETask, TaskType } from './../../model/fe-models.interface';
import DataTable from 'datatables.net-vue3';
import DataTablesCore from "datatables.net-bs5";
import TasksService from './../../services/tasks.service';
import { BEPaginatedResponse } from 'resources/ts/model/be-models.interface';
import "datatables.net-responsive";
import { computed, defineComponent, onMounted, reactive, ref, watch } from 'vue';
import ModalContentCallLog from '../leadDetailsV2/ModalContentCallLog.vue';
import ModalContentFollowup from '../leadDetailsV2/ModalContentFollowup.vue';
import ModalContentMeeting from '../leadDetailsV2/ModalContentMeeting.vue';
import { useToast } from 'vue-toast-notification';
import 'vue-toast-notification/dist/theme-bootstrap.css';
export declare const $: any;
declare const bootstrap: any;
DataTable.use(DataTablesCore);

export interface TaskScreenState {
    actionType: TaskType;
    currentAction: {
        isDataValid: boolean;
        payload: any;
        isLoading: boolean;
    };
    selectedActivity: FETask,
    modal: {
        isOpen: boolean,
        title: string
    },
    recordType: 'lead' | 'contact',
    currentFilter: string
}
export default defineComponent({
    name: "UserHotelRatePlans",
    setup() {
        let dt: any;
        const table = ref();
        const $toast = useToast();
        const modalRef = ref<any>(null);
        const taskType = TaskType;
        const filtersMap: {
            [key: string]: string
        } = {
            '': 'All',
            // 'open': 'Open Tasks',
            'overdue': 'Overdue Tasks',
            'today': 'Today Tasks',
            // 'completed': 'Completed Tasks',
            'autogenerated': 'Autogenerated Tasks',
            'upcoming': 'Upcoming Tasks'
        };
        const state = reactive<TaskScreenState>({
            actionType: null,
            currentAction: {
                isDataValid: false,
                payload: null,
                isLoading: false
            },
            selectedActivity: null,
            modal: {
                isOpen: false,
                title: ''
            },
            recordType: null,
            currentFilter: ''
        });
        const fetchData = (data: any, callback: any) => {
            const orderColumn = data.columns[data.order[0].column];

            TasksService.loadUserFollowupTasks(
                state.currentFilter,
                data.start,
                data.length,
                orderColumn.data,
                data.order[0].dir
            ).then((response: BEPaginatedResponse<BETask>) => {
                callback({
                    recordsTotal: response.meta.total,
                    recordsFiltered: response.meta.total,
                    data: response.data,
                });
            });
        };

        const applyFilter = (filter: string) => {
            state.currentFilter = filter;
            dt.draw();
        };


        const onModalContentCallLogDataChange = (evt: { callResponse: string, callNotes: string }) => {
            state.currentAction.isDataValid = !!evt.callResponse.trim();
            state.currentAction.payload = {
                type: TaskType.CallLog,
                ...evt
            };
        }

        const onModalContentFollowupDataChange = (evt: { formData: ActivityFollowupFormData, isValid: boolean }) => {
            state.currentAction.isDataValid = evt.isValid;
            state.currentAction.payload = {
                type: TaskType.FollowUp,
                ...evt.formData
            };
        }

        const onModalContentMeetingDataChange = (evt: ActivityMeetingFormData) => {
            state.currentAction.isDataValid = TasksService.isValidMeetingFormContent(evt);
            state.currentAction.payload = {
                type: state.actionType == TaskType.Meeting ? TaskType.Meeting : TaskType.Viewing,
                ...evt
            };
        }

        const closeModal = () => {
            state.modal.isOpen = false;
        }

        const confirmAction = () => {
            if (state.currentAction.isDataValid) {
                state.currentAction.isLoading = true;
                let promise = null;
                const taskPayload = TasksService.formTaskWrite(state.currentAction.payload)
                promise = TasksService.saveTask(state.recordType, taskPayload.id, taskPayload);
                promise.then((beTask: BETask) => {
                    // handleLeadActivityStatusUpdate(beTask);
                    $toast.open({
                        message: "The activity was saved",
                        position: "bottom",
                        type: "success"
                    });
                    state.modal.isOpen = false;
                    dt.draw();
                })
                    .catch((err: any) => {
                        $toast.open({
                            message: "The call log cannot be saved",
                            position: "bottom",
                            type: "error"
                        });
                        state.modal.isOpen = false;
                    })
                    .finally(() => {
                        state.currentAction.isLoading = false;
                    })
            }
        }

        const onDraw = ({ event }: any) => {
            $(event.currentTarget).off('click');
            $(event.currentTarget).on('click', 'tr', (evt: any) => {
                const rowData = dt.row(evt.currentTarget).data();
                loadTaskDetails(rowData.id);
            })
        }

        const loadTaskDetails = (taskId: number) => {
            TasksService.loadTask(taskId).then((beTask: BETask) => {
                const feTask = TasksService.formTaskFEObject(beTask);
                state.actionType = feTask.type;
                state.selectedActivity = feTask;
                state.modal.title = feTask.type
                state.modal.isOpen = true;
                state.recordType = beTask.object_type;
            });
        }

        const canSaveModal = computed(() => {
            return state.selectedActivity?.type != TaskType.CallLog;
        });

        const tableConfig = {
            columns: [
                { title: "Subject", data: "subject" },
                {
                    title: "Status", data: "status", render(data: any, type: any, row: any) {
                        return `<span class="statusBadge ${data == 'not_started' ? 'not-started' : data == 'in_progress' ? 'in-progress' : data == 'completed' ? 'completed' : ''}">${data}</span>`;
                    }
                },
                {
                    title: 'Type', data: 'object_type'
                },
                { 
                    title: 'Contact Name', data: 'ct_contact_name', render(data: any, type: any, row: any) {
                        return row.ct_contact_name ? `<a target="_blank" href="/crm/contacts-list#/${row.ct_contact_id}/edit">${row.ct_contact_name}</a>` : `<a target="_blank" href="/crm/leads/${row.lead.id}/edit">${row.lead.name}</a>`
                    }
                },
                {
                    title: 'Due Date', data: 'due_date'
                }
            ],
            options: {
                serverSide: true,
                ajax: fetchData,
                dom: `
        "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6 d-flex justify-content-end'f>>" +
        "<'row'<'col-sm-12'tr>>" +
        "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7 d-flex justify-content-end'p>>"
        `,
            rowCallback: function(row: any, data: any) {
                if (data.automatization !== null && data.automatization !== undefined) {
                    $(row).addClass("automatization-highlight");
                }
            }
            },
        };

        watch(() => state.modal.isOpen, () => {
            state.modal.isOpen ? modalRef.value.show() : modalRef.value.hide();
            if (!state.modal.isOpen) {
                state.selectedActivity = null;
            }
            // emits('modalVisibilityChange', { isModalOpen: state.modal.isOpen })
        })

        onMounted(function () {
            dt = table?.value?.dt;
            const modalEl = document.getElementById("actionModal");
            const modal = new bootstrap.Modal(modalEl, {
                keyboard: false,
            });
            modalRef.value = modal;
            modalEl.addEventListener('hidden.bs.modal', function () {
                state.modal.isOpen = false;
                state.actionType = null;
            });
            const queryParams = new URLSearchParams(window.location.search);
            if(queryParams.has('filter')) {
                setTimeout(() => {
                    applyFilter(queryParams.get('filter'))
                }, 100);
            }
        });

        return {
            state,
            fetchData,
            applyFilter,
            onModalContentCallLogDataChange,
            onModalContentFollowupDataChange,
            onModalContentMeetingDataChange,
            closeModal,
            confirmAction,
            onDraw,
            tableConfig,
            taskType,
            table,
            filtersMap,
            canSaveModal
        }
    },
    components: {
        DataTable,
        ModalContentCallLog,
        ModalContentFollowup,
        ModalContentMeeting
    }
});


</script>

<style lang="scss">
// @import "datatables.net-bs5";

// .vscomp-ele {
//     width: 100%;
//     max-width: 100%;
// }
</style>