<?php

namespace App\Console\Commands;

use App\Models\Lead;
use App\Services\OperationHistoryService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RestoreLeadsAndUpdateStatus extends Command
{
    protected $signature = 'leads:restore-and-update-status';
    protected $description = 'Restore deleted lead assignments and update leads with their latest status';

    private $operationHistoryService;
    public function __construct(OperationHistoryService $operationHistoryService)
    {
        parent::__construct();
        $this->operationHistoryService = $operationHistoryService;
    }

    public function handle()
    {
        $this->info('Starting process...');

        // DB::beginTransaction();

        try {
            // Step 1: Select deleted lead_assignments between 06:00 and 06:10 after June 3, 2025
            $deletedAssignments = DB::table('lead_assignments')
                ->whereNotNull('deleted_at')
                ->whereDate('deleted_at', '>=', '2025-06-03')
                ->whereTime('deleted_at', '>=', '06:00:00')
                ->whereTime('deleted_at', '<=', '06:10:00')
                ->get();

            $leadIds = $deletedAssignments->pluck('lead_id')->unique()->values();

            // Step 2: Undelete the lead_assignments
            DB::table('lead_assignments')
                ->whereIn('lead_id', $leadIds)
                ->whereNotNull('deleted_at')
                ->whereDate('deleted_at', '>=', '2025-06-03')
                ->whereTime('deleted_at', '>=', '06:00:00')
                ->whereTime('deleted_at', '<=', '06:10:00')
                ->update(['deleted_at' => null, 'updated_at' => now()]);

            $this->info('Undeleted ' . $leadIds->count() . ' lead assignments.');

            // Step 3: For each lead, find most recent final status and update leads table
            $statusCache = [];

            foreach ($leadIds as $leadId) {
                $latestStatusId = DB::table('lead_x_status')
                    ->where('lead_id', $leadId)
                    ->whereNull('deleted_at')
                    ->whereNotNull('lead_final_status_id')
                    ->orderByDesc('created_at')
                    ->value('lead_final_status_id');

                $statusName = "-";

                if ($latestStatusId) {
                    // Get status name from cache or DB
                    if (!isset($statusCache[$latestStatusId])) {
                        $statusCache[$latestStatusId] = DB::table('lead_status')
                            ->where('id', $latestStatusId)
                            ->value('name');
                    }

                    $statusName = $statusCache[$latestStatusId];
                }

                $lead = Lead::find($leadId);
                if (!is_null($lead)) {
                    $lead->lead_status_id = $latestStatusId;
                    $lead->save();

                    $this->info("Updated lead ID {$leadId} to status '{$statusName}'");

                    $this->operationHistoryService->addOperationHistory(
                        $lead,
                        "[Automation] - Lead assignment reverted."
                    );

                    $this->operationHistoryService->addOperationHistory(
                        $lead,
                        "[Automation] - old status reverted to '{$statusName}'"
                    );
                }
                $this->warn("No status found for lead ID {$leadId}");
            }

            DB::commit();
            $this->info('Process completed successfully.');
        } catch (\Throwable $e) {
            DB::rollBack();
            $this->error('Error: ' . $e->getMessage());
        }
    }
}
