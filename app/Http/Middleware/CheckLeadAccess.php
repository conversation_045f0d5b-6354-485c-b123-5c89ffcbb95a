<?php

namespace App\Http\Middleware;

use App\Models\Crm\RolesDef;
use App\Models\Lead;
use App\Models\LeadStatus;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Log;
use Cache;
use App\Models\CacheKeys;

class CheckLeadAccess
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();
        $leadId = $request->route('id');
        $lead = Lead::with(['latestAssignment', 'latestAssignment.user', 'author'])
            ->findOrFail($leadId);

        $leadStatuses = Cache::remember(CacheKeys::LEAD_MASTER_STATUSES_IDS, 3600 * 24 * 30, function () {
            return LeadStatus::whereIn('name', [LeadStatus::STATUS_NOT_QUALIFIED, LeadStatus::STATUS_GHOSTED, LeadStatus::STATUS_CLOSED_LOSE, LeadStatus::STATUS_NEW_NOT_UPDATED, LeadStatus::STATUS_NOT_UPDATED])->pluck('id')->toArray();
        });

        if (!$lead) {
            abort(404, 'Lead not found');
        }

        $hasOfficeManagerRole = $user->hasRole(RolesDef::OFFICE_MANAGER);

        $authorIsTeamLeader = false;
        $latestAssignmentCheck = false;
        $hasAssignment = false;

        if (!is_null($lead->latestAssignment)) {
            $hasAssignment = true;
            $latestAssignmentCheck = $lead->latestAssignment->user_id == $user->id;
            $authorIsTeamLeader = $lead->latestAssignment->user->teamLeader && $lead->latestAssignment->user->teamLeader->id == $user->id;
        }

        Log::info("Lead access check", [
            "latestAssignment" => $lead->latestAssignment,
            "hasOfficeManagerRole" => $hasOfficeManagerRole,
            "latestAssignmentCheck" => $latestAssignmentCheck,
            "authorIsTeamLeader" => $authorIsTeamLeader,
            "leadId" => $lead->id,
            "userId" => $user->id
        ]);

        if (!$hasOfficeManagerRole && $hasAssignment && !$latestAssignmentCheck && !$authorIsTeamLeader && !in_array($lead->lead_status_id, $leadStatuses)) {
            abort(403, 'Access to this lead is restricted.');
        }

        return $next($request);
    }
}
