<section class="mt-0">
    <script>
        function onVideoCanPlay() {
            document.querySelector('.splash img').classList.add('hidden');
            document.querySelector('.splash video').classList.remove('hidden');
        }
        function toggleVideoSound() {
            const video = document.querySelector('.splash video');
            const toggleBtn = document.querySelector('.splash__cta span');
            const audibleCtrl = document.querySelector('.splash__cta--soundOn');
            const mutedCtrl = document.querySelector('.splash__cta--soundOff');
            const currentMutedState = video.muted;

            if(!currentMutedState) {
                audibleCtrl.classList.add('hidden');
                mutedCtrl.classList.remove('hidden');
            } else {
                audibleCtrl.classList.remove('hidden');
                mutedCtrl.classList.add('hidden');
            }
            video.muted = !currentMutedState;
        }
    </script>
    @php
        $locale = App::getLocale();
        $imageURL = asset('images/Pool beach.jpg');
        $splashImageAlt = "Homepage image";
        $splashCtaURL = "/lp/jmj-waterfront/?utm_source=website&utm_medium=banner&utm_campaign=promotion";
        $splashCtaCaption = "Enquire Today";
        $splashCtaText = "EXPERIENCE BEACHFRONT LIVING AT ITS FINEST";
        $splashCtaSubText = "";
        if(env('FEATURE_CVIEW_ENABLED')) {
            $imageURL = asset('images/projects/cview/home.jpg');
            $splashImageAlt = "CView Residence";

            $dev = \App\Models\Development::with(['translations' => function($q) use($locale) { return $q->where('language', $locale); }])->where('slug', 'cview')->first();
            if(!is_null($dev)) {
                $developmentURL = persistQueryParamsToURL($dev->url);
                $splashCtaURL = persistQueryParamsToURL($dev->url);
                if($locale != 'en') {
                    $firstTranslation = $dev->translations->first();
                    if(!is_null($firstTranslation)) {
                        if(!empty($firstTranslation->url)) {
                            $developmentURL = persistQueryParamsToURL($firstTranslation->url);
                            $splashCtaURL = persistQueryParamsToURL($firstTranslation->url);
                        }
                    }
                }
            }
            $splashCtaCaption = __("Enquire Now");
            $splashCtaText = "A PRESTIGIOUS WATERFRONT ADDRESS IN THE HEART OF LUSAIL";
        }
        /*if(env('BANNER_WHY_INVEST_IN_QATAR_V2_VISIBLE') || request()->has('why-invest-in-qatar')) {
            $imageURL = mix('images/why-invest-in-qatar.jpg');
            $splashImageAlt = "Why invest in Qatar?";
            $splashCtaURL = '/blog/investors-path/';
            $splashCtaCaption = __("Find Out More");
            $splashCtaText = "WHY INVEST IN QATAR?";
        }*/
        if(env('BANNER_WHY_INVEST_IN_QATAR_V2_VISIBLE') || request()->has('why-invest-in-qatar')) {
            $imageURL = mix('images/DohaSkyline.jpg');
            $splashImageAlt = "Why invest in Qatar?";
            $splashCtaURL = '/blog/investors-path/';
            $splashCtaCaption = __("Find Out More");
            $splashCtaText = "WHY AND HOW";
            $splashCtaSubText = "TO INVEST IN QATAR REAL ESTATE?";
        }
    @endphp
    <div class="high-section">
        <div class="layer-golden"></div>
        <div class="splash">
            <img class="xl:rounded-md" src="{{$imageURL}}" alt="{{$splashImageAlt}}">
            <div class="splash__overlay"></div>
            {{-- <img src="{{mix('images/world-cup-snapshot.webp')}}" alt="Video player snapshot">
            <video
                autoplay
                muted
                playsinline
                loop
                oncanplaythrough="onVideoCanPlay()"
                preload="auto"
                class="hidden"
            > --}}
            {{-- <source src="/video/world-cup.webm" type="video/webm"> --}}
            {{-- <source src="/video/world-cup.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <div class="splash__cta">
                <div onclick="toggleVideoSound()" class="bg-black bg-opacity-20 p-1 rounded">
                    <div class="splash__cta--soundOn flex hidden" style="width: 25px; height: 20.85px"></div>
                    <div class="splash__cta--soundOff flex" style="height: 20.83px"></div>
                </div>
            </div> --}}
            <div class="enquireWrapper @if(env('FEATURE_CVIEW_ENABLED') && !(env('BANNER_WHY_INVEST_IN_QATAR_V2_VISIBLE') || request()->has('why-invest-in-qatar'))) enquireWrapper--cviewVariant @endif @if(env('BANNER_WHY_INVEST_IN_QATAR_V2_VISIBLE') || request()->has('why-invest-in-qatar')) enquireWrapper--whyInvestInQatarVariant @endif">
                <h2 class="enquireWrapper__title">{{__($splashCtaText)}}</h2>
                @if(!empty($splashCtaSubText))
                <h3 class="enquireWrapper__subtitle">{{__($splashCtaSubText)}}</h3>
                @endif
                <a class="enquireWrapper__cta" href="{{$splashCtaURL}}" >{{__($splashCtaCaption)}}</a>
            </div>
        </div>
        {{-- this will be overriden by Vue components --}}
        <start-box>
            <div class="start-box__tabs min-w-120">
                <a class="active" href="javascript:void(0)">{{__('Buy')}}</a>
                <a href="javascript:void(0)">{{__('Rent')}}</a>
            </div>
            <div class="start-box__content">
                <h1>
                    {{__("Find Exclusive Properties in Qatar")}}
                </h1>
                <p class="mt-2 text-gray-60 text-normal">{{__('Discover the most attractive properties depending on your budget and preferences')}}</p>
                <form autocomplete="off" onsubmit="javascript: return false;">
                    <input autocomplete="false" name="hidden" type="text" style="display:none;">
                    <div class="mt-3 search-container">
                        <div class="input-group w-full">
                            <div class="searchbox flex-1">
                                <input
                                    class="w-full min-w-0"
                                    type="search"
                                    placeholder="{{__('Location, ref. no., keywords')}}"
                                >
                                <div class="dropdown__options">
                                    <ul>
                                        <li>
                                            {{__('Property type')}}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="dropdown min-w-150">
                                <span class="dropdown__caption">{{__('Property type')}}</span>
                                <div class="dropdown__options">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="_actions flex justify-end mt-4">
                        {{--
                        <label class="checkbox text-gray-90">
                            <input type="checkbox">
                            {{__('Show Results in Map View')}}
                        </label>--}}
                        <button class="btn">{{__('Search')}}</button>
                    </div>
                </form>
            </div>
        </start-box>
    </div>
</section>
