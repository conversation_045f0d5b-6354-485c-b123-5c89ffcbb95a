<!DOCTYPE html>
<html lang="en-US">
@php
	$isProductionEnvironment = env('APP_ENV') == 'production';
@endphp
<head>
	<meta charset="UTF-8">
	<title>Marina Height Residence Thank You! &#8211; FGREALTY©</title>
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<meta name='robots' content='max-image-preview:large' />
	<style>
		img:is([sizes="auto" i], [sizes^="auto," i]) {
			contain-intrinsic-size: 3000px 1500px
		}
	</style>
	<title>Marina Height Residence You! &#8211; FGREALTY©</title>

	@if($isProductionEnvironment)
    <script>
        window.console.log = function() {};
        window.console.error = function() {};
        (function(w, d, s, l, i) {
            w[l] = w[l] || [];
            w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
            });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-WPD3N3NC');
    </script>
    @endif
	<style type="text/css">
		@include("projects.marina-heights.inlinecss");
	</style>
	<style id='wp-emoji-styles-inline-css'>
		img.wp-smiley,
		img.emoji {
			display: inline !important;
			border: none !important;
			box-shadow: none !important;
			height: 1em !important;
			width: 1em !important;
			margin: 0 0.07em !important;
			vertical-align: -0.1em !important;
			background: none !important;
			padding: 0 !important;
		}
	</style>
	<style id='wp-block-library-inline-css'>
		:root {
			--wp-admin-theme-color: #007cba;
			--wp-admin-theme-color--rgb: 0, 124, 186;
			--wp-admin-theme-color-darker-10: #006ba1;
			--wp-admin-theme-color-darker-10--rgb: 0, 107, 161;
			--wp-admin-theme-color-darker-20: #005a87;
			--wp-admin-theme-color-darker-20--rgb: 0, 90, 135;
			--wp-admin-border-width-focus: 2px;
			--wp-block-synced-color: #7a00df;
			--wp-block-synced-color--rgb: 122, 0, 223;
			--wp-bound-block-color: var(--wp-block-synced-color)
		}

		@media (min-resolution:192dpi) {
			:root {
				--wp-admin-border-width-focus: 1.5px
			}
		}

		.wp-element-button {
			cursor: pointer
		}

		:root {
			--wp--preset--font-size--normal: 16px;
			--wp--preset--font-size--huge: 42px
		}

		:root .has-very-light-gray-background-color {
			background-color: #eee
		}

		:root .has-very-dark-gray-background-color {
			background-color: #313131
		}

		:root .has-very-light-gray-color {
			color: #eee
		}

		:root .has-very-dark-gray-color {
			color: #313131
		}

		:root .has-vivid-green-cyan-to-vivid-cyan-blue-gradient-background {
			background: linear-gradient(135deg, #00d084, #0693e3)
		}

		:root .has-purple-crush-gradient-background {
			background: linear-gradient(135deg, #34e2e4, #4721fb 50%, #ab1dfe)
		}

		:root .has-hazy-dawn-gradient-background {
			background: linear-gradient(135deg, #faaca8, #dad0ec)
		}

		:root .has-subdued-olive-gradient-background {
			background: linear-gradient(135deg, #fafae1, #67a671)
		}

		:root .has-atomic-cream-gradient-background {
			background: linear-gradient(135deg, #fdd79a, #004a59)
		}

		:root .has-nightshade-gradient-background {
			background: linear-gradient(135deg, #330968, #31cdcf)
		}

		:root .has-midnight-gradient-background {
			background: linear-gradient(135deg, #020381, #2874fc)
		}

		.has-regular-font-size {
			font-size: 1em
		}

		.has-larger-font-size {
			font-size: 2.625em
		}

		.has-normal-font-size {
			font-size: var(--wp--preset--font-size--normal)
		}

		.has-huge-font-size {
			font-size: var(--wp--preset--font-size--huge)
		}

		.has-text-align-center {
			text-align: center
		}

		.has-text-align-left {
			text-align: left
		}

		.has-text-align-right {
			text-align: right
		}

		#end-resizable-editor-section {
			display: none
		}

		.aligncenter {
			clear: both
		}

		.items-justified-left {
			justify-content: flex-start
		}

		.items-justified-center {
			justify-content: center
		}

		.items-justified-right {
			justify-content: flex-end
		}

		.items-justified-space-between {
			justify-content: space-between
		}

		.screen-reader-text {
			border: 0;
			clip: rect(1px, 1px, 1px, 1px);
			clip-path: inset(50%);
			height: 1px;
			margin: -1px;
			overflow: hidden;
			padding: 0;
			position: absolute;
			width: 1px;
			word-wrap: normal !important
		}

		.screen-reader-text:focus {
			background-color: #ddd;
			clip: auto !important;
			clip-path: none;
			color: #444;
			display: block;
			font-size: 1em;
			height: auto;
			left: 5px;
			line-height: normal;
			padding: 15px 23px 14px;
			text-decoration: none;
			top: 5px;
			width: auto;
			z-index: 100000
		}

		html :where(.has-border-color) {
			border-style: solid
		}

		html :where([style*=border-top-color]) {
			border-top-style: solid
		}

		html :where([style*=border-right-color]) {
			border-right-style: solid
		}

		html :where([style*=border-bottom-color]) {
			border-bottom-style: solid
		}

		html :where([style*=border-left-color]) {
			border-left-style: solid
		}

		html :where([style*=border-width]) {
			border-style: solid
		}

		html :where([style*=border-top-width]) {
			border-top-style: solid
		}

		html :where([style*=border-right-width]) {
			border-right-style: solid
		}

		html :where([style*=border-bottom-width]) {
			border-bottom-style: solid
		}

		html :where([style*=border-left-width]) {
			border-left-style: solid
		}

		html :where(img[class*=wp-image-]) {
			height: auto;
			max-width: 100%
		}

		:where(figure) {
			margin: 0 0 1em
		}

		html :where(.is-position-sticky) {
			--wp-admin--admin-bar--position-offset: var(--wp-admin--admin-bar--height, 0px)
		}

		@media screen and (max-width:600px) {
			html :where(.is-position-sticky) {
				--wp-admin--admin-bar--position-offset: 0px
			}
		}
	</style>
	<!-- <link rel='stylesheet' id='jet-engine-frontend-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_c3894a4f010f6ad827eeddbf4d87d24b.css?ver=3.6.3' media='all' /> -->
	<style id='global-styles-inline-css'>
		:root {
			--wp--preset--aspect-ratio--square: 1;
			--wp--preset--aspect-ratio--4-3: 4/3;
			--wp--preset--aspect-ratio--3-4: 3/4;
			--wp--preset--aspect-ratio--3-2: 3/2;
			--wp--preset--aspect-ratio--2-3: 2/3;
			--wp--preset--aspect-ratio--16-9: 16/9;
			--wp--preset--aspect-ratio--9-16: 9/16;
			--wp--preset--color--black: #000000;
			--wp--preset--color--cyan-bluish-gray: #abb8c3;
			--wp--preset--color--white: #ffffff;
			--wp--preset--color--pale-pink: #f78da7;
			--wp--preset--color--vivid-red: #cf2e2e;
			--wp--preset--color--luminous-vivid-orange: #ff6900;
			--wp--preset--color--luminous-vivid-amber: #fcb900;
			--wp--preset--color--light-green-cyan: #7bdcb5;
			--wp--preset--color--vivid-green-cyan: #00d084;
			--wp--preset--color--pale-cyan-blue: #8ed1fc;
			--wp--preset--color--vivid-cyan-blue: #0693e3;
			--wp--preset--color--vivid-purple: #9b51e0;
			--wp--preset--color--base: #f9f9f9;
			--wp--preset--color--base-2: #ffffff;
			--wp--preset--color--contrast: #111111;
			--wp--preset--color--contrast-2: #636363;
			--wp--preset--color--contrast-3: #A4A4A4;
			--wp--preset--color--accent: #cfcabe;
			--wp--preset--color--accent-2: #c2a990;
			--wp--preset--color--accent-3: #d8613c;
			--wp--preset--color--accent-4: #b1c5a4;
			--wp--preset--color--accent-5: #b5bdbc;
			--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple: linear-gradient(135deg, rgba(6, 147, 227, 1) 0%, rgb(155, 81, 224) 100%);
			--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan: linear-gradient(135deg, rgb(122, 220, 180) 0%, rgb(0, 208, 130) 100%);
			--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange: linear-gradient(135deg, rgba(252, 185, 0, 1) 0%, rgba(255, 105, 0, 1) 100%);
			--wp--preset--gradient--luminous-vivid-orange-to-vivid-red: linear-gradient(135deg, rgba(255, 105, 0, 1) 0%, rgb(207, 46, 46) 100%);
			--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray: linear-gradient(135deg, rgb(238, 238, 238) 0%, rgb(169, 184, 195) 100%);
			--wp--preset--gradient--cool-to-warm-spectrum: linear-gradient(135deg, rgb(74, 234, 220) 0%, rgb(151, 120, 209) 20%, rgb(207, 42, 186) 40%, rgb(238, 44, 130) 60%, rgb(251, 105, 98) 80%, rgb(254, 248, 76) 100%);
			--wp--preset--gradient--blush-light-purple: linear-gradient(135deg, rgb(255, 206, 236) 0%, rgb(152, 150, 240) 100%);
			--wp--preset--gradient--blush-bordeaux: linear-gradient(135deg, rgb(254, 205, 165) 0%, rgb(254, 45, 45) 50%, rgb(107, 0, 62) 100%);
			--wp--preset--gradient--luminous-dusk: linear-gradient(135deg, rgb(255, 203, 112) 0%, rgb(199, 81, 192) 50%, rgb(65, 88, 208) 100%);
			--wp--preset--gradient--pale-ocean: linear-gradient(135deg, rgb(255, 245, 203) 0%, rgb(182, 227, 212) 50%, rgb(51, 167, 181) 100%);
			--wp--preset--gradient--electric-grass: linear-gradient(135deg, rgb(202, 248, 128) 0%, rgb(113, 206, 126) 100%);
			--wp--preset--gradient--midnight: linear-gradient(135deg, rgb(2, 3, 129) 0%, rgb(40, 116, 252) 100%);
			--wp--preset--gradient--gradient-1: linear-gradient(to bottom, #cfcabe 0%, #F9F9F9 100%);
			--wp--preset--gradient--gradient-2: linear-gradient(to bottom, #C2A990 0%, #F9F9F9 100%);
			--wp--preset--gradient--gradient-3: linear-gradient(to bottom, #D8613C 0%, #F9F9F9 100%);
			--wp--preset--gradient--gradient-4: linear-gradient(to bottom, #B1C5A4 0%, #F9F9F9 100%);
			--wp--preset--gradient--gradient-5: linear-gradient(to bottom, #B5BDBC 0%, #F9F9F9 100%);
			--wp--preset--gradient--gradient-6: linear-gradient(to bottom, #A4A4A4 0%, #F9F9F9 100%);
			--wp--preset--gradient--gradient-7: linear-gradient(to bottom, #cfcabe 50%, #F9F9F9 50%);
			--wp--preset--gradient--gradient-8: linear-gradient(to bottom, #C2A990 50%, #F9F9F9 50%);
			--wp--preset--gradient--gradient-9: linear-gradient(to bottom, #D8613C 50%, #F9F9F9 50%);
			--wp--preset--gradient--gradient-10: linear-gradient(to bottom, #B1C5A4 50%, #F9F9F9 50%);
			--wp--preset--gradient--gradient-11: linear-gradient(to bottom, #B5BDBC 50%, #F9F9F9 50%);
			--wp--preset--gradient--gradient-12: linear-gradient(to bottom, #A4A4A4 50%, #F9F9F9 50%);
			--wp--preset--font-size--small: 0.9rem;
			--wp--preset--font-size--medium: 1.05rem;
			--wp--preset--font-size--large: clamp(1.39rem, 1.39rem + ((1vw - 0.2rem) * 0.767), 1.85rem);
			--wp--preset--font-size--x-large: clamp(1.85rem, 1.85rem + ((1vw - 0.2rem) * 1.083), 2.5rem);
			--wp--preset--font-size--xx-large: clamp(2.5rem, 2.5rem + ((1vw - 0.2rem) * 1.283), 3.27rem);
			--wp--preset--font-family--body: "Inter", sans-serif;
			--wp--preset--font-family--heading: Cardo;
			--wp--preset--font-family--system-sans-serif: -apple-system, BlinkMacSystemFont, avenir next, avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto, arial, sans-serif;
			--wp--preset--font-family--system-serif: Iowan Old Style, Apple Garamond, Baskerville, Times New Roman, Droid Serif, Times, Source Serif Pro, serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
			--wp--preset--spacing--20: min(1.5rem, 2vw);
			--wp--preset--spacing--30: min(2.5rem, 3vw);
			--wp--preset--spacing--40: min(4rem, 5vw);
			--wp--preset--spacing--50: min(6.5rem, 8vw);
			--wp--preset--spacing--60: min(10.5rem, 13vw);
			--wp--preset--spacing--70: 3.38rem;
			--wp--preset--spacing--80: 5.06rem;
			--wp--preset--spacing--10: 1rem;
			--wp--preset--shadow--natural: 6px 6px 9px rgba(0, 0, 0, 0.2);
			--wp--preset--shadow--deep: 12px 12px 50px rgba(0, 0, 0, 0.4);
			--wp--preset--shadow--sharp: 6px 6px 0px rgba(0, 0, 0, 0.2);
			--wp--preset--shadow--outlined: 6px 6px 0px -3px rgba(255, 255, 255, 1), 6px 6px rgba(0, 0, 0, 1);
			--wp--preset--shadow--crisp: 6px 6px 0px rgba(0, 0, 0, 1);
		}

		:root {
			--wp--style--global--content-size: 620px;
			--wp--style--global--wide-size: 1280px;
		}

		:where(body) {
			margin: 0;
		}

		.wp-site-blocks {
			padding-top: var(--wp--style--root--padding-top);
			padding-bottom: var(--wp--style--root--padding-bottom);
		}

		.has-global-padding {
			padding-right: var(--wp--style--root--padding-right);
			padding-left: var(--wp--style--root--padding-left);
		}

		.has-global-padding>.alignfull {
			margin-right: calc(var(--wp--style--root--padding-right) * -1);
			margin-left: calc(var(--wp--style--root--padding-left) * -1);
		}

		.has-global-padding :where(:not(.alignfull.is-layout-flow) > .has-global-padding:not(.wp-block-block, .alignfull)) {
			padding-right: 0;
			padding-left: 0;
		}

		.has-global-padding :where(:not(.alignfull.is-layout-flow) > .has-global-padding:not(.wp-block-block, .alignfull))>.alignfull {
			margin-left: 0;
			margin-right: 0;
		}

		.wp-site-blocks>.alignleft {
			float: left;
			margin-right: 2em;
		}

		.wp-site-blocks>.alignright {
			float: right;
			margin-left: 2em;
		}

		.wp-site-blocks>.aligncenter {
			justify-content: center;
			margin-left: auto;
			margin-right: auto;
		}

		:where(.wp-site-blocks)>* {
			margin-block-start: 1.2rem;
			margin-block-end: 0;
		}

		:where(.wp-site-blocks)> :first-child {
			margin-block-start: 0;
		}

		:where(.wp-site-blocks)> :last-child {
			margin-block-end: 0;
		}

		:root {
			--wp--style--block-gap: 1.2rem;
		}

		:root :where(.is-layout-flow)> :first-child {
			margin-block-start: 0;
		}

		:root :where(.is-layout-flow)> :last-child {
			margin-block-end: 0;
		}

		:root :where(.is-layout-flow)>* {
			margin-block-start: 1.2rem;
			margin-block-end: 0;
		}

		:root :where(.is-layout-constrained)> :first-child {
			margin-block-start: 0;
		}

		:root :where(.is-layout-constrained)> :last-child {
			margin-block-end: 0;
		}

		:root :where(.is-layout-constrained)>* {
			margin-block-start: 1.2rem;
			margin-block-end: 0;
		}

		:root :where(.is-layout-flex) {
			gap: 1.2rem;
		}

		:root :where(.is-layout-grid) {
			gap: 1.2rem;
		}

		.is-layout-flow>.alignleft {
			float: left;
			margin-inline-start: 0;
			margin-inline-end: 2em;
		}

		.is-layout-flow>.alignright {
			float: right;
			margin-inline-start: 2em;
			margin-inline-end: 0;
		}

		.is-layout-flow>.aligncenter {
			margin-left: auto !important;
			margin-right: auto !important;
		}

		.is-layout-constrained>.alignleft {
			float: left;
			margin-inline-start: 0;
			margin-inline-end: 2em;
		}

		.is-layout-constrained>.alignright {
			float: right;
			margin-inline-start: 2em;
			margin-inline-end: 0;
		}

		.is-layout-constrained>.aligncenter {
			margin-left: auto !important;
			margin-right: auto !important;
		}

		.is-layout-constrained> :where(:not(.alignleft):not(.alignright):not(.alignfull)) {
			max-width: var(--wp--style--global--content-size);
			margin-left: auto !important;
			margin-right: auto !important;
		}

		.is-layout-constrained>.alignwide {
			max-width: var(--wp--style--global--wide-size);
		}

		body .is-layout-flex {
			display: flex;
		}

		.is-layout-flex {
			flex-wrap: wrap;
			align-items: center;
		}

		.is-layout-flex> :is(*, div) {
			margin: 0;
		}

		body .is-layout-grid {
			display: grid;
		}

		.is-layout-grid> :is(*, div) {
			margin: 0;
		}

		body {
			background-color: var(--wp--preset--color--base);
			color: var(--wp--preset--color--contrast);
			font-family: var(--wp--preset--font-family--body);
			font-size: var(--wp--preset--font-size--medium);
			font-style: normal;
			font-weight: 400;
			line-height: 1.55;
			--wp--style--root--padding-top: 0px;
			--wp--style--root--padding-right: var(--wp--preset--spacing--50);
			--wp--style--root--padding-bottom: 0px;
			--wp--style--root--padding-left: var(--wp--preset--spacing--50);
		}

		a:where(:not(.wp-element-button)) {
			color: var(--wp--preset--color--contrast);
			text-decoration: underline;
		}

		:root :where(a:where(:not(.wp-element-button)):hover) {
			text-decoration: none;
		}

		h1,
		h2,
		h3,
		h4,
		h5,
		h6 {
			color: var(--wp--preset--color--contrast);
			font-family: var(--wp--preset--font-family--heading);
			font-weight: 400;
			line-height: 1.2;
		}

		h1 {
			font-size: var(--wp--preset--font-size--xx-large);
			line-height: 1.15;
		}

		h2 {
			font-size: var(--wp--preset--font-size--x-large);
		}

		h3 {
			font-size: var(--wp--preset--font-size--large);
		}

		h4 {
			font-size: clamp(1.1rem, 1.1rem + ((1vw - 0.2rem) * 0.767), 1.5rem);
		}

		h5 {
			font-size: var(--wp--preset--font-size--medium);
		}

		h6 {
			font-size: var(--wp--preset--font-size--small);
		}

		:root :where(.wp-element-button, .wp-block-button__link) {
			background-color: var(--wp--preset--color--contrast);
			border-radius: .33rem;
			border-color: var(--wp--preset--color--contrast);
			border-width: 0;
			color: var(--wp--preset--color--base);
			font-family: inherit;
			font-size: var(--wp--preset--font-size--small);
			font-style: normal;
			font-weight: 500;
			line-height: inherit;
			padding-top: 0.6rem;
			padding-right: 1rem;
			padding-bottom: 0.6rem;
			padding-left: 1rem;
			text-decoration: none;
		}

		:root :where(.wp-element-button:hover, .wp-block-button__link:hover) {
			background-color: var(--wp--preset--color--contrast-2);
			border-color: var(--wp--preset--color--contrast-2);
			color: var(--wp--preset--color--base);
		}

		:root :where(.wp-element-button:focus, .wp-block-button__link:focus) {
			background-color: var(--wp--preset--color--contrast-2);
			border-color: var(--wp--preset--color--contrast-2);
			color: var(--wp--preset--color--base);
			outline-color: var(--wp--preset--color--contrast);
			outline-offset: 2px;
		}

		:root :where(.wp-element-button:active, .wp-block-button__link:active) {
			background-color: var(--wp--preset--color--contrast);
			color: var(--wp--preset--color--base);
		}

		:root :where(.wp-element-caption, .wp-block-audio figcaption, .wp-block-embed figcaption, .wp-block-gallery figcaption, .wp-block-image figcaption, .wp-block-table figcaption, .wp-block-video figcaption) {
			color: var(--wp--preset--color--contrast-2);
			font-family: var(--wp--preset--font-family--body);
			font-size: 0.8rem;
		}

		.has-black-color {
			color: var(--wp--preset--color--black) !important;
		}

		.has-cyan-bluish-gray-color {
			color: var(--wp--preset--color--cyan-bluish-gray) !important;
		}

		.has-white-color {
			color: var(--wp--preset--color--white) !important;
		}

		.has-pale-pink-color {
			color: var(--wp--preset--color--pale-pink) !important;
		}

		.has-vivid-red-color {
			color: var(--wp--preset--color--vivid-red) !important;
		}

		.has-luminous-vivid-orange-color {
			color: var(--wp--preset--color--luminous-vivid-orange) !important;
		}

		.has-luminous-vivid-amber-color {
			color: var(--wp--preset--color--luminous-vivid-amber) !important;
		}

		.has-light-green-cyan-color {
			color: var(--wp--preset--color--light-green-cyan) !important;
		}

		.has-vivid-green-cyan-color {
			color: var(--wp--preset--color--vivid-green-cyan) !important;
		}

		.has-pale-cyan-blue-color {
			color: var(--wp--preset--color--pale-cyan-blue) !important;
		}

		.has-vivid-cyan-blue-color {
			color: var(--wp--preset--color--vivid-cyan-blue) !important;
		}

		.has-vivid-purple-color {
			color: var(--wp--preset--color--vivid-purple) !important;
		}

		.has-base-color {
			color: var(--wp--preset--color--base) !important;
		}

		.has-base-2-color {
			color: var(--wp--preset--color--base-2) !important;
		}

		.has-contrast-color {
			color: var(--wp--preset--color--contrast) !important;
		}

		.has-contrast-2-color {
			color: var(--wp--preset--color--contrast-2) !important;
		}

		.has-contrast-3-color {
			color: var(--wp--preset--color--contrast-3) !important;
		}

		.has-accent-color {
			color: var(--wp--preset--color--accent) !important;
		}

		.has-accent-2-color {
			color: var(--wp--preset--color--accent-2) !important;
		}

		.has-accent-3-color {
			color: var(--wp--preset--color--accent-3) !important;
		}

		.has-accent-4-color {
			color: var(--wp--preset--color--accent-4) !important;
		}

		.has-accent-5-color {
			color: var(--wp--preset--color--accent-5) !important;
		}

		.has-black-background-color {
			background-color: var(--wp--preset--color--black) !important;
		}

		.has-cyan-bluish-gray-background-color {
			background-color: var(--wp--preset--color--cyan-bluish-gray) !important;
		}

		.has-white-background-color {
			background-color: var(--wp--preset--color--white) !important;
		}

		.has-pale-pink-background-color {
			background-color: var(--wp--preset--color--pale-pink) !important;
		}

		.has-vivid-red-background-color {
			background-color: var(--wp--preset--color--vivid-red) !important;
		}

		.has-luminous-vivid-orange-background-color {
			background-color: var(--wp--preset--color--luminous-vivid-orange) !important;
		}

		.has-luminous-vivid-amber-background-color {
			background-color: var(--wp--preset--color--luminous-vivid-amber) !important;
		}

		.has-light-green-cyan-background-color {
			background-color: var(--wp--preset--color--light-green-cyan) !important;
		}

		.has-vivid-green-cyan-background-color {
			background-color: var(--wp--preset--color--vivid-green-cyan) !important;
		}

		.has-pale-cyan-blue-background-color {
			background-color: var(--wp--preset--color--pale-cyan-blue) !important;
		}

		.has-vivid-cyan-blue-background-color {
			background-color: var(--wp--preset--color--vivid-cyan-blue) !important;
		}

		.has-vivid-purple-background-color {
			background-color: var(--wp--preset--color--vivid-purple) !important;
		}

		.has-base-background-color {
			background-color: var(--wp--preset--color--base) !important;
		}

		.has-base-2-background-color {
			background-color: var(--wp--preset--color--base-2) !important;
		}

		.has-contrast-background-color {
			background-color: var(--wp--preset--color--contrast) !important;
		}

		.has-contrast-2-background-color {
			background-color: var(--wp--preset--color--contrast-2) !important;
		}

		.has-contrast-3-background-color {
			background-color: var(--wp--preset--color--contrast-3) !important;
		}

		.has-accent-background-color {
			background-color: var(--wp--preset--color--accent) !important;
		}

		.has-accent-2-background-color {
			background-color: var(--wp--preset--color--accent-2) !important;
		}

		.has-accent-3-background-color {
			background-color: var(--wp--preset--color--accent-3) !important;
		}

		.has-accent-4-background-color {
			background-color: var(--wp--preset--color--accent-4) !important;
		}

		.has-accent-5-background-color {
			background-color: var(--wp--preset--color--accent-5) !important;
		}

		.has-black-border-color {
			border-color: var(--wp--preset--color--black) !important;
		}

		.has-cyan-bluish-gray-border-color {
			border-color: var(--wp--preset--color--cyan-bluish-gray) !important;
		}

		.has-white-border-color {
			border-color: var(--wp--preset--color--white) !important;
		}

		.has-pale-pink-border-color {
			border-color: var(--wp--preset--color--pale-pink) !important;
		}

		.has-vivid-red-border-color {
			border-color: var(--wp--preset--color--vivid-red) !important;
		}

		.has-luminous-vivid-orange-border-color {
			border-color: var(--wp--preset--color--luminous-vivid-orange) !important;
		}

		.has-luminous-vivid-amber-border-color {
			border-color: var(--wp--preset--color--luminous-vivid-amber) !important;
		}

		.has-light-green-cyan-border-color {
			border-color: var(--wp--preset--color--light-green-cyan) !important;
		}

		.has-vivid-green-cyan-border-color {
			border-color: var(--wp--preset--color--vivid-green-cyan) !important;
		}

		.has-pale-cyan-blue-border-color {
			border-color: var(--wp--preset--color--pale-cyan-blue) !important;
		}

		.has-vivid-cyan-blue-border-color {
			border-color: var(--wp--preset--color--vivid-cyan-blue) !important;
		}

		.has-vivid-purple-border-color {
			border-color: var(--wp--preset--color--vivid-purple) !important;
		}

		.has-base-border-color {
			border-color: var(--wp--preset--color--base) !important;
		}

		.has-base-2-border-color {
			border-color: var(--wp--preset--color--base-2) !important;
		}

		.has-contrast-border-color {
			border-color: var(--wp--preset--color--contrast) !important;
		}

		.has-contrast-2-border-color {
			border-color: var(--wp--preset--color--contrast-2) !important;
		}

		.has-contrast-3-border-color {
			border-color: var(--wp--preset--color--contrast-3) !important;
		}

		.has-accent-border-color {
			border-color: var(--wp--preset--color--accent) !important;
		}

		.has-accent-2-border-color {
			border-color: var(--wp--preset--color--accent-2) !important;
		}

		.has-accent-3-border-color {
			border-color: var(--wp--preset--color--accent-3) !important;
		}

		.has-accent-4-border-color {
			border-color: var(--wp--preset--color--accent-4) !important;
		}

		.has-accent-5-border-color {
			border-color: var(--wp--preset--color--accent-5) !important;
		}

		.has-vivid-cyan-blue-to-vivid-purple-gradient-background {
			background: var(--wp--preset--gradient--vivid-cyan-blue-to-vivid-purple) !important;
		}

		.has-light-green-cyan-to-vivid-green-cyan-gradient-background {
			background: var(--wp--preset--gradient--light-green-cyan-to-vivid-green-cyan) !important;
		}

		.has-luminous-vivid-amber-to-luminous-vivid-orange-gradient-background {
			background: var(--wp--preset--gradient--luminous-vivid-amber-to-luminous-vivid-orange) !important;
		}

		.has-luminous-vivid-orange-to-vivid-red-gradient-background {
			background: var(--wp--preset--gradient--luminous-vivid-orange-to-vivid-red) !important;
		}

		.has-very-light-gray-to-cyan-bluish-gray-gradient-background {
			background: var(--wp--preset--gradient--very-light-gray-to-cyan-bluish-gray) !important;
		}

		.has-cool-to-warm-spectrum-gradient-background {
			background: var(--wp--preset--gradient--cool-to-warm-spectrum) !important;
		}

		.has-blush-light-purple-gradient-background {
			background: var(--wp--preset--gradient--blush-light-purple) !important;
		}

		.has-blush-bordeaux-gradient-background {
			background: var(--wp--preset--gradient--blush-bordeaux) !important;
		}

		.has-luminous-dusk-gradient-background {
			background: var(--wp--preset--gradient--luminous-dusk) !important;
		}

		.has-pale-ocean-gradient-background {
			background: var(--wp--preset--gradient--pale-ocean) !important;
		}

		.has-electric-grass-gradient-background {
			background: var(--wp--preset--gradient--electric-grass) !important;
		}

		.has-midnight-gradient-background {
			background: var(--wp--preset--gradient--midnight) !important;
		}

		.has-gradient-1-gradient-background {
			background: var(--wp--preset--gradient--gradient-1) !important;
		}

		.has-gradient-2-gradient-background {
			background: var(--wp--preset--gradient--gradient-2) !important;
		}

		.has-gradient-3-gradient-background {
			background: var(--wp--preset--gradient--gradient-3) !important;
		}

		.has-gradient-4-gradient-background {
			background: var(--wp--preset--gradient--gradient-4) !important;
		}

		.has-gradient-5-gradient-background {
			background: var(--wp--preset--gradient--gradient-5) !important;
		}

		.has-gradient-6-gradient-background {
			background: var(--wp--preset--gradient--gradient-6) !important;
		}

		.has-gradient-7-gradient-background {
			background: var(--wp--preset--gradient--gradient-7) !important;
		}

		.has-gradient-8-gradient-background {
			background: var(--wp--preset--gradient--gradient-8) !important;
		}

		.has-gradient-9-gradient-background {
			background: var(--wp--preset--gradient--gradient-9) !important;
		}

		.has-gradient-10-gradient-background {
			background: var(--wp--preset--gradient--gradient-10) !important;
		}

		.has-gradient-11-gradient-background {
			background: var(--wp--preset--gradient--gradient-11) !important;
		}

		.has-gradient-12-gradient-background {
			background: var(--wp--preset--gradient--gradient-12) !important;
		}

		.has-small-font-size {
			font-size: var(--wp--preset--font-size--small) !important;
		}

		.has-medium-font-size {
			font-size: var(--wp--preset--font-size--medium) !important;
		}

		.has-large-font-size {
			font-size: var(--wp--preset--font-size--large) !important;
		}

		.has-x-large-font-size {
			font-size: var(--wp--preset--font-size--x-large) !important;
		}

		.has-xx-large-font-size {
			font-size: var(--wp--preset--font-size--xx-large) !important;
		}

		.has-body-font-family {
			font-family: var(--wp--preset--font-family--body) !important;
		}

		.has-heading-font-family {
			font-family: var(--wp--preset--font-family--heading) !important;
		}

		.has-system-sans-serif-font-family {
			font-family: var(--wp--preset--font-family--system-sans-serif) !important;
		}

		.has-system-serif-font-family {
			font-family: var(--wp--preset--font-family--system-serif) !important;
		}

		:where(.wp-site-blocks *:focus) {
			outline-width: 2px;
			outline-style: solid
		}
	</style>
	<style id='wp-block-template-skip-link-inline-css'>
		.skip-link.screen-reader-text {
			border: 0;
			clip: rect(1px, 1px, 1px, 1px);
			clip-path: inset(50%);
			height: 1px;
			margin: -1px;
			overflow: hidden;
			padding: 0;
			position: absolute !important;
			width: 1px;
			word-wrap: normal !important;
		}

		.skip-link.screen-reader-text:focus {
			background-color: #eee;
			clip: auto !important;
			clip-path: none;
			color: #444;
			display: block;
			font-size: 1em;
			height: auto;
			left: 5px;
			line-height: normal;
			padding: 15px 23px 14px;
			text-decoration: none;
			top: 5px;
			width: auto;
			z-index: 100000;
		}
	</style>
	<!-- <link rel='stylesheet' id='elementor-frontend-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/frontend.min.css?ver=3.27.3' media='all' /> -->
	<!-- <link rel='stylesheet' id='elementor-post-11-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_7daa6e250cc52251dbe46e6eddc87ab5.css?ver=1738745532' media='all' /> -->
	<!-- <link rel='stylesheet' id='widget-heading-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-heading.min.css?ver=3.27.3' media='all' /> -->
	<!-- <link rel='stylesheet' id='widget-text-editor-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-text-editor.min.css?ver=3.27.3' media='all' /> -->
	<!-- <link rel='stylesheet' id='widget-image-css' href='https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/css/widget-image.min.css?ver=3.27.3' media='all' /> -->
	<!-- <link rel='stylesheet' id='elementor-post-1424-css' href='https://fgrealty.qa/lp/wp-content/cache/autoptimize/css/autoptimize_single_58378b6a80f112bcc7ac4b15ab1d1456.css?ver=1738771083' media='all' /> -->
	<link rel="https://api.w.org/" href="https://fgrealty.qa/lp/wp-json/" />
	<link rel="alternate" title="JSON" type="application/json" href="https://fgrealty.qa/lp/wp-json/wp/v2/pages/1424" />
	<meta name="generator" content="WordPress 6.7.2" />
	<link rel="canonical" href="https://fgrealty.qa/en/projects/marina-heights/thankyou" />
	<link rel='shortlink' href='https://fgrealty.qa/lp/?p=1424' />
	<link rel="alternate" title="oEmbed (XML)" type="text/xml+oembed" href="https://fgrealty.qa/lp/wp-json/oembed/1.0/embed?url=https%3A%2F%2Ffgrealty.qa%2Flp%2Fjmj-thank-you%2F&#038;format=xml" />
	<meta name="generator" content="Elementor 3.27.3; features: e_font_icon_svg, additional_custom_breakpoints; settings: css_print_method-external, google_font-disabled, font_display-swap">
	<script defer src="data:text/javascript;base64,DQogIHdpbmRvdy5kYXRhTGF5ZXIgPSB3aW5kb3cuZGF0YUxheWVyIHx8IFtdOw0KICBmdW5jdGlvbiBndGFnKCl7ZGF0YUxheWVyLnB1c2goYXJndW1lbnRzKTt9DQogIGd0YWcoJ2pzJywgbmV3IERhdGUoKSk7DQoNCiAgZ3RhZygnY29uZmlnJywgJ0ctR1lCUzJFUlNFTicpOw0K"></script>
	<script defer src="data:text/javascript;base64,DQohZnVuY3Rpb24oZixiLGUsdixuLHQscykNCntpZihmLmZicSlyZXR1cm47bj1mLmZicT1mdW5jdGlvbigpe24uY2FsbE1ldGhvZD8NCm4uY2FsbE1ldGhvZC5hcHBseShuLGFyZ3VtZW50cyk6bi5xdWV1ZS5wdXNoKGFyZ3VtZW50cyl9Ow0KaWYoIWYuX2ZicSlmLl9mYnE9bjtuLnB1c2g9bjtuLmxvYWRlZD0hMDtuLnZlcnNpb249JzIuMCc7DQpuLnF1ZXVlPVtdO3Q9Yi5jcmVhdGVFbGVtZW50KGUpO3QuYXN5bmM9ITA7DQp0LnNyYz12O3M9Yi5nZXRFbGVtZW50c0J5VGFnTmFtZShlKVswXTsNCnMucGFyZW50Tm9kZS5pbnNlcnRCZWZvcmUodCxzKX0od2luZG93LCBkb2N1bWVudCwnc2NyaXB0JywNCidodHRwczovL2Nvbm5lY3QuZmFjZWJvb2submV0L2VuX1VTL2ZiZXZlbnRzLmpzJyk7DQpmYnEoJ2luaXQnLCAnNzE0MzE2NDAzNTc5MzQ4MCcpOw0KZmJxKCd0cmFjaycsICdQYWdlVmlldycpOw0K"></script> <noscript><img height="1" width="1" style="display:none"
			src="https://www.facebook.com/tr?id=7143164035793480&ev=PageView&noscript=1" /></noscript>
	<style>
		.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload),
		.e-con.e-parent:nth-of-type(n+4):not(.e-lazyloaded):not(.e-no-lazyload) * {
			background-image: none !important;
		}

		@media screen and (max-height: 1024px) {

			.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload),
			.e-con.e-parent:nth-of-type(n+3):not(.e-lazyloaded):not(.e-no-lazyload) * {
				background-image: none !important;
			}
		}

		@media screen and (max-height: 640px) {

			.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload),
			.e-con.e-parent:nth-of-type(n+2):not(.e-lazyloaded):not(.e-no-lazyload) * {
				background-image: none !important;
			}
		}
	</style>
	<style class='wp-fonts-local'>
		@font-face {
			font-family: Inter;
			font-style: normal;
			font-weight: 300 900;
			font-display: fallback;
			src: url('https://fgrealty.qa/lp/wp-content/themes/twentytwentyfour/assets/fonts/inter/Inter-VariableFont_slnt,wght.woff2') format('woff2');
			font-stretch: normal;
		}

		@font-face {
			font-family: Cardo;
			font-style: normal;
			font-weight: 400;
			font-display: fallback;
			src: url('https://fgrealty.qa/lp/wp-content/themes/twentytwentyfour/assets/fonts/cardo/cardo_normal_400.woff2') format('woff2');
		}

		@font-face {
			font-family: Cardo;
			font-style: italic;
			font-weight: 400;
			font-display: fallback;
			src: url('https://fgrealty.qa/lp/wp-content/themes/twentytwentyfour/assets/fonts/cardo/cardo_italic_400.woff2') format('woff2');
		}

		@font-face {
			font-family: Cardo;
			font-style: normal;
			font-weight: 700;
			font-display: fallback;
			src: url('https://fgrealty.qa/lp/wp-content/themes/twentytwentyfour/assets/fonts/cardo/cardo_normal_700.woff2') format('woff2');
		}
	</style>
	<link rel="icon" href="https://fgrealty.qa/lp/wp-content/uploads/2024/05/favicon.svg" sizes="32x32" />
	<link rel="icon" href="https://fgrealty.qa/lp/wp-content/uploads/2024/05/favicon.svg" sizes="192x192" />
	<link rel="apple-touch-icon" href="https://fgrealty.qa/lp/wp-content/uploads/2024/05/favicon.svg" />
	<meta name="msapplication-TileImage" content="https://fgrealty.qa/lp/wp-content/uploads/2024/05/favicon.svg" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
</head>

<body class="page-template page-template-elementor_canvas page page-id-1424 wp-embed-responsive elementor-default elementor-template-canvas elementor-kit-11 elementor-page elementor-page-1424"> <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WPD3N3NC"
			height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript></head>

	<body>
		<script defer src="data:text/javascript;base64,DQogIGZicSgndHJhY2snLCAnTGVhZCcsIHsNCiAgICB2YWx1ZTogMSwNCiAgICBjdXJyZW5jeTogJyQnLA0KICB9KTsNCg=="></script>
		<div data-elementor-type="wp-page" data-elementor-id="1424" class="elementor elementor-1424" data-elementor-post-type="page">
			<div class="elementor-element elementor-element-339a271 e-flex e-con-boxed e-con e-parent" data-id="339a271" data-element_type="container">
				<div class="e-con-inner">
					<div class="elementor-element elementor-element-7c849a5 elementor-widget elementor-widget-heading" data-id="7c849a5" data-element_type="widget" data-widget_type="heading.default">
						<div class="elementor-widget-container">
							<h2 class="elementor-heading-title elementor-size-default">{{__('Thank YOU!')}}</h2>
						</div>
					</div>
					<div class="elementor-element elementor-element-add0f9f elementor-widget__width-initial elementor-widget elementor-widget-text-editor" data-id="add0f9f" data-element_type="widget" data-widget_type="text-editor.default">
						<div class="elementor-widget-container">
							<p>{{__('Thank you for submitting your request.')}}<br />{{__('One of our agents will be in touch with you shortly.')}}</p>
						</div>
					</div>
					<div class="elementor-element elementor-element-8118df9 elementor-widget elementor-widget-image" data-id="8118df9" data-element_type="widget" data-widget_type="image.default">
						<div class="elementor-widget-container"> <a href="https://www.fgrealty.qa"> <img decoding="async" width="97" height="48" src="https://fgrealty.qa/lp/wp-content/uploads/2024/05/FGREALTY_logo.svg" class="attachment-large size-large wp-image-29" alt="" /> </a></div>
					</div>
				</div>
			</div>
		</div>
		<script defer src="data:text/javascript;base64,CgkJCQljb25zdCBsYXp5bG9hZFJ1bk9ic2VydmVyID0gKCkgPT4gewoJCQkJCWNvbnN0IGxhenlsb2FkQmFja2dyb3VuZHMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCBgLmUtY29uLmUtcGFyZW50Om5vdCguZS1sYXp5bG9hZGVkKWAgKTsKCQkJCQljb25zdCBsYXp5bG9hZEJhY2tncm91bmRPYnNlcnZlciA9IG5ldyBJbnRlcnNlY3Rpb25PYnNlcnZlciggKCBlbnRyaWVzICkgPT4gewoJCQkJCQllbnRyaWVzLmZvckVhY2goICggZW50cnkgKSA9PiB7CgkJCQkJCQlpZiAoIGVudHJ5LmlzSW50ZXJzZWN0aW5nICkgewoJCQkJCQkJCWxldCBsYXp5bG9hZEJhY2tncm91bmQgPSBlbnRyeS50YXJnZXQ7CgkJCQkJCQkJaWYoIGxhenlsb2FkQmFja2dyb3VuZCApIHsKCQkJCQkJCQkJbGF6eWxvYWRCYWNrZ3JvdW5kLmNsYXNzTGlzdC5hZGQoICdlLWxhenlsb2FkZWQnICk7CgkJCQkJCQkJfQoJCQkJCQkJCWxhenlsb2FkQmFja2dyb3VuZE9ic2VydmVyLnVub2JzZXJ2ZSggZW50cnkudGFyZ2V0ICk7CgkJCQkJCQl9CgkJCQkJCX0pOwoJCQkJCX0sIHsgcm9vdE1hcmdpbjogJzIwMHB4IDBweCAyMDBweCAwcHgnIH0gKTsKCQkJCQlsYXp5bG9hZEJhY2tncm91bmRzLmZvckVhY2goICggbGF6eWxvYWRCYWNrZ3JvdW5kICkgPT4gewoJCQkJCQlsYXp5bG9hZEJhY2tncm91bmRPYnNlcnZlci5vYnNlcnZlKCBsYXp5bG9hZEJhY2tncm91bmQgKTsKCQkJCQl9ICk7CgkJCQl9OwoJCQkJY29uc3QgZXZlbnRzID0gWwoJCQkJCSdET01Db250ZW50TG9hZGVkJywKCQkJCQknZWxlbWVudG9yL2xhenlsb2FkL29ic2VydmUnLAoJCQkJXTsKCQkJCWV2ZW50cy5mb3JFYWNoKCAoIGV2ZW50ICkgPT4gewoJCQkJCWRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoIGV2ZW50LCBsYXp5bG9hZFJ1bk9ic2VydmVyICk7CgkJCQl9ICk7CgkJCQ=="></script> <noscript>
			<style>
				.lazyload {
					display: none;
				}
			</style>
		</noscript>
		<script data-noptimize="1">
			window.lazySizesConfig = window.lazySizesConfig || {};
			window.lazySizesConfig.loadMode = 1;
		</script>
		<script defer data-noptimize="1" src='https://fgrealty.qa/lp/wp-content/plugins/autoptimize/classes/external/js/lazysizes.min.js?ao_version=3.1.13'></script>
		<script defer id="wp-block-template-skip-link-js-after" src="data:text/javascript;base64,CgkoIGZ1bmN0aW9uKCkgewoJCXZhciBza2lwTGlua1RhcmdldCA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoICdtYWluJyApLAoJCQlzaWJsaW5nLAoJCQlza2lwTGlua1RhcmdldElELAoJCQlza2lwTGluazsKCgkJLy8gRWFybHkgZXhpdCBpZiBhIHNraXAtbGluayB0YXJnZXQgY2FuJ3QgYmUgbG9jYXRlZC4KCQlpZiAoICEgc2tpcExpbmtUYXJnZXQgKSB7CgkJCXJldHVybjsKCQl9CgoJCS8qCgkJICogR2V0IHRoZSBzaXRlIHdyYXBwZXIuCgkJICogVGhlIHNraXAtbGluayB3aWxsIGJlIGluamVjdGVkIGluIHRoZSBiZWdpbm5pbmcgb2YgaXQuCgkJICovCgkJc2libGluZyA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoICcud3Atc2l0ZS1ibG9ja3MnICk7CgoJCS8vIEVhcmx5IGV4aXQgaWYgdGhlIHJvb3QgZWxlbWVudCB3YXMgbm90IGZvdW5kLgoJCWlmICggISBzaWJsaW5nICkgewoJCQlyZXR1cm47CgkJfQoKCQkvLyBHZXQgdGhlIHNraXAtbGluayB0YXJnZXQncyBJRCwgYW5kIGdlbmVyYXRlIG9uZSBpZiBpdCBkb2Vzbid0IGV4aXN0LgoJCXNraXBMaW5rVGFyZ2V0SUQgPSBza2lwTGlua1RhcmdldC5pZDsKCQlpZiAoICEgc2tpcExpbmtUYXJnZXRJRCApIHsKCQkJc2tpcExpbmtUYXJnZXRJRCA9ICd3cC0tc2tpcC1saW5rLS10YXJnZXQnOwoJCQlza2lwTGlua1RhcmdldC5pZCA9IHNraXBMaW5rVGFyZ2V0SUQ7CgkJfQoKCQkvLyBDcmVhdGUgdGhlIHNraXAgbGluay4KCQlza2lwTGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoICdhJyApOwoJCXNraXBMaW5rLmNsYXNzTGlzdC5hZGQoICdza2lwLWxpbmsnLCAnc2NyZWVuLXJlYWRlci10ZXh0JyApOwoJCXNraXBMaW5rLmhyZWYgPSAnIycgKyBza2lwTGlua1RhcmdldElEOwoJCXNraXBMaW5rLmlubmVySFRNTCA9ICdTa2lwIHRvIGNvbnRlbnQnOwoKCQkvLyBJbmplY3QgdGhlIHNraXAgbGluay4KCQlzaWJsaW5nLnBhcmVudEVsZW1lbnQuaW5zZXJ0QmVmb3JlKCBza2lwTGluaywgc2libGluZyApOwoJfSgpICk7CgkK"></script>
		<script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/js/webpack-pro.runtime.min.js?ver=3.27.2" id="elementor-pro-webpack-runtime-js"></script>
		<script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/js/webpack.runtime.min.js?ver=3.27.3" id="elementor-webpack-runtime-js"></script>
		<script src="https://fgrealty.qa/lp/wp-includes/js/jquery/jquery.min.js?ver=3.7.1" id="jquery-core-js"></script>
		<script defer src="https://fgrealty.qa/lp/wp-includes/js/jquery/jquery-migrate.min.js?ver=3.4.1" id="jquery-migrate-js"></script>
		<script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/js/frontend-modules.min.js?ver=3.27.3" id="elementor-frontend-modules-js"></script>
		<script src="https://fgrealty.qa/lp/wp-includes/js/dist/hooks.min.js?ver=4d63a3d491d11ffd8ac6" id="wp-hooks-js"></script>
		<script src="https://fgrealty.qa/lp/wp-includes/js/dist/i18n.min.js?ver=5e580eb46a90c2b997e6" id="wp-i18n-js"></script>
		<script defer id="wp-i18n-js-after" src="data:text/javascript;base64,CndwLmkxOG4uc2V0TG9jYWxlRGF0YSggeyAndGV4dCBkaXJlY3Rpb25cdTAwMDRsdHInOiBbICdsdHInIF0gfSApOwo="></script>
		<script defer id="elementor-pro-frontend-js-before" src="data:text/javascript;base64,CnZhciBFbGVtZW50b3JQcm9Gcm9udGVuZENvbmZpZyA9IHsiYWpheHVybCI6Imh0dHBzOlwvXC9mZ3JlYWx0eS5xYVwvbHBcL3dwLWFkbWluXC9hZG1pbi1hamF4LnBocCIsIm5vbmNlIjoiYmJiN2FkZmZkZiIsInVybHMiOnsiYXNzZXRzIjoiaHR0cHM6XC9cL2ZncmVhbHR5LnFhXC9scFwvd3AtY29udGVudFwvcGx1Z2luc1wvZWxlbWVudG9yLXByb1wvYXNzZXRzXC8iLCJyZXN0IjoiaHR0cHM6XC9cL2ZncmVhbHR5LnFhXC9scFwvd3AtanNvblwvIn0sInNldHRpbmdzIjp7ImxhenlfbG9hZF9iYWNrZ3JvdW5kX2ltYWdlcyI6dHJ1ZX0sInBvcHVwIjp7Imhhc1BvcFVwcyI6dHJ1ZX0sInNoYXJlQnV0dG9uc05ldHdvcmtzIjp7ImZhY2Vib29rIjp7InRpdGxlIjoiRmFjZWJvb2siLCJoYXNfY291bnRlciI6dHJ1ZX0sInR3aXR0ZXIiOnsidGl0bGUiOiJUd2l0dGVyIn0sImxpbmtlZGluIjp7InRpdGxlIjoiTGlua2VkSW4iLCJoYXNfY291bnRlciI6dHJ1ZX0sInBpbnRlcmVzdCI6eyJ0aXRsZSI6IlBpbnRlcmVzdCIsImhhc19jb3VudGVyIjp0cnVlfSwicmVkZGl0Ijp7InRpdGxlIjoiUmVkZGl0IiwiaGFzX2NvdW50ZXIiOnRydWV9LCJ2ayI6eyJ0aXRsZSI6IlZLIiwiaGFzX2NvdW50ZXIiOnRydWV9LCJvZG5va2xhc3NuaWtpIjp7InRpdGxlIjoiT0siLCJoYXNfY291bnRlciI6dHJ1ZX0sInR1bWJsciI6eyJ0aXRsZSI6IlR1bWJsciJ9LCJkaWdnIjp7InRpdGxlIjoiRGlnZyJ9LCJza3lwZSI6eyJ0aXRsZSI6IlNreXBlIn0sInN0dW1ibGV1cG9uIjp7InRpdGxlIjoiU3R1bWJsZVVwb24iLCJoYXNfY291bnRlciI6dHJ1ZX0sIm1peCI6eyJ0aXRsZSI6Ik1peCJ9LCJ0ZWxlZ3JhbSI6eyJ0aXRsZSI6IlRlbGVncmFtIn0sInBvY2tldCI6eyJ0aXRsZSI6IlBvY2tldCIsImhhc19jb3VudGVyIjp0cnVlfSwieGluZyI6eyJ0aXRsZSI6IlhJTkciLCJoYXNfY291bnRlciI6dHJ1ZX0sIndoYXRzYXBwIjp7InRpdGxlIjoiV2hhdHNBcHAifSwiZW1haWwiOnsidGl0bGUiOiJFbWFpbCJ9LCJwcmludCI6eyJ0aXRsZSI6IlByaW50In0sIngtdHdpdHRlciI6eyJ0aXRsZSI6IlgifSwidGhyZWFkcyI6eyJ0aXRsZSI6IlRocmVhZHMifX0sImZhY2Vib29rX3NkayI6eyJsYW5nIjoiZW5fVVMiLCJhcHBfaWQiOiIifSwibG90dGllIjp7ImRlZmF1bHRBbmltYXRpb25VcmwiOiJodHRwczpcL1wvZmdyZWFsdHkucWFcL2xwXC93cC1jb250ZW50XC9wbHVnaW5zXC9lbGVtZW50b3ItcHJvXC9tb2R1bGVzXC9sb3R0aWVcL2Fzc2V0c1wvYW5pbWF0aW9uc1wvZGVmYXVsdC5qc29uIn19Owo="></script>
		<script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/js/frontend.min.js?ver=3.27.2" id="elementor-pro-frontend-js"></script>
		<script defer src="https://fgrealty.qa/lp/wp-includes/js/jquery/ui/core.min.js?ver=1.13.3" id="jquery-ui-core-js"></script>
		<script defer id="elementor-frontend-js-before" src="data:text/javascript;base64,CnZhciBlbGVtZW50b3JGcm9udGVuZENvbmZpZyA9IHsiZW52aXJvbm1lbnRNb2RlIjp7ImVkaXQiOmZhbHNlLCJ3cFByZXZpZXciOmZhbHNlLCJpc1NjcmlwdERlYnVnIjpmYWxzZX0sImkxOG4iOnsic2hhcmVPbkZhY2Vib29rIjoiU2hhcmUgb24gRmFjZWJvb2siLCJzaGFyZU9uVHdpdHRlciI6IlNoYXJlIG9uIFR3aXR0ZXIiLCJwaW5JdCI6IlBpbiBpdCIsImRvd25sb2FkIjoiRG93bmxvYWQiLCJkb3dubG9hZEltYWdlIjoiRG93bmxvYWQgaW1hZ2UiLCJmdWxsc2NyZWVuIjoiRnVsbHNjcmVlbiIsInpvb20iOiJab29tIiwic2hhcmUiOiJTaGFyZSIsInBsYXlWaWRlbyI6IlBsYXkgVmlkZW8iLCJwcmV2aW91cyI6IlByZXZpb3VzIiwibmV4dCI6Ik5leHQiLCJjbG9zZSI6IkNsb3NlIiwiYTExeUNhcm91c2VsUHJldlNsaWRlTWVzc2FnZSI6IlByZXZpb3VzIHNsaWRlIiwiYTExeUNhcm91c2VsTmV4dFNsaWRlTWVzc2FnZSI6Ik5leHQgc2xpZGUiLCJhMTF5Q2Fyb3VzZWxGaXJzdFNsaWRlTWVzc2FnZSI6IlRoaXMgaXMgdGhlIGZpcnN0IHNsaWRlIiwiYTExeUNhcm91c2VsTGFzdFNsaWRlTWVzc2FnZSI6IlRoaXMgaXMgdGhlIGxhc3Qgc2xpZGUiLCJhMTF5Q2Fyb3VzZWxQYWdpbmF0aW9uQnVsbGV0TWVzc2FnZSI6IkdvIHRvIHNsaWRlIn0sImlzX3J0bCI6ZmFsc2UsImJyZWFrcG9pbnRzIjp7InhzIjowLCJzbSI6NDgwLCJtZCI6NzY4LCJsZyI6MTAyNSwieGwiOjE0NDAsInh4bCI6MTYwMH0sInJlc3BvbnNpdmUiOnsiYnJlYWtwb2ludHMiOnsibW9iaWxlIjp7ImxhYmVsIjoiTW9iaWxlIFBvcnRyYWl0IiwidmFsdWUiOjc2NywiZGVmYXVsdF92YWx1ZSI6NzY3LCJkaXJlY3Rpb24iOiJtYXgiLCJpc19lbmFibGVkIjp0cnVlfSwibW9iaWxlX2V4dHJhIjp7ImxhYmVsIjoiTW9iaWxlIExhbmRzY2FwZSIsInZhbHVlIjo4ODAsImRlZmF1bHRfdmFsdWUiOjg4MCwiZGlyZWN0aW9uIjoibWF4IiwiaXNfZW5hYmxlZCI6ZmFsc2V9LCJ0YWJsZXQiOnsibGFiZWwiOiJUYWJsZXQgUG9ydHJhaXQiLCJ2YWx1ZSI6MTAyNCwiZGVmYXVsdF92YWx1ZSI6MTAyNCwiZGlyZWN0aW9uIjoibWF4IiwiaXNfZW5hYmxlZCI6dHJ1ZX0sInRhYmxldF9leHRyYSI6eyJsYWJlbCI6IlRhYmxldCBMYW5kc2NhcGUiLCJ2YWx1ZSI6MTIwMCwiZGVmYXVsdF92YWx1ZSI6MTIwMCwiZGlyZWN0aW9uIjoibWF4IiwiaXNfZW5hYmxlZCI6ZmFsc2V9LCJsYXB0b3AiOnsibGFiZWwiOiJMYXB0b3AiLCJ2YWx1ZSI6MTM2NiwiZGVmYXVsdF92YWx1ZSI6MTM2NiwiZGlyZWN0aW9uIjoibWF4IiwiaXNfZW5hYmxlZCI6ZmFsc2V9LCJ3aWRlc2NyZWVuIjp7ImxhYmVsIjoiV2lkZXNjcmVlbiIsInZhbHVlIjoyNDAwLCJkZWZhdWx0X3ZhbHVlIjoyNDAwLCJkaXJlY3Rpb24iOiJtaW4iLCJpc19lbmFibGVkIjpmYWxzZX19LCJoYXNDdXN0b21CcmVha3BvaW50cyI6ZmFsc2V9LCJ2ZXJzaW9uIjoiMy4yNy4zIiwiaXNfc3RhdGljIjpmYWxzZSwiZXhwZXJpbWVudGFsRmVhdHVyZXMiOnsiZV9mb250X2ljb25fc3ZnIjp0cnVlLCJhZGRpdGlvbmFsX2N1c3RvbV9icmVha3BvaW50cyI6dHJ1ZSwiY29udGFpbmVyIjp0cnVlLCJlX3N3aXBlcl9sYXRlc3QiOnRydWUsImVfb25ib2FyZGluZyI6dHJ1ZSwidGhlbWVfYnVpbGRlcl92MiI6dHJ1ZSwiaG9tZV9zY3JlZW4iOnRydWUsImxhbmRpbmctcGFnZXMiOnRydWUsIm5lc3RlZC1lbGVtZW50cyI6dHJ1ZSwiZWRpdG9yX3YyIjp0cnVlLCJsaW5rLWluLWJpbyI6dHJ1ZSwiZmxvYXRpbmctYnV0dG9ucyI6dHJ1ZSwibWVnYS1tZW51Ijp0cnVlfSwidXJscyI6eyJhc3NldHMiOiJodHRwczpcL1wvZmdyZWFsdHkucWFcL2xwXC93cC1jb250ZW50XC9wbHVnaW5zXC9lbGVtZW50b3JcL2Fzc2V0c1wvIiwiYWpheHVybCI6Imh0dHBzOlwvXC9mZ3JlYWx0eS5xYVwvbHBcL3dwLWFkbWluXC9hZG1pbi1hamF4LnBocCIsInVwbG9hZFVybCI6Imh0dHBzOlwvXC9mZ3JlYWx0eS5xYVwvbHBcL3dwLWNvbnRlbnRcL3VwbG9hZHMifSwibm9uY2VzIjp7ImZsb2F0aW5nQnV0dG9uc0NsaWNrVHJhY2tpbmciOiI3YmI5YzdiYjFhIn0sInN3aXBlckNsYXNzIjoic3dpcGVyIiwic2V0dGluZ3MiOnsicGFnZSI6W10sImVkaXRvclByZWZlcmVuY2VzIjpbXX0sImtpdCI6eyJib2R5X2JhY2tncm91bmRfYmFja2dyb3VuZCI6ImNsYXNzaWMiLCJhY3RpdmVfYnJlYWtwb2ludHMiOlsidmlld3BvcnRfbW9iaWxlIiwidmlld3BvcnRfdGFibGV0Il0sImdsb2JhbF9pbWFnZV9saWdodGJveCI6InllcyIsImxpZ2h0Ym94X2VuYWJsZV9jb3VudGVyIjoieWVzIiwibGlnaHRib3hfZW5hYmxlX2Z1bGxzY3JlZW4iOiJ5ZXMiLCJsaWdodGJveF9lbmFibGVfem9vbSI6InllcyIsImxpZ2h0Ym94X2VuYWJsZV9zaGFyZSI6InllcyIsImxpZ2h0Ym94X3RpdGxlX3NyYyI6InRpdGxlIiwibGlnaHRib3hfZGVzY3JpcHRpb25fc3JjIjoiZGVzY3JpcHRpb24ifSwicG9zdCI6eyJpZCI6MTQyNCwidGl0bGUiOiJKTUolMjBUaGFuayUyMFlvdSUyMSUyMCVFMiU4MCU5MyUyMEZHUkVBTFRZJUMyJUE5IiwiZXhjZXJwdCI6IiIsImZlYXR1cmVkSW1hZ2UiOmZhbHNlfX07Cg=="></script>
		<script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor/assets/js/frontend.min.js?ver=3.27.3" id="elementor-frontend-js"></script>
		<script defer src="https://fgrealty.qa/lp/wp-content/plugins/elementor-pro/assets/js/elements-handlers.min.js?ver=3.27.2" id="pro-elements-handlers-js"></script>
		<script defer src="data:text/javascript;base64,DQpfbGlua2VkaW5fcGFydG5lcl9pZCA9ICI3MTUyNzA1IjsNCndpbmRvdy5fbGlua2VkaW5fZGF0YV9wYXJ0bmVyX2lkcyA9IHdpbmRvdy5fbGlua2VkaW5fZGF0YV9wYXJ0bmVyX2lkcyB8fCBbXTsNCndpbmRvdy5fbGlua2VkaW5fZGF0YV9wYXJ0bmVyX2lkcy5wdXNoKF9saW5rZWRpbl9wYXJ0bmVyX2lkKTsNCg=="></script>
		<script defer src="data:text/javascript;base64,DQooZnVuY3Rpb24obCkgew0KaWYgKCFsKXt3aW5kb3cubGludHJrID0gZnVuY3Rpb24oYSxiKXt3aW5kb3cubGludHJrLnEucHVzaChbYSxiXSl9Ow0Kd2luZG93LmxpbnRyay5xPVtdfQ0KdmFyIHMgPSBkb2N1bWVudC5nZXRFbGVtZW50c0J5VGFnTmFtZSgic2NyaXB0IilbMF07DQp2YXIgYiA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoInNjcmlwdCIpOw0KYi50eXBlID0gInRleHQvamF2YXNjcmlwdCI7Yi5hc3luYyA9IHRydWU7DQpiLnNyYyA9ICJodHRwczovL3NuYXAubGljZG4uY29tL2xpLmxtcy1hbmFseXRpY3MvaW5zaWdodC5taW4uanMiOw0Kcy5wYXJlbnROb2RlLmluc2VydEJlZm9yZShiLCBzKTt9KSh3aW5kb3cubGludHJrKTsNCg=="></script> <noscript> <img height="1" width="1" style="display:none;" alt="" src="https://px.ads.linkedin.com/collect/?pid=7152705&fmt=gif" /> </noscript>
	</body>

</html>