@extends('crm.layout')

@section('navbar')
<x-crm.main-navigation></x-crm.main-navigation>
@endsection
@section('content')
<link rel="stylesheet" type="text/css" href="/css/virtual-select.min.css" />
<link rel="stylesheet" href="{{ mix('css/countries-flags.css') }}" />
<style>
    .vscomp-ele {
        max-width: 100%;
        width: 100%;
    }

    .landlord-scrollspy {
        position: relative;
        height: calc(100vh - 180px);
        margin-top: .5rem;
        overflow-y: auto;
    }
    .add-file-container {
        display: flex;
        justify-content: flex-start
    }
</style>

<div class="relative">
    <nav id="navbarBrokerLandlordForm" class="navbar navbar-light bg-light px-3">
        <ul class="nav nav-pills">
            @foreach($formSections as $sectionId => $sectionLabel)
            <li class="nav-item">
                <a class="nav-link" href="#{{$sectionId}}">{{$sectionLabel}}</a>
            </li>
            @endforeach
        </ul>
    </nav>
</div>

@php
$taskIdInitialValue = "";

if(isset($createFromTask) && !is_null($createFromTask)) {
    $taskIdInitialValue = $createFromTask->id;
}
@endphp

<div data-bs-spy="scroll" data-bs-target="#navbarBrokerLandlordForm" class="landlord-scrollspy" data-bs-offset="0" tabindex="0">

    @if($errors->any())
    <div class="alert alert-danger">Please review the fields marked with red</div>
    @endif

    @if (session('message.success'))
    <div class="alert alert-success">
        {{ session('message.success') }}
    </div>
    @endif

    @if (session('message.error'))
    <div class="alert alert-danger">
        {{ session('message.error') }}
    </div>
    @endif

    @if(!$accessReadonly)
    <form style="width:100%" id="landlordForm" name="landlordForm" action="{{$formAction}}" enctype="multipart/form-data" class="container" method="POST">

        @csrf
        @endif

        @include('crm.components.form.input',
        [
        'fieldName' => 'source_contact_id',
        'fieldType' => 'hidden',
        'extraCssClasses' => 'hidden',
        'fieldLabel' => '',
        'defaultValue' => defaultValue($contact, 'source_contact_id')
        ]
        )

        @component('crm.components.form.section-card', ['sectionId' => 'broker-landlord'])
        <h4> Landlord details</h4>
        <div class="row">
            <div class="py-2">
                @if($contact->landlordIsVerified())
                <span data-bs-toggle="tooltip" data-bs-placement="top" title="Has a valid marketing agreement" class="badge rounded bg-primary">Verified</span>
                @else
                <span data-bs-toggle="tooltip" data-bs-placement="top" title="Doesn't have marketing agreement or marketing agreement is expired" class="badge rounded bg-secondary">Not verified</span>
                @endif
                {{-- <input class="form-check-input" name="verified" type="checkbox" value="1" id="contactVerified" @if(defaultValue($contact, 'verified' )=='1' ) checked @endif>
                <label class="form-check-label" for="contactVerified">
                    Verified
                </label> --}}
            </div>
            <div class="col-12 col-sm-6 col-lg-4">
                <label class="form-label" for="landlord-ref_no">Landlord No.</label>
                <input type="text" class="form-control" readonly="readonly" value="{{ defaultValue($contact, 'record_no') }}">
            </div>
            <div class="col-12 col-sm-6 col-lg-4" id="qidField">
                @include('crm.components.form.input',
                [
                'fieldName' => 'qatar_id_no',
                'fieldLabel' => 'QID ID',
                'defaultValue' => defaultValue($contact, 'qatar_id_no')
                ]
                )
            </div>
            <input id="task_id" type="hidden" name="landlordData-task_id" value="{{ $taskIdInitialValue }}">
            <div class="col-12 col-sm-6 col-lg-4" id="qidUpload">
                <div class="{{$errors->has('landlordData-qatar_id_no_pdf') ? 'is-invalid' : ''}}">
                    <label for="fileQatarIdNoPdf" class="form-label">QID ID Upload</label>
                    <input @if($accessReadonly) readonly disabled @endif class="form-control form-control-sm" id="fileQatarIdNoPdf" name="landlordData-qatar_id_no_pdf" type="file">
                    @if(!is_null($landlordData) && !empty($landlordData->qatar_id_no_pdf_path))
                    <div class="mb-2">
                        <a href="{{route('broker-landlords.qatar_id_no_pdf.download', ['contactId' => $landlordData->contact_id])}}" class="link-primary">{{$landlordData->qatar_id_no_pdf_title}}</a>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="landlordData-qatar_id_no_pdf-remove" value="1" id="fileQatarIdNoPdfRemove">
                        <label class="form-check-label" for="fileQatarIdNoPdfRemove">
                            Remove file
                        </label>
                    </div>
                    @endif
                </div>

                @if($errors->has('landlordData-qatar_id_no_pdf'))
                <div class="invalid-feedback">
                    {{ $errors->first('landlordData-qatar_id_no_pdf') }}
                </div>
                @endif
            </div>
            <div class="col-12 col-sm-6 col-lg-4">
                @include('crm.components.form.input',
                [
                'fieldName' => 'name',
                'fieldLabel' => 'Full Name',
                'defaultValue' => defaultValue($contact, 'name')

                ]
                )
            </div>
            <div class="col-12 col-sm-6 col-lg-4">
                @include('crm.components.form.input',
                [
                'fieldName' => 'email_1',
                'fieldLabel' => 'Email',
                'fieldType' => 'email',
                'defaultValue' => defaultValue($contact, 'email_1')
                ]
                )
                <a class="btn btn-outline-secondary btn-sm mb-2 float-end d-none" style="margin-top:-14px" id="existingContactBtnForEmail" type="button">
                </a>
            </div>
            <div class="col-12 col-sm-6 col-lg-4">
                @include('crm.components.form.input-phone',
                [
                'fieldName' => 'mobile_1',
                'fieldLabel' => 'Phone Number',
                'defaultValue' => defaultValue($contact, 'mobile_1'),
                'defaultPrefixValue' => defaultValue($contact, 'prefix_mobile_1'),
                ]
                )
                <a class="btn btn-outline-secondary btn-sm mb-2 float-end d-none" style="margin-top:-14px" id="existingContactBtnForMobile" type="button">
                </a>
            </div>

            <div class="col-12 col-sm-6 col-lg-4">
                @include('crm.components.form.input-phone',
                [
                'fieldName' => 'mobile_2',
                'fieldLabel' => 'Phone Number(2)',
                'defaultValue' => defaultValue($contact, 'mobile_2'),
                'defaultPrefixValue' => defaultValue($contact, 'prefix_mobile_2'),
                ]
                )
            </div>
            <div class="col-12 col-sm-6 col-lg-4">
                @include('crm.components.form.input',
                [
                'fieldName' => 'landlordData-contact_person',
                'fieldLabel' => 'Contact Person',
                'defaultValue' => defaultValue($landlordData, 'contact_person')

                ]
                )
            </div>
            <div class="col-12 col-sm-6 col-lg-4">
                @include('crm.components.form.input',
                [
                'fieldName' => 'date_of_birth',
                'fieldLabel' => 'Date of birth',
                'fieldType' => 'date',
                'defaultValue' => defaultValue($contact, 'date_of_birth')
                ]
                )
            </div>
            <div class="col-12 col-sm-6 col-lg-4">
                @include('crm.components.form.input',
                [
                'fieldName' => 'unit_number',
                'fieldLabel' => 'Unit Number',
                'defaultValue' => defaultValue($contact, 'unit_number')
                ]
                )
            </div>
            <div class="col-12 col-sm-6 col-lg-4 mb-2">
                <label for="nationality" class="form-label">Nationality</label>
                @if($accessReadonly)
                {{$contact->nationality ? $contact->nationality->name : '-'}}
                @else
                <div id="nationality" class="{{ $errors->has('nationality_id') ? 'is-invalid' : '' }}"></div>
                @if($errors->has('nationality_id'))
                <div class="invalid-feedback" role="alert">
                    {{ $errors->first('nationality_id') }}
                </div>
                @endif
                @endif
            </div>

            @if($userIsAdmin || $userHasEditPermission || $userHasTemporaryAccess)
            <div class="col-12 col-sm-6 col-lg-4">
                @include('crm.components.form.select',
                [
                'fieldName' => 'landlordData-is_corporate_landlord',
                'fieldLabel' => 'Landlord',
                'options' => $is_corporateOptions,
                'defaultValue' => defaultValue($landlordData, 'is_corporate_landlord')
                ])
            </div>
            @else
            <input type="hidden" name="is_corporate_landlord" value="0">
            @endif
            {{--
            <div class="col-12 col-sm-6 col-lg-4">
                <div class="mb-3">
                    <label class="form-label" for="contact-towers">Towers</label>
                    @if($accessReadonly)
                    {{$contact->towers ? $contact->towers->map(function($t) { return $t->name; })->join(",")  : '-'}}
                    @else
                    <div id="contact-towers"></div>
                    @endif
                </div>
            </div>
            --}}
            <div class="col-12 col-sm-6 col-lg-4">
                @include('crm.components.form.input',
                [
                'fieldName' => 'landlordData-developer_name',
                'fieldLabel' => 'Developer',
                'fieldType' => 'input',
                'defaultValue' => defaultValue($landlordData, 'developer_name')
                ]
                )
            </div>
            {{-- @if($userIsAdmin)
            <div class="col-12 col-sm-6 col-lg-4">
                <div class="mb-3 {{$errors->has('landlordData-contract') ? 'is-invalid' : ''}}">
                    <label for="contract" class="form-label">Contract</label>
                    <input class="form-control form-control-sm" id="fileContract" name="landlordData-contract" type="file">
                    @if(!is_null($landlordData) && !empty($landlordData->contract_path))
                    <div class="mb-2">
                        <a href="{{route('broker-landlords.contract.download', ['contactId' => $landlordData->contact_id])}}" class="link-primary">{{$landlordData->contract_title}}</a>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="landlordData-contract-remove" value="1" id="fileContractRemove">
                        <label class="form-check-label" for="fileContractRemove">
                            Remove file
                        </label>
                    </div>
                    @endif
                </div>

                @if($errors->has('landlordData-contract'))
                <div class="invalid-feedback">
                    {{ $errors->first('landlordData-contract') }}
                </div>
                @endif
            </div>
            @endif --}}

        </div>
        @endcomponent

        @component('crm.components.form.section-card', ['sectionId' => 'unit-details'])
        <h4> Unit details</h4>
        <div class="row">
            @php
                $hasTowerError = false;
                foreach($errors->getMessages() as $field => $messages) {
                    $lowerField = strtolower($field);
                    if(str_contains($lowerField, "landlorddata-unit_tower_id") || str_contains($lowerField, "landlorddata-unit_no")) {
                        $hasTowerError = true;
                        break;
                    }
                }
            @endphp
            @if($hasTowerError)
                <div class="text-danger">Please make sure the Tower and Unit no are set for all the rows below</div>
            @endif
            <landlord-details></landlord-details>
        </div>
        @endcomponent


        @php
        $currentCorporateId = old('is_corporate_landlord') ?? $landlordData->is_corporate_landlord;
        @endphp

        @component('crm.components.form.section-card', ['sectionId' => 'contract'])
        <h3>Contract</h3>
        <div class="row">

        @if($userIsAdmin || $userHasEditPermission)
             <div class="col-12">
                <div class="mb-3{{$errors->has('landlordData-marketing_agreement') ? 'is-invalid' : ''}}">
                    <label for="fileMarketingAgreement" class="form-label">Marketing agreement</label>
                    <input class="form-control form-control-sm" id="fileMarketingAgreement" name="landlordData-marketing_agreement" type="file">
                    @if(!is_null($landlordData) && !empty($landlordData->marketing_agreement_path))
                    <div class="mb-2">
                    <a href="{{route('broker-landlords.marketing.download', ['contactId' => $landlordData->contact_id])}}" class="link-primary">{{$landlordData->marketing_agreement_title}}</a>
                    </div>
                    @endif
                    @if(!is_null($landlordData) && !empty($landlordData->marketing_agreement_path))
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="landlordData-marketing_agreement-remove" value="1" id="fileMarketingAgreementRemove">
                        <label class="form-check-label" for="fileMarketingAgreementRemove">
                            Remove file
                        </label>
                    </div>
                    @endif
                </div>
                @if($errors->has('landlordData-marketing_agreement'))
                <div class="invalid-feedback">
                    {{ $errors->first('landlordData-marketing_agreement') }}
                </div>
                @endif
            </div>
            @endif
            <div class="col-12 col-sm-6 col-lg-4">
                @include('crm.components.form.input',
                [
                    'fieldName' => 'contract_end_date',
                    'fieldLabel' => 'Contract end date',
                    'fieldType' => 'date',
                    'defaultValue' => defaultValue($contact, 'contract_end_date')
                ])
            </div>
        </div>
        @endcomponent
        @component('crm.components.form.section-card', ['sectionId' => 'closing'])
                <h3>Closing</h3>
                <div class="col-12 col-md-6">
                        <div class="mb-3 {{$errors->has('landlordData-booking_form') ? 'is-invalid' : ''}}">
                            <label for="fileBookingForm" class="form-label">Booking form</label>
                            @if(!is_null($landlordData) && !empty($landlordData->booking_form_path))
                                <div class="mb-2">
                                    <a href="{{route('broker-landlords.booking.download', ['contactId' => $landlordData->contact_id])}}"
                                       class="link-primary">{{$landlordData->booking_form_title}}</a>
                                </div>
                            @endif
                            @if(!$accessReadonly)
                            <input class="form-control form-control-sm" id="fileBookingForm" name="landlordData-booking_form"
                                   type="file">
                            @endif
                            @if(!is_null($landlordData) && !empty($landlordData->booking_form_path))
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="landlordData-booking_form-remove"
                                           value="1" id="fileBookingFormRemove">
                                    <label class="form-check-label" for="fileBookingFormRemove">
                                        Remove file
                                    </label>
                                </div>
                            @endif
                        </div>
                        @if($errors->has('landlordData-booking_form'))
                            <div class="invalid-feedback">
                                {{ $errors->first('landlordData-booking_form') }}
                            </div>
                        @endif
                </div>
                <div class="row">
                    <div class="col-12 col-sm-4 col-lg-3">
                        @include('crm.components.form.input',
                            [
                                'fieldName' => 'landlordData-payment_booking_fee',
                                'fieldLabel' => 'Booking fee',
                                'fieldType' => '',
                                'defaultValue' => defaultValue($landlordData, 'payment_booking_fee')
                            ]
                        )
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 col-sm-6">
                        @include('crm.components.form.textarea',
                            [
                                'fieldName' => 'landlordData-payment_cheque_addressed_to',
                                'fieldLabel' => 'Payment addressed to',
                                'defaultValue' => defaultValue($landlordData, 'payment_cheque_addressed_to'),
                                'rowsNo' => 3
                            ]
                        )
                    </div>
                    <div class="col-12 col-sm-6">
                        @include('crm.components.form.textarea',
                            [
                                'fieldName' => 'landlordData-payment_closing_requiring_documents',
                                'fieldLabel' => 'Required documents',
                                'defaultValue' => defaultValue($landlordData, 'payment_closing_requiring_documents'),
                                'rowsNo' => 3
                            ]
                        )
                    </div>
                    <div class="col-12 col-sm-6">
                        @include('crm.components.form.select',
                            [
                                'fieldName' => 'landlordData-payment_security_deposit_dated',
                                'fieldLabel' => 'Security deposit',
                                'options' => $datedUndatedOpts,
                                'defaultValue' => defaultValue($landlordData, 'payment_security_deposit_dated')
                            ]
                        )
                    </div>
        @endcomponent

        @component('crm.components.form.section-card', ['sectionId' => 'documents', 'isHidden' => !$currentCorporateId])
        <h3>Documents required</h3>
        <div class="row">
            <div class="col-12 mt-4">
                <label class="form-label">Availability list</label>
                <table class="table table-bordered table-condensed">
                    <tr>
                        <th>Title</th>
                        <th>Tower</th>
                        <th>Last update date</th>
                        @if($userIsAdmin || $userHasEditPermission)
                        <th>Remove</th>
                        @endif
                    </tr>
                    @if(count($landlordData->availability_lists ?? []) > 0)
                        @foreach($landlordData->availability_lists as $availability_list)
                        @php
                        $towerName = "-";
                        if(!empty($availability_list->towerId)) {
                            $currTower = $towersList->where('value', $availability_list->towerId)->first();
                            if(!is_null($currTower)) {
                                $towerName = $currTower['label'];
                            }
                        }
                        @endphp
                        <tr>
                            <td style="max-width: 200px;"><a href="{{ route('broker-landlords.availability.download', ['contactId' => $landlordData->contact_id, 'availabilityFilePath' => $availability_list->path]) }}">{{$availability_list->title}}</a></td>
                            <td>{{$towerName}}</td>
                            <td>{{$availability_list->aTime ?? '-'}}</td>
                            @if($userIsAdmin || $userHasEditPermission)
                            <td><label><input name="landlordData-availability_lists-remove[]" type="checkbox" value="{{$loop->index}}"></label> </td>
                            @endif
                        </tr>
                        @endforeach
                    @else
                    <tr>
                        <td colspan="@if($userIsAdmin || $userHasEditPermission) 3 @else 2 @endif">
                            <div>No availability uploaded</div>
                        </td>
                    </tr>
                    @endif
                </table>
                <div id="availabilityListsFilesContainer" class="d-flex flex-column gap-1"></div>
                @if($userIsAdmin || $userHasEditPermission)
                <div class="p-1 d-flex">
                    <button type="button" id="availabilityListsAddFilesBtn" class="btn btn-secondary btn-sm">Add file</button>
                </div>
                @endif
            </div>

            <div class="col-12 mt-4">
                <label class="form-label">Forms</label>
                <table class="table table-bordered table-condensed">
                    <tr>
                        <th>Title</th>
                        <th>Last update date</th>
                        @if($userIsAdmin || $userHasEditPermission)
                        <th>&nbsp;</th>
                        @endif
                    </tr>
                    @if(count($landlordData->forms ?? []) > 0)
                    @foreach($landlordData->forms as $form)
                    <tr>
                        <td style="max-width: 200px;"><a href="{{ route('broker-landlords.forms.download', ['contactId' => $landlordData->contact_id, 'filePath' => $form->path]) }}">{{$form->title}}</a></td>
                        <td>{{$form->aTime ?? '-'}}</td>
                        @if($userIsAdmin || $userHasEditPermission)
                        <td><label><input name="landlordData-forms-remove[]" type="checkbox" value="{{$loop->index}}"> Mark for removal</label></td>
                        @endif
                    </tr>
                    @endforeach
                    @else
                    <tr>
                        <td colspan="@if($userIsAdmin || $userHasEditPermission) 3 @else 2 @endif">
                            <div>No forms uploaded</div>
                        </td>
                    </tr>
                    @endif
                </table>
                <div id="formsFilesContainer" class="d-flex flex-column gap-1"></div>
                @if($userIsAdmin || $userHasEditPermission)
                <div class="p-1 d-flex">
                    <button type="button" id="formsAddFilesBtn" class="btn btn-secondary btn-sm">Add file</button>
                </div>
                @endif
            </div>
        </div>
        @endcomponent

        @if($contact->exists)
        @component('crm.components.form.section-card', ['sectionId' => 'listings'])
        <div class="mt-3 mb-3 d-none" id="messagesContainer">
            <div class="alert alert-warning text-end"></div>
        </div>
        <div>
            <h3>Listings</h3>
            <div id="invTable"></div>
        </div>
        @if($userIsAdmin || $userHasEditPermission)
        <div class="mt-2 mb-3">
            <dl class="row">
                <h4>Old info</h4>
                <dt class="col-sm-3">Phone #1</dt>
                <dd class="col-sm-9">{{$contact->phone_1 ?? '-'}}</dd>
                <dt class="col-sm-3">Phone #2</dt>
                <dd class="col-sm-9">{{$contact->phone_2 ?? '-'}}</dd>
                <dt class="col-sm-3">Mobile #1</dt>
                <dd class="col-sm-9">{{$contact->mobile_1 ?? '-'}}</dd>
                <dt class="col-sm-3">Mobile #2</dt>
                <dd class="col-sm-9">{{$contact->mobile_2 ?? '-'}}</dd>
                <dt class="col-sm-3">Unit NO</dt>
                <dd class="col-sm-9">{{$contact->unit_number ?? '-'}}</dd>
                <dt class="col-sm-3">Location</dt>
                <dd class="col-sm-9">{{$contact->location ?? '-'}}</dd>
                <dt class="col-sm-3">Tower(s)</dt>
                <dd class="col-sm-9">{{$contact->tower ?? '-'}}</dd>
            </dl>
        </div>
        @endif
        @endcomponent
        @endif
        @component('crm.components.form.section-card', ['sectionId' => 'operationHistory'])
            <h4>Notes</h4>
            <div class="row">
                <div class="col-12 col-sm-6 col-lg-4">
                    @include('crm.components.form.input',
                    [
                        'fieldName' => 'last_contact_date',
                        'fieldLabel' => 'Last contact date',
                        'fieldType' => 'date',
                        'defaultValue' => defaultValue($contact, 'last_contact_date')
                    ])
                </div>
                <div class="col-12">
                    @include('crm.components.form.textarea',
                        [
                            'fieldName' => 'notes',
                            'fieldLabel' => 'Notes',
                            'defaultValue' => defaultValue($contact, 'notes'),
                            'rowsNo' => 5
                        ]
                    )
                </div>
                <div class="col-12">
                    <div style="max-height:300px; overflow-y: auto;">
                        @foreach($operationHistory as $remark)
                            <article class="pb-2">
                                <aside>{{ $remark->content }}</aside>
                                <footer><small>{{ $remark->created_at->format('d M Y H:i') }} - <cite
                                            title="Source Title">{{ $remark->author ? $remark->author->name : '-' }}</cite></small>
                                </footer>
                            </article>
                        @endforeach
                    </div>
                    <div class="mt-3">
                        <label class="form-label" for="operation-history">Remarks</label>
                        <textarea @if($accessReadonly) disabled readonly @endif name="operation-history" class="form-control"
                                    id="" cols="30" rows="5"></textarea>
                    </div>
                </div>
            </div>
        @endcomponent
        @if(!$accessReadonly)
    </form>
    @endif
</div>
<div class="mt-3 mb-3">
    <div class="col-sm-12 header-btn text-end">
        @if(!$accessReadonly)
        <button class="btn btn-outline-primary" type="submit" form="landlordForm">{{ $contact->exists ? 'Update' : 'Create'  }}</button>
        @endif
        <a href="{{ route('broker-landlords.index')}}" class="btn btn-outline-secondary">Cancel </a>
    </div>
</div>

<script>
    window.addEventListener('load', () => {
        document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach((tooltip) => {
            new bootstrap.Tooltip(tooltip);
        });
    });
    const agents = JSON.parse('{!! addslashes(json_encode($agents->map(function($agent){ return $agent->only("id", "selectName"); })->values()->toArray())) !!}')
    const selectedTowers = JSON.parse('{!! addslashes($selectedTowersString) !!}');
    const towersList = JSON.parse('{!! addslashes(json_encode($towersList)) !!}');
    const nationalitiesMap = JSON.parse('{!! addslashes($nationalities->mapWithKeys(function($v) { return [$v->id => $v->name];  })->toJSON()) !!}');
    const nationalities = Object.keys(nationalitiesMap).map((key) => ({
        label: nationalitiesMap[key],
        value: key
    }));

    const corporateSelector = document.querySelector('#landlordData-is_corporate_landlord');

    window.addEventListener('load', () => {
        const nationalitySelectConfig = {
            ele: '#nationality',
            options: nationalities,
            search: true,
            name: 'nationality_id',
            additionalClasses: 'form-input',
            placeholder: 'Please select',
        };

        if ('{{ old("nationality_id", defaultValue($contact, "nationality_id")) }}') {
            nationalitySelectConfig.selectedValue = "{{ old('nationality_id', defaultValue($contact, 'nationality_id')) }}";
        }

        // const towersConfig = {
        //      ele: '#contact-towers',
        //      options: towersList,
        //      search: true,
        //      multiple: true,
        //      name: 'towers',
        //      placeholder: 'Please select',
        //      selectedValue: selectedTowers
        // };

        const configs = [
            nationalitySelectConfig,
            // towersConfig,
        ];
        @if(!$accessReadonly)
        configs.forEach(config => VirtualSelect.init(config))
        @endif
    })

    const createTRMarkupForListingRow = (item, index) => {
        const primaryImage = item.images.find(img => img.is_primary) || item.images[0];
        const firstPicMarkup = primaryImage ? `<img src="${primaryImage.img_url}" class="img-thumbnail" style="max-width: 100px"/>` : 'No image';
        let markup = `<tr>
                    <td>${index+1}</td>
                    <td>${firstPicMarkup}</td>
                    <td>${item.ref_no}</td>
                    <td>${item.property_type}</td>
                    <td>${item.location}</td>
                    <td>${item.tower.name ? item.tower.name : '-'}</td>
                    <td>${item.bedrooms_no ? item.bedrooms_no : '-'}</td>
                    <td>${item.bathrooms_no ? item.bathrooms_no : '-'}</td>
                    <td>${item.unit_no}</td>
                    <td><a href="/crm/inventory/${item.asset_id}/edit">Details</a></td>
                </tr>`

        return markup;

    }
    const contactId = '{{ $contact->exists ? $contact->id : 0 }}';

    if (contactId != 0) {
        request(`/api/crm/listing?contact_id=${contactId}&vt=landlord-details`)
            .then(data => {
                const tableRows = data.data.map((item, index) => createTRMarkupForListingRow(item, index)).join('');
                const table = `<table class="table">
                                        <thead>
                                            <tr>
                                                <th>#</th>
                                                <th>&nbsp;</th>
                                                <th>Ref No</th>
                                                <th>Property type</th>
                                                <th>Location</th>
                                                <th>Tower</th>
                                                <th>Bedrooms</th>
                                                <th>Bathrooms</th>
                                                <th>Unit No</th>
                                                <th>&nbsp;</th>
                                            </tr>
                                        </thead>
                                        <tbody>${tableRows ? tableRows : '<tr><td colspan="10">No records</td></tr>'}</tbody>
                                    </table>`
                const valueToWrite = data && data.data && data.data.length > 0 ? table : '<div>No listings yet.</div>';
                document.getElementById('invTable').innerHTML = valueToWrite;
            })
            .catch(err => console.log('do something with the err'));
    }

    const handleCorporateChange = () => {
        const selectedOption = corporateSelector.value;
        // document.querySelector('#qidField').classList.add('d-none');
        // document.querySelector('#qidUpload').classList.add('d-none');
        if (selectedOption == 0) {
            document.querySelector('#documents').classList.add('d-none');
            document.querySelector('#qidField').classList.remove('d-none');
            document.querySelector('#qidUpload').classList.remove('d-none');
        } else if (selectedOption == 1) {
            document.querySelector('#documents').classList.remove('d-none');
            document.querySelector('#qidField').classList.add('d-none');
            document.querySelector('#qidUpload').classList.add('d-none');
        }
    }

    @if(!$accessReadonly && ($userIsAdmin || $userHasEditPermission))
    corporateSelector.addEventListener('change', () => {
        handleCorporateChange();
    })
    @endif

    document.getElementById('mobile_1').addEventListener('change',
    function checkContactExistsByPhone(event){
        const data = {
            phone: event.target.value,
            id: '{{$landlordData ? $landlordData->contact_id : null}}'
        }
        if(event.target.value.length > 0){
        return fetch("/api/contacts-list/check-contact-exists-phone", {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json"
            }
        })
        .then(response => response.json())
        .then(serverReturn => {
            const input = document.getElementById('mobile_1');
            if (serverReturn && Object.keys(serverReturn).length > 0) {
                document.getElementById('existingContactBtnForMobile').classList.remove('d-none');
                document.getElementById('existingContactBtnForMobile').href = '/crm/broker-landlords/'+serverReturn.existingContactId+'/edit';
                if(serverReturn.contactHasLandlord == false){
                    document.getElementById("existingContactBtnForMobile").textContent = "Use this contact";

                    input.classList.add('is-invalid');
                    const parent = input.closest('.input-group.has-validation');
                    const errorDiv = parent ? parent.querySelector('.invalid-feedback') : null;
                    if (errorDiv) {
                        errorDiv.innerText = 'This contact already exists';
                        input.classList.add('is-invalid');
                    } else {
                        const newErrorDiv = document.createElement('div');
                        newErrorDiv.className = 'invalid-feedback';
                        newErrorDiv.setAttribute('role', 'alert');
                        newErrorDiv.innerText = 'This contact already exists';
                        parent.appendChild(newErrorDiv);
                        input.classList.add('is-invalid');
                    }
                }else{
                    document.getElementById("existingContactBtnForMobile").textContent = "See landlord details";

                    input.classList.add('is-invalid');
                    const parent = input.closest('.input-group.has-validation');
                    const errorDiv = parent ? parent.querySelector('.invalid-feedback') : null;
                    if (errorDiv) {
                        errorDiv.innerText = 'This contact already exists as a landlord';
                        input.classList.add('is-invalid');
                    } else {
                        const newErrorDiv = document.createElement('div');
                        newErrorDiv.className = 'invalid-feedback';
                        newErrorDiv.setAttribute('role', 'alert');
                        newErrorDiv.innerText = 'This contact already exists as a landlord';
                        parent.appendChild(newErrorDiv);
                        input.classList.add('is-invalid');
                    }
                }
            }else{
                input.classList.remove('is-invalid');
                document.getElementById('existingContactBtnForMobile').classList.add('d-none');
            }
            return serverReturn;
        });
        }
    });

    document.getElementById('email_1').addEventListener('change',
    function checkContactExists(event){
        const data = {
            email: event.target.value,
            id: '{{$landlordData ? $landlordData->contact_id : null}}'
        }
        if(event.target.value.length > 0){
        return fetch("/api/contacts-list/check-contact-exists", {
            method: "POST",
            body: JSON.stringify(data),
            headers: {
                "Content-Type": "application/json"
            }
        })
        .then(response => response.json())
        .then(serverReturn => {
            const input = document.getElementById('email_1');
            if (serverReturn && Object.keys(serverReturn).length > 0) {
                document.getElementById('existingContactBtnForEmail').classList.remove('d-none');
                document.getElementById('existingContactBtnForEmail').href = '/crm/broker-landlords/'+serverReturn.existingContactId+'/edit';
                if(serverReturn.contactHasLandlord == false) {
                    document.getElementById("existingContactBtnForEmail").textContent = "Use this contact";

                    input.classList.add('is-invalid');
                    const parent = input.closest('.input-group.has-validation');
                    const errorDiv = parent ? parent.querySelector('.invalid-feedback') : null;
                    if (errorDiv) {
                        errorDiv.innerText = 'This contact already exists';
                        input.classList.add('is-invalid');
                    } else {
                        const newErrorDiv = document.createElement('div');
                        newErrorDiv.className = 'invalid-feedback';
                        newErrorDiv.setAttribute('role', 'alert');
                        newErrorDiv.innerText = 'This contact already exists';
                        parent.appendChild(newErrorDiv);
                        input.classList.add('is-invalid');
                    }
                }else{
                    document.getElementById("existingContactBtnForEmail").textContent = "See landlord details";

                    input.classList.add('is-invalid');
                    const parent = input.closest('.input-group.has-validation');
                    const errorDiv = parent ? parent.querySelector('.invalid-feedback') : null;
                    if (errorDiv) {
                        errorDiv.innerText = 'This contact already exists as a landlord';
                        input.classList.add('is-invalid');
                    } else {
                        const newErrorDiv = document.createElement('div');
                        newErrorDiv.className = 'invalid-feedback';
                        newErrorDiv.setAttribute('role', 'alert');
                        newErrorDiv.innerText = 'This contact already exists as a landlord';
                        parent.appendChild(newErrorDiv);
                        input.classList.add('is-invalid');
                    }
                }
            }else{
                input.classList.remove('is-invalid');
                document.getElementById('existingContactBtnForEmail').classList.add('d-none');
            }
            return serverReturn;
        });
        }
    });
</script>

@endsection
@section('body-js')
<script type="text/javascript" src="{{mix('/js/virtual-select.min.js')}}"></script>
<script type="text/javascript" src="{{mix('/js/landlord-details.js')}}"></script>
@if($userIsAdmin || $userHasEditPermission)
<script>
    class FileUploader {
        filesContainer = document.querySelector('#formsFilesContainer');

        addFile() {
            const fileInput = document.createElement('input');
            fileInput.setAttribute('type', 'file');
            fileInput.setAttribute('name', 'landlordData-forms[]');
            fileInput.classList.add('form-control', 'form-control-sm');

            const wrapperDiv = document.createElement('div');
            wrapperDiv.classList.add('p-1');
            wrapperDiv.appendChild(fileInput)
            this.filesContainer.appendChild(wrapperDiv);
        }
    }

    class AvailabilityListsFileUploader {
        availabilityListsFilesContainer = document.querySelector('#availabilityListsFilesContainer');

        availabilityAddFile() {
            const availabilityListsFileInput = document.createElement('input');
            availabilityListsFileInput.setAttribute('type', 'file');
            availabilityListsFileInput.setAttribute('name', 'landlordData-availability_lists[]');
            availabilityListsFileInput.classList.add('form-control', 'form-control-sm');

            const wrapperDiv = document.createElement('div');
            wrapperDiv.classList.add(['add-file-container']);
            const div1 = document.createElement('div');
            div1.classList.add("p-2")
            div1.appendChild(availabilityListsFileInput)
            const div2 = document.createElement('div');
            const towersSelect = document.createElement('select');
            towersSelect.setAttribute('name', 'landlordData-availability_list_towers[]');
            towersSelect.classList.add('form-select', 'form-select-sm');
            towersSelect.style.width = "250px";
            towersSelect.style.maxWidth = "250px";
            const opt = document.createElement('option');
            opt.text = "Select a tower";
            opt.value = "";
            towersSelect.add(opt);
            if(selectedTowers.length > 0) {
                selectedTowers.forEach(towerData => {
                    const usedTower = towersList.find(item => item.value === Number(towerData[0]));
                    if(!!usedTower) {
                        const opt = document.createElement('option');
                        opt.text = usedTower.label;
                        opt.value = usedTower.value;
                        towersSelect.add(opt);
                    }
                })
            }

            div2.classList.add("p-2")
            div2.appendChild(towersSelect)
            wrapperDiv.appendChild(div1)
            wrapperDiv.appendChild(div2)
            this.availabilityListsFilesContainer.appendChild(wrapperDiv);
        }
    }
    window.addEventListener('load', () => {
        const fu = new FileUploader();
        const availabilityListsFu = new AvailabilityListsFileUploader();

        const addFileBtn = document.querySelector('#formsAddFilesBtn');
        const availabilityListsAddFileBtn = document.querySelector('#availabilityListsAddFilesBtn');
        addFileBtn.addEventListener('click', () => fu.addFile());
        availabilityListsAddFileBtn.addEventListener('click', () => availabilityListsFu.availabilityAddFile());
        handleCorporateChange();
    });
    const selectSelection = document.querySelectorAll('.input-group .dropdown-menu')
        for (const selection of selectSelection) {
            selection.addEventListener('click', (evt) => {
                if(evt.target.classList.contains('countryLi')) {
                    const node = evt.target;
                    const countryName = node.getAttribute('data-countryname')
                    const countryPrefix = node.getAttribute('data-countryprefix')
                    const countryCode = node.getAttribute('data-countrycode')
                    const captionContainer = node.closest('.fg-dropdown').querySelector('.dropdown__caption')
                    const hiddenInput = node.closest('.fg-dropdown').querySelector('.phonePrefixInput')
                    hiddenInput.value = countryPrefix;
                    captionContainer.innerHTML = `<span class="flag flag-${countryCode}"></span> ${countryPrefix}`;
                }
            })
        }
</script>
@endif
@endsection
